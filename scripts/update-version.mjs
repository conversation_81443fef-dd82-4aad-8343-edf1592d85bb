import fs from "fs";
import { execSync } from "child_process";
import path from "path";

// Path to version file
const versionFilePath = path.resolve("public/version.json");

// Read existing version
let baseVersion = "1.0.0";
if (fs.existsSync(versionFilePath)) {
  const existingData = JSON.parse(fs.readFileSync(versionFilePath, "utf8"));
  const currentVersion = existingData.version?.split("-")[0]; // e.g., '1.0.1'
  if (currentVersion && /^\d+\.\d+\.\d+$/.test(currentVersion)) {
    baseVersion = currentVersion;
  }
}

// Increment patch version (x.y.z)
const [major, minor, patch] = baseVersion.split(".").map(Number);
const newVersion = `${major}.${minor}.${patch + 1}`;

// Optional: Add Git hash or timestamp

// Final version format
const finalVersion = `${newVersion}`;

// Write version.json
fs.writeFileSync(
  versionFilePath,
  JSON.stringify({ version: finalVersion }, null, 2),
  "utf8"
);

console.log("✅ version.json updated:", finalVersion);
