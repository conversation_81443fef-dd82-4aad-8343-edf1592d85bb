(function () {
  'use strict';

  // ---------------------------
  // Helpers at IIFE root (fixes eslint no-inner-declarations)
  // ---------------------------
  let PARAM_PREFIX = 'hw_'; // will be set after we know widgetId

  const getContainer = (targetSelector) => {
    let el = null;
    if (targetSelector) el = document.querySelector(targetSelector);
    if (!el) el = document.getElementById('hop-widget-root') || document.querySelector('[data-hop-widget]');
    if (!el) {
      el = document.createElement('div');
      el.id = 'hop-widget-root';
      document.body.appendChild(el);
    }
    return el;
  };

  const readFiltersFromUrl = () => {
    const p = new URLSearchParams(window.location.search);
    const out = {};
    p.forEach((v, k) => {
      if (k.startsWith(PARAM_PREFIX)) {
        const key = k.slice(PARAM_PREFIX.length);
        out[key] = v;
      }
    });
    return out;
  };

  const writeFiltersToUrl = (filters, opts) => {
    const options = opts || {};
    const replace = options.replace !== undefined ? options.replace : true;

    const url = new URL(window.location.href);

    // Remove previous keys for this widget
    Array.from(url.searchParams.keys()).forEach((k) => {
      if (k.startsWith(PARAM_PREFIX)) url.searchParams.delete(k);
    });

    // Add current keys
    if (filters && typeof filters === 'object') {
      Object.keys(filters).forEach((k) => {
        const val = filters[k];
        if (val !== undefined && val !== null && String(val).trim() !== '') {
          url.searchParams.set(PARAM_PREFIX + k, String(val));
        }
      });
    }

    const method = replace ? 'replaceState' : 'pushState';
    window.history[method]({ filters }, '', url);
  };

  // ---------------------------
  // Main init
  // ---------------------------
  try {
    // Find the <script> that loaded this file
    const findCurrentScript = () => {
      if (document.currentScript) return document.currentScript;
      const scripts = Array.from(document.querySelectorAll('script'));
      return scripts.find((s) => s.src && s.src.includes('embed.js')) || null;
    };

    const currentScript = findCurrentScript();

    let widgetId = null;
    let origin = null;
    let target = null;
    const config = {};

    if (currentScript) {
      const scriptUrl = new URL(currentScript.src, window.location.href);
      widgetId = scriptUrl.searchParams.get('widgetId');
      origin = scriptUrl.searchParams.get('origin') || scriptUrl.origin;
      target = scriptUrl.searchParams.get('target');

      scriptUrl.searchParams.forEach((value, key) => {
        if (!['widgetId', 'origin', 'target'].includes(key)) {
          config[key] = value;
        }
      });
    }

    // Fallback: read widgetId from DOM
    if (!widgetId) {
      const domRoot = document.getElementById('hop-widget-root') || document.querySelector('[data-hop-widget]');
      widgetId = (domRoot && (domRoot.dataset.widgetId || domRoot.dataset.hopWidget)) || null;
    }

    if (!widgetId) {
      console.error('Hop Widget: ❌ Missing widgetId. Provide it via script query param, data-widget-id, or data-hop-widget.');
      return;
    }

    console.log('✅ Hop Widget: Initializing for widgetId =', widgetId);

    // Now that we know widgetId, set a unique URL param prefix
    PARAM_PREFIX = `hw_${widgetId}_`;

    // Resolve / create container
    const container = getContainer(target);
    container.dataset.widgetId = widgetId;
    Object.keys(config).forEach((k) => { container.dataset[k] = config[k]; });

    // -------- URL <-> Filter Sync --------
    const initialFilters = readFiltersFromUrl();

    // Widget -> Host URL: when widget fires 'hop-filter-change'
    container.addEventListener('hop-filter-change', (e) => {
      const detail = (e && e.detail) || {};
      // mode: 'live' (replaceState) vs 'apply' (pushState)
      const mode = detail.mode || 'live';
      const filters = detail.filters || (typeof detail === 'object' ? detail : {});
      writeFiltersToUrl(filters, { replace: mode !== 'apply' });
    });

    // Host Back/Forward -> Widget: send 'hop-set-filters'
    window.addEventListener('popstate', () => {
      const f = readFiltersFromUrl();
      container.dispatchEvent(new CustomEvent('hop-set-filters', { detail: f }));
    });
    const resolvedOrigin = origin || (currentScript ? new URL(currentScript.src).origin : window.location.origin);

    const css = document.createElement('link');
    css.rel = 'stylesheet';
    css.href = `${resolvedOrigin}/widgets/widget.css`;
    css.onerror = () => console.warn(`Hop Widget: CSS not found at ${css.href}`);
    document.head.appendChild(css);
    // -------- Load the widget bundle (render.js) --------
    const renderScript = document.createElement('script');
    renderScript.type = 'module';

    renderScript.src = `${resolvedOrigin}/widgets/render.js`;
    renderScript.onerror = () => {
      console.error(`Hop Widget: Failed to load ${resolvedOrigin}/widgets/render.js`);
      if (resolvedOrigin !== 'https://cdn.hopwellness.com') {
        console.log('Hop Widget: Trying CDN fallback...');
        const cdn = document.createElement('script');
        cdn.type = 'module';
        cdn.src = 'https://cdn.hopwellness.com/widgets/render.js';
        document.body.appendChild(cdn);
      }
    };
    document.body.appendChild(renderScript);

    // -------- Public API on window --------
    window.HopWidget = window.HopWidget || {};
    window.HopWidget.config = { widgetId, origin: resolvedOrigin, ...config, initialFilters };

    // Trigger a manual data refresh inside the widget
    window.HopWidget.refresh = function refresh() {
      container.dispatchEvent(new CustomEvent('hop-widget-refresh'));
    };

    // Programmatically set filters from host code
    window.HopWidget.setFilters = function setFilters(filters, options) {
      writeFiltersToUrl(filters, { replace: !(options && options.push) });
      container.dispatchEvent(new CustomEvent('hop-set-filters', { detail: filters || {} }));
    };

    // Read current filters from the URL
    window.HopWidget.getFiltersFromUrl = function getFiltersFromUrl() {
      return readFiltersFromUrl();
    };

    // Tell the widget its initial filters after it mounts listeners in render.js
    Promise.resolve().then(() => {
      container.dispatchEvent(new CustomEvent('hop-set-filters', { detail: initialFilters }));
    });
  } catch (err) {
    console.error('Hop Widget: 💥 Failed to initialize widget:', err);
  }
})();
