name: Deploy Dashboard to EC2

on:
    push:
        branches: [production]

jobs:
    deploy:
        runs-on: ubuntu-latest
        environment: Production

        steps:
            - uses: actions/checkout@v3

            - name: Setup Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: '22'
                  cache: 'yarn'

            - name: Create env file
              run: |
                  cat << EOF > .env.production
                  VITE_PROJECT_URL=${{ vars.VITE_PROJECT_URL }}
                  VITE_APP_MODE=${{ vars.VITE_APP_MODE }}
                  EOF

            - name: Install yarn
              run: npm install -g yarn

            - name: Install dependencies
              run: yarn install

            - name: Build
              run: yarn build:prod

            - name: Create SSH directory
              run: mkdir -p ~/.ssh/

            - name: Add SSH private key
              run: |
                  echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
                  chmod 600 ~/.ssh/id_rsa

            - name: Add host to known hosts
              run: ssh-keyscan -H ${{ secrets.SERVER_HOST }} >> ~/.ssh/known_hosts

            - name: Check deploy path
              run: |
                  ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} '
                    if [ ! -d "${{ vars.DEPLOY_PATH }}" ]; then
                      sudo mkdir -p ${{ vars.DEPLOY_PATH }}
                    fi'

            - name: Deploy to server
              run: |
                  rsync -avz --delete ./dist/ ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/tmp/dashboard-deploy
                  ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} '
                    sudo rm -rf ${{ vars.DEPLOY_PATH }}/* 
                    sudo mv /tmp/dashboard-deploy/* ${{ vars.DEPLOY_PATH }}/ 
                    sudo chown -R www-data:www-data ${{ vars.DEPLOY_PATH }}
                  '

            - name: Reload Nginx Configuration
              run: |
                  ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} 'sudo systemctl reload nginx'
