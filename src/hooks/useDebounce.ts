import debounce from 'lodash.debounce';
import React, { useEffect, useMemo, useRef } from 'react';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { EmailOrMobileValidation } from '~/redux/actions/staff-action';

type CallbackFunction = (...args: any[]) => void;

export const useDebounce = (
    callback: CallbackFunction,
    timer: number = 1000
): CallbackFunction => {
    const debouncedCallbackRef = useRef<CallbackFunction | null>(null);

    const debouncedCallback = useMemo(() => {
        const debouncedFunction = (...args: any[]) => {
            if (debouncedCallbackRef.current) {
                debouncedCallbackRef.current(...args);
            }
        };

        return debounce(debouncedFunction, timer);
    }, [timer]);

    useEffect(() => {
        debouncedCallbackRef.current = callback;
    }, [callback, debouncedCallback]);

    useEffect(() => {
        return () => {
            debouncedCallback.cancel(); // Clean up the debounce timer
        };
    }, [debouncedCallback]);

    return debouncedCallback;
};

export const useEmailMobileValidator = () => {
    const dispatch = useAppDispatch();
    const pending = React.useRef<{
        [key: string]: ReturnType<typeof setTimeout>;
    }>({});

    const validate = (value: string, isEmail: boolean, id?: string) => {
        return new Promise<void>((resolve, reject) => {
            const key = isEmail ? 'email' : 'mobile';
            if (pending.current[key]) {
                clearTimeout(pending.current[key]);
            }

            pending.current[key] = setTimeout(async () => {
                try {
                    const res = await dispatch(
                        EmailOrMobileValidation({ value, id })
                    ).unwrap();
                    console.log('RES for email is', res);

                    if (res.res.data) {
                        reject(
                            new Error(
                                `${
                                    isEmail ? 'Email' : 'Mobile'
                                } is already in use.`
                            )
                        );
                    } else {
                        resolve();
                    }
                } catch (error) {
                    reject(new Error('Validation failed.'));
                }
            }, 500);
        });
    };

    return validate;
};

//! Example

// import React, { useState } from 'react';

// const Input = () => {
//     const [value, setValue] = useState<string>('');

//     const debouncedRequest = useDebounce(() => {
//         //! send request to the backend
//         //! access to latest state here
//*         console.log(value);
//     });

//     const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//         const newValue = e.target.value;
//         setValue(newValue);

//
