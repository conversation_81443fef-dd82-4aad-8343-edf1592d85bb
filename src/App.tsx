import { Layout, theme } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import Container from '~/components/container.js/container';
import ErrorBoundary from '~/components/container.js/error-boundary';
import Routes from '~/routes/dashboard-routes';
import SideBar from './components/navigation/sidebar';
import TopBar from './components/navigation/topBar';
import { Content } from 'antd/es/layout/layout';
import { connect, ConnectedProps, useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import store, { RootState } from './redux/store';
import AuthRoutes from './routes/auth-routes';
import { RoleType, Staff_Roles } from './types/enums';
import { useAppDispatch, useAppSelector } from './hooks/redux-hooks';
import { CountryList } from './redux/actions/common-action';
import { GetSettings } from './redux/actions/settings-actions';
import { ClearPosPurchasedData } from './redux/slices/purchaged-slice';
import { setCollapsed } from './redux/slices/topBar-slice';
import { GetAllPermissions } from './redux/actions/permission-action';
import { FacilitiesList } from './redux/actions/facility-action';

const mapStateToProps = (state: RootState) => ({
    is_login: state.auth_store.is_Login,
    role: state.auth_store.role,
});

const connector = connect(mapStateToProps);

type PropsFromRedux = ConnectedProps<typeof connector>;

const App: React.FC<PropsFromRedux> = (props) => {
    // const [collapsed, setCollapsed] = useState(false);
    const {
        token: { colorBgContainer, borderRadiusLG },
    } = theme.useToken();

    const [location, setLocation] = useLocation();
    // const prevLocation = useRef<string | null>(null);

    const dispatch = useAppDispatch();
    const { role, user, userId } = useAppSelector(
        (state: any) => state.auth_store
    );

    const updateFavicon = (iconPath: string) => {
        let link = document.querySelector(
            "link[rel~='icon']"
        ) as HTMLLinkElement;
        if (!link) {
            link = document.createElement('link');
            link.rel = 'icon';
            document.head.appendChild(link);
        }
        link.href = iconPath ? iconPath : '/assets/hop-H.png';
    };

    useEffect(() => {
        if (!user || !user.name) {
            return;
        }
        if (role === 'superAdmin') {
            document.title = 'Hop Wellness';
            updateFavicon('/assets/hop-H.png');
        } else {
            document.title = `${user?.name} - Hop Wellness`;
            updateFavicon('/assets/hop-H.png');
        }
    }, [role, user]);

    useEffect(() => {
        if (props.is_login) {
            if (role != RoleType.SUPER_ADMIN) {
                dispatch(FacilitiesList({ page: 1, pageSize: 10 }));
            }
            dispatch(CountryList({ page: 1, pageSize: 50 }));

            if (
                role !== Staff_Roles.trainer &&
                (role === RoleType.ORGANIZATION || Staff_Roles[role])
            ) {
                dispatch(GetSettings({}));
            }
            if (role != RoleType.ORGANIZATION && role != RoleType.SUPER_ADMIN) {
                dispatch(GetAllPermissions({ userId })).then(
                    (response: any) => {
                        const state = store.getState();
                        const all_permissions_for_role =
                            state.permission_store.all_permissions_for_role;
                        // console.log(
                        //     'All permissions for role:',
                        //     all_permissions_for_role
                        // );
                    }
                );
            }
        }
    }, [props.is_login]);
    
    useEffect(() => {
        const checkVersion = async () => {
            try {
                const res = await fetch(`/version.json?ts=${Date.now()}`, {
                    cache: 'no-store',
                });

                const data = await res.json();
                // console.log(data, 'version');
                const currentVersion = localStorage.getItem('appVersion');

                if (data.version !== currentVersion) {
                    localStorage.setItem('appVersion', data.version);
                    window.location.replace(window.location.href);
                    // force reload
                }
            } catch (err) {
                console.error('Version check failed:', err);
            }
        };

        checkVersion();

        const interval = setInterval(checkVersion, 30000);

        return () => clearInterval(interval);
    }, []);
    useEffect(() => {
        const isWaitTimePage = location.startsWith('/wait-time/');
        if (!props.is_login) {
            if (
                location === '/set-password' ||
                location === '/reset-password' ||
                location === '/signup' ||
                location === '/verify-otp' ||
                isWaitTimePage
            ) {
                return;
            } else {
                setLocation('/signin');
            }
        } else {
            if (location === '/' && props.role === RoleType.SUPER_ADMIN) {
                setLocation('/organizations');
            } else if (
                location === '/' &&
                props.role === RoleType.ORGANIZATION
            ) {
                setLocation('/setup-checklist');
            }
        }
    }, [props.is_login]);

    useEffect(() => {
        const allowedRoutes = [
            '/payment',
            '/point-of-sales',
            /^\/point-of-sales\/[^/]+\/[^/]+$/,
        ];

        //     const current = parseURL(location);
        // const previous = prevLocation.current
        //     ? parseURL(prevLocation.current)
        //     : null;

        const isAllowedRoute = allowedRoutes?.some((route) => {
            if (typeof route === 'string') {
                return location === route;
            }
            if (route instanceof RegExp) {
                return route.test(location);
            }
            return false;
        });

        // if (
        //     previous &&
        //     previous.pathname === '/payment' &&
        //     previous.search &&
        //     current.pathname.startsWith('/point-of-sales')
        // ) {
        //     dispatch(ClearPosPurchasedData());
        // }

        // prevLocation.current = location;

        if (!isAllowedRoute) {
            dispatch(ClearPosPurchasedData());
        }
    }, [location, dispatch]);
    const collapsed = useSelector((state: any) => state.sidebar.collapsed);
    const handleSetCollapsed = (val: boolean) => {
        dispatch(setCollapsed(val));
    };

    return (
        <ErrorBoundary>
            <Container>
                {props.is_login ? (
                    <Layout className="max-h-screen overflow-hidden">
                        <SideBar
                            collapsed={collapsed}
                            setCollapsed={handleSetCollapsed}
                        />

                        <Layout>
                            <TopBar
                                collapsed={collapsed}
                                setCollapsed={handleSetCollapsed}
                                colorBgContainer={colorBgContainer}
                            />

                            <Content
                                style={{
                                    // margin: '20px',
                                    marginLeft: '1px',
                                    padding: 24,
                                    minHeight: 280,

                                    background: colorBgContainer,
                                    // borderRadius: borderRadiusLG,
                                }}
                                className=" overflow-y-scroll shadow-md"
                            >
                                <Routes />
                            </Content>
                        </Layout>
                    </Layout>
                ) : (
                    <AuthRoutes />
                )}
            </Container>
        </ErrorBoundary>
    );
};

export default connector(App);
