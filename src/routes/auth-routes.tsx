import React from 'react';
import { Route, Router, Switch } from 'wouter';
import SignIn from '~/screens/auth/signin';
import SetPasswordScreen from '~/screens/auth/set-password';
import ForgetPasswordScreen from '~/screens/auth/forget-password';
import VerifyOtpScreen from '~/screens/auth/verify-otp';
import ForgetSetPasswordScreen from '~/screens/auth/forget-setpassword';
import SignUp from '~/screens/auth/signup';
import WaitTimeScreen from '~/screens/waitTimeScreen';

const RouteNotFound = React.lazy(() => import('~/screens/404'));

const AuthRoutes: React.FC = () => {
    return (
        <>
            <Router>
                <React.Suspense fallback={<h1>Loading Routes...</h1>}>
                    <Switch>
                        <Route path="/signin" component={SignIn} />
                        <Route path="/signup" component={SignUp} />
                        <Route
                            path="/set-password"
                            component={SetPasswordScreen}
                        />
                        <Route
                            path="/reset-password"
                            component={ForgetPasswordScreen}
                        />
                        <Route path="/verify-otp" component={VerifyOtpScreen} />
                        <Route
                            path="/forget-setpassword"
                            component={ForgetSetPasswordScreen}
                        />
                        <Route path="/wait-time/:id" component={WaitTimeScreen} />
                        <Route component={RouteNotFound} />
                    </Switch>
                </React.Suspense>
            </Router>
        </>
    );
};

export default AuthRoutes;
