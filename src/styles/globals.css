/* "base": for resetting the css and style the native elements */
@tailwind base;
/* "components": for style your own custom classes */
@tailwind components;
/* "utilities": for styling of the classes provided by the tailwind */
@tailwind utilities;

/* .ant-switch-checked {
    @apply bg-switch-on !important;
}

.ant-switch {
    @apply bg-switch-off !important;
} */

@layer components {
    /* always set this like this html and body element */
    /* * {
        --background-color: rgb(13, 0, 255);
        --color: #fff;
    }

    h4 {
        background-color: var(--background-color);
        color: var(--color) !important;
    } */

    html,
    body {
        background-color: #ffffff;
        font-family: 'Roboto', sans-serif;
        min-height: 100vh;
        width: 100%;
    }

    html {
        font-size: 62.5%;
    }

    body {
        font-size: 1.6rem;
        font-family: 'Roboto', sans-serif !important;
    }

    .font-semibold {
        font-weight: 600;
        font-family: 'Roboto', sans-serif;
    }

    .checkbox-text {
        font-family: 'Roboto', sans-serif;
    }

    h4 {
        /* font-size: 1.35vw !important; */
        font-weight: bold;
        font-family: 'Roboto', sans-serif !important;
        margin-bottom: 0 !important;
    }

    label {
        font-family: 'Roboto', sans-serif !important;
    }

    input {
        font-family: 'Roboto', sans-serif !important;
    }

    div {
        font-family: 'Roboto', sans-serif !important;
    }

    p {
        font-family: 'Roboto', sans-serif !important;
    }

    .custom-collapse .ant-collapse-content {
        background-color: #f8f8f8;
        /* Replace with your desired color */
    }

    .waitTime_screen_font {
        font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
        line-height: normal !important;
    }

    #root {
        height: 100vh;
        width: 100%;
    }

    /* min-height is not inherited by the child  so we have replace the min-height by the height property */
}

/* Ripple Animation */
.custom_btn {
    position: relative;
    overflow: hidden;
    transition: 500ms;
    cursor: pointer;
}

.custom_btn:hover {
    background-color: transparent;
}

span.custom_ripple {
    position: absolute;

    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: ripple_animation 500ms linear;
    background-color: rgba(255, 255, 255, 0.7);
}

@keyframes ripple_animation {
    to {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* table pagination */
.ant-table-wrapper .ant-table-pagination-right {
    justify-content: center !important;
}

.ant-pagination .ant-pagination-prev,
.ant-pagination .ant-pagination-next {
    border: 1px solid #e6ebf1;
    border-radius: 50% !important;
    overflow: hidden;
}

.ant-pagination .ant-pagination-prev .ant-pagination-item-link,
.ant-pagination .ant-pagination-next .ant-pagination-item-link {
    display: grid;
    place-items: center;
}

.ant-pagination .ant-pagination-item-active {
    background-color: #8143d1;
    border-color: #8143d1 !important;
    border-radius: 50%;
}

.ant-pagination .ant-pagination-item-active a {
    color: #fff !important;
}

.ant-pagination .ant-pagination-item:not(.ant-pagination-item-active):hover {
    border-radius: 50%;
}

.custom-tabs .ant-tabs-ink-bar {
    display: none;
}

.custom-tabs .ant-tabs-nav::before {
    display: none;
}

.justified-tabs .ant-tabs-nav-list {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.justified-tabs .ant-tabs-tab {
    flex: 1;
    text-align: center;
    justify-content: center;
}

#swtich-off td.ant-table-cell button {
    background-color: #a9abae;
}

.ql-toolbar.ql-snow {
    border-radius: 8px 8px 0 0;
}

.ql-container.ql-snow {
    border-radius: 0 0 8px 8px;
    min-height: 200px;
}

.avatar-uploader .ant-upload.ant-upload-select {
    width: 100% !important;
    height: 30vh !important;
}

@media only screen and (min-width: 768px) and (max-width: 1280px) {

    .ant-input,
    .ant-picker-input>input {
        font-size: 14px !important;
        padding: 5px 8px !important;
    }

    .ant-form-item-label {
        padding-bottom: 0 !important;
    }

    .ant-select,
    .ant-select-selection-search-input {
        font-size: 14px !important;
        height: 30px !important;
    }

    .ant-select-selection-item {
        font-size: 14px !important;
    }

    .justified-tabs .ant-tabs-nav-list {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    .justified-tabs .ant-tabs-tab {
        flex: 1;
        text-align: center;
        justify-content: center;
    }

    #customer-listing-tab {
        width: 150px !important;
    }
}

.ant-form-item-required {
    flex-direction: row-reverse !important;
    column-gap: 5px;
}

.ant-form-item-required::after {
    display: none;
}

/* =====================checkup collapse==================== */
.ant-collapse-borderless>.ant-collapse-item {
    background-color: #ffffff !important;
    border: 1px solid #e6ebf1 !important;
}

/* ===========================================input margin bottom======================== */

.ant-form-item {
    margin-bottom: 15px;
}

.organizationFilter.ant-form-item {
    margin-bottom: 0px !important;
}

/* ===========================================Global PLaceholders======================== */
::placeholder {
    font-size: 13px !important;
}

.ant-select-selection-placeholder {
    font-size: 13px !important;
}

/* =============================global label======================== */

.ant-form-item-label>label {
    color: #1a3353 !important;
    font-size: 13px !important;
    font-weight: 500;
    margin-left: 5px !important;

    /* Add any other styles you want */
}

/* ==========================antd layout content====================== */
@media screen and (max-width: 768px) {
    .ant-layout-content {
        margin: 0 !important;
        padding: 14px !important;
    }
}

/* ============================client profile collapse=========================== */

/* .client-profile-collapse.ant-collapse-borderless
    > .ant-collapse-item:last-child,
.ant-collapse-borderless > .ant-collapse-item:last-child .ant-collapse-header {

    border-radius: 15px !important;
} */

/* ============================Horizontal form design=========================== */
.ant-form-horizontal .ant-form-item-row {
    display: flex;
    justify-content: space-between;
}

.ant-form-horizontal .ant-form-item-row .ant-form-item-control {
    max-width: 80%;
}

.create-discount-form .ant-col {
    max-width: 100%;
    min-width: 100%;
}

.create-discount-form .ant-form-item {
    margin-bottom: 0px;
}

.checkbox-custom .ant-form-item {
    margin-bottom: 0 !important;
}

.checkbox-custom .ant-form-item label {
    height: auto !important;
}

.ant-form-horizontal .additional-options .ant-form-item-label {
    max-width: 18%;
    text-wrap: wrap;
    text-align: left;
}

/* ============================sidebar  color ad hide scrollbar=========================== */

.ant-menu-item-selected {
    /* background-color: #8143d1 !important; */

    color: #ffffff !important;
}

.sidebar-scrollbar::-webkit-scrollbar {
    display: none;
}

/* Global overflow scrollbar */
::-webkit-scrollbar {
    width: 5px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: #e7e5e5;
}

::-webkit-scrollbar-thumb:hover {
    background: #b8b7b7;
}

/* ---------------------------------side bar menu ------------------------ */
.ant-menu-light.ant-menu-root.ant-menu-inline {
    border-inline-end: 0;
}

.ant-menu-inline .ant-menu-sub.ant-menu-inline {
    padding: 0px;
    border: 0;
    border-radius: 10px;
    box-shadow: none;
    margin: 4px;
}

/* -------------------------antd button------------------ */

.ant-btn-primary {
    box-shadow: none;
}

/* -------------------------checkbox------------------ */

.ant-checkbox-checked .ant-checkbox-inner {
    background-color: theme('colors.checkbox-checked') !important;
    border-color: theme('colors.checkbox-checked') !important;
}

.ant-checkbox-disabled .ant-checkbox-inner:after {
    border-color: rgba(255, 255, 255);
}

/* -------------------------switch------------------ */
/* .ant-switch-checked {
    background-color: theme('colors.switch-on') !important;
}

.ant-switch {
    background-color: theme('colors.switch-off') !important;
} */

.ant-switch-checked .ant-switch-inner {
    background-color: #8143d1 !important;
    /* Background color when checked */
}

/* ---------------------150% calendar screen------------------- */

/* @media screen and (min-resolution: 1.5dppx) {
    .today-button {
        width: 45px;
    }
    .calendar-filters {
        width: 130px !important;
    }
}

@media screen and (min-resolution: 1.25dppx) {
    .calendar-filters {
        width: 130px !important;
    }
} */
/* @media screen and (min-resolution: 1.5dppx) {
    pos-package-card-div {
        height: 16vh;
    }
} */

.calendar-filters {
    width: 120px !important;
}

.rbc-timeslot-group {
    min-height: 60px;
}

/* hide time in the column box */
.rbc-day-slot .rbc-event-label {
    display: none;
}

.rbc-day-slot .rbc-events-container {
    margin-right: 0px;
}

.rbc-day-slot .rbc-event,
.rbc-day-slot .rbc-background-event {
    border: 2px solid rgb(250, 247, 247) !important;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 12px 30px !important;
}

.rbc-event:focus {
    outline: 0;
}

/* .calendar-filters {
    width: 120px !important;
} */

/* ----------------------global antd input and collapse header label color------------------------ */

.ant-input,
.ant-picker-input>input {
    color: #455560 !important;
}

.ant-input:disabled {
    color: rgba(69, 85, 96, 0.3) !important;
}

.ant-select-selector {
    color: #455560 !important;
}

.ant-select.ant-select-disabled .ant-select-selector {
    color: rgba(69, 85, 96, 0.3) !important;
}

.ant-select-item-option {
    color: #455560 !important;
}

/* .ant-pagination .ant-pagination-item a {
    color: #455560 !important;
}

.ant-pagination .ant-pagination-prev a,
.ant-pagination .ant-pagination-next a {
    color: #455560 !important;
} */

.ant-collapse-header {
    color: #1a3353 !important;
    /* Replace with your desired color */
}

.ant-modal-header {
    color: #1a3353 !important;
    /* Replace with your desired color */
}

.rbc-time-view-resources .rbc-header,
.rbc-time-view-resources .rbc-day-bg,
.rbc-label,
.rbc-header+.rbc-header {
    color: #1a3353;
}

.rbc-time-slot {
    /* background-color: #fff !important; */
}

.rbc-timeslot-group {
    min-height: 80px !important;
}

/* .calendar-header {
    z-index: 1152;
    position: relative;
    background-color: #fff;
    padding-y: 2.5rem;
} */

.ant-dropdown {
    z-index: 1152;
}

/* ----------------------------global input antd------------------------ */

.ant-form-item .ant-form-item-label>label::after {
    display: none !important;
}

.truncated-text {
    width: 20ch;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/* --------------------------global datepicker--------------------- */
/* Customize the background color for the selected date */
.custom-datepicker .ant-picker-cell-selected .ant-picker-cell-inner {
    background-color: #8143d1 !important;
    color: white !important;
    border-color: #8143d1 !important;
    /* Adjust text color for better contrast */
}

/* Optional: Customize hover color for the selected date */
.custom-datepicker .ant-picker-cell-selected:hover .ant-picker-cell-inner {
    background-color: #8143d1 !important;
    border-color: #8143d1 !important;
}

/*----------------------appointment week view calendar----------------------------- */
.ant-picker-dropdown .ant-picker-week-panel-row-range-start td.ant-picker-cell:before,
.ant-picker-dropdown .ant-picker-week-panel-row-range-end td.ant-picker-cell:before,
.ant-picker-dropdown .ant-picker-week-panel-row-selected td.ant-picker-cell:before,
.ant-picker-dropdown .ant-picker-week-panel-row-hover td.ant-picker-cell:before {
    background-color: #8143d1 !important;
    color: white !important;
}

/* ----------------range picker start end date------------------------- */

.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner,
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
    background-color: #8143d1 !important;
}

/* Radio buttons without the circle */
.custom-radio .ant-radio {
    display: none !important;
}

.custom-radio .ant-radio-wrapper>span:nth-child(2) {
    width: 100%;
}

.custom-radio>label:last-child {
    margin-inline-end: 8px;
}

/* ----------------------------------------------date picker padding------------------------- */
/* base (all sizes) */
.ant-picker-large {
    padding: 0 11px !important;
}

/* xs: <576px */
@media (max-width: 575.98px) {
    .ant-picker-large {
        padding: 0 11px !important;
    }
}

/* sm: ≥576px */
@media (min-width: 576px) and (max-width: 767.98px) {
    .ant-picker-large {
        padding: 0 11px !important;
    }
}

/* md: ≥768px */
@media (min-width: 768px) and (max-width: 991.98px) {
    .ant-picker-large {
        padding: 0 11px !important;
    }
}

/* lg: ≥992px */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .ant-picker-large {
        padding: 0 11px !important;
    }
}

/* xl: ≥1200px */
/* xl: 1200–1439.98 */
@media (min-width: 1200px) and (max-width: 1439.98px) {
    .ant-picker-large {
        padding: 0 11px !important;
    }
}

/* xl+ (new): 1440–1599.98 */
@media (min-width: 1440px) and (max-width: 1599.98px) {
    .ant-picker-large {
        padding: 6px 11px !important;
    }

    /* tweak as needed */
}

/* xxl: ≥1600 */
@media (min-width: 1600px) {
    .ant-picker-large {
        padding: 7px 11px !important;
    }
}

/* OTP Input mask */
/* somewhere global */
.ant-otp .ant-otp-input {
    -webkit-text-security: disc;
}