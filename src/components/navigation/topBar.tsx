import { UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Dropdown, Input, Menu, Modal } from 'antd';
import { Header } from 'antd/es/layout/layout';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import { useAppSelector } from '~/hooks/redux-hooks';
import { clearSearchValue, setSearchValue } from '~/redux/slices/common-slice';
import { AppDispatch } from '~/redux/store';
import { Staff_Roles } from '~/types/enums';
import ChangePasswordModal from '../modals/change-password-modal';
import { LogoutUser } from '~/redux/actions/auth-actions';
const { Search } = Input;

const TopBar = (props: any) => {
    const [isLogoutButtonVisible, setIsLogoutButtonVisible] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [ShowPasswordModal, setShowPasswordModal] = useState<boolean>();
    const dispatch = useDispatch<AppDispatch>();
    const [location, setLocation] = useLocation();
    const searchValue = useSelector(
        (state: any) => state.common_store.searchValue
    );

    const store = useAppSelector((state: any) => ({
        role: state.auth_store.role,
        userId: state.auth_store.userId,
    }));

    const handleAvatarClick = () => {
        setIsLogoutButtonVisible(!isLogoutButtonVisible);
    };
    const changePassword = () => {
        setShowPasswordModal(true);
    };
    const handleClosePasswordModal = () => {
        setShowPasswordModal(false);
    };

    const handleLogout = () => {
        setIsModalVisible(true);
    };

    const handleOk = () => {
        dispatch(LogoutUser()).then((res: any) => {
            if (res?.payload?.status === 200 || res?.payload?.status === 201) {
                localStorage.removeItem('selectedKey');
                localStorage.removeItem('token');
            }
        });
        setIsModalVisible(false);
        setLocation('/signin');
    };

    const handleCancel = () => {
        setIsModalVisible(false);
    };

    useEffect(() => {
        dispatch(clearSearchValue());
    }, [location]);

    const onSearch = (value: string) => {
        console.log(value);
        dispatch(setSearchValue(value));
    };

    function handleProfile() {
        setLocation(`/staff-details/${store.userId}`);
    }

    const menuItems = [
        ...(Staff_Roles[store.role]
            ? [
                  {
                      key: 'profile',
                      label: <div onClick={handleProfile}>Profile</div>,
                  },
              ]
            : []),
        {
            key: 'changePassword',
            label: <div onClick={changePassword}>Change Password</div>,
        },
        {
            key: 'logout',
            label: (
                <div onClick={handleLogout} className="w-full">
                    Logout
                </div>
            ),
        },
    ];

    return (
        <div className=" sticky top-0 z-10 border-l bg-white py-[3px] shadow-md">
            <Header
                style={{
                    padding: 0,
                    background: props.colorBgContainer,
                }}
                className="  flex items-center justify-between "
            >
                <div className="flex items-center gap-16 ">
                    <Button
                        type="text"
                        icon={
                            props.collapsed ? (
                                <div>
                                    <svg
                                        viewBox="64 64 896 896"
                                        focusable="false"
                                        data-icon="menu-unfold"
                                        width="1.2em"
                                        height="1.2em"
                                        fill="currentColor"
                                        aria-hidden="true"
                                    >
                                        <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"></path>
                                    </svg>
                                </div>
                            ) : (
                                <div>
                                    {' '}
                                    <svg
                                        viewBox="64 64 896 896"
                                        focusable="false"
                                        data-icon="menu-fold"
                                        width="1.2em"
                                        height="1.2em"
                                        fill="currentColor"
                                        aria-hidden="true"
                                    >
                                        <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"></path>
                                    </svg>
                                </div>
                            )
                        }
                        onClick={() => {
                            props.setCollapsed(!props.collapsed);
                        }}
                        style={{
                            fontSize: '16px',
                            width: 64,
                            height: 64,
                        }}
                    />
                </div>
                <div className="px-5">
                    <Dropdown
                        menu={{ items: menuItems }}
                        trigger={['click']}
                        open={isLogoutButtonVisible}
                        onOpenChange={(visible) =>
                            setIsLogoutButtonVisible(visible)
                        }
                    >
                        <Avatar
                            size={45}
                            icon={<UserOutlined />}
                            onClick={handleAvatarClick}
                            style={{ cursor: 'pointer' }}
                        />
                    </Dropdown>
                </div>
                <Modal
                    title={
                        <div className="border-b-2 text-[#1A3353]">
                            Confirm Logout
                        </div>
                    }
                    open={isModalVisible}
                    onOk={handleOk}
                    onCancel={handleCancel}
                    footer={false}
                    centered
                    className="w-[25vw]"
                >
                    <div className=" flex flex-col justify-center gap-6 pt-3">
                        <p className=" text-2xl text-[#455560]">
                            Are you sure you want to logout?
                        </p>
                        <div className="flex justify-end">
                            <Button
                                key="cancel"
                                onClick={handleCancel}
                                className="border-[#1a3353]"
                                style={{ float: 'right' }}
                            >
                                Cancel
                            </Button>
                            <Button
                                key="submit"
                                className="bg-purpleLight text-[white]"
                                onClick={handleOk}
                                style={{ float: 'right', marginLeft: 10 }}
                            >
                                Logout
                            </Button>
                        </div>
                    </div>
                </Modal>
            </Header>
            <ChangePasswordModal
                isVisible={ShowPasswordModal}
                onClose={handleClosePasswordModal}
            />
        </div>
    );
};

export default TopBar;
