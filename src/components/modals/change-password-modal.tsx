// MyModal.tsx
import React, { useState } from 'react';
import { Button, Form, FormProps, Input, Modal } from 'antd';
import PasswordChecklist from '../common/password-checklist';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { ChangeProfilePassword } from '~/redux/actions/auth-actions';

interface ChangePasswordModalProps {
    isVisible: boolean | undefined;
    onClose: () => void;
}

const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({
    isVisible,
    onClose,
}) => {
    const [password, setPassword] = useState('');
    const dispatch = useAppDispatch();
    const [form] = Form.useForm();

    const onFinish: FormProps['onFinish'] = (values) => {
        console.log('valuesalues---', values);

        const payload = {
            oldPassword: values.oldPassword,
            newPassword: values.newPassword,
            confirmPassword: values.confirmPassword,
        };

        console.log('Change Password -----------', payload);
        dispatch(ChangeProfilePassword(payload)).then((res: any) => {
            console.log('Res--------------', res);
            if (res?.payload?.status === 200 || res?.payload?.status === 201) {
                onClose();
                form.resetFields();
            }
        });
    };

    return (
        <Modal
            title={<div className="border-b-2">Change Password</div>}
            open={isVisible}
            onOk={onClose}
            footer={false}
            onCancel={onClose}
        >
            <Form
                className="pt-10"
                name="ChangePassword"
                layout="vertical"
                size="large"
                onFinish={onFinish}
            >
                <Form.Item
                    label="Old Password"
                    name="oldPassword"
                    rules={[
                        {
                            required: true,
                            message: 'Please input Old Password',
                        },
                    ]}
                >
                    <Input.Password placeholder="Enter Old Password" />
                </Form.Item>
                <Form.Item
                    label="New Password"
                    name="newPassword"
                    rules={[
                        {
                            required: true,
                            message: 'Please input New Password',
                        },
                    ]}
                >
                    <Input.Password
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter New Password"
                    />
                </Form.Item>
                <Form.Item
                    label="Confirm New Password"
                    name="confirmPassword"
                    rules={[
                        {
                            required: true,
                            message: 'Please Enter New Password',
                        },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (
                                    !value ||
                                    getFieldValue('newPassword') === value
                                ) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(
                                    new Error(
                                        'The two passwords that you entered do not match!'
                                    )
                                );
                            },
                        }),
                    ]}
                >
                    <Input.Password
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Confirm New Password.Password"
                    />
                </Form.Item>
                <PasswordChecklist password={password} />

                <Form.Item>
                    <div className="flex flex-row justify-end pt-6">
                        <Button
                            className="bg-purpleLight text-white"
                            htmlType="submit"
                        >
                            Confirm
                        </Button>
                    </div>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default ChangePasswordModal;
