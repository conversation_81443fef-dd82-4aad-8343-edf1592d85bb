import { MoreOutlined } from '@ant-design/icons';
import {
    Button,
    ConfigProvider,
    Dropdown,
    Modal,
    Pagination,
    Table,
    Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useLoader } from '~/hooks/useLoader';
import {
    AssignDiscountToPrice,
    DiscountDetails,
    DiscountList,
    DiscountListOnPricePage,
} from '~/redux/actions/createDiscountAction';
import InventoryCreateDiscountModal from '~/screens/merchandise/stores/storeCreateDiscount';
import Alertify from '~/services/alertify';
import { getQueryParams } from '~/utils/getQueryParams';

interface ModalProps {
    priceToCompare: number;
    visible: boolean;
    onCancel: () => void;
    comingDiscounts: string[];
    pricingId: string;
    setDiscountData: any;
    setComingDiscounts: any;
    setSelectedDiscount: any;
    selectedDiscount: any;
    preloadedDiscounts?: any;
    autoApplyId?: string;
    setAutoApplyId?: (id: string) => void;
}
const { Title } = Typography;

const AddInventoryDiscountModal: React.FC<ModalProps> = ({
    visible,
    onCancel,
    pricingId,
    priceToCompare,
    setDiscountData,
    selectedDiscount,
    setSelectedDiscount,
    setComingDiscounts,
    comingDiscounts,
    preloadedDiscounts,
    autoApplyId,
    setAutoApplyId,
}) => {
    const [newDiscountModal, setNewDiscountModal] = useState(false);
    const dispatch = useDispatch();
    const [selectBranches, setSelectedBranches] = useState<string[]>([]);
    const [dataSource, setDataSource] = useState<string[]>([]);
    const [discountDetails, setDiscountDetails] = useState<string[]>([]);
    const [totalItems, setTotalItems] = useState(0);
    const params = getQueryParams();
    const [perPage, setPerPage] = useState(10);
    const pageParam = Number(params.page);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [edit, setEdit] = useState(false);
    const [editId, setEditId] = useState('');
    const [selectionType] = useState('checkbox');
    const [applyPromotions, setApplyPromotions] =
        useState<string[]>(comingDiscounts);
    const [removePromotions, setRemovePromotions] = useState<string[]>([]);
    const [selectedRowMap, setSelectedRowMap] = useState<Record<string, any>>(
        {}
    );
    const [loader, startLoader, endLoader] = useLoader();

    console.log('pricingId', pricingId);

    const fetchDiscounts = (
        page: number,
        perPage: number,
        pricingId: string
    ) => {
        if (pricingId === '0') {
            setApplyPromotions(selectedDiscount || []);
            const queryParams = { page, perPage, itemType: 'product' };
            dispatch(DiscountList(queryParams))
                .then((response: any) => {
                    const data = response?.payload?.data?.data.map(
                        (item: any) => ({
                            ...item,
                            key: item._id,
                            facilityIds: ((item?.facilityIds as []) || [])
                                .map((cc: any) => cc.facilityName)
                                .join(', '),
                        })
                    );
                    setDataSource(data);
                    setTotalItems(
                        response?.payload?.data?._metadata?.pagination?.total ||
                            0
                    );
                    if (page === 1) {
                        const preSelectedIds = preloadedDiscounts
                            ? preloadedDiscounts.map((item: any) => item._id)
                            : selectedDiscount || [];

                        setApplyPromotions(preSelectedIds);

                        if (selectedDiscount && selectedDiscount.length > 0) {
                            const selectedRows: Record<string, any> = {};
                            data.forEach((row: any) => {
                                if (selectedDiscount.includes(row.key)) {
                                    selectedRows[row.key] = row;
                                }
                            });
                            setSelectedRowMap(selectedRows);
                        }
                    }
                })
                .catch((error: any) => {
                    console.log('Error in fetch:', error);
                });
        } else {
            const queryParams = {
                page,
                perPage,
                pricingId,
                itemType: 'product',
            };
            dispatch(DiscountList(queryParams))
                .then((response: any) => {
                    const data = response?.payload?.data?.data.map(
                        (item: any) => ({
                            ...item,
                            key: item._id,
                            facilityIds: ((item?.facilityIds as []) || [])
                                .map((cc: any) => cc.facilityName)
                                .join(', '),
                        })
                    );
                    const apiHasSelected = data.some(
                        (d: any) => 'selected' in d
                    );
                    if (page === 1) {
                        if (apiHasSelected) {
                            const pre = data
                                .filter((d: any) => d.selected)
                                .map((d: any) => d._id);
                            setApplyPromotions(pre);
                        } else {
                            setApplyPromotions(comingDiscounts || []);
                        }
                    }

                    const ids = apiHasSelected
                        ? data
                              .filter((d: any) => d.selected)
                              .map((d: any) => d.key)
                        : comingDiscounts || [];
                    setSelectedRowMap((prev) => {
                        const next = { ...prev };
                        for (const row of data)
                            if (ids.includes(row.key)) next[row.key] = row;
                        return next;
                    });
                    setDataSource(data);
                    setTotalItems(
                        response?.payload?.data?._metadata?.pagination?.total ||
                            0
                    );
                })
                .catch((error: any) => {
                    console.log('Error in fetch:', error);
                });
        }
    };

    // Only fetch discounts on page/perPage/edit/editId change
    useEffect(() => {
        if (visible) {
            fetchDiscounts(currentPage, perPage, pricingId);
        }
        if (edit) {
            dispatch(DiscountDetails({ discountId: editId }))
                .then((response: any) => {
                    setDiscountDetails(response?.payload?.data?.data);
                })
                .catch((error: any) => {
                    console.log('Error in fetch:', error);
                });
        }
    }, [currentPage, perPage, edit, editId]);

    useEffect(() => {
        if (visible) {
            const initialSelection =
                pricingId === '0'
                    ? selectedDiscount || []
                    : comingDiscounts || [];
            setApplyPromotions(initialSelection);
        }
    }, [visible, pricingId]);

    const handlePageChange = (page: number, perPage?: number) => {
        setCurrentPage(page);
        if (perPage) setPerPage(perPage);
        if (pricingId != '0') {
            dispatch(
                AssignDiscountToPrice({
                    pricingId: pricingId,
                    applyPromotions: applyPromotions,
                    removePromotions: removePromotions,
                    type:"product"
                })
            ).then((res: any) => {
                Alertify.success('Discounts assigned successfully');
                dispatch(
                    DiscountListOnPricePage({
                        pricingId: pricingId,
                        currentPage: 1,
                    })
                )
                    .then((response: any) => {
                        setDiscountData(response?.payload?.data?.data);
                    })
                    .catch((error: any) => {
                        Alertify.error(
                            'Could not get the discounts list. Please try again later.'
                        );
                        console.log('Error in fetch discounts list:', error);
                    });
            });
        }
    };

    const columns: any = [
        {
            key: 'discountName',
            title: 'Name',
            dataIndex: 'name',
        },
        {
            key: 'discountType',
            title: 'Discount Type',
            dataIndex: 'type',
            width: '120px',
        },
        {
            key: 'discountValue',
            title: 'Value',
            dataIndex: 'value',
        },
        {
            key: 'facilityIds',
            title: 'Facility',
            dataIndex: 'facilityIds',
        },
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            width: '120px',
            align: 'center',
            render: (record: any) => {
                const menuItems = [
                    {
                        key: 'edit',
                        label: (
                            <div
                                className="text-xl text-[#1A3353]"
                                onClick={() => {
                                    setNewDiscountModal(true);
                                    setEdit(true);
                                    setEditId(record._id);
                                }}
                            >
                                Edit Discount
                            </div>
                        ),
                    },
                    {
                        key: 'delete',
                        label: (
                            <div className="text-xl text-[#1A3353]">
                                Delete Discount
                            </div>
                        ),
                    },
                ];
                return (
                    <span className="flex justify-center gap-5">
                        <Dropdown
                            menu={{ items: menuItems }}
                            trigger={['click']}
                        >
                            <MoreOutlined
                                style={{
                                    fontSize: '20px',
                                    cursor: 'pointer',
                                }}
                            />
                        </Dropdown>
                    </span>
                );
            },
        },
    ];

    const rowSelection = {
        selectedRowKeys: applyPromotions,
        onChange: (
            selectedRowKeys: string[],
            selectedRowsOnThisPage: any[]
        ) => {
            // make table selection the source of truth
            const prevKeys = applyPromotions;
            setApplyPromotions(selectedRowKeys);

            const added = selectedRowKeys.filter((k) => !prevKeys.includes(k));
            const removed = prevKeys.filter(
                (k) => !selectedRowKeys.includes(k)
            );

            if (
                autoApplyId &&
                removed.includes(autoApplyId) &&
                setAutoApplyId
            ) {
                setAutoApplyId('');
                console.log('Auto-apply discount cleared:', autoApplyId);
            }

            setRemovePromotions((prev) => {
                const newRemovals = removed.filter(
                    (k) => comingDiscounts.includes(k) && !prev.includes(k)
                );
                const filtered = prev.filter((k) => !added.includes(k));
                return [...filtered, ...newRemovals];
            });

            if (pricingId === '0') {
                setSelectedRowMap((prev) => {
                    const next: Record<string, any> = { ...prev };

                    removed.forEach((id) => {
                        delete next[id];
                    });

                    if (added.length > 0) {
                        const pageRowsById: Record<string, any> = {};
                        for (const row of dataSource)
                            pageRowsById[row.key] = row;
                        for (const id of added) {
                            const row = pageRowsById[id];
                            if (row) next[id] = row;
                        }
                    }

                    setDiscountData(Object.values(next));
                    return next;
                });
            }
        },
        getCheckboxProps: (record: any) => {
            const isDisabled =
                record.type === 'Flat' && Number(record.value) > priceToCompare;
            return {
                disabled: isDisabled,
                name: record.name,
                title: isDisabled
                    ? 'Flat discount value exceeds item price'
                    : '',
            };
        },
    };

    // Add a function to render rows with different styles based on validity
    const getRowClassName = (record: any) => {
        if (record.type === 'Flat' && Number(record.value) > priceToCompare) {
            return 'bg-gray-100 opacity-60'; // Apply styling for disabled rows
        }
        return '';
    };

    const cancelAll = () => {
        if (pricingId != '0') {
            setApplyPromotions([]);
            setRemovePromotions([]);
        }
        onCancel();
    };
    const onSave = () => {
        if (pricingId === '0') {
            setSelectedDiscount(applyPromotions);
            const selectedDiscountData = dataSource.filter((item: any) =>
                applyPromotions.includes(item.key)
            );
            setDiscountData(selectedDiscountData);
            onCancel();
        } else {
            dispatch(
                AssignDiscountToPrice({
                    pricingId: pricingId,
                    applyPromotions: applyPromotions,
                    removePromotions: removePromotions,
                    type:"product"
                })
            ).then((res: any) => {
                Alertify.success('Discounts assigned successfully');
                dispatch(
                    DiscountListOnPricePage({
                        pricingId: pricingId,
                        currentPage: 1,
                    })
                )
                    .then((response: any) => {
                        setDiscountData(response?.payload?.data?.data);
                        setComingDiscounts(
                            response?.payload?.data?.data.map(
                                (item: any) => item._id
                            )
                        );
                    })
                    .catch((error: any) => {
                        Alertify.error(
                            'Could not get the discounts list. Please try again later.'
                        );
                        console.log('Error in fetch discounts list:', error);
                    });
                onCancel();
            });
        }
    };
    console.log('dataSource', dataSource);
    // console.log('applyPromotions', applyPromotions);
    // console.log('removePromotions', removePromotions);
    return (
        <Modal
            open={visible}
            centered
            title="Discount(s)"
            width={800}
            onCancel={onCancel}
            footer={null}
        >
            <div className="mb-4 flex items-center justify-end ">
                <Button
                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                    onClick={() => setNewDiscountModal(true)}
                >
                    Create Discount
                    <span className="-translate-y-1 text-3xl">+</span>
                </Button>
            </div>
            <ConfigProvider
                theme={{
                    token: {
                        // colorBorder: '#8143D1',
                        colorPrimary: '#8143D1',
                        colorPrimaryHover: '#8143D1',
                    },
                }}
            >
                <Table
                    rowSelection={{ type: selectionType, ...rowSelection }}
                    columns={columns}
                    dataSource={dataSource}
                    pagination={false}
                    rowClassName={getRowClassName}
                />
            </ConfigProvider>
            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={totalItems}
                    pageSize={perPage}
                    onChange={handlePageChange}
                    showSizeChanger
                    pageSizeOptions={['10', '20', '50']}
                />
            </div>

            <div className="flex justify-end gap-5">
                <Button
                    className="fw-500 flex items-center rounded-lg border px-8 py-3 text-xl"
                    onClick={cancelAll}
                >
                    Close
                </Button>
                <Button
                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                    onClick={onSave}
                >
                    Save
                </Button>
            </div>

            <InventoryCreateDiscountModal
                edit={edit}
                setEdit={setEdit}
                editId={editId}
                setEditId={setEditId}
                setTotalItems={setTotalItems}
                selectBranches={selectBranches}
                setSelectedBranches={setSelectedBranches}
                newDiscountModal={newDiscountModal}
                setNewDiscountModal={setNewDiscountModal}
                discountDetails={discountDetails}
                setDiscountDetails={setDiscountDetails}
                setDataSource={setDataSource}
            />
        </Modal>
    );
};

export default AddInventoryDiscountModal;
