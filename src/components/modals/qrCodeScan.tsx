import { useState } from 'react';
import { Modal, Input, Button } from 'antd';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { qrCodePurchaseDetails } from '~/redux/actions/scheduling-action';
import BookingModal from '~/screens/appointment/booking-modal';
import Alertify from '~/services/alertify';

const QRCodeModal = ({ visible, onClose }: any) => {
    const dispatch = useAppDispatch();
    const [purchaseId, setPurchaseId] = useState('');
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const [apiData, setApiData] = useState<any>(null);
    const [openBookingModal, setOpenBookingModal] = useState<boolean>(false);
    const [bookingType, setBookingType] = useState<string>('');

    const handleInputChange = (e: any) => {
        setPurchaseId(e.target.value);
    };

    const handleConfirm = async () => {
        if (!purchaseId) {
            setError('Please enter the purchase ID');
            return;
        }

        console.log('Purchase id-----------', purchaseId);

        let extractedId;
        try {
            const cleanString = purchaseId.replace(/\\/g, '');
            const parsed = JSON.parse(cleanString);
            extractedId = parsed.purchase_id;
            console.log('Extracted ID-----------', extractedId);
        } catch (error) {
            setError(
                'Invalid input format. Make sure it is JSON with purchase_id'
            );
            return;
        }

        setLoading(true);
        try {
            const response = await dispatch(
                qrCodePurchaseDetails({ purchaseId: extractedId })
            ).unwrap();

            console.log('Resposne-----------', response);

            const status = response?.payload?.status ?? response?.status;

            if (status === 200 || status === 201) {
                const data = response?.data?.data?.data;
                const classType = data?.bookingType;
                if (classType === 'classes' || classType === 'courses') {
                    Alertify.error(
                        `You can not book ${classType} via QR code. `
                    );
                    return;
                }
                setApiData(response?.data?.data?.data);
                setBookingType(response?.data?.data?.data?.bookingType);
                // onClose();
                setOpenBookingModal(true);
            } else {
                setError('Failed to process the request');
            }
        } catch (error) {
            setError('API request failed');
        } finally {
            setLoading(false);
        }
    };

    // Handle Cancel button click
    const handleCancel = () => {
        onClose();
        setOpenBookingModal(false);
        setApiData(null);
        setPurchaseId('');
        setBookingType('');
    };

    return (
        <>
            <Modal
                open={visible}
                title="Enter Purchase ID"
                onCancel={handleCancel}
                footer={null}
                className="rounded-lg shadow-xl"
            >
                <div className="flex flex-col  ">
                    <Input
                        value={purchaseId}
                        onChange={handleInputChange}
                        placeholder="Enter Purchase ID"
                        className="mt-6 w-full"
                    />
                    {error && <span className="text-red-400">{error}</span>}

                    <div className="mt-20 flex w-full justify-end gap-4">
                        <Button
                            className="border-1 border-[#1A3353]"
                            key="back"
                            onClick={handleCancel}
                        >
                            Cancel
                        </Button>

                        <Button
                            style={{ marginLeft: 10 }}
                            key="submit"
                            className="bg-purpleLight text-white"
                            onClick={handleConfirm}
                            loading={loading}
                        >
                            Confirm
                        </Button>
                    </div>
                </div>
            </Modal>
            {openBookingModal && (
                <BookingModal
                    visible={openBookingModal}
                    onClose={handleCancel}
                    purchaseId={apiData?.purchaseId}
                    facilityId={apiData?.facilityId}
                    scanBooking={true}
                    scanBookingData={apiData}
                    tabValue={bookingType}
                />
            )}
        </>
    );
};

export default QRCodeModal;
