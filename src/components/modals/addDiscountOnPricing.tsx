import { MoreOutlined } from '@ant-design/icons';
import {
    Button,
    ConfigProvider,
    Dropdown,
    Modal,
    Pagination,
    Table,
    Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import {
    AssignDiscountToPrice,
    DiscountDetails,
    DiscountList,
    DiscountListOnPricePage,
    DiscountListWithPricingId,
} from '~/redux/actions/createDiscountAction';
import CreateDiscountModal from '~/screens/services/createDiscount';
import Alertify from '~/services/alertify';
import { getQueryParams } from '~/utils/getQueryParams';

interface ModalProps {
    priceToCompare: number;
    visible: boolean;
    onCancel: () => void;
    comingDiscounts: string[];
    pricingId: string;
    setDiscountData: any;
    setComingDiscounts: any;
    setSelectedDiscount: any;
    selectedDiscount: any;
}
const { Title } = Typography;

const AddDiscountModal: React.FC<ModalProps> = ({
    visible,
    onCancel,
    pricingId,
    priceToCompare,
    setDiscountData,
    selectedDiscount,
    setSelectedDiscount,
    setComingDiscounts,
    comingDiscounts,
}) => {
    const [newDiscountModal, setNewDiscountModal] = useState(false);
    const dispatch = useDispatch();
    const [selectBranches, setSelectedBranches] = useState<string[]>([]);
    const [dataSource, setDataSource] = useState<string[]>([]);
    const [discountDetails, setDiscountDetails] = useState<string[]>([]);
    const [totalItems, setTotalItems] = useState(0);
    const params = getQueryParams();
    const [perPage, setPerPage] = useState(10);
    const pageParam = Number(params.page);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [edit, setEdit] = useState(false);
    const [editId, setEditId] = useState('');
    const [selectionType] = useState('checkbox');
    const [applyPromotions, setApplyPromotions] =
        useState<string[]>(comingDiscounts);
    const [removePromotions, setRemovePromotions] = useState<string[]>([]);
    console.log('pricingId', pricingId);
    const fetchDiscounts = (
        page: number,
        perPage: number,
        pricingId: string
    ) => {
        if (pricingId === '0') {
            setComingDiscounts(selectedDiscount);
            const queryParams = { page, perPage };
            dispatch(DiscountList(queryParams))
                .then((response: any) => {
                    const data = response?.payload?.data?.data.map(
                        (item: any) => ({
                            ...item,
                            key: item._id,
                            facilityIds: ((item?.facilityIds as []) || [])
                                .map((cc: any) => cc.facilityName)
                                .join(', '),
                        })
                    );
                    setDataSource(data);
                    setTotalItems(
                        response?.payload?.data?._metadata?.pagination?.total ||
                            0
                    );
                })
                .catch((error: any) => {
                    console.log('Error in fetch:', error);
                });
        } else {
            const queryParams = { page, perPage, pricingId };
            dispatch(DiscountListWithPricingId(queryParams))
                .then((response: any) => {
                    const data = response?.payload?.data?.data.map(
                        (item: any) => ({
                            ...item,
                            key: item._id,
                            facilityIds: ((item?.facilityIds as []) || [])
                                .map((cc: any) => cc.facilityName)
                                .join(', '),
                        })
                    );
                    const selectedDiscount = response?.payload?.data?.data
                        .filter((item: any) => item.selected === true)
                        .map((item: any) => item._id);
                    setComingDiscounts(selectedDiscount);
                    setDataSource(data);
                    setTotalItems(
                        response?.payload?.data?._metadata?.pagination?.total ||
                            0
                    );
                })
                .catch((error: any) => {
                    console.log('Error in fetch:', error);
                });
        }
    };

    // Only fetch discounts on page/perPage/edit/editId change
    useEffect(() => {
        fetchDiscounts(currentPage, perPage, pricingId);

        if (edit) {
            dispatch(DiscountDetails({ discountId: editId }))
                .then((response: any) => {
                    setDiscountDetails(response?.payload?.data?.data);
                })
                .catch((error: any) => {
                    console.log('Error in fetch:', error);
                });
        }
    }, [currentPage, perPage, edit, editId]);

    // Only update applyPromotions when comingDiscounts changes (e.g., modal open)
    useEffect(() => {
        setApplyPromotions(comingDiscounts);
    }, [comingDiscounts]);

    const handlePageChange = (page: number, perPage?: number) => {
        setCurrentPage(page);
        if (perPage) setPerPage(perPage);
        if (pricingId != '0') {
            dispatch(
                AssignDiscountToPrice({
                    pricingId: pricingId,
                    applyPromotions: applyPromotions,
                    removePromotions: removePromotions,
                })
            ).then((res: any) => {
                Alertify.success('Discounts assigned successfully');
                dispatch(
                    DiscountListOnPricePage({
                        pricingId: pricingId,
                        currentPage: 1,
                    })
                )
                    .then((response: any) => {
                        setDiscountData(response?.payload?.data?.data);
                    })
                    .catch((error: any) => {
                        Alertify.error(
                            'Could not get the discounts list. Please try again later.'
                        );
                        console.log('Error in fetch discounts list:', error);
                    });
            });
        }
    };

    const columns = [
        {
            key: 'discountName',
            title: 'Name',
            dataIndex: 'name',
        },
        {
            key: 'discountType',
            title: 'Discount Type',
            dataIndex: 'type',
            width: '120px',
        },
        {
            key: 'discountValue',
            title: 'Value',
            dataIndex: 'value',
        },
        {
            key: 'facilityIds',
            title: 'Facility',
            dataIndex: 'facilityIds',
        },
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            width: '120px',
            align: 'center',
            render: (record: any) => {
                const menuItems = [
                    {
                        key: 'edit',
                        label: (
                            <div
                                className="text-xl text-[#1A3353]"
                                onClick={() => {
                                    setNewDiscountModal(true);
                                    setEdit(true);
                                    setEditId(record._id);
                                }}
                            >
                                Edit Discount
                            </div>
                        ),
                    },
                    {
                        key: 'delete',
                        label: (
                            <div className="text-xl text-[#1A3353]">
                                Delete Discount
                            </div>
                        ),
                    },
                ];
                return (
                    <span className="flex justify-center gap-5">
                        <Dropdown
                            menu={{ items: menuItems }}
                            trigger={['click']}
                        >
                            <MoreOutlined
                                style={{
                                    fontSize: '20px',
                                    cursor: 'pointer',
                                }}
                            />
                        </Dropdown>
                    </span>
                );
            },
        },
    ];

    const rowSelection = {
        selectedRowKeys: applyPromotions,
        onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
            // Calculate what was checked and unchecked on this page
            const added = selectedRowKeys.filter(
                (k) => !applyPromotions.includes(k)
            );
            const removed = applyPromotions.filter(
                (k) =>
                    !selectedRowKeys.includes(k) &&
                    dataSource.some((row) => row.key === k)
            );

            // Add newly checked keys to applyPromotions (accumulate)
            setApplyPromotions((prev) => {
                const updatedPromotions = [
                    ...prev,
                    ...added.filter((k) => !prev.includes(k)),
                ].filter((k) => !removed.includes(k));

                // Update localStorage if pricingId is '0'
                if (pricingId === '0') {
                    // Get existing items from localStorage or initialize empty array
                    const existingItems = JSON.parse(
                        localStorage.getItem('createPricingDiscounts') || '[]'
                    );
                    console.log('Existing localStorage items:', existingItems);

                    // Add newly selected rows
                    const addedRows = dataSource.filter((item: any) =>
                        added.includes(item.key)
                    );
                    console.log('Newly added rows:', addedRows);

                    // Remove deselected rows
                    const updatedItems = [
                        ...existingItems.filter(
                            (item: any) => !removed.includes(item.key)
                        ),
                        ...addedRows,
                    ];

                    localStorage.setItem(
                        'createPricingDiscounts',
                        JSON.stringify(updatedItems)
                    );
                    console.log(
                        'Updated localStorage:',
                        JSON.parse(
                            localStorage.getItem('createPricingDiscounts') ||
                                '[]'
                        )
                    );
                    setDiscountData(updatedItems);
                }

                return updatedPromotions;
            });

            // For unchecked keys that exist in comingDiscounts, add to removePromotions
            setRemovePromotions((prev) => {
                const newRemovals = removed.filter(
                    (k) => comingDiscounts.includes(k) && !prev.includes(k)
                );
                // Remove any keys that were just checked again
                const filtered = prev.filter((k) => !added.includes(k));
                return [...filtered, ...newRemovals];
            });
        },
        getCheckboxProps: (record: any) => {
            // Disable checkbox if discount type is Flat and value is greater than priceToCompare
            const isDisabled =
                record.type === 'Flat' && Number(record.value) > priceToCompare;

            return {
                disabled: isDisabled,
                name: record.name,
                title: isDisabled
                    ? 'Flat discount value exceeds item price'
                    : '',
            };
        },
    };

    // Add a function to render rows with different styles based on validity
    const getRowClassName = (record: any) => {
        if (record.type === 'Flat' && Number(record.value) > priceToCompare) {
            return 'bg-gray-100 opacity-60'; // Apply styling for disabled rows
        }
        return '';
    };

    const cancelAll = () => {
        if (pricingId != '0') {
            setApplyPromotions([]);
            setRemovePromotions([]);
        }
        onCancel();
    };
    const onSave = () => {
        if (pricingId === '0') {
            setSelectedDiscount(applyPromotions);
            onCancel();
        } else {
            dispatch(
                AssignDiscountToPrice({
                    pricingId: pricingId,
                    applyPromotions: applyPromotions,
                    removePromotions: removePromotions,
                })
            ).then((res: any) => {
                Alertify.success('Discounts assigned successfully');
                dispatch(
                    DiscountListOnPricePage({
                        pricingId: pricingId,
                        currentPage: 1,
                    })
                )
                    .then((response: any) => {
                        setDiscountData(response?.payload?.data?.data);
                        setComingDiscounts(
                            response?.payload?.data?.data.map(
                                (item: any) => item._id
                            )
                        );
                    })
                    .catch((error: any) => {
                        Alertify.error(
                            'Could not get the discounts list. Please try again later.'
                        );
                        console.log('Error in fetch discounts list:', error);
                    });
                onCancel();
            });
        }
    };
    console.log('comingDiscounts', priceToCompare);
    // console.log('applyPromotions', applyPromotions);
    // console.log('removePromotions', removePromotions);
    return (
        <Modal
            open={visible}
            centered
            title="Discount(s)"
            width={800}
            onCancel={onCancel}
            footer={null}
        >
            <div className="mb-4 flex items-center justify-end ">
                <Button
                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                    onClick={() => setNewDiscountModal(true)}
                >
                    Create Discount
                    <span className="-translate-y-1 text-3xl">+</span>
                </Button>
            </div>
            <ConfigProvider
                theme={{
                    token: {
                        // colorBorder: '#8143D1',
                        colorPrimary: '#8143D1',
                        colorPrimaryHover: '#8143D1',
                    },
                }}
            >
                <Table
                    rowSelection={{ type: selectionType, ...rowSelection }}
                    columns={columns}
                    dataSource={dataSource}
                    pagination={false}
                    rowClassName={getRowClassName}
                />
            </ConfigProvider>
            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={totalItems}
                    pageSize={perPage}
                    onChange={handlePageChange}
                    showSizeChanger
                    pageSizeOptions={['10', '20', '50', '100']}
                />
            </div>

            <div className="flex justify-end gap-5">
                <Button
                    className="fw-500 flex items-center rounded-lg border px-8 py-3 text-xl"
                    onClick={cancelAll}
                >
                    Close
                </Button>
                <Button
                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                    onClick={onSave}
                >
                    Save
                </Button>
            </div>

            <CreateDiscountModal
                edit={edit}
                setEdit={setEdit}
                editId={editId}
                setEditId={setEditId}
                setTotalItems={setTotalItems}
                selectBranches={selectBranches}
                setSelectedBranches={setSelectedBranches}
                newDiscountModal={newDiscountModal}
                setNewDiscountModal={setNewDiscountModal}
                discountDetails={discountDetails}
                setDiscountDetails={setDiscountDetails}
                setDataSource={setDataSource}
            />
        </Modal>
    );
};

export default AddDiscountModal;
