import React from 'react';
import { Mo<PERSON>, Button } from 'antd';

interface CommonConfirmationModalProps {
    visible: boolean;
    onConfirm: () => void;
    onCancel: () => void;
    message: string;
    loader?:boolean
}

const CommonConfirmationModal: React.FC<CommonConfirmationModalProps> = ({
    visible,
    onConfirm,
    onCancel,
    message,
    loader
}) => {
    return (
        <Modal
            title={<div className="border-b-2">Confirmation</div>}
            open={visible}
            onOk={onConfirm}
            onCancel={onCancel}
            className="w-[30vw]"
            footer={[
                <div className="mt-14">
                    <Button
                        className="border-1 border-[#1A3353]"
                        key="back"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        style={{ marginLeft: 10 }}
                        key="submit"
                        className="bg-purpleLight text-white"
                        onClick={onConfirm}
                        loading={loader ? loader : false}
                    >
                        Confirm
                    </Button>
                </div>,
            ]}
        >
            <p className="mt-8">{message}</p>
        </Modal>
    );
};

export default CommonConfirmationModal;
