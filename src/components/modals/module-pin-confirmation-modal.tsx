import React, { useEffect, useRef, useState } from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { ValidateModulePin } from '~/redux/actions/auth-actions';
import { useAppDispatch } from '~/hooks/redux-hooks';
import Alertify from '~/services/alertify';

interface ModalProps {
    visible: boolean;
    onConfirm: (userId?: string, policies?: any) => void;
    onCancel: () => void;
    module?: string;
    subModule?: string;
    isModule?: boolean;
    isCartDiscount?: boolean;
    setIsCartDiscount?: any;
    setCreatedByUserId?: (id: string) => void;
    setUserIdForCartDiscount?: (id: string) => void;
}

const ModulePinConfirmationModal: React.FC<ModalProps> = ({
    visible,
    onConfirm,
    onCancel,
    isModule = false,
    module,
    subModule,
    isCartDiscount,
    setIsCartDiscount,
    setCreatedByUserId,
    setUserIdForCartDiscount,
}) => {
    const [loader, setLoader] = useState(false);
    const [error, setError] = useState('');
    const dispatch = useAppDispatch();
    const otpContainerRef = useRef<HTMLDivElement>(null);

    const handleKeyDown = (e: KeyboardEvent) => {
        if (
            !/[0-9]/.test(e.key) &&
            !['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)
        ) {
            e.preventDefault();
        }
    };
    console.log('Module is::::', module);
    const handlePaste = (e: ClipboardEvent) => {
        const pasted = e?.clipboardData?.getData('Text') || '';
        const digits = pasted.replace(/\D/g, '');
        if (digits.length > 6 || digits.length === 0) {
            e.preventDefault();
        }
    };

    useEffect(() => {
        if (!visible) return;

        const inputs = otpContainerRef.current?.querySelectorAll('input') || [];

        inputs.forEach((input) => {
            input.setAttribute('inputmode', 'numeric');
            input.setAttribute('pattern', '[0-9]*');
            input.setAttribute('type', 'text');
            input.setAttribute('autocomplete', 'off');
            input.addEventListener('keydown', handleKeyDown);
            input.addEventListener('paste', handlePaste);
            input.setAttribute('data-1p-ignore', 'true'); // 1Password
            input.setAttribute('data-lpignore', 'true'); // LastPass
            input.setAttribute('data-bwignore', 'true'); // Bitwarden
            input.setAttribute('data-form-type', 'other');
        });
        setTimeout(() => {
            inputs[0]?.focus();
        }, 100);
        // Clean up on unmount
        return () => {
            inputs.forEach((input) => {
                input.removeEventListener('keydown', handleKeyDown);
                input.removeEventListener('paste', handlePaste);
            });
        };
    }, [visible]);

    async function handlePin(pin: string) {
        setLoader(true);
        setError('');
        try {
            const res = await dispatch(ValidateModulePin({ pin })).unwrap();
            const moduleData = res.data?.data?.policies?.find(
                (data: any) => data.type === module
            );
            if (!moduleData) {
                setError('The user does not have the access.');
                setLoader(false);
                return;
            }
            if (!isModule) {
                const subModuleData = moduleData?.permissions?.some(
                    (data: any) => data.type === subModule
                );
                if (!subModuleData) {
                    setError('The user does not have the access.');
                    setLoader(false);
                    return;
                }
            }
            if ([200, 201].includes(res.status)) {
                const userId = res?.data?.data?.user?._id;
                setCreatedByUserId?.(userId);
                console.log('isCartDiscount--------', isCartDiscount);
                if (isCartDiscount) {
                    setUserIdForCartDiscount?.(userId);
                    setIsCartDiscount?.(false);
                }
                onConfirm(userId, res.data?.data?.policies);
            }
        } catch (error: any) {
            setError(error.message?.[0] || 'Something went wrong');
        } finally {
            setLoader(false);
        }
    }
    // to disable autofill
    const otpRef = useRef<any>(null);

    useEffect(() => {
        if (visible && otpRef.current) {
            setTimeout(() => {
                otpRef.current?.focus();
            }, 100);
        }
    }, [visible]);
    useEffect(() => {
        if (!visible) return;

        const inputs = otpContainerRef.current?.querySelectorAll('input') || [];
        inputs.forEach((input) => {
            input.setAttribute('inputmode', 'numeric');
            input.setAttribute('pattern', '[0-9]*');
            input.setAttribute('type', 'text');
            input.setAttribute('autocomplete', 'off'); // stop autofill

            input.addEventListener('keydown', handleKeyDown);
            input.addEventListener('paste', handlePaste);
            input.setAttribute('data-1p-ignore', 'true'); // 1Password
            input.setAttribute('data-lpignore', 'true'); // LastPass
            input.setAttribute('data-bwignore', 'true'); // Bitwarden
            input.setAttribute('data-form-type', 'other');
        });

        // Focus first input after render
        setTimeout(() => {
            inputs[0]?.focus();
        }, 50);

        return () => {
            inputs.forEach((input) => {
                input.removeEventListener('keydown', handleKeyDown);
                input.removeEventListener('paste', handlePaste);
            });
        };
    }, [visible]);
    // to disable autofill END
    return (
        <Modal
            title={
                <p className="pt-10 text-center text-3xl text-[#1A3353]">
                    Enter your pin to unlock
                </p>
            }
            className="lg:w-[30%]"
            centered
            open={visible}
            maskClosable={false}
            // key={Date.now()}
            keyboard={false}
            closable={false}
            onCancel={onCancel}
            footer={null}
            destroyOnHidden // ensures OTP inputs are recreated fresh
            getContainer={false} // to disable autofill
        >
            <div
                className="flex flex-col items-center justify-center gap-7 pb-10 pt-5"
                ref={otpContainerRef}
            >
                {/* <Form
                    autoComplete="off"
                    className="flex flex-col items-center justify-center gap-7 pb-10 pt-5"
                >
                    <Form.Item name="otp"> */}
                <Input.OTP
                    length={4}
                    ref={otpRef}
                    inputMode="numeric"
                    spellCheck={false}
                    aria-autocomplete="none"
                    onChange={(val) => val.length === 4 && handlePin(val)}
                />
                {/* </Form.Item> */}
                {error && <span className="text-red-500">{error}</span>}
                <div className="mt-1">
                    <Button
                        className="border-1 border-[#1A3353]"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        style={{ marginLeft: 10 }}
                        disabled={loader}
                        loading={loader}
                        className="bg-purpleLight text-white"
                    >
                        Confirm
                    </Button>
                </div>
                {/* </Form> */}
            </div>
        </Modal>
    );
};

export default ModulePinConfirmationModal;
