import { Collapse } from 'antd';

const AppointCollapse = (props: any) => {
    return (
        <Collapse
            size="large"
            items={[
                {
                    key: props.key,
                    label: (
                        <div className="flex flex-row items-center gap-10">
                            <p className="text-2xl text-[#1A3353]">
                                {props.title}
                            </p>
                            {props.edit ? (
                                <span className="text-[#8143D1]">Edit</span>
                            ) : (
                                ''
                            )}
                        </div>
                    ),

                    children: props.children,
                },
            ]}
        />
    );
};

export default AppointCollapse;
