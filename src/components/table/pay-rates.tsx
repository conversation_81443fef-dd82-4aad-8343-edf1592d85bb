import { Button, Select, Menu, Dropdown, Pagination } from 'antd';
import React, { useEffect, useState } from 'react';
import CommonTable from '../common/commonTable';
import Title from 'antd/es/typography/Title';
import { useParams } from 'wouter';
import AddPayRate from './addPayRate';
import { MoreOutlined } from '@ant-design/icons';
import DeleteModal from '~/components/common/deleteModal';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    deletePayRate,
    getAllPayRates,
} from '~/redux/actions/pay-rate-actions';
import { useLoader } from '~/hooks/useLoader';
import { navigate } from 'wouter/use-location';
import { getQueryParams } from '~/utils/getQueryParams';
import { clearPayRateDetails } from '~/redux/slices/pay-rate-slice';
import { capitalizeFirstLetter, goBack } from '../common/function';
import { GetStaffProfileDetails } from '~/redux/actions/staff-action';
import { add } from 'date-fns';

const { Option } = Select;

// Define the type for the data source item
interface DataSourceItem {
    key: number;
    serviceCategory: string;
    appointmentType: string;
    serviceType: string;
    payRate: string;
}
const payRates: any = {
    'Percentage %': 'Percentage %',
    'Flat Rate': 'Flat Rate',
    'No Pay': 'No-pay',
};

// Handle the change in pay rate here-----------------------
const handlePayRateChange = (key: number, value: string) => {
    console.log(`Pay rate changed for record with key ${key} to ${value}`);
    // You can update the dataSource state here if needed
};

const columns = [
    {
        title: 'Service Type',
        dataIndex: 'serviceType',
        key: 'serviceType',
    },
    {
        title: 'Service Category',
        dataIndex: 'serviceCategory',
        key: 'serviceCategory',
    },
    {
        title: 'Sub-Type',
        dataIndex: 'appointmentType',
        key: 'appointmentType',
    },
    // {
    //     title: 'Staff Pay Rate',
    //     dataIndex: 'payRate',
    //     key: 'payRate',

    //     render: (_: string, record: DataSourceItem) => payRates[record.payRate],
    // },
    // {
    //     title: '',
    //     dataIndex: 'Total',
    //     key: 'Total',
    // },
];

const PayRates: React.FC = () => {
    const dispatch = useAppDispatch();
    const { staffId } = useParams();
    const [addModal, setAddModal] = useState(false);
    const [payRateId, setPayRateId] = useState(null);
    const params = getQueryParams();
    const [loader, startLoader, endLoader] = useLoader();
    const [isDeleteModalVisible, setDeleteIsModalVisible] =
        useState<boolean>(false);
    const addSpecialization = params.addSpecialization;
    // console.log('addSpecialization', addSpecialization);
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    const store = useAppSelector((state) => ({
        ServiceCategoryListData:
            state.service_category_store.ServiceCategoryListData,
        payRateList: state.pay_rate_store.payRateList,
        payRateListCount: state.pay_rate_store.payRateListCount,
        firstName: state.staff_store.personalInfo?.firstName,
        lastName: state.staff_store.personalInfo?.lastName,
    }));

    useEffect(() => {
        dispatch(GetStaffProfileDetails({ id: staffId }));
        if (addSpecialization) {
            setAddModal(true);
        }
    }, [addSpecialization]);

    const deletePayRateData = () => {
        if (payRateId) {
            dispatch(deletePayRate(payRateId));
        }
        setDeleteIsModalVisible(false);
    };

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
    }

    useEffect(() => {
        startLoader();
        dispatch(
            getAllPayRates({ page: currentPage, pageSize: pageSizes, staffId })
        )
            .unwrap()
            .then(() => {})
            .finally(endLoader);
    }, [currentPage, pageSizes]);

    const serviceTypes: any = {
        personalAppointment: 'Personal Appointment',
        classes: 'Classes',
        bookings: 'Bookings',
        courses: 'Courses',
    };

    const dataSource: DataSourceItem[] = store.payRateList.map(
        (rate: any, i: number) => ({
            key: i,
            _id: rate._id,
            serviceType: serviceTypes[rate.serviceType],
            serviceCategory: rate.serviceCategoryName,
            appointmentType: rate.appointmentTypeName,
            payRate: rate.payRate,
        })
    );

    const selectColumn = [
        {
            title: 'Action',
            dataIndex: '',
            width: '120px',
            align: 'center',
            key: 'action',
            render: (record: any) => {
                const menu = (
                    <Menu>
                        <Menu.Item key="edit">
                            <div
                                onClick={() => {
                                    setAddModal(true);
                                    setPayRateId(record._id);
                                }}
                            >
                                Edit
                            </div>
                        </Menu.Item>
                        <Menu.Item key="add-branch">
                            <div
                                onClick={() => {
                                    setDeleteIsModalVisible(true);
                                    setPayRateId(record._id);
                                }}
                            >
                                Delete
                            </div>
                        </Menu.Item>
                    </Menu>
                );
                return (
                    <>
                        <span className="flex gap-5 ">
                            <div>
                                <Dropdown overlay={menu} trigger={['click']}>
                                    <MoreOutlined
                                        style={{
                                            fontSize: '20px',
                                            cursor: 'pointer',
                                        }}
                                    />
                                </Dropdown>
                            </div>
                        </span>
                    </>
                );
            },
        },
    ];
    const combinedColumns = [...columns, ...selectColumn];
    return (
        <>
            {/* <h4 className="text-[#1A3353]">Appointment Setup</h4> */}
            <div className="flex items-center gap-3 ">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title className="text-[#1A3353]" level={4}>
                    Staff Specialization
                </Title>
            </div>
            <div className=" rounded-xl ">
                <div className=" flex items-center justify-between ">
                    {store.firstName && (
                        <p className="text-2xl text-[#455560]">
                            Set up appointments for{' '}
                            {capitalizeFirstLetter(store.firstName) +
                                ' ' +
                                store.lastName}
                        </p>
                    )}
                    <div></div>
                    <Button
                        onClick={() => {
                            // SetLocation(`/create-pay-rates`);
                            setAddModal(true);
                            setPayRateId(null);
                        }}
                        className="  bg-purpleLight px-5"
                        type="primary"
                    >
                        Add Specialization
                    </Button>
                </div>
                <div>
                    <CommonTable
                        className="min-w-min"
                        dataSource={dataSource}
                        columns={combinedColumns}
                    />
                </div>

                <div className="flex justify-center  py-10">
                    <Pagination
                        current={currentPage}
                        total={store.payRateListCount}
                        pageSize={pageSizes}
                        onChange={paginate}
                        // hideOnSinglePage
                    />
                </div>
                {addModal && (
                    <AddPayRate
                        visible={addModal}
                        onClose={() => {
                            setAddModal(false);
                            setPayRateId(null);
                            dispatch(clearPayRateDetails());
                        }}
                        id={payRateId}
                        staffId={staffId}
                    />
                )}
                {isDeleteModalVisible && (
                    <DeleteModal
                        title="Confirm Delete"
                        message={`Do you want to delete this specialization?`}
                        isVisible={isDeleteModalVisible}
                        onDelete={deletePayRateData}
                        onCancel={() => {
                            setDeleteIsModalVisible(false);
                        }}
                    />
                )}
            </div>
            {/*======================================= modals======================================= */}
        </>
    );
};

export default PayRates;
