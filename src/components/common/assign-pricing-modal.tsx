import React, { useEffect, useState } from 'react';
import {
    Modal,
    Table,
    Checkbox,
    Button,
    ConfigProvider,
    Pagination,
    Switch,
} from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    AssignPricingToSubType,
    PriceListByServiceCategory,
    PriceListBySubType,
} from '~/redux/actions/pricing-actions';
import { useLoader } from '~/hooks/useLoader';
import clsx from 'clsx';
import Alertify from '~/services/alertify';

const columns = [
    {
        title: 'Sessions',
        dataIndex: 'sessionType',
        key: 'sessionType',
    },
    {
        title: 'Service Type',
        dataIndex: 'classType',
        key: 'classType',
        render: (classType: string) => {
            if (classType === 'personalAppointment') return 'Appointment';
            if (classType === 'classes') return 'Classes';
            if (classType === 'bookings') return 'Bookings';
            if (classType === 'courses') return 'Courses';
            return classType;
        },
    },
    {
        title: 'Service Category',
        dataIndex: 'serviceCategoryName',
        key: 'serviceCategoryName',
        render: (text: string, record: any) => {
            return <p className="capitalize">record?.serviceCategoryName</p>;
        },
    },
    {
        title: 'Price',
        dataIndex: '',
        key: 'price',
        // align: 'center',
        // render: (value: any) => value?.toFixed(2),
        render: (record: any) => {
            return Math.trunc(Number(record.price) * 100) / 100;
        },
    },
    {
        title: 'Sold Online',
        dataIndex: 'isSellOnline',
        key: 'isSellOnline',
        align: 'center',
        render: (soldOnline: boolean) =>
            soldOnline ? <span>✔</span> : <span>✘</span>,
    },
];

interface AssignPricingModalProps {
    isVisible: boolean;
    onClose: () => void;
    subTypeId: string | null;
    serviceId: string | null;
    serviceType?: string;
    openedPanel?: any;
}

const AssignPricingModal: React.FC<AssignPricingModalProps> = ({
    isVisible,
    subTypeId,
    serviceId,
    onClose,
    serviceType,
    openedPanel,
}) => {
    const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
    const dispatch = useAppDispatch();
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [loader, startLoader, endLoader] = useLoader();

    const store = useAppSelector((state) => ({
        organizationId: state.auth_store.organizationId,
        priceListBySubType: state.pricing_store.pricingListBySubType,
        priceListBySubTypeCount: state.pricing_store.pricingListBySubTypeCount,
    }));

    const fetchPricingData = (page: number, size: number) => {
        startLoader();
        const payload = {
            organizationId: store.organizationId,
            subTypeId: subTypeId,
            serviceId: serviceId,
            classType: serviceType,
            page,
            pageSize: size,
        };
        dispatch(PriceListBySubType(payload))
            .unwrap()
            .then(() => {})
            .catch(() => {})
            .finally(endLoader);
    };

    const handleClose = () => {
        onClose();
        setCurrentPage(1);
        setPageSize(10);
    };

    useEffect(() => {
        if (openedPanel?.serviceCategoryId && openedPanel?.appointmentTypeId) {
            fetchPricingData(currentPage, pageSize);
        }
    }, [isVisible, currentPage, pageSize]);

    const handleCheckboxChange = (
        e: React.ChangeEvent<HTMLInputElement>,
        recordId: string
    ) => {
        setSelectedIds((prev) => {
            const updatedSet = new Set(prev);
            if (e.target.checked) {
                updatedSet.add(recordId);
            } else {
                updatedSet.delete(recordId);
            }
            return updatedSet;
        });
    };

    // Handle select all checkbox change
    const handleSelectAllChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.checked) {
            // Select all items on the current page
            const allIds = store.priceListBySubType.map(
                (item: any) => item._id
            );
            setSelectedIds(new Set(allIds));
        } else {
            // Deselect all items
            setSelectedIds(new Set());
        }
    };

    // Check if all items on the current page are selected
    const isAllSelected =
        store.priceListBySubType?.length > 0 &&
        store.priceListBySubType.every((item: any) =>
            selectedIds.has(item._id)
        );

    // Check if some but not all items are selected (for indeterminate state)
    const isSomeSelected = selectedIds.size > 0 && !isAllSelected;

    const handleSubmit = () => {
        const selectedIdArray = Array.from(selectedIds)?.filter((id) => id);
        if (selectedIdArray.length === 0) {
            Alertify.error('No pricing selected');
        } else {
            console.log('Selected IDs:', selectedIdArray);
            const payload = {
                subTypeId: subTypeId,
                serviceCategoryId: serviceId,
                pricingIds: selectedIdArray,
            };
            dispatch(AssignPricingToSubType(payload))
                .unwrap()
                .then((res: any) => {
                    if (res?.status === 200 || res?.status === 201) {
                        dispatch(
                            PriceListByServiceCategory({
                                serviceId: serviceId,
                                appointmentId: subTypeId,
                            })
                        );
                        setSelectedIds(new Set());
                        handleClose();
                    }
                });
        }
    };

    const selectColumn = {
        title: (
            <div className="flex items-center gap-2">
                <ConfigProvider
                    theme={{
                        token: {
                            colorPrimary: '#8143D1',
                        },
                    }}
                >
                    <Checkbox
                        checked={isAllSelected}
                        indeterminate={isSomeSelected}
                        onChange={handleSelectAllChange}
                    />
                </ConfigProvider>
                <span>Pricing Option Name</span>
            </div>
        ),
        dataIndex: 'pricingOptionName',
        key: 'pricingOptionName',
        render: (text: string, record: any) => (
            <ConfigProvider
                theme={{
                    token: {
                        colorPrimary: '#8143D1',
                    },
                }}
            >
                <Checkbox
                    className=""
                    checked={selectedIds?.has(record._id)}
                    onChange={(e: any) => handleCheckboxChange(e, record._id)}
                >
                    {text}
                </Checkbox>
            </ConfigProvider>
        ),
    };

    const combinedColumns = [selectColumn, ...columns];

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };
    console.log('store.priceListBySubType', store.priceListBySubType);
    return (
        <div className="">
            <Modal
                title="Assign Pricing"
                visible={isVisible}
                onCancel={handleClose}
                centered
                footer={false}
                className="w-[80%]"
            >
                <div>
                    <ConfigProvider
                        theme={{
                            components: {
                                Table: {
                                    colorPrimary: '#8143D1',
                                },
                            },
                        }}
                    >
                        <Table
                            columns={combinedColumns}
                            dataSource={store.priceListBySubType}
                            pagination={false}
                            loading={loader}
                            rowKey="_id"
                        />
                    </ConfigProvider>
                    <div className="flex justify-center py-10">
                        <Pagination
                            current={currentPage}
                            total={store.priceListBySubTypeCount}
                            pageSize={pageSize}
                            onChange={handlePageChange}
                            hideOnSinglePage
                        />
                    </div>

                    <div className=" flex justify-end ">
                        <Button
                            key="submit"
                            type="primary"
                            className="bg-purpleLight px-10"
                            onClick={handleSubmit}
                        >
                            Submit
                        </Button>
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default AssignPricingModal;
