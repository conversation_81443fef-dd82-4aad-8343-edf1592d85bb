import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);
export const capitalizeFirstLetter = (string) => {
    return string
        ?.split(' ')
        ?.map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(' ');
};

export function goBack() {
    window.history.back();
}

export function renderImage(imageLink) {
    if (imageLink) {
        return imageLink;
    } else {
        return 'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
    }
}

export function formatDateString(isoDate) {
    if (!isoDate) {
        return '-';
    }
    const date = new Date(isoDate);
    const day = date.getDate();
    const month = date
        .toLocaleString('en-US', { month: 'short' })
        .toUpperCase();
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
}

export function formatStringWithSpaces(input) {
    if (typeof input !== 'string') {
        return '';
    }

    let result = input?.charAt(0).toUpperCase() + input?.slice(1);

    result = result?.replace(/([A-Z])/g, (match, p1, offset) => {
        return offset > 0 && result[offset - 1] !== ' ' ? ` ${p1}` : p1;
    });

    return result;
}

export function formatDate(dateString) {
    const date = new Date(dateString);

    const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ];

    const month = months[date.getUTCMonth()];
    const day = date.getUTCDate();
    const year = date.getUTCFullYear();

    const formattedDate = `${day}-${month}-${year}`;

    return formattedDate;
}

export const cleanSchedule = (schedule) => {
    const cleanedSchedule = {};

    Object.keys(schedule)?.forEach((day) => {
        cleanedSchedule[day] = schedule[day]?.filter(
            (timeBlock) => timeBlock.from !== null && timeBlock.to !== null
        );
    });

    return cleanedSchedule;
};

export const cleanScheduleAndShowError = (schedule, availabilityType) => {
    const cleanedSchedule = {};
    let hasErrors = false;
    let showEmptyError = true;
    Object.keys(schedule)?.forEach((day) => {
        cleanedSchedule[day] = schedule[day]
            ?.filter(
                (timeBlock) => timeBlock.from !== null || timeBlock.to !== null
            )
            ?.map((slot) => {
                const errors = {
                    showStartTimeError: !slot.from,
                    showEndTimeError: !slot.to,
                    showServiceCategoryError:
                        availabilityType === 'available' &&
                        (!slot.payRateIds || slot.payRateIds.length === 0),
                };

                if (
                    errors.showStartTimeError ||
                    errors.showEndTimeError ||
                    errors.showServiceCategoryError
                )
                    hasErrors = true;

                return {
                    from: slot.from,
                    to: slot.to,
                    payRateIds: slot.payRateIds,
                    // ...errors,
                };
            });
        if (showEmptyError) showEmptyError = !cleanedSchedule[day].length;
    });

    return { cleanedSchedule, hasErrors, showEmptyError };
};

export const generateUniqueKey = () => {
    return `${Date.now()}-${Math.random()}`;
};

export const convertToWords = (num) => {
    if (!num) return '';

    const units = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten',
        'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen',
        'eighteen', 'nineteen'];
    const tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];

    const handleTwoDigits = (n) => {
        if (n < 20) return units[n];
        return tens[Math.floor(n / 10)] + (n % 10 === 0 ? '' : ' ' + units[n % 10]);
    };

    num = parseInt(num);
    if (num === 0) return 'zero rupees';

    const words = [];

    // Handle crores
    const crores = Math.floor(num / 10000000);
    if (crores > 0) {
        words.push(handleTwoDigits(crores) + ' crore');
        num = num % 10000000;
    }

    // Handle lakhs
    const lakhs = Math.floor(num / 100000);
    if (lakhs > 0) {
        words.push(handleTwoDigits(lakhs) + ' lakh');
        num = num % 100000;
    }

    // Handle thousands
    const thousands = Math.floor(num / 1000);
    if (thousands > 0) {
        words.push(handleTwoDigits(thousands) + ' thousand');
        num = num % 1000;
    }

    // Handle hundreds
    const hundreds = Math.floor(num / 100);
    if (hundreds > 0) {
        words.push(units[hundreds] + ' hundred');
        num = num % 100;
    }

    // Handle remaining digits
    if (num > 0) {
        if (words.length > 0) words.push('and');
        words.push(handleTwoDigits(num));
    }

    return words?.join(' ') + ' RUPEES';
};


export const formatCamelCase = (input) => {
    if (!input) return '';

    return input
        ?.replace(/([a-z])([A-Z])/g, '$1 $2')
        ?.replace(/^./, (str) => str.toUpperCase());
};

export function toTitleCase(str) {
    return str
        ?.split(' ') // Split the string into an array of words
        ?.map(word => word.charAt(0).toUpperCase() + word?.slice(1).toLowerCase()) // Capitalize the first letter of each word
        ?.join(' '); // Join the array of words back into a string
}

export function formatTime(dateStr) {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return 'N/A';
    return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
    }); // e.g., 09:24 AM
}

