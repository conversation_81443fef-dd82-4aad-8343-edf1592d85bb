// components/common/day-selector.tsx

import React, { useState } from 'react';

const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

interface DaySelectorProps {
    onChange?: (day: string) => void; // Optional: Inform parent
}

const DaySelector: React.FC<DaySelectorProps> = ({ onChange }) => {
    const [selectedDay, setSelectedDay] = useState<string | null>(null);

    const handleDayClick = (day: string) => {
        const lowerDay = day.toLowerCase();
        setSelectedDay(lowerDay);
        onChange?.(lowerDay); // Optional: inform parent
    };

    return (
        <div className="flex flex-row">
            <div className="w-[20%]"></div>
            <div className="flex  space-x-2">
                {days.map((day) => {
                    const isSelected = selectedDay === day.toLowerCase();
                    return (
                        <button
                            key={day}
                            onClick={() => handleDayClick(day)}
                            className={`flex h-14 w-14 items-center justify-center rounded-full 
                            border text-lg font-medium transition-all duration-200
                            ${
                                isSelected
                                    ? 'bg-gray-700 text-white'
                                    : 'border-gray-300 bg-white text-gray-800 hover:bg-gray-200'
                            }`}
                        >
                            {day}
                        </button>
                    );
                })}
            </div>
        </div>
    );
};

export default DaySelector;
