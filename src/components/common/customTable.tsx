import {
    Button,
    ConfigProvider,
    Input,
    Select,
    Table,
    Typography,
    DatePicker,
} from 'antd';
import { Link, useLocation } from 'wouter';
import React, { useState } from 'react';
import { STAFF_ROLE_DROPDOWN } from '~/types/enums-value';
import { useAppSelector } from '~/hooks/redux-hooks';
import { navigate } from 'wouter/use-location';
import { DownloadOutlined } from '@ant-design/icons';
import { CATEGORY_TYPE } from '~/types/category-enum';
import { RoleType } from '~/types/enums';

const { RangePicker } = DatePicker;

const { Option } = Select;

const { Title, Text } = Typography;
const { Search } = Input;

interface IFacility {
    _id: string;
    facilityName: string;
    [key: string]: any;
}

function goBack() {
    window.history.back();
}

const tabs = [
    { key: 1, label: 'Packages' },
    { key: 2, label: 'Booking & Check In' },
];

interface CustomTableProps {
    onClearFilters?: any;
    heading?: string;
    headingContent?: string;
    bulkAction?: boolean;
    addNewLink?: string;
    addNewTitle?: string;
    addNew?: () => void;
    columns?: any[];
    dataSource1?: any[];
    dataSource2?: any[];
    toggleDiv?: (record: any, rowIndex: any) => void;
    loading?: boolean;
    className?: string;
    addNewModal?: boolean;
    showBooking?: boolean;
    showPackages?: boolean;
    openModal?: any;
    onSearch?: (val: string) => void;
    search?: string;
    showDateRange?: boolean;
    showServiceCategory?: boolean;
    membershipStatus?: boolean;
    setStatusFilter?: any;
    statusFilter?: any;
    setStatusChanged?: any;
    showClient?: boolean;
    showBookingStatus?: boolean;
    selectedClients?: any;
    selectedServiceCategories?: any;
    selectedDateRange?: any;
    selectedBookingStatus?: any;
    setSelectedClients?: any;
    setSelectedServiceCategories?: any;
    setSelectedBookingStatus?: any;
    setSelectedDateRange?: any;
    selectedCity?: any;
    setSelectedCity?: any;
    showSearch?: boolean;
    showStaffLocation?: boolean;
    columns2?: any;
    setIsModalVisible?: any;
    setActiveKey?: any;
    activeKey?: any;
    showPaymentStatus?: boolean;
    showTabs?: boolean;
    showAddButton?: boolean;
    showApplyButton?: boolean;
    selectedPaymentStatus?: string;
    setSelectedPaymentStatus?: any;
    showExport?: boolean;
    handleExport?: any;
    showProductAddButton?: boolean;
    showClearButton?: boolean;
    showProductScreenButtons?: boolean;
    showStoreScreenButtons?: boolean;
    showAddNewSKUButton?: boolean;
    showStoreDetailScreenButtons?: boolean;
    showCategoryAddButton?: boolean;
    onAddCategory?: any;
    onCategoryFilter?: boolean;
    setSelectedCategoryLevel?: any;
    onTypeChange?: (val: string) => void;
    bulkImport?: boolean;
    onBulkImportClick?: () => void;
    hasBookingWritePermission?: boolean;
    discountManagementButtonForInventory?: boolean;
    storeId?: string;
    onImportClick?: () => void;
    onExportClick?: () => void;
    ExportProductsTemplate?: () => void;
    rowClassName?: (record: any, index: number) => string;
    setCurrentPage?: any;
}

const CustomTable: React.FC<CustomTableProps> = (props) => {
    const [, setLocation] = useLocation();

    // const [activeKey, setActiveKey] = useState(1);
    const handleChange = (value: any) => {
        if (value.includes('all')) {
            props.setSelectedCity(['all']);
        } else {
            props.setSelectedCity(value);
        }
    };

    const handleChangeDateRange = (dates: any, dateStrings: any) => {
        console.log('Selected Dates: ', dates);
        console.log('Formatted Dates: ', dateStrings); //
        props.setSelectedDateRange(dates);
    };

    const handleChangeServiceCategory = (value: any) => {
        if (value.includes('all')) {
            props.setSelectedServiceCategories(['all']);
        } else {
            props.setSelectedServiceCategories(value);
        }
    };

    const handleChangePaymentStatus = (value: any) => {
        // if (value.includes('all')) {
        //     props.setSelectedPaymentStatus(['all']);
        // } else {
        props.setSelectedPaymentStatus(value);
        // }
    };

    const handleChangeBookingStatus = (value: any) => {
        if (value.includes('all')) {
            props.setSelectedBookingStatus(['all']);
        } else {
            props.setSelectedBookingStatus(value);
        }
    };

    const handleClearAll = () => {
        // If parent component provides a clear function, use it
        if (props.onClearFilters) {
            props.onClearFilters();
        } else {
            // Fallback to the original clear logic
            props.setStatusFilter && props.setStatusFilter(null);
            props.setSelectedCity && props.setSelectedCity([]);
            props.setSelectedClients && props.setSelectedClients([]);
            props.setSelectedServiceCategories &&
                props.setSelectedServiceCategories([]);
            props.setSelectedBookingStatus &&
                props.setSelectedBookingStatus([]);
            props.setSelectedPaymentStatus &&
                props.setSelectedPaymentStatus('');
            props.setSelectedDateRange && props.setSelectedDateRange([]);
            props.setSelectedCategoryLevel &&
                props.setSelectedCategoryLevel('');
        }
    };

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        ServiceCategoryListData:
            state.service_category_store.ServiceCategoryListData,
        role: state.auth_store.role,
    }));

    const handleSeachChange = (value: string) => {
        props.onSearch?.(value);
        navigate(`?page=1&pageSize=10&search=${value}`, { replace: true });
    };

    const handleCategoryChange = (value: any) => {
        props.setSelectedCategoryLevel(value);
    };
    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Table: {
                        borderColor: '#0000001A',
                        cellFontSize: 13,
                        headerBg: '#fff',
                        headerColor: '#1A3353',
                        colorText: '#455560',
                    },
                },
            }}
        >
            <div className=" w-full">
                {props.heading && (
                    <div className="flex flex-wrap items-center justify-between gap-4 pb-8">
                        <div className="flex items-center gap-5">
                            <img
                                src="/icons/back.svg"
                                alt="edit"
                                className="h-[10px] cursor-pointer"
                                onClick={goBack}
                            />
                            <Title className="text-[#1A3353]" level={4}>
                                {props.heading}
                            </Title>
                        </div>

                        <div className="flex flex-wrap items-center gap-4">
                            {props.showAddButton && (
                                <Button className="fw-500 w-[100px] rounded-2xl bg-purpleLight py-3 text-xl text-white">
                                    Add orders +
                                </Button>
                            )}
                            {props.showProductAddButton && (
                                <Link to="/create-product">
                                    <Button className="fw-500 w-[100px] rounded-2xl bg-purpleLight py-3 text-xl text-white">
                                        Add Products +
                                    </Button>
                                </Link>
                            )}
                            {props.showCategoryAddButton && (
                                <Button
                                    className="fw-500 rounded-2xl bg-purpleLight py-3 text-xl text-white"
                                    onClick={props.onAddCategory}
                                >
                                    Add Category +
                                </Button>
                            )}
                            {props.discountManagementButtonForInventory && (
                                <Button
                                    className="fw-500 flex items-center rounded-lg border px-8 py-3 text-xl"
                                    onClick={() =>
                                        setLocation(
                                            `/store-detail/discount-management/${props.storeId}`
                                        )
                                    }
                                >
                                    <p>Discount Management</p>
                                </Button>
                            )}
                            {props.addNewLink && (
                                <Link to={props.addNewLink}>
                                    <Button
                                        className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                        onClick={props.addNew}
                                    >
                                        <p>{props.addNewTitle}</p>
                                        <span className="-translate-y-1 text-3xl">
                                            +
                                        </span>
                                    </Button>
                                </Link>
                            )}
                            {props.bulkImport && (
                                <Button
                                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                    onClick={props.onBulkImportClick}
                                >
                                    <p>Bulk Import</p>
                                    <span className="-translate-y-1 text-3xl">
                                        +
                                    </span>
                                </Button>
                            )}
                            {props.addNewModal && (
                                <Button
                                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                    onClick={props.openModal}
                                >
                                    <p>{props.addNewTitle}</p>
                                    <span className="-translate-y-1 text-3xl">
                                        +
                                    </span>
                                </Button>
                            )}
                            {props.showExport && (
                                <Button
                                    className="fw-500 flex w-[100px] items-center rounded-2xl bg-purpleLight py-3 text-xl text-white"
                                    onClick={props?.handleExport}
                                >
                                    Export
                                    <DownloadOutlined className="ml-2 text-16" />
                                </Button>
                            )}
                        </div>
                    </div>
                )}
                <div className="shadow-b-md flex items-center justify-between bg-[#FFFFFF] pb-7 pt-5 @sm:flex-col @sm:gap-4 @sm:pb-7">
                    {props.showSearch && (
                        <div className="">
                            <Search
                                className=""
                                allowClear
                                placeholder="Search"
                                onChange={(e) =>
                                    handleSeachChange(e.target.value)
                                }
                                value={props.search}
                                style={{ width: '85%' }}
                            />
                        </div>
                    )}
                    {props.showDateRange && (
                        <div className="lg:w-[20%]">
                            <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                Date Range
                            </p>
                            <RangePicker
                                value={props.selectedDateRange}
                                onChange={handleChangeDateRange}
                                placeholder={[
                                    'Start Date (DD/MM/YYYY)',
                                    'End Date (DD/MM/YYYY)',
                                ]}
                                format="DD/MM/YYYY"
                                // disabledDate={disabledDate}
                                style={{ width: '85%' }}
                            />
                        </div>
                    )}

                    {props.showStaffLocation && (
                        <div className="lg:w-[20%]">
                            <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                Location
                            </p>
                            <Select
                                mode="multiple"
                                maxTagCount="responsive"
                                placeholder="Select locations"
                                value={props.selectedCity}
                                onChange={handleChange}
                                style={{ width: '85%' }}
                            >
                                {store.facilityList.map(
                                    (facility: IFacility) => (
                                        <Option
                                            key={facility?._id}
                                            value={facility?._id}
                                        >
                                            {facility?.facilityName}
                                        </Option>
                                    )
                                )}
                            </Select>
                        </div>
                    )}
                    {props.showServiceCategory && (
                        <div className="lg:w-[20%]">
                            <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                Service Category
                            </p>
                            <Select
                                maxTagCount="responsive"
                                mode="multiple"
                                placeholder="Select service category"
                                value={props.selectedServiceCategories}
                                onChange={handleChangeServiceCategory}
                                style={{ width: '85%' }}
                            >
                                {store.ServiceCategoryListData.map(
                                    (service: any) => (
                                        <Option
                                            key={service?._id}
                                            value={service?._id}
                                        >
                                            {service?.name}
                                        </Option>
                                    )
                                )}
                            </Select>
                        </div>
                    )}
                    {props.membershipStatus && (
                        <div className="lg:w-[20%]">
                            <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                Status
                            </p>
                            <Select
                                allowClear={true}
                                onChange={(value) => {
                                    // console.log('Selected Status:', value);
                                    props.setStatusFilter(value);
                                    props.setStatusChanged(true);
                                }}
                                value={props.statusFilter}
                                placeholder="Select status"
                                style={{ width: '150px' }}
                                options={[
                                    {
                                        label: 'Active',
                                        value: 'active',
                                    },
                                    {
                                        label: 'Renewals',
                                        value: 'renewals',
                                    },
                                    {
                                        label: 'Expired',
                                        value: 'expired',
                                    },
                                ]}
                            />
                        </div>
                    )}
                    {props.showBookingStatus && props.activeKey === 2 && (
                        <div className="lg:w-[20%]">
                            <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                Booking Status
                            </p>
                            <Select
                                // mode="multiple"
                                placeholder="Select Booking Status"
                                value={props.selectedBookingStatus}
                                onChange={handleChangeBookingStatus}
                                style={{ width: '85%' }}
                                options={[
                                    { label: 'All', value: '' },
                                    { label: 'Booked', value: 'booked' },
                                    {
                                        label: 'Checked In',
                                        value: 'checked-in',
                                    },
                                    { label: 'Canceled', value: 'canceled' },
                                ]}
                            />
                        </div>
                    )}
                    {props.showPaymentStatus && (
                        <div className="lg:w-[20%]">
                            <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                Payment Status
                            </p>
                            <Select
                                // mode="multiple"
                                allowClear
                                placeholder="Select Payment Status"
                                value={props?.selectedPaymentStatus}
                                onChange={handleChangePaymentStatus}
                                style={{ width: '85%' }}
                                options={[
                                    // { label: 'All', value: undefined },
                                    { label: 'Paid', value: 'completed' },
                                    {
                                        label: 'Pending',
                                        value: 'pending',
                                    },
                                    { label: 'Refunded', value: 'refund' },
                                    { label: 'Cancelled', value: 'cancel' },

                                    // { label: 'Failed', value: 'failed' },
                                ]}
                            />
                        </div>
                    )}

                    <div className="flex items-center gap-5">
                        {props.showClearButton && (
                            <Button
                                className=" fw-500 flex w-[100px] items-center  rounded-2xl border border-[#1A3353]  
                                     py-3 text-xl text-[#1A3353]"
                                onClick={handleClearAll}
                            >
                                Clear
                            </Button>
                        )}

                        {props.showApplyButton && (
                            <Button
                                className=" fw-500 flex w-[100px] items-center rounded-2xl  border border-purpleLight  
                                     py-3 text-xl text-purpleLight"
                            >
                                Apply
                            </Button>
                        )}
                    </div>
                    {props.onCategoryFilter && (
                        <div className="lg:w-[20%]">
                            <p className="text-lg font-medium text-[#1A3353] ">
                                Category Type
                            </p>
                            <Select
                                className="w-full"
                                placeholder="Select Category Type"
                                options={CATEGORY_TYPE.map((role) => ({
                                    label: role.label,
                                    value: role.value,
                                }))}
                                allowClear
                                showSearch
                                onChange={handleCategoryChange}
                            />
                        </div>
                    )}
                    {props.showProductScreenButtons && (
                        <div className="flex flex-row items-center gap-5">
                            <div className="">
                                <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                    Categories
                                </p>
                                <Select
                                    // mode="multiple"
                                    allowClear
                                    placeholder="Select category"
                                    style={{ width: '100%' }}
                                    options={[
                                        // { label: 'All', value: undefined },
                                        { label: 'Paid', value: 'completed' },
                                        {
                                            label: 'Pending',
                                            value: 'pending',
                                        },
                                        // { label: 'Failed', value: 'failed' },
                                    ]}
                                />
                            </div>
                            <div className="">
                                <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                    Action
                                </p>
                                <Select
                                    // mode="multiple"
                                    allowClear
                                    placeholder="Select action"
                                    style={{ width: '100%' }}
                                    options={[
                                        // { label: 'All', value: undefined },
                                        { label: 'Paid', value: 'completed' },
                                        {
                                            label: 'Pending',
                                            value: 'pending',
                                        },
                                        // { label: 'Failed', value: 'failed' },
                                    ]}
                                />
                            </div>

                            <div>
                                <Button
                                    className=" fw-500 flex w-[100px] items-center  rounded-2xl border border-[#1A3353]  
                                     py-3 text-xl text-[#1A3353]"
                                >
                                    Export
                                </Button>
                            </div>
                        </div>
                    )}
                    {props.showStoreScreenButtons && (
                        <div className="flex w-[80%] flex-row items-center  justify-evenly">
                            <div className="w-[20%]">
                                <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                    Pincode
                                </p>
                                <Select
                                    // mode="multiple"
                                    allowClear
                                    placeholder="Select pincode"
                                    style={{ width: '100%' }}
                                />
                            </div>
                            <div className="w-[20%]">
                                <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                    Action
                                </p>
                                <Select
                                    // mode="multiple"
                                    allowClear
                                    placeholder="Select action"
                                    style={{ width: '100%' }}
                                    options={[
                                        // { label: 'All', value: undefined },
                                        { label: 'Paid', value: 'completed' },
                                        {
                                            label: 'Pending',
                                            value: 'pending',
                                        },
                                        // { label: 'Failed', value: 'failed' },
                                    ]}
                                />
                            </div>

                            <div>
                                <Button
                                    className=" fw-500 flex w-[100px] items-center  rounded-2xl border border-[#1A3353]  
                                     py-3 text-xl text-[#1A3353]"
                                >
                                    Export
                                </Button>
                            </div>
                        </div>
                    )}

                    {props.showStoreDetailScreenButtons && (
                        <div className="flex w-[70%] flex-row items-end justify-end gap-5  ">
                            <div className="w-[20%]">
                                <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                    Type
                                </p>
                                <Select
                                    // mode="multiple"
                                    allowClear
                                    placeholder="Select type"
                                    className="w-[100%]"
                                    options={[
                                        {
                                            value: 'simple',
                                            label: 'Simple Product',
                                        },
                                        {
                                            value: 'variable',
                                            label: 'Variable',
                                        },
                                    ]}
                                    onChange={(value) =>
                                        props?.onTypeChange?.(value)
                                    }
                                />
                            </div>
                            {/* <div className="w-[20%]">
                                <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                    Categories
                                </p>
                                <Select
                                    // mode="multiple"
                                    allowClear
                                    placeholder="Select type"
                                    className="w-[100%]"
                                />
                            </div>
                            <div className="w-[20%]">
                                <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                    Action
                                </p>
                                <Select
                                    // mode="multiple"
                                    allowClear
                                    className="w-[100%]"
                                    placeholder="Select action"
                                    options={[
                                        // { label: 'All', value: undefined },
                                        { label: 'Paid', value: 'completed' },
                                        {
                                            label: 'Pending',
                                            value: 'pending',
                                        },
                                        // { label: 'Failed', value: 'failed' },
                                    ]}
                                />
                            </div>

                            <div>
                                <Button
                                    className=" fw-500 flex w-[100px] items-center  rounded-2xl border border-[#1A3353]  
                                     py-3 text-xl text-[#1A3353]"
                                >
                                    Export
                                </Button>
                            </div> */}
                            <div>
                                <Button
                                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                    onClick={props?.onImportClick}
                                >
                                    <p>Bulk Import</p>
                                    <span className="-translate-y-1 text-3xl">
                                        +
                                    </span>
                                </Button>
                            </div>
                            <div>
                                <Button
                                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                    onClick={props?.onExportClick}
                                >
                                    Export
                                    <DownloadOutlined className="ml-2 text-16" />
                                </Button>
                            </div>
                            {/* <div>
                                <Button
                                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                    onClick={props?.ExportProductsTemplate}
                                >
                                    Export
                                    <DownloadOutlined className="ml-2 text-16" />
                                </Button>
                            </div> */}
                        </div>
                    )}
                </div>
                {props.showTabs && (
                    <div className="flex items-center justify-between pb-5 pt-9 ">
                        <div className="flex flex-row gap-10">
                            {tabs.map((tab) => {
                                return (
                                    <p
                                        onClick={() => {
                                            props.setActiveKey(tab.key);
                                            props.setCurrentPage(1);
                                        }}
                                        key={tab.key}
                                        className={`cursor-pointer pb-1.5 text-2xl font-semibold text-[#1A3353] ${
                                            props.activeKey === tab.key
                                                ? 'border-b-2 border-primary'
                                                : ''
                                        }`}
                                    >
                                        {tab.label}
                                    </p>
                                );
                            })}
                        </div>
                        <div className="flex items-center gap-5">
                            {(props.hasBookingWritePermission ||
                                store.role === RoleType.ORGANIZATION) && (
                                <>
                                    {props.activeKey !== 1 && (
                                        <Button
                                            className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                            onClick={props?.onExportClick}
                                        >
                                            Export
                                            <DownloadOutlined className="ml-2 text-16" />
                                        </Button>
                                    )}

                                    <Button
                                        className=" fw-500 flex w-[140px] items-center rounded-2xl   bg-purpleLight  
                                    py-3 text-xl text-white"
                                        onClick={() =>
                                            props.setIsModalVisible(true)
                                        }
                                    >
                                        + Add Booking
                                    </Button>
                                </>
                            )}
                        </div>
                    </div>
                )}

                {props.activeKey === 1 && (
                    <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        selectionColumnWidth: 50,
                                        cellPaddingBlock: 10,
                                        cellPaddingInline: 10,
                                    },
                                },
                            }}
                        >
                            <Table
                                id=""
                                className="m-2 overflow-x-auto rounded-[6px] border-1"
                                pagination={false}
                                columns={props.columns}
                                dataSource={props.dataSource1?.map(
                                    (row: any, index: number) => ({
                                        ...row,
                                        key: index,
                                    })
                                )}
                                // onRow={props.toggleDiv}
                                loading={props.loading}
                                rowClassName={props.rowClassName}
                            />
                        </ConfigProvider>
                    </div>
                )}
                {props.activeKey === 2 && (
                    <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {},
                                },
                            }}
                        >
                            <Table
                                id=""
                                className="m-2 overflow-x-auto rounded-[6px] border-1"
                                pagination={false}
                                columns={props.columns2}
                                dataSource={props.dataSource2?.map(
                                    (row: any, index: number) => ({
                                        ...row,
                                        key: index,
                                    })
                                )}
                                // onRow={props.toggleDiv}
                                loading={props.loading}
                                rowClassName={props.rowClassName}
                            />
                        </ConfigProvider>
                    </div>
                )}
            </div>
        </ConfigProvider>
    );
};

export default CustomTable;
