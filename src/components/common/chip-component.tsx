export const RejectedButtonChip = () => (
    <button className="h-[20px] rounded bg-[#DE44361A] px-4 text-xl  font-[600] text-[#DE4436]">
        Rejected
    </button>
);

export const ConfimredButtonChip = () => (
    <button
        className="w-fit rounded bg-green-100 bg-opacity-50 px-3 py-1 text-xl
 text-green-500"
    >
        Paid
    </button>
);

export const PendingButtonChip = () => (
    <button className="h-[24px] rounded bg-[#ECF2FE] px-4 text-xl  font-[600] text-[#3E79F7]">
        Pending
    </button>
);

export const ReturnButtonChip = ({ text }: any) => (
    <button className="h-[20px] rounded bg-red-100 px-4 text-[13px] font-[600] text-red-500">
        {text}
    </button>
);

export const CheckInButtonChip = () => (
    <button
        className="w-fit rounded bg-green-100 bg-opacity-50 px-3 py-1 text-xl
 text-green-500"
    >
        Checked-In
    </button>
);
