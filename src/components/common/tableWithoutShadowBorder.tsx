import { DownOutlined } from '@ant-design/icons';
import { ConfigProvider, Dropdown, MenuProps, Table, Typography } from 'antd';
import { Link } from 'wouter';
import React from 'react';

const { Title } = Typography;

function goBack() {
    window.history.back();
}

// BULK ACTION ITEMS
const items: MenuProps['items'] = [
    {
        label: <a href="#">EDIT</a>,
        key: '0',
    },
    {
        label: <a href="#">DELETE</a>,
        key: '1',
    },
];

interface CommonTableProps {
    heading?: string;
    bulkAction?: boolean;
    addNewLink?: string;
    addNewTitle?: string;
    addNew?: () => void;
    columns: any[];
    dataSource: any[];
    toggleDiv?: (record: any, rowIndex: any) => void;
    loading?: boolean;
    className?: string;
    addNewModal?: boolean;
    openModal?: any;
}

const NoShadowTable: React.FC<CommonTableProps> = (props) => {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Table: {
                        borderColor: '#0000001A',
                        cellFontSize: 13,
                        headerBg: '#fff',
                        headerColor: '#1A3353',
                        colorText: '#455560',
                    },
                },
            }}
        >
            <div>
                <div className="flex items-center justify-between  pb-4">
                    {props.heading && (
                        <div className="flex items-center gap-4">
                            <img
                                src="/icons/back.svg"
                                alt="edit"
                                className="h-[10px] cursor-pointer"
                                onClick={goBack}
                            />
                            <Title level={4}>{props.heading}</Title>
                        </div>
                    )}
                    <div className="flex items-center gap-4">
                        {/* {props.bulkAction && (
                            <Dropdown menu={{ items }} trigger={['click']}>
                                <button
                                    className="fw-500 rounded border border-[#8143D1] bg-[#8143D1] px-4 py-1 text-white"
                                    onClick={(e) => e.preventDefault()}
                                >
                                    Bulk Action
                                    <span className="ms-4">
                                        <DownOutlined className="text-[12px]" />
                                    </span>
                                </button>
                            </Dropdown>
                        )} */}
                        {props.addNewLink && (
                            <Link to={props.addNewLink}>
                                <button
                                    className="fw-500 flex items-center rounded border border-[#8143D1] bg-[#8143D1] px-4 py-1 text-white"
                                    onClick={props.addNew}
                                >
                                    {props.addNewTitle}
                                    <span className="ms-4">+</span>
                                </button>
                            </Link>
                        )}
                        {props.addNewModal ? (
                            <button
                                className="fw-500 flex items-center rounded border border-[#8143D1] bg-[#8143D1] px-4 py-1 text-white"
                                onClick={props.openModal}
                            >
                                {props.addNewTitle}
                                <span className="ms-4">+</span>
                            </button>
                        ) : (
                            ''
                        )}
                    </div>
                </div>
                <div className="m-0 w-full rounded-xl bg-white">
                    <Table
                        id=""
                        className="overflow-x-auto rounded-[6px]"
                        pagination={false}
                        columns={props.columns}
                        dataSource={props.dataSource?.map(
                            (row: any, index: number) => ({
                                ...row,
                                key: index,
                            })
                        )}
                        // onRow={props.toggleDiv}
                        loading={props.loading}
                    />
                </div>
            </div>
        </ConfigProvider>
    );
};

export default NoShadowTable;
