import { CheckCircleOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';

interface PasswordChecklistProps {
    password: string;
}

const PasswordChecklist: React.FC<PasswordChecklistProps> = ({ password }) => {
    const [checks, setChecks] = useState({
        length: false,
        upperCase: false,
        lowerCase: false,
        numberOrSpecialChar: false,
    });

    useEffect(() => {
        setChecks({
            length: password?.length >= 8 && password?.length <= 30,
            upperCase: /[A-Z]/.test(password),
            lowerCase: /[a-z]/.test(password),
            numberOrSpecialChar: /[0-9!@#$%^&*]/?.test(password),
        });
    }, [password]);

    return (
        <div>
            <ul>
                <li
                    className="mb-2 flex items-center gap-3"
                    style={{ color: checks?.length ? 'green' : 'red' }}
                >
                    {checks?.length && <CheckCircleOutlined />}
                    <p> Between 8 and 30 characters </p>
                </li>
                <li
                    className="mb-2 flex items-center gap-3"
                    style={{ color: checks.upperCase ? 'green' : 'red' }}
                >
                    {checks?.upperCase && <CheckCircleOutlined />}
                    <p> At least one upper case letter </p>
                </li>
                <li
                    className="mb-2 flex items-center gap-3"
                    style={{ color: checks.lowerCase ? 'green' : 'red' }}
                >
                    {checks?.lowerCase && <CheckCircleOutlined />}
                    At least one lower case letter
                </li>
                <li
                    className="mb-2 flex items-center gap-3"
                    style={{
                        color: checks?.numberOrSpecialChar ? 'green' : 'red',
                    }}
                >
                    {checks?.numberOrSpecialChar && <CheckCircleOutlined />}
                    <p> At least one number or special character </p>
                </li>
            </ul>
        </div>
    );
};

export default PasswordChecklist;
