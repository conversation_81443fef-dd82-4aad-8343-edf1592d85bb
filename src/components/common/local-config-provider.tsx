import { ConfigProvider } from 'antd';
import React from 'react';

interface ILocalConfigProvider {
    children: React.ReactNode;
}

const themeConfig = {
    components: {
        Form: {
            fontFamil: 'Roboto',
            labelFontSize: 16,
        },
        Typography: {
            titleMarginBottom: 0,
            titleMarginTop: 0,
        },
        Input: {
            colorPrimary: '#E6EBF1',
            colorPrimaryActive: '#E6EBF1',
            colorPrimaryHover: '#E6EBF1',
        },
        Select: {
            colorPrimary: '#E6EBF1',
            colorPrimaryActive: '#E6EBF1',
            colorPrimaryHover: '#E6EBF1',
        },
        DatePicker: {},
        Switch: {
            colorPrimaryBorder: '#8143D1',
            colorPrimary: '#8143D1',
            colorTextQuaternary: 'gray',
            colorFillQuaternary: 'gray',
        },
    },
};

const LocalConfigProvider: React.FC<ILocalConfigProvider> = ({ children }) => {
    return (
        <>
            <ConfigProvider theme={themeConfig}>{children}</ConfigProvider>
        </>
    );
};

export default LocalConfigProvider;
