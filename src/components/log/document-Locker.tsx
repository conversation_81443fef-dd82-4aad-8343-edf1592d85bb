import {
    MoreOutlined,
    UploadOutlined,
    DeleteOutlined,
    CloudUploadOutlined,
    EyeInvisibleOutlined,
    EyeOutlined,
    DownloadOutlined,
    ReloadOutlined,
    LinkOutlined,
} from '@ant-design/icons';
import {
    Button,
    ConfigProvider,
    Dropdown,
    Table,
    Modal,
    Upload,
    message,
    Form,
    Input,
    MenuProps,
    Image,
    Spin,
    Alert,
} from 'antd';
import { useEffect, useState, useCallback } from 'react';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import {
    ClientCreateDocument,
    ClientsDocumentListing,
    ClientUploadDocument,
    DeleteClientDocument,
} from '~/redux/actions/customer-action';
import { useAppDispatch } from '~/hooks/redux-hooks';
import Alertify from '~/services/alertify';

const { Dragger } = Upload;

type DocumentRow = {
    _id?: string;
    id?: string;
    key?: string;
    date?: string;
    prescription?: string; // "Document Name" column
    documentName?: string;
    uploadBy?: string;
    uploadedBy?: string;
    createdBy?: string;
    file?: string; // URL/path to file
    url?: string;
    document?: string;
    fileUrl?: string;
    uploadedAt?: string | number | Date;
    [k: string]: any;
};

interface Props {
    userId: string;
    facilityId: string;
}

// Custom hook for PDF preview functionality - SIMPLIFIED VERSION
const usePdfPreview = () => {
    const [pdfVisible, setPdfVisible] = useState(false);
    const [pdfSrc, setPdfSrc] = useState('');
    const [pdfLoading, setPdfLoading] = useState(false);
    const [pdfError, setPdfError] = useState('');
    const [pdfOriginalUrl, setPdfOriginalUrl] = useState('');

    const openPdfPreview = useCallback((url: string) => {
        console.log('Opening PDF preview for:', url);

        setPdfVisible(true);
        setPdfLoading(true);
        setPdfError('');
        setPdfOriginalUrl(url);

        // Use Google Docs Viewer directly - most reliable
        const googleViewerUrl = `https://docs.google.com/gview?url=${encodeURIComponent(
            url
        )}&embedded=true`;
        setPdfSrc(googleViewerUrl);

        // Stop loading after 2 seconds
        setTimeout(() => {
            setPdfLoading(false);
        }, 2000);
    }, []);

    const closePdfPreview = useCallback(() => {
        console.log('Closing PDF preview');
        setPdfVisible(false);
        setPdfLoading(false);
        setPdfError('');
        setPdfSrc('');
        setPdfOriginalUrl('');
    }, []);

    const retryPdfLoad = useCallback(() => {
        if (pdfOriginalUrl) {
            console.log('Retrying PDF load');
            openPdfPreview(pdfOriginalUrl);
        }
    }, [pdfOriginalUrl, openPdfPreview]);

    return {
        pdfVisible,
        pdfSrc,
        pdfLoading,
        pdfError,
        pdfOriginalUrl,
        openPdfPreview,
        closePdfPreview,
        retryPdfLoad,
    };
};

// PDF Modal Component - SIMPLIFIED VERSION
const PdfModal = ({
    visible,
    src,
    loading,
    error,
    originalUrl,
    onClose,
    onRetry,
}: {
    visible: boolean;
    src: string;
    loading: boolean;
    error: string;
    originalUrl: string;
    onClose: () => void;
    onRetry: () => void;
}) => {
    const downloadFile = () => {
        console.log('Downloading file:', originalUrl);
        const link = document.createElement('a');
        link.href = originalUrl;
        link.download = originalUrl.split('/').pop() || 'document.pdf';
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const openInNewTab = () => {
        console.log('Opening in new tab:', originalUrl);
        window.open(originalUrl, '_blank', 'noopener,noreferrer');
    };

    return (
        <Modal
            title="PDF Preview"
            open={visible}
            onCancel={onClose}
            footer={null}
            width="90vw"
            style={{ maxWidth: '1200px' }}
            bodyStyle={{
                height: '80vh',
                padding: 0,
            }}
            destroyOnClose
            maskClosable
            centered
        >
            {loading ? (
                <div className="flex h-full flex-col items-center justify-center">
                    <Spin size="large" />
                    <p className="mt-4 text-lg">Loading PDF preview...</p>
                </div>
            ) : error ? (
                <div className="mx-auto flex h-full max-w-md flex-col items-center justify-center text-center">
                    <Alert
                        message="Unable to Display PDF"
                        description={error}
                        type="warning"
                        showIcon
                        className="mb-6"
                    />

                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            icon={<LinkOutlined />}
                            onClick={openInNewTab}
                        >
                            Open in New Tab
                        </Button>
                        <Button
                            size="large"
                            icon={<DownloadOutlined />}
                            onClick={downloadFile}
                        >
                            Download File
                        </Button>
                    </div>
                </div>
            ) : src ? (
                <iframe
                    title="pdf-preview"
                    src={src}
                    style={{
                        border: 0,
                        width: '100%',
                        height: '100%',
                        minHeight: '100%',
                        backgroundColor: 'white',
                    }}
                    sandbox="allow-same-origin allow-scripts allow-popups"
                />
            ) : null}
        </Modal>
    );
};

const getDocId = (r: DocumentRow) => r._id || r.id || r.key || '';

const DocumentLocker = ({ userId, facilityId }: Props) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [documentsData, setDocumentsData] = useState<DocumentRow[]>([]);
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const [uploading, setUploading] = useState(false);
    const [deletingId, setDeletingId] = useState<string | null>(null);

    // Image preview states
    const [imgPreviewVisible, setImgPreviewVisible] = useState(false);
    const [imgPreviewSrc, setImgPreviewSrc] = useState<string>('');

    // Use PDF preview hook
    const {
        pdfVisible,
        pdfSrc,
        pdfLoading,
        pdfError,
        pdfOriginalUrl,
        openPdfPreview,
        closePdfPreview,
        retryPdfLoad,
    } = usePdfPreview();

    const getFileUrl = (r: DocumentRow) =>
        r.fileUrl || r.file || r.url || r.document || '';

    const isImageUrl = (url: string) =>
        /\.(png|jpe?g|gif|webp|bmp|svg)(\?.*)?$/i.test(url);

    const isPdfUrl = (url: string) => {
        try {
            return new URL(url).pathname.toLowerCase().endsWith('.pdf');
        } catch {
            return /\.pdf(\?.*)?$/i.test(url.toLowerCase());
        }
    };

    const showModal = () => {
        setIsModalOpen(true);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
        form.resetFields();
        setFileList([]);
    };

    // Function to fetch documents
    const fetchDocuments = async () => {
        try {
            setLoading(true);
            console.log('Fetching documents for user:', userId);

            const response = await dispatch(
                ClientsDocumentListing({ userId: userId })
            ).unwrap();

            console.log('Documents API Response:', response);

            const transformedData =
                response?.data?.map((item: any, index: number) => ({
                    key: item.id || index.toString(),
                    date:
                        item.uploadedAt ?? item.date
                            ? new Date(item.uploadedAt ?? item.date)
                                  .toLocaleDateString('en-GB')
                                  .replace(/\//g, '-')
                            : '',
                    prescription:
                        item.documentName || item.name || item.prescription,
                    uploadBy:
                        item.uploadedBy || item.createdBy || item.uploadBy,
                    ...item,
                })) || [];

            setDocumentsData(transformedData);
            console.log(
                'Documents loaded successfully:',
                transformedData.length
            );
        } catch (error) {
            console.error('Failed to fetch documents:', error);
            message.error('Failed to load documents');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (userId) {
            fetchDocuments();
        }
    }, [userId]);

    const handleUpload = async (values: { documentName: string }) => {
        try {
            if (fileList.length === 0) {
                message.error('Please select at least one file to upload');
                return;
            }

            setUploading(true);
            const file = fileList[0];

            if (!file.originFileObj) {
                message.error('Invalid file selected');
                return;
            }

            console.log('Starting file upload...', {
                fileName: file.name,
                fileSize: file.size,
                fileType: file.type,
                documentName: values.documentName,
            });

            const uploadResponse = await dispatch(
                ClientUploadDocument({
                    file: file.originFileObj,
                })
            );

            console.log('Upload Response:', uploadResponse);

            const isUploadSuccess =
                uploadResponse?.payload?.res?.status === 200 ||
                uploadResponse?.payload?.res?.status === 201 ||
                uploadResponse?.payload?.status === 200 ||
                uploadResponse?.payload?.status === 201;

            if (isUploadSuccess) {
                const documentUrl =
                    uploadResponse?.payload?.data?.data?.document;

                console.log('Document URL from upload:', documentUrl);

                const createDocumentPayload = {
                    facilityId: facilityId,
                    documentName: values.documentName,
                    file: documentUrl,
                    userId: userId,
                };

                const createResponse = await dispatch(
                    ClientCreateDocument({ createDocumentPayload })
                );

                if (
                    createResponse?.payload?.status === 200 ||
                    createResponse?.payload?.status === 201
                ) {
                    Alertify.success('Document uploaded successfully');
                    setIsModalOpen(false);
                    form.resetFields();
                    setFileList([]);
                    await fetchDocuments();
                } else {
                    const errorMessage =
                        createResponse?.payload?.message ||
                        'Failed to create document record';
                    Alertify.error(errorMessage);
                }
            } else {
                const errorMessage =
                    uploadResponse?.payload?.res?.message ||
                    uploadResponse?.payload?.message ||
                    'Upload failed. Please try again.';
                message.error(errorMessage);
            }
        } catch (error) {
            console.error('Upload error:', error);
            if (error instanceof Error) {
                message.error(`Upload failed: ${error.message}`);
            } else {
                message.error('Upload failed. Please try again.');
            }
        } finally {
            setUploading(false);
        }
    };

    const handleDeleteDocument = async (docId: string) => {
        try {
            setDeletingId(docId);
            await dispatch(DeleteClientDocument(docId)).unwrap();
            Alertify.success('Document deleted successfully');
            await fetchDocuments();
        } catch (error) {
            console.error('Failed to delete document:', error);
            message.error('Failed to delete document');
        } finally {
            setDeletingId(null);
        }
    };

    const openDeleteConfirm = (record: DocumentRow) => {
        const id = getDocId(record);
        Modal.confirm({
            title: 'Delete this document?',
            icon: null,
            content: `This will permanently delete "${
                record.documentName || record.prescription || 'this file'
            }".`,
            okText: 'Delete',
            okType: 'primary',
            okButtonProps: {
                type: 'default',
                style: {
                    backgroundColor: '#A77BDF',
                    borderColor: '#A77BDF',
                    color: '#fff',
                },
            },
            cancelText: 'Cancel',
            cancelButtonProps: {
                type: 'default',
                style: {
                    backgroundColor: 'transparent',
                    borderColor: '#1A3353',
                    color: '#1A3353',
                },
            },
            onOk: () => handleDeleteDocument(id),
        });
    };

    const handleViewClick = async (record: DocumentRow) => {
        const url = getFileUrl(record);
        if (!url) {
            message.error('No file to view');
            return;
        }

        console.log('Opening file:', url);

        if (isImageUrl(url)) {
            console.log('Opening image preview');
            setImgPreviewSrc(url);
            setImgPreviewVisible(true);
        } else if (isPdfUrl(url)) {
            console.log('Opening PDF preview');
            await openPdfPreview(url);
        } else {
            console.log('Opening in new tab');
            window.open(url, '_blank', 'noopener,noreferrer');
        }
    };

    const uploadProps: UploadProps = {
        name: 'file',
        multiple: false,
        fileList,
        beforeUpload: (file: File) => {
            const isImage = file.type.startsWith('image/');
            const isPDF = file.type === 'application/pdf';
            if (!isImage && !isPDF) {
                message.error(
                    'Only image (JPEG/PNG) or PDF files are allowed.'
                );
                return Upload.LIST_IGNORE;
            }
            const isLt5M = file.size / 1024 / 1024 < 5;
            if (!isLt5M) {
                message.error('File must be smaller than 5MB!');
                return Upload.LIST_IGNORE;
            }
            return false;
        },
        onChange: (info) => setFileList(info.fileList),
        onDrop(e) {
            console.log('Dropped files', e.dataTransfer?.files);
        },
    };

    const columns = [
        {
            title: 'Date',
            dataIndex: 'date',
            key: 'date',
        },
        {
            title: 'Document Name',
            dataIndex: 'prescription',
            key: 'prescription',
        },
        {
            title: 'Upload by',
            dataIndex: 'uploadBy',
            key: 'uploadBy',
        },
        {
            title: 'View',
            key: 'view',
            align: 'center' as const,
            render: (_: any, record: DocumentRow) => {
                const hasFile = Boolean(getFileUrl(record));

                return (
                    <Button
                        type="text"
                        aria-label="View document"
                        onClick={(e) => {
                            e.stopPropagation();
                            if (hasFile) handleViewClick(record);
                        }}
                        icon={
                            hasFile ? (
                                <EyeOutlined style={{ fontSize: 18 }} />
                            ) : (
                                <EyeInvisibleOutlined
                                    style={{ fontSize: 18, opacity: 0.5 }}
                                />
                            )
                        }
                        disabled={!hasFile}
                        title={hasFile ? 'View document' : 'No file available'}
                    />
                );
            },
        },
        {
            title: 'Action',
            dataIndex: 'action',
            key: 'action',
            width: 120,
            align: 'center' as const,
            render: (_: any, record: DocumentRow) => {
                const items: MenuProps['items'] = [
                    {
                        key: 'download',
                        label: 'Download',
                        onClick: ({ domEvent }: any) => {
                            domEvent.stopPropagation();
                            const url = getFileUrl(record);
                            if (!url)
                                return message.error('No file to download');

                            console.log('Downloading:', url);
                            const link = document.createElement('a');
                            link.href = url;
                            link.download =
                                record.documentName ||
                                record.prescription ||
                                'document';
                            link.target = '_blank';
                            link.rel = 'noopener noreferrer';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        },
                    },
                    { key: 'delete', label: 'Delete Document' },
                ];

                const onClick: MenuProps['onClick'] = ({ key }) => {
                    if (key === 'delete') openDeleteConfirm(record);
                };

                return (
                    <Dropdown menu={{ items, onClick }} trigger={['click']}>
                        <Button
                            type="text"
                            icon={<MoreOutlined />}
                            loading={deletingId === getDocId(record)}
                            onClick={(e) => e.stopPropagation()}
                        />
                    </Dropdown>
                );
            },
        },
    ];

    return (
        <>
            <div className="flex flex-row items-center justify-between border-b pb-5">
                <p className="text-[15px] font-semibold text-[#1a3353]"></p>
                <div>
                    <Button
                        className="h-12 border border-purpleLight bg-purpleLight px-4 text-xl text-[#ffffff]"
                        onClick={showModal}
                    >
                        <UploadOutlined />
                        Upload Document
                    </Button>
                </div>
            </div>

            <div className="py-14">
                <ConfigProvider>
                    <Table
                        columns={columns}
                        dataSource={documentsData}
                        loading={loading}
                        pagination={{
                            pageSize: 10,
                            showSizeChanger: true,
                            showQuickJumper: true,
                        }}
                        scroll={{ x: 800 }}
                    />
                </ConfigProvider>
            </div>

            {/* Upload Modal */}
            <Modal
                title="Upload Client Document"
                open={isModalOpen}
                onCancel={handleCancel}
                footer={null}
                width={600}
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleUpload}
                    className="mt-4"
                >
                    <Form.Item>
                        <Dragger {...uploadProps}>
                            <div className="ant-upload-drag-icon flex items-center justify-center pb-3">
                                <CloudUploadOutlined
                                    style={{
                                        fontSize: '6vw',
                                        color: '#8143D1',
                                    }}
                                />
                            </div>
                            <p className="ant-upload-text">
                                Click or drag files to this area to upload
                            </p>
                            <p className="ant-upload-hint">
                                Support formats: JPEG, PNG, PDF <br /> Maximum
                                file size: 5MB per file.
                            </p>
                        </Dragger>
                    </Form.Item>

                    <Form.Item
                        name="documentName"
                        label="Document Name"
                        rules={[
                            {
                                required: true,
                                message: 'Please enter document name!',
                            },
                        ]}
                    >
                        <Input placeholder="Enter document name" />
                    </Form.Item>

                    <Form.Item className="mb-0 mt-6 flex justify-end">
                        <div className="flex gap-3">
                            <Button onClick={handleCancel} disabled={uploading}>
                                Cancel
                            </Button>
                            <Button
                                type="primary"
                                htmlType="submit"
                                className="bg-purpleLight"
                                loading={uploading}
                            >
                                Upload Document
                            </Button>
                        </div>
                    </Form.Item>
                </Form>
            </Modal>

            {/* Image Preview Modal */}
            <Image
                style={{ display: 'none' }}
                src={imgPreviewSrc}
                alt="preview"
                preview={{
                    visible: imgPreviewVisible,
                    src: imgPreviewSrc,
                    onVisibleChange: (vis) => setImgPreviewVisible(vis),
                }}
            />

            {/* PDF Preview Modal */}
            <PdfModal
                visible={pdfVisible}
                src={pdfSrc}
                loading={pdfLoading}
                error={pdfError}
                originalUrl={pdfOriginalUrl}
                onClose={closePdfPreview}
                onRetry={retryPdfLoad}
            />
        </>
    );
};

export default DocumentLocker;
