import React, { useState } from 'react';
import { Button, Input, Modal } from 'antd';
import { ValidatePin } from '~/redux/actions/auth-actions';
import Alertify from '~/services/alertify';
import { useAppDispatch } from '~/hooks/redux-hooks';

interface ModalProps {
    visible: boolean | undefined;
    onClose: () => void;
}

const LoginPinSystemModal: React.FC<ModalProps> = ({ visible, onClose }) => {
    const [pin, setPin] = useState(''); // State to manage the input value
    const dispatch = useAppDispatch();

    // Only close modal when close icon is clicked
    const handleCancel = () => {
        setPin('');
        // setIsModalOpen(false); // This will close the modal when the close icon is clicked
        onClose();
    };
    const onChange = (text: any) => {
        console.log('onChange:', text);
        setPin(text);
    };
    const sharedProps = {
        onChange,
    };

    function handlePin() {
        if (pin.length === 4) {
            return dispatch(ValidatePin({ pin }))
                .unwrap()
                .then(() => onClose());
        }
        return Alertify.error('Please enter complete pin');
    }

    return (
        <>
            <Modal
                title={
                    <p className="pt-10 text-center text-3xl text-[#1A3353]">
                        Enter your pin to unlock
                    </p>
                }
                className="lg:w-[30%]"
                centered
                open={visible}
                onCancel={handleCancel}
                footer={null}
                maskClosable={false}
                keyboard={false}
                closable={false}
            >
                <div className="flex flex-col items-center justify-center gap-12 pb-10 pt-5">
                    <Input.OTP length={4} {...sharedProps} />
                    <Button
                        className=" w-[110px] border-[#8143D1] bg-[#8143D1] text-lg text-white"
                        onClick={handlePin}
                        disabled={pin.length < 4}
                    >
                        Submit
                    </Button>
                </div>
            </Modal>
        </>
    );
};

export default LoginPinSystemModal;
