import React, { useEffect } from 'react';
import LoginPinSystemModal from '~/components/container.js/login-pin-system-modal';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { GetSettings } from '~/redux/actions/settings-actions';
import { SetShowPinModal } from '~/redux/slices/common-slice';
import { RoleType, Staff_Roles } from '~/types/enums';

const Container: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const dispatch = useAppDispatch();

    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        pinTimer: state.auth_store.pinStandBy,
        is_login: state.auth_store.is_Login,
        showPinModal: state.common_store.showPinModal,
    }));

    useEffect(() => {
        if (store.is_login) {
            if (store.role === RoleType.ORGANIZATION) {
                dispatch(GetSettings({}));
            }
        }
    }, [store.is_login]);

    function closePin() {
        dispatch(SetShowPinModal(false));
        // setShouldShowModal(false);
    }

    useEffect(() => {
        let timeoutId: any;
        if (store.is_login && Staff_Roles[store.role]) {
            const resetTimer = () => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    console.log('User has been inactive for 5 minutes');
                    dispatch(SetShowPinModal(true));
                    // setShouldShowModal(true);
                }, store.pinTimer * 1000);
            };

            // Events to detect activity
            window.onload = resetTimer;
            document.onmousemove = resetTimer;
            document.onkeypress = resetTimer;
            document.onclick = resetTimer;
            document.onscroll = resetTimer;
            document.onmousedown = resetTimer; // touchscreen presses
            document.ontouchstart = resetTimer; // touches on the screen
            document.onkeypress = resetTimer;
        }

        return () => {
            clearTimeout(timeoutId);
            document.onmousemove = null;
            document.onkeypress = null;
            document.onclick = null;
            document.onscroll = null;
            document.onmousedown = null;
            document.ontouchstart = null;
            document.onkeypress = null;
        };
    }, [store.is_login]);

    return (
        <>
            <div className="h-full w-full">
                <>
                    {children}
                    <LoginPinSystemModal
                        // visible={store.showPinModal}
                        visible={false}
                        onClose={closePin}
                    />
                </>
            </div>
        </>
    );
};

export default Container;
