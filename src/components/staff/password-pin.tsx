import { But<PERSON>, ConfigProvider, Form, Tabs, TabsProps } from 'antd';
import React, { useState } from 'react';
import ChangePassword from './change-password';
import ChangePin from './change-pin';
import {
    CompleteStaffProfile,
    GeneratePin,
} from '~/redux/actions/staff-action';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { summarizeNestedObject } from '~/redux/actions/settings-actions';
import GeneratePinModal from './generate-pin-modal';
import GeneratePasswordModal from './generate-password-modal';
import { useParams } from 'wouter';

const PasswordPin = (props: any) => {
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        staffOnboarding: state.settings_store.staffOnboarding,
    }));

    const { id } = useParams();

    const ShowTabvalue = summarizeNestedObject(store.staffOnboarding);

    const [pinGenerateModal, setPinGenerateModal] = useState(false);
    const showPinModal = () => {
        setPinGenerateModal(true);
    };

    const [passwordGenerateModal, setPasswordGenerateModal] = useState(false);
    const showPasswordModal = () => {
        setPasswordGenerateModal(true);
    };

    const onChange = (key: string) => {
        console.log(key);
    };

    const hidePinModal = () => {
        setPinGenerateModal(false);
    };

    const hidePasswordModal = () => {
        setPasswordGenerateModal(false);
    };

    function handleSave() {
        dispatch(GeneratePin({ userId: id }))
            .unwrap()
            .finally(() => {
                hidePinModal();
                hidePasswordModal();
            });
    }

    const items: TabsProps['items'] = [
        // {
        //     key: '1',

        //     label: (
        //         <div className="flex gap-5">
        //             <div className="px-8 font-semibold ">Password</div>
        //         </div>
        //     ),
        //     children: <ChangePassword onSave={() => {}} />,
        // },

        {
            key: '2',
            label: (
                <div className="flex gap-5">
                    <div className="px-8 font-semibold">Pin</div>
                </div>
            ),
            children: (
                <ChangePin
                    userId={id}
                    setParentActiveTab={props.setParentActiveTab}
                />
            ),
        },
    ];

    return (
        <div>
            <div className="">
                <div className="flex gap-5 lg:flex-row lg:justify-end lg:pe-10">
                    {/* <Button
                        onClick={showPinModal}
                        className=" cursor-pointer border-purpleLight bg-purpleLight py-5 text-xl text-[#fff]"
                    >
                        Generate Pin
                    </Button>
                    <Button
                        onClick={showPinModal}
                        className=" cursor-pointer border-purpleLight bg-purpleLight py-5 text-xl text-[#fff]"
                    >
                        Reset Pin
                    </Button>
                    <Button
                        onClick={showPasswordModal}
                        className=" cursor-pointer border-purpleLight bg-purpleLight py-5 text-xl text-[#fff]"
                    >
                        Generate Password
                    </Button> */}
                </div>

                <ConfigProvider
                    theme={{
                        components: {
                            Tabs: {
                                inkBarColor: '#8143D1',
                                itemActiveColor: '#000',
                                itemSelectedColor: '#000',
                                itemColor: '#000',
                                itemHoverColor: '#000000',
                                colorPrimaryActive: '#000',
                            },
                        },
                    }}
                >
                    <Tabs
                        defaultActiveKey="1"
                        items={items}
                        onChange={onChange}
                        tabPosition="top"
                        tabBarStyle={
                            {
                                // backgroundColor: '#f5f5f5',
                            }
                        }
                    />
                </ConfigProvider>
            </div>
            {pinGenerateModal && (
                <GeneratePinModal
                    isVisible={pinGenerateModal}
                    onClose={hidePinModal}
                    onSave={handleSave}
                />
            )}
            {passwordGenerateModal && (
                <GeneratePasswordModal
                    isVisible={passwordGenerateModal}
                    onClose={hidePasswordModal}
                />
            )}
        </div>
    );
};

export default PasswordPin;
