import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Collapse, Form, Input, Select, Space } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useMemo, useState } from 'react';
import ReactQuill from 'react-quill';
import { useSelector } from 'react-redux';
import { useAppSelector } from '~/hooks/redux-hooks';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';

const { Option } = Select;

const Skills = ({ onSave, role }) => {
    const [form] = useForm();

    const store = useAppSelector((state) => ({
        certification: state.staff_store.additionalInfo.certification,
        experience: state.staff_store.additionalInfo.experience,
        description: state.staff_store.additionalInfo.description,
        role: state.auth_store.role,
    }));
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const [isEditing, setIsEditing] = useState(false);
    const hasStaffUpdatePermission = useMemo(() => {
        if (store.role === RoleType.ORGANIZATION) {
            return true;
        } else {
            return all_permissions_for_role?.some((module: any) =>
                module.subjects?.some(
                    (subject: any) =>
                        subject.type ===
                            SUBJECT_TYPE.AUTHENTICATION_STAFF_ONBOARDING &&
                        subject.actions?.some((action: any) =>
                            action.permissions?.some(
                                (permission: any) =>
                                    permission.type ===
                                    PERMISSIONS_ENUM.STAFF_UPDATE
                            )
                        )
                )
            );
        }
    }, [all_permissions_for_role]);
    const text1 = (
        <>
            {/* Certification Name Input  */}
            <Form.Item
                label="Certification Name"
                name="certification"
                rules={[
                    {
                        required: true,
                        message: 'Please input Certification Name',
                    },
                ]}
            >
                <Input placeholder="Enter Certification Name" />
            </Form.Item>

            {/* Experience Input */}
            <Form.Item
                label="Experience Years"
                name="experienceYears"
                rules={[
                    {
                        required: true,
                        message: 'Please select Experience in Years',
                    },
                ]}
            >
                <Select placeholder="Select Years">
                    <Option value="0">00</Option>
                    <Option value="1">01</Option>
                    <Option value="2">02</Option>
                    <Option value="3">03</Option>
                    <Option value="4">04</Option>
                    <Option value="5">05</Option>
                    <Option value="6">06</Option>
                    <Option value="7">07</Option>
                    <Option value="8">08</Option>
                    <Option value="9">09</Option>
                    <Option value="10">10</Option>
                    <Option value="11">11</Option>
                    <Option value="12">12</Option>
                </Select>
            </Form.Item>

            <Form.Item
                label="Experience Months"
                name="experienceMonths"
                rules={[
                    {
                        required: false,
                        message: 'Please select Experience in Months',
                    },
                ]}
            >
                <Select placeholder="Select Months">
                    <Option value="0">00</Option>
                    <Option value="1">01</Option>
                    <Option value="2">02</Option>
                    <Option value="3">03</Option>
                    <Option value="4">04</Option>
                    <Option value="5">05</Option>
                    <Option value="6">06</Option>
                    <Option value="7">07</Option>
                    <Option value="8">08</Option>
                    <Option value="9">09</Option>
                    <Option value="10">10</Option>
                    <Option value="11">11</Option>
                    <Option value="12">12</Option>
                </Select>
            </Form.Item>

            <Form.Item
                label="Description"
                name="description"
                rules={[
                    {
                        required: true,
                        // message: 'Please input your address!',
                    },
                ]}
            >
                <ReactQuill
                    readOnly={!hasStaffUpdatePermission}
                    theme="snow"
                    className={!hasStaffUpdatePermission ? 'bg-gray-100' : ''}
                />
            </Form.Item>
        </>
    );
    const onChange = (key: string | string[]) => {
        console.log(key);
    };
    const customExpandIcon = (panelProps: any) =>
        panelProps.isActive ? <MinusOutlined /> : <PlusOutlined />;

    async function saveDetails() {
        await form.validateFields();
        const { experienceYears, experienceMonths, ...fields } =
            form.getFieldsValue();
        const payload = {
            ...fields,
            experience: `${experienceYears}.${experienceMonths} Years`,
        };
        onSave(payload);
        setIsEditing(false);
    }

    useEffect(() => {
        // Parse experience
        let years = null;
        let months = null;
        if (store.experience) {
            const [numericPart] = store.experience.split(' '); // Split by space and take the numeric part
            const [yearsPart, monthsPart] = numericPart.split('.'); // Split the numeric part by dot

            years = parseInt(yearsPart, 10); // Parse the years part
            months = parseInt(monthsPart, 10); // Parse the months part
        }

        console.log({ years, months });

        form.setFieldsValue({
            certification: store.certification,
            experienceYears: years,
            experienceMonths: months,
            description: store.description,
        });
    }, [store]);
    return (
        <div className="w-full py-10 pr-10">
            {hasStaffUpdatePermission && !isEditing && (
                <div
                    className="mb-2 flex justify-end"
                    onClick={() => setIsEditing(true)}
                >
                    <img
                        src="/icons/common/edit.svg"
                        alt="edit"
                        className="ms-auto h-[20px] cursor-pointer"
                    />
                </div>
            )}
            <Space className="w-full" direction="vertical" size="large">
                <Collapse
                    className="custom-collapse client-profile-collapse w-full rounded-2xl"
                    bordered={false}
                    defaultActiveKey={['1', '2']}
                    onChange={onChange}
                    expandIcon={customExpandIcon}
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-primary font-semibold">
                                    Skill and Experiences
                                </div>
                            ),
                            children: (
                                <Form
                                    name="skills"
                                    layout="vertical"
                                    size="large"
                                    autoComplete="off"
                                    disabled={
                                        !hasStaffUpdatePermission || !isEditing
                                    }
                                    // initialValues={{ gender: 'select' }}
                                    form={form}
                                >
                                    <p>{text1}</p>
                                </Form>
                            ),
                        },
                    ]}
                    // className="custom-collapse w-full rounded-2xl bg-[F2F2F280]"
                    // className="custom-collapse"
                    expandIconPosition="right"
                />
            </Space>
            {hasStaffUpdatePermission && isEditing && (
                <div className="flex flex-row justify-end gap-5">
                    <Form.Item>
                        <div className="mt-10" style={{ display: 'flex' }}>
                            <Button
                                className="border-1 border-[#1A3353] py-7 text-xl text-[#1A3353] lg:px-20 @sm:px-10"
                                onClick={() => setIsEditing(false)}
                            >
                                Cancel
                            </Button>
                        </div>
                    </Form.Item>
                    <Form.Item>
                        <div
                            className="mt-10"
                            style={{ display: 'flex', gap: '10px' }}
                        >
                            <Button
                                className="bg-purpleLight px-20 py-7 text-xl text-white"
                                onClick={saveDetails}
                            >
                                Confirm
                            </Button>
                        </div>
                    </Form.Item>
                </div>
            )}
        </div>
    );
};

export default Skills;
