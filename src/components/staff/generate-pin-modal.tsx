// ModalComponent.tsx
import React from 'react';
import { Modal, Button } from 'antd';

interface GeneratePinModalProps {
    isVisible: boolean;
    onClose: () => void;
    onSave: () => void;
}

const GeneratePinModal: React.FC<GeneratePinModalProps> = ({
    isVisible,
    onClose,
    onSave,
}) => {
    return (
        <Modal
            title={<p className="border-b-2 text-[#1A3353]">Generate Pin</p>}
            open={isVisible}
            footer={false}
            onCancel={onClose}
        >
            <div className="flex flex-col gap-10">
                <p>Do you want to generate a pin ?</p>
                <div className="flex justify-end gap-5">
                    <Button
                        className="w-[90px] bg-[#8143D1] text-white"
                        onClick={onSave}
                    >
                        Yes
                    </Button>
                    <Button
                        onClick={onClose}
                        className="w-[90px] border border-[#1A3353] text-[#1A3353]"
                    >
                        No
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default GeneratePinModal;
