import React, { useEffect, useState } from 'react';
import { TimePicker, Checkbox, Button, Select, Switch } from 'antd';
import { PlusOutlined, CloseOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import { capitalizeFirstLetter } from '../common/function';
import { DateRangeType } from '~/types/enums';
import { useAppSelector } from '~/hooks/redux-hooks';

interface TimeSlot {
    from: Dayjs | null;
    to: Dayjs | null;
    payRateIds: any;
    showStartTimeError?: boolean;
    showEndTimeError?: boolean;
    showServiceCategoryError?: boolean;
}

interface WorkingHours {
    [key: string]: TimeSlot[];
}

interface WeeklySchedule {
    dateRange: string;
    workingHours: WorkingHours;
    setWorkingHours: React.Dispatch<React.SetStateAction<WorkingHours>>;
    daySelected: string;
    dateSelectedTime?: string;
    availabilityType?: string;
    isEdit?: boolean;
}

const WeeklySchedule: React.FC<WeeklySchedule> = ({
    dateRange,
    workingHours,
    setWorkingHours,
    daySelected,
    dateSelectedTime,
    availabilityType,
    isEdit,
}) => {
    const [selectedDay, setSelectedDay] = useState<string>('');
    const [duplicateStatus, setDuplicateStatus] = useState<{
        [day: string]: boolean[];
    }>({});
    console.log('workingHours-----------------', workingHours);

    const daysOfWeek = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

    const handleDayClick = (day: string) => {
        setSelectedDay(day);
    };

    useEffect(() => {
        if (daySelected) {
            setSelectedDay(daySelected);
        }
    }, [daySelected]);

    const store = useAppSelector((state) => ({
        serviceCategoryList: state.pay_rate_store.serviceCategoryList,
    }));

    console.log('Selcted Date--------', selectedDay, dateSelectedTime);

    const handleAddTimeSlot = () => {
        const newSlot = { from: null, to: null, payRateIds: [] };
        setWorkingHours((prev) => ({
            ...prev,
            [selectedDay]: [...(prev[selectedDay] || []), newSlot],
        }));
    };

    const ServiceCategoryOption = store.serviceCategoryList?.map(
        (item: any) => ({
            value: item.serviceId,
            label: item.serviceCategoryName,
            id: item._id,
            ...item,
        })
    );

    const handleRemoveTimeSlot = (index: number) => {
        setWorkingHours((prev) => ({
            ...prev,
            [selectedDay]: prev[selectedDay]?.filter((_, i) => i !== index),
        }));
    };

    const handleTimeChange = (
        index: number,
        type: 'from' | 'to',
        time: Dayjs | null
    ) => {
        const formattedTime = time ? time.format('HH:mm') : null;
        setWorkingHours((prev) => {
            const updatedWorkingHours = { ...prev };
            updatedWorkingHours[selectedDay] = updatedWorkingHours[
                selectedDay
            ]?.map((slot, i) => {
                if (i === index) {
                    const updatedSlot = { ...slot, [type]: formattedTime };
                    if (type === 'from') updatedSlot.to = null;
                    return updatedSlot;
                }
                return slot;
            });
            return updatedWorkingHours;
        });
    };

    const handleServiceCategoryChange = (
        index: number,
        value: string,
        options: any
    ) => {
        console.log('Updated code with changes---------------------', options);
        // let selected: any = value;
        // if (value.includes('all'))
        //     selected = ServiceCategoryOption?.map(
        //         (category: any) => category.value
        //     );

        let selectedPayRateIds: string[] = [];

        if (value.includes('all')) {
            selectedPayRateIds = ServiceCategoryOption.flatMap(
                (option) => option.payRateId
            );
        } else {
            selectedPayRateIds = options
                .map((option: any) => option.payRateId)
                .flat();
        }

        setWorkingHours((prev) => {
            const updatedWorkingHours = { ...prev };
            updatedWorkingHours[selectedDay] = updatedWorkingHours[
                selectedDay
            ]?.map((slot, i) =>
                i === index ? { ...slot, payRateIds: selectedPayRateIds } : slot
            );
            return updatedWorkingHours;
        });
    };

    // const handleDuplicateForAllDays = (e: any, index: number) => {
    //     setWorkingHours((prev) => {
    //         const newWorkingHours: WorkingHours = { ...prev };
    //         const slotToDuplicate = workingHours[selectedDay][index];

    //         const isSlotValid = slotToDuplicate.from && Duplicate.to;

    //         if (e.target.checked && isSlotValid) {
    //             // Duplicate the slot to all other days
    //             daysOfWeek.forEach((day) => {
    //                 if (day !== selectedDay) {
    //                     if (isEdit) {
    //                         // In edit mode, replace all existing slots
    //                         newWorkingHours[day] = [{ ...slotToDuplicate }];
    //                     } else {
    //                         // In add mode, append to existing slots
    //                         newWorkingHours[day] = [
    //                             ...(newWorkingHours[day] || []),
    //                             { ...slotToDuplicate },
    //                         ];
    //                     }
    //                 }
    //             });
    //         } else {
    //             // Remove the duplicated slot from all other days
    //             daysOfWeek.forEach((day) => {
    //                 if (day !== selectedDay) {
    //                     if (isEdit) {
    //                         // In edit mode, clear all slots
    //                         newWorkingHours[day] = [];
    //                     } else {
    //                         // In add mode, only remove the specific slot
    //                         newWorkingHours[day] = (
    //                             newWorkingHours[day] || []
    //                         ).filter(
    //                             (slot) =>
    //                                 !(
    //                                     slot.from === slotToDuplicate.from &&
    //                                     slot.to === slotToDuplicate.to
    //                                 )
    //                         );
    //                     }
    //                 }
    //             });
    //         }

    //         setDuplicateStatus((prevStatus) => ({
    //             ...prevStatus,
    //             [selectedDay]: {
    //                 ...prevStatus[selectedDay],
    //                 [index]: e.target.checked,
    //             },
    //         }));

    //         return newWorkingHours;
    //     });
    // };

    // useEffect(() => {
    //     if (!workingHours[selectedDay]?.length) handleAddTimeSlot();
    // }, [selectedDay]);

    const handleDuplicateForAllDays = (e: any, index: number) => {
        setWorkingHours((prev) => {
            const newWorkingHours: WorkingHours = { ...prev };
            const slotToDuplicate = workingHours[selectedDay][index];

            const isSlotValid = slotToDuplicate.from && slotToDuplicate.to;

            if (e.target.checked && isSlotValid) {
                daysOfWeek.forEach((day) => {
                    if (day !== selectedDay) {
                        newWorkingHours[day] = [{ ...slotToDuplicate }];
                    }
                });
            } else {
                daysOfWeek.forEach((day) => {
                    if (day !== selectedDay) {
                        newWorkingHours[day] = [];
                    }
                });
            }

            setDuplicateStatus((prevStatus) => ({
                ...prevStatus,
                [selectedDay]: {
                    ...prevStatus[selectedDay],
                    [index]: e.target.checked,
                },
            }));

            return newWorkingHours;
        });
    };

    useEffect(() => {
        if (selectedDay) {
            setWorkingHours((prev) => {
                const updatedWorkingHours = { ...prev };
                updatedWorkingHours[selectedDay] =
                    updatedWorkingHours[selectedDay]?.filter(
                        (slot) => slot.from !== null || slot.to !== null
                    ) || [];

                if (!updatedWorkingHours[selectedDay]?.length) {
                    updatedWorkingHours[selectedDay] = [
                        { from: null, to: null, payRateIds: [] },
                    ];
                }

                return updatedWorkingHours;
            });
        }
    }, [selectedDay]);

    const parseTimeString = (
        dateStr: string | undefined,
        timeStr: Dayjs | string | null
    ): Dayjs | null => {
        if (!timeStr) return null;
        let date = dayjs(dateStr);
        const time = dayjs(timeStr, 'HH:mm');
        date = date.hour(time.hour()).minute(time.minute());
        console.log('Updated date with new time:', dayjs(date.format()));
        return dayjs(date);
    };

    const getDisabledEndHours = (time: any) => {
        const startTime = dayjs(time, 'HH:mm');
        if (!startTime) return [];
        const startMinute = startTime.minute();
        const startHour = startTime.hour();

        return startHour !== null
            ? Array.from({ length: 24 }, (_, i) => i).filter(
                  (hour) =>
                      hour <= (startMinute < 59 ? startHour - 1 : startHour)
              )
            : [];
    };

    const getDisabledEndMinutes = (selectedHour: any, time: any) => {
        const startTime = time ? dayjs(time, 'HH:mm') : null;

        if (!startTime) return [];
        const startMinute = startTime.minute();
        const startHour = startTime.hour();
        return selectedHour === startHour
            ? Array.from({ length: 60 }, (_, i) => i).filter(
                  (minute) => minute <= startMinute
              )
            : [];
    };

    return (
        <div className="w-full rounded-2xl">
            {dateRange !== 'Single' && (
                <div className="flex flex-row items-center justify-between  pb-9 text-[13px] font-medium text-[#1A3353]">
                    Select Days
                    <div className=" flex flex-wrap lg:w-[80%] lg:gap-3  @xl:gap-8">
                        {dateRange === DateRangeType.MULTIPLE &&
                            daysOfWeek?.map((day) => (
                                <div className="">
                                    <Button
                                        shape="circle"
                                        className={`p-2   ${
                                            selectedDay?.includes(day)
                                                ? 'bg-[#455560] text-white'
                                                : 'bg-white'
                                        }`}
                                        onClick={() => handleDayClick(day)}
                                    >
                                        {capitalizeFirstLetter(day.slice(0, 3))}
                                    </Button>
                                </div>
                            ))}
                    </div>
                </div>
            )}
            <div className="w-full">
                {workingHours[selectedDay]?.map((slot, index) => {
                    console.log('Slot-------------', slot);
                    return (
                        <>
                            <div className=" flex w-full flex-col rounded-xl">
                                {index > 0 && (
                                    <div className="flex justify-end">
                                        <div
                                            onClick={() =>
                                                handleRemoveTimeSlot(index)
                                            }
                                        >
                                            <CloseOutlined />
                                        </div>
                                    </div>
                                )}
                                <div className="flex w-full flex-col justify-between gap-9 ">
                                    <div className="  flex justify-between lg:w-[100%]  lg:flex-row lg:items-center @sm:flex-col">
                                        <p className=" text-[13px] font-medium text-[#1A3353] lg:ms-2 lg:w-[65px] @sm:-ms-1.5 @sm:mb-4 @sm:w-[23%]">
                                            Start Time
                                        </p>
                                        <div className="flex w-[80%] flex-col gap-1">
                                            <TimePicker
                                                variant="borderless"
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                }}
                                                className="w-[100%]"
                                                format="HH:mm"
                                                minuteStep={15}
                                                placeholder="Opening Time"
                                                value={parseTimeString(
                                                    dateSelectedTime,
                                                    slot.from
                                                )}
                                                onChange={(time) =>
                                                    handleTimeChange(
                                                        index,
                                                        'from',
                                                        time
                                                    )
                                                }
                                            />
                                            {slot.showStartTimeError && (
                                                <span className="text-red-400">
                                                    Start Time is required
                                                </span>
                                            )}
                                        </div>
                                    </div>

                                    <div className="  flex justify-between  lg:w-[100%] lg:flex-row lg:items-center @sm:flex-col">
                                        <p className="text-[13px] font-medium text-[#1A3353] lg:ms-2 lg:w-[65px] @sm:-ms-1.5 @sm:mb-4 @sm:w-[23%]">
                                            End Time
                                        </p>

                                        <div className="flex w-[80%] flex-col gap-1 ">
                                            <TimePicker
                                                variant="borderless"
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                }}
                                                className="w-[100%]"
                                                format="HH:mm"
                                                minuteStep={15}
                                                placeholder="Closing Time"
                                                value={parseTimeString(
                                                    dateSelectedTime,
                                                    slot.to
                                                )}
                                                disabled={!slot.from}
                                                disabledTime={() => ({
                                                    disabledHours: () =>
                                                        getDisabledEndHours(
                                                            slot.from
                                                        ),
                                                    disabledMinutes: (
                                                        selectedHour
                                                    ) =>
                                                        getDisabledEndMinutes(
                                                            selectedHour,
                                                            slot.from
                                                        ),
                                                })}
                                                onChange={(time) =>
                                                    handleTimeChange(
                                                        index,
                                                        'to',
                                                        time
                                                    )
                                                }
                                            />
                                            {slot.showEndTimeError && (
                                                <span className="text-red-400">
                                                    End Time is required
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                    {availabilityType !== 'unavailable' && (
                                        <div className="  flex justify-between lg:-mt-3 lg:w-[100%]  lg:flex-row lg:items-center @sm:flex-col">
                                            <p className="text-[13px] font-medium text-[#1A3353] lg:ms-2 lg:w-[65px] @sm:-ms-1.5 @sm:mb-4 @sm:w-[23%]">
                                                Service{' '}
                                                <span className="text-[14px] text-red-400">
                                                    *
                                                </span>{' '}
                                                Category
                                            </p>
                                            <div className="flex w-[80%] flex-col gap-1">
                                                <Select
                                                    variant="borderless"
                                                    style={{
                                                        borderBottom:
                                                            '1px solid #e5e7eb',
                                                        borderRadius: '0px',
                                                    }}
                                                    maxTagCount="responsive"
                                                    className="w-[100%]"
                                                    mode="multiple"
                                                    value={ServiceCategoryOption?.filter(
                                                        (option) =>
                                                            option.payRateId?.some(
                                                                (id: string) =>
                                                                    slot.payRateIds?.includes(
                                                                        id
                                                                    )
                                                            ) // Check if payRateId exists
                                                    ).map(
                                                        (option) => option.value
                                                    )}
                                                    onChange={(
                                                        value: any,
                                                        option: any
                                                    ) =>
                                                        handleServiceCategoryChange(
                                                            index,
                                                            value,
                                                            option
                                                        )
                                                    }
                                                    placeholder="Select Service  Category"
                                                    filterOption={(
                                                        input,
                                                        option
                                                    ) =>
                                                        String(
                                                            option?.label ?? ''
                                                        )
                                                            .toLowerCase()
                                                            .includes(
                                                                input.toLowerCase()
                                                            )
                                                    }
                                                    options={[
                                                        ...(ServiceCategoryOption.length >
                                                        1
                                                            ? [
                                                                  {
                                                                      value: 'all',
                                                                      label: 'All',
                                                                  },
                                                              ]
                                                            : []),
                                                        ...ServiceCategoryOption,
                                                    ]}
                                                />
                                                {slot.showServiceCategoryError && (
                                                    <span className="text-red-400">
                                                        At Least one service
                                                        category is required
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <div className="flex flex-row items-center pt-5">
                                    <div className="lg:w-[20%] @sm:hidden"></div>
                                    <div className="flex justify-end lg:w-[80%]">
                                        {dateRange ===
                                            DateRangeType.MULTIPLE && (
                                            // <Checkbox
                                            //     checked={
                                            //         duplicateStatus[
                                            //             selectedDay
                                            //         ]?.[index] || false
                                            //     }
                                            //     onChange={(e: any) =>
                                            //         handleDuplicateForAllDays(
                                            //             e,
                                            //             index
                                            //         )
                                            //     }
                                            // >
                                            //     Duplicate for all days
                                            // </Checkbox>
                                            // <Button
                                            //     onClick={() =>
                                            //         handleDuplicateForAllDays(
                                            //             {
                                            //                 target: {
                                            //                     checked: true,
                                            //                 },
                                            //             },
                                            //             index
                                            //         )
                                            //     }
                                            //     className="h-12 bg-purpleLight text-xl text-[#ffffff]"
                                            // >
                                            //     Duplicate for all days
                                            // </Button>
                                            <div className="mb-8">
                                                <Switch
                                                    checked={
                                                        duplicateStatus[
                                                            selectedDay
                                                        ]?.[index] || false
                                                    }
                                                    onChange={(checked) =>
                                                        handleDuplicateForAllDays(
                                                            {
                                                                target: {
                                                                    checked,
                                                                },
                                                            },
                                                            index
                                                        )
                                                    }
                                                />
                                                <span className="ml-3 text-[14px] text-[#1A3353]">
                                                    Duplicate for all days
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </>
                    );
                })}
                {/* {!isEdit && (
                    <div className="flex justify-end py-5">
                        <Button
                            className="h-12 border border-[#1A3353] text-xl text-[#1A3353]"
                            icon={<PlusOutlined />}
                            onClick={handleAddTimeSlot}
                        >
                            Add Shift
                        </Button>
                    </div>
                )} */}
            </div>
        </div>
    );
};

export default WeeklySchedule;
