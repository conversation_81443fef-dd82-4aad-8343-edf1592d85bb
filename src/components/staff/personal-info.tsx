import React, { useEffect, useMemo, useState } from 'react';
import { Collapse, Space, Form, Input, Button, Select } from 'antd';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { CityList } from '~/redux/actions/common-action';
import { STAFF_ROLE_DROPDOWN } from '~/types/enums-value';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { useLocation } from 'wouter';
import { GetRolesList } from '~/redux/actions/permission-action';
import { useSelector } from 'react-redux';
import { useEmailMobileValidator } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { getQueryParams } from '~/utils/getQueryParams';

const { Option } = Select;

interface IPersionalInfo {
    onSave?: () => void;
    role?: RoleType;
}

interface IFacility {
    _id: string;
    facilityName: string;
}

const PersonalInfo: React.FC<IPersionalInfo> = ({ onSave, role }) => {
    const [personalForm] = Form.useForm();
    const [addressForm] = Form.useForm();
    const dispatch = useAppDispatch();
    const [_, setLocation] = useLocation();
    const [rolesList, setRolesList] = useState([]);
    const [isEditing, setIsEditing] = useState(false);
    const [loader, startLoader, endLoader] = useLoader();
    const params = getQueryParams();

    const store = useAppSelector((state) => ({
        personalInfo: state.staff_store.personalInfo,
        facilityInfo: state.staff_store.facilityInfo,
        countryList: state.common_store.countryList,
        cityList: state.common_store.cityList,
        facilityList: state.facility_store.facilityList,
        staffOnboarding: state.settings_store.staffOnboarding,
        role: state.auth_store.role,
    }));

    useEffect(() => {
        dispatch(GetRolesList()).then((res: any) => {
            console.log('res', res?.payload?.data?.data);
            setRolesList(res?.payload?.data?.data || []);
        });
    }, []);
    useEffect(() => {
        if (params?.edit) {
            setIsEditing(true);
        }
    }, [params]);
    useEffect(() => {
        if (store.personalInfo) {
            const formattedDateOfBirth = store.personalInfo?.dateOfBirth
                ? new Date(store.personalInfo?.dateOfBirth)
                      .toISOString()
                      .split('T')[0]
                : null;

            personalForm.setFieldsValue({
                firstName: store.personalInfo?.firstName,
                lastName: store.personalInfo?.lastName,
                email: store.personalInfo?.email,
                mobile: store.personalInfo?.mobile,
                gender: store.personalInfo?.gender,
                role: store.personalInfo?.role,
                facilityId: store.facilityInfo?.facilityId,
                dateOfBirth: formattedDateOfBirth,
            });
            addressForm.setFieldsValue({
                street: store.personalInfo?.address?.street,
                stateId: store.personalInfo?.address?.stateId,
                country: store.personalInfo?.address?.country,
            });
        }
    }, [store.personalInfo]);

    useEffect(() => {
        if (store.personalInfo?.address?.stateId) {
            dispatch(
                CityList({
                    stateId: store.personalInfo?.address?.stateId,
                    page: 1,
                    pageSize: 50,
                })
            );
            dispatch(
                CityList({
                    stateId: store.personalInfo?.address?.stateId,
                    cityId: store.personalInfo?.address?.cityId,
                    page: 1,
                    pageSize: 50,
                })
            )
                .unwrap()
                .then((data) => {
                    addressForm.setFieldsValue({
                        cityId: data.data.data[0]._id,
                    });
                });
        }
    }, [store.personalInfo?.address?.stateId]);

    useEffect(() => {
        dispatch(FacilitiesList({})).unwrap();
    }, []);

    const validateEmailAndPhone = useEmailMobileValidator();

    const onChange = (key: string | string[]) => {
        console.log(key);
    };
    const customExpandIcon = (panelProps: any) =>
        panelProps.isActive ? <MinusOutlined /> : <PlusOutlined />;

    const handleConfirm = async () => {
        try {
            startLoader();
            const [personalValues, addressValues] = await Promise.all([
                personalForm.validateFields(),
                addressForm.validateFields(),
            ]);
            const combinedValues = {
                ...personalValues,
                address: addressValues,
            };
            console.log('Combined Values:', combinedValues);
            const res = await onSave(combinedValues);
            setIsEditing(false);
            if (
                res?.payload?.res?.status === 200 ||
                res?.payload?.res?.status === 200
            )
                setLocation('/staffs');
        } catch (error) {
            console.error('Validation Failed:', error);
        } finally {
            endLoader();
        }
    };

    const handleStateChange = (value: string) => {
        dispatch(CityList({ stateId: value, page: 1, pageSize: 50 }));
        addressForm.setFieldsValue({
            cityId: null,
        });
    };

    const CountryOptions = store.countryList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const CityOptions = store.cityList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasStaffUpdatePermission = useMemo(() => {
        if (store.role === RoleType.ORGANIZATION) {
            return true;
        } else {
            return all_permissions_for_role?.some((module: any) =>
                module.subjects?.some(
                    (subject: any) =>
                        subject.type ===
                            SUBJECT_TYPE.AUTHENTICATION_STAFF_ONBOARDING &&
                        subject.actions?.some((action: any) =>
                            action.permissions?.some(
                                (permission: any) =>
                                    permission.type ===
                                    PERMISSIONS_ENUM.STAFF_UPDATE
                            )
                        )
                )
            );
        }
    }, [all_permissions_for_role]);

    const text1 = (
        <>
            {/* Full Name Input  */}

            <Form.Item
                label="First Name"
                name="firstName"
                rules={[
                    {
                        required: true,
                        message: 'Please enter first name',
                    },
                ]}
            >
                <Input placeholder="Enter First Name" />
            </Form.Item>
            <Form.Item label="Last Name" name="lastName">
                <Input placeholder="Enter Last Name" />
            </Form.Item>

            {/* Gender Input */}

            <Form.Item
                label="Gender"
                name="gender"
                rules={[
                    {
                        required: true,
                        message: 'Please select gender',
                    },
                ]}
            >
                <Select placeholder="Select Gender">
                    <Option value="male">Male</Option>
                    <Option value="female">Female</Option>
                    <Option value="other">Other</Option>
                </Select>
            </Form.Item>

            {/* D.O.B Input */}

            <Form.Item
                label="D.O.B"
                name="dateOfBirth"
                rules={[
                    {
                        required: false,
                        message: 'Please select D.O.B',
                    },
                ]}
            >
                <Input type="date" placeholder="Enter your D.O.B" />
            </Form.Item>

            {/* Email Input */}

            <Form.Item
                label="Email"
                name="email"
                rules={[
                    {
                        required: true,
                        message: 'Please input email',
                    },
                    {
                        type: 'email',
                        message: 'The input is not valid E-mail!',
                    },
                    {
                        validator: (_, value) =>
                            value
                                ? validateEmailAndPhone(
                                      value,
                                      true,
                                      store.personalInfo?._id
                                  )
                                : Promise.resolve(),
                    },
                ]}
            >
                <Input type="email" placeholder="Enter Email" />
            </Form.Item>
            <Form.Item
                label="Mobile"
                name="mobile"
                rules={[
                    {
                        required: true,
                        message: 'Mobile number is required',
                    },
                    {
                        pattern: /^\d{10}$/,
                        message: 'Mobile number must be exactly 10 digits',
                    },
                    {
                        validator: (_, value) =>
                            value
                                ? validateEmailAndPhone(
                                      value,
                                      false,
                                      store.personalInfo?._id
                                  )
                                : Promise.resolve(),
                    },
                ]}
            >
                <Input
                    type="text"
                    maxLength={10}
                    onInput={(e: any) => {
                        e.target.value = e.target.value.replace(/[^0-9]/g, '');
                    }}
                    placeholder="Enter Mobile"
                />
            </Form.Item>

            <Form.Item
                label="Role"
                name="role"
                rules={[
                    {
                        required: true,
                        message: 'Please select role',
                    },
                ]}
            >
                <Select placeholder="Select Role">
                    {rolesList.map((role: any) => (
                        <Option key={role._id} value={role._id}>
                            {role.name}
                        </Option>
                    ))}
                </Select>
            </Form.Item>
            <Form.Item
                label="Branch Name"
                name="facilityId"
                rules={[
                    {
                        required: true,
                        message: 'Please select branch name(s)',
                    },
                ]}
            >
                <Select mode="multiple" placeholder="Enter the branch name">
                    {store.facilityList.map((facility: IFacility) => (
                        <Option key={facility._id} value={facility._id}>
                            {facility.facilityName}
                        </Option>
                    ))}
                </Select>
            </Form.Item>
        </>
    );

    const text2 = (
        <>
            {/* Street Input  */}

            <Form.Item
                label="Street"
                name="street"
                rules={[
                    {
                        required: false,
                        message: 'Please enter street',
                    },
                ]}
            >
                <Input placeholder="Enter Street Name" />
            </Form.Item>

            {/* State Input */}

            <Form.Item
                label="State"
                name="stateId"
                rules={[
                    {
                        required: false,
                        message: 'Please select state',
                    },
                ]}
            >
                <Select
                    showSearch
                    placeholder="Select state"
                    filterOption={(input, option) =>
                        String(option?.label ?? '')
                            ?.toLowerCase()
                            .includes(input.toLowerCase())
                    }
                    options={CountryOptions}
                    onChange={handleStateChange}
                />
            </Form.Item>

            {/* City Input  */}

            <Form.Item
                label="City"
                name="cityId"
                rules={[
                    {
                        required: false,
                        message: 'Please enter City',
                    },
                ]}
            >
                <Select
                    showSearch
                    placeholder="Select city"
                    filterOption={(input, option) =>
                        String(option?.label ?? '')
                            .toLowerCase()
                            .includes(input.toLowerCase())
                    }
                    options={CityOptions}
                />
            </Form.Item>

            {/* Country Input */}
            {/* <Form.Item
                label="Country"
                name="country"
                rules={[
                    {
                        required: true,
                        message: 'Please select country',
                    },
                ]}
            >
                <Select placeholder="Select state">
                    <Option value="india">India</Option>
                </Select>
            </Form.Item> */}
        </>
    );

    return (
        <div className="w-full lg:py-10 lg:pr-10 ">
            {hasStaffUpdatePermission && !isEditing && (
                <div
                    className="mb-2 flex justify-end"
                    onClick={() => setIsEditing(true)}
                >
                    <img
                        src="/icons/common/edit.svg"
                        alt="edit"
                        className="ms-auto h-[20px] cursor-pointer"
                    />
                </div>
            )}
            <Space className="w-full" direction="vertical" size="large">
                <Collapse
                    className="custom-collapse client-profile-collapse w-full rounded-2xl"
                    bordered={false}
                    defaultActiveKey={['1', '2']}
                    onChange={onChange}
                    expandIcon={customExpandIcon}
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-primary font-semibold">
                                    Personal Details
                                </div>
                            ),
                            children: (
                                <Form
                                    name="personal-info"
                                    layout="vertical"
                                    size="large"
                                    autoComplete="off"
                                    initialValues={{ gender: 'select' }}
                                    disabled={
                                        !hasStaffUpdatePermission || !isEditing
                                    }
                                    form={personalForm}
                                >
                                    {text1}
                                </Form>
                            ),
                        },
                    ]}
                    // className="custom-collapse w-full rounded-2xl bg-[F2F2F280]"
                    // className="custom-collapse"
                    expandIconPosition="end"
                />
                <Collapse
                    className="custom-collapse client-profile-collapse w-full rounded-2xl"
                    bordered={false}
                    defaultActiveKey={['1', '2']}
                    onChange={onChange}
                    expandIcon={customExpandIcon}
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-primary font-semibold">
                                    Address
                                </div>
                            ),
                            children: (
                                <Form
                                    name="address"
                                    layout="vertical"
                                    size="large"
                                    autoComplete="off"
                                    initialValues={{ gender: 'select' }}
                                    disabled={
                                        !hasStaffUpdatePermission || !isEditing
                                    }
                                    form={addressForm}
                                >
                                    {text2}
                                </Form>
                            ),
                        },
                    ]}
                    // className="custom-collapse w-full bg-[F2F2F280]"
                    // className="custom-collapse"
                    expandIconPosition="end"
                />
            </Space>
            {hasStaffUpdatePermission && isEditing && (
                <div className="flex flex-row justify-end gap-5 @sm:justify-center">
                    <Form.Item>
                        <div className="mt-10" style={{ display: 'flex' }}>
                            <Button
                                className="border-1 border-[#1A3353] py-7 text-xl text-[#1A3353] lg:px-20 @sm:px-10"
                                onClick={() => setIsEditing(false)}
                            >
                                Cancel
                            </Button>
                        </div>
                    </Form.Item>
                    <Form.Item>
                        <div
                            className="mt-10"
                            style={{ display: 'flex', gap: '10px' }}
                        >
                            <Button
                                loading={loader}
                                className="bg-purpleLight py-7  text-xl lg:px-20 @sm:px-6"
                                type="primary"
                                onClick={handleConfirm}
                            >
                                Confirm
                            </Button>
                        </div>
                    </Form.Item>
                </div>
            )}
        </div>
    );
};

export default PersonalInfo;
