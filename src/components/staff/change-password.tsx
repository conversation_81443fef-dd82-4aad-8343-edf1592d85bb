import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Collapse, Form, Input, Space } from 'antd';
import { useState } from 'react';
import PasswordChecklist from '../common/password-checklist';
import { useAppDispatch } from '~/hooks/redux-hooks';

const ChangePassword = ({ onSave }) => {
    const dispatch = useAppDispatch();
    const [form] = Form.useForm();
    const onChange = (key: string | string[]) => {
        console.log(key);
    };
    const [password, setPassword] = useState('');
    const customExpandIcon = (panelProps: any) =>
        panelProps.isActive ? <MinusOutlined /> : <PlusOutlined />;

    async function savePassword() {
        await form.validateFields();
        const fields = form.getFieldsValue();
        onSave(fields);
    }
    return (
        <div className="w-full  lg:py-10 lg:pr-10">
            <Space direction="vertical" size="large">
                <Collapse
                    className="custom-collapse client-profile-collapse w-full rounded-2xl bg-[#F2F2F280]"
                    bordered={false}
                    defaultActiveKey={['1', '2']}
                    onChange={onChange}
                    expandIcon={customExpandIcon}
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-[#1A3353] font-semibold">
                                    Reset Password
                                </div>
                            ),
                            children: (
                                <Form
                                    name="change-password"
                                    layout="vertical"
                                    size="large"
                                    autoComplete="off"
                                    initialValues={{ gender: 'select' }}
                                    form={form}
                                >
                                    {/* Full Name Input  */}

                                    <Form.Item
                                        label="Old Password"
                                        name="oldPassword"
                                        rules={[
                                            {
                                                required: true,
                                                // message: 'Please input password',
                                            },
                                        ]}
                                    >
                                        <Input.Password
                                            placeholder="Enter Old Password"
                                            value={password}
                                            onChange={(e) =>
                                                setPassword(e.target.value)
                                            }
                                        />
                                    </Form.Item>

                                    <Form.Item
                                        label="Set New Password"
                                        name="newPassword"
                                        rules={[
                                            {
                                                required: true,
                                                // message: 'Please input password',
                                            },
                                        ]}
                                    >
                                        <Input.Password
                                            placeholder="Enter New Password"
                                            value={password}
                                            onChange={(e) =>
                                                setPassword(e.target.value)
                                            }
                                        />
                                    </Form.Item>

                                    <Form.Item
                                        label="Confirm Password"
                                        name="confirmPassword"
                                        rules={[
                                            {
                                                required: true,
                                                // message: 'Please input password',
                                            },
                                        ]}
                                    >
                                        <Input.Password
                                            placeholder="Confirm Password"
                                            value={password}
                                            onChange={(e) =>
                                                setPassword(e.target.value)
                                            }
                                        />
                                    </Form.Item>
                                    <PasswordChecklist password={password} />
                                </Form>
                            ),
                        },
                    ]}
                    className="custom-collapse rounded-2xl bg-[#F2F2F280] lg:w-[60vw]"
                    expandIconPosition="right"
                />
            </Space>

            <div className="flex flex-row justify-end gap-5 lg:w-[60vw] @sm:justify-center">
                <Form.Item>
                    <div className="mt-10" style={{ display: 'flex' }}>
                        <Button className="border-1 border-[#1A3353] py-7 text-xl text-[#1A3353] lg:px-20 @sm:px-10">
                            Cancel
                        </Button>
                    </div>
                </Form.Item>
                <Form.Item>
                    <div
                        className="mt-10"
                        style={{ display: 'flex', gap: '10px' }}
                    >
                        <Button
                            className="bg-purpleLight py-7 text-xl text-white lg:px-20 @sm:px-10"
                            // type="primary"
                        >
                            Confirm
                        </Button>
                    </div>
                </Form.Item>
            </div>
        </div>
    );
};

export default ChangePassword;
