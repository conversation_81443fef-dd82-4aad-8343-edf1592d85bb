import React, { useState, useRef } from 'react';
import {
    MinusOutlined,
    PlusOutlined,
    EyeOutlined,
    EyeInvisibleOutlined,
} from '@ant-design/icons';
import { Button, Collapse, Space } from 'antd';
import { SetStaffPin } from '~/redux/actions/staff-action';
import { useAppDispatch } from '~/hooks/redux-hooks';

const ChangePin = ({ userId }: any) => {
    const [loading, setLoading] = useState(false);
    const dispatch = useAppDispatch();
    const [pinDigits, setPinDigits] = useState(Array(4).fill(''));
    const [confirmPinDigits, setConfirmPinDigits] = useState(Array(4).fill(''));
    const [error, setError] = useState('');
    const [pinVisible, setPinVisible] = useState(false);
    const [confirmPinVisible, setConfirmPinVisible] = useState(false);

    const pinRefs = useRef<(HTMLInputElement | null)[]>([]);
    const confirmPinRefs = useRef<(HTMLInputElement | null)[]>([]);

    const getPinString = (digits: string[]) => digits.join('');

    const onClose = () => {
        setPinDigits(Array(4).fill(''));
        setConfirmPinDigits(Array(4).fill(''));
        setError('');
        setLoading(false);
    };

    const handleDigitChange = (
        e: React.ChangeEvent<HTMLInputElement>,
        index: number,
        isConfirm: boolean = false
    ) => {
        const val = e.target.value;
        if (!/^\d?$/.test(val)) return;

        if (isConfirm) {
            const newDigits = [...confirmPinDigits];
            newDigits[index] = val;
            setConfirmPinDigits(newDigits);
            if (val && index < 5) {
                confirmPinRefs.current[index + 1]?.focus();
            }
        } else {
            const newDigits = [...pinDigits];
            newDigits[index] = val;
            setPinDigits(newDigits);
            if (val && index < 5) {
                pinRefs.current[index + 1]?.focus();
            }
        }
    };

    const handleKeyDown = (
        e: React.KeyboardEvent<HTMLInputElement>,
        index: number,
        isConfirm: boolean = false
    ) => {
        if (e.key === 'Backspace') {
            e.preventDefault();

            if (isConfirm) {
                const newDigits = [...confirmPinDigits];
                if (newDigits[index]) {
                    newDigits[index] = '';
                    setConfirmPinDigits(newDigits);
                } else if (index > 0) {
                    confirmPinRefs.current[index - 1]?.focus();
                    const prevDigits = [...confirmPinDigits];
                    prevDigits[index - 1] = '';
                    setConfirmPinDigits(prevDigits);
                }
            } else {
                const newDigits = [...pinDigits];
                if (newDigits[index]) {
                    newDigits[index] = '';
                    setPinDigits(newDigits);
                } else if (index > 0) {
                    pinRefs.current[index - 1]?.focus();
                    const prevDigits = [...pinDigits];
                    prevDigits[index - 1] = '';
                    setPinDigits(prevDigits);
                }
            }
        }
    };

    const handlePaste = (
        e: React.ClipboardEvent<HTMLInputElement>,
        isConfirm: boolean = false
    ) => {
        e.preventDefault();
        const pasted = e.clipboardData
            .getData('Text')
            .replace(/\D/g, '')
            .slice(0, 4);

        if (isConfirm) {
            const newDigits = pasted.split('');
            while (newDigits.length < 4) newDigits.push('');
            setConfirmPinDigits(newDigits);
            confirmPinRefs.current[Math.min(pasted.length, 5)]?.focus();
        } else {
            const newDigits = pasted.split('');
            while (newDigits.length < 4) newDigits.push('');
            setPinDigits(newDigits);
            pinRefs.current[Math.min(pasted.length, 5)]?.focus();
        }
    };

    const handleSubmit = async () => {
        const pin = getPinString(pinDigits);
        const confirmPin = getPinString(confirmPinDigits);

        if (pin.length !== 4 || confirmPin.length !== 4) {
            setError('Please enter a 4-digit PIN in both fields.');
            return;
        }
        if (pin !== confirmPin) {
            setError('Pins do not match.');
            return;
        }

        setError('');
        setLoading(true);
        await dispatch(SetStaffPin({ pin, confirmPin, userId }));
        onClose();
    };

    const customExpandIcon = (panelProps: any) =>
        panelProps.isActive ? <MinusOutlined /> : <PlusOutlined />;

    return (
        <div className="w-full lg:py-10 lg:pr-10">
            <Space direction="vertical" size="large">
                <Collapse
                    className="custom-collapse client-profile-collapse w-full rounded-2xl bg-[#F2F2F280] lg:w-[60vw]"
                    bordered={false}
                    defaultActiveKey={['1']}
                    expandIcon={customExpandIcon}
                    expandIconPosition="right"
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-[#1A3353] font-semibold">
                                    Set Pin
                                </div>
                            ),
                            children: (
                                <div className="flex w-full flex-col gap-5 lg:w-[30%]">
                                    <div>
                                        <label className="mb-1 block text-base font-medium text-[#1A3353]">
                                            Enter PIN
                                        </label>
                                        <div className="flex gap-2">
                                            {pinDigits.map((digit, idx) => (
                                                <input
                                                    key={idx}
                                                    ref={(el) =>
                                                        (pinRefs.current[idx] =
                                                            el)
                                                    }
                                                    type={
                                                        pinVisible
                                                            ? 'text'
                                                            : 'password'
                                                    }
                                                    maxLength={1}
                                                    inputMode="numeric"
                                                    className="h-12 w-10 rounded border border-gray-300 text-center text-xl"
                                                    value={digit}
                                                    onChange={(e) =>
                                                        handleDigitChange(
                                                            e,
                                                            idx,
                                                            false
                                                        )
                                                    }
                                                    onKeyDown={(e) =>
                                                        handleKeyDown(
                                                            e,
                                                            idx,
                                                            false
                                                        )
                                                    }
                                                    onPaste={(e) =>
                                                        handlePaste(e, false)
                                                    }
                                                    autoComplete="off"
                                                />
                                            ))}
                                            <Button
                                                type="text"
                                                size="small"
                                                icon={
                                                    pinVisible ? (
                                                        <EyeInvisibleOutlined />
                                                    ) : (
                                                        <EyeOutlined />
                                                    )
                                                }
                                                onClick={() =>
                                                    setPinVisible(!pinVisible)
                                                }
                                            />
                                        </div>
                                    </div>

                                    {/* Confirm PIN */}
                                    <div>
                                        <label className="mb-1 block text-base font-medium text-[#1A3353]">
                                            Confirm PIN
                                        </label>
                                        <div className="flex gap-2">
                                            {confirmPinDigits.map(
                                                (digit, idx) => (
                                                    <input
                                                        key={idx}
                                                        ref={(el) =>
                                                            (confirmPinRefs.current[
                                                                idx
                                                            ] = el)
                                                        }
                                                        type={
                                                            confirmPinVisible
                                                                ? 'text'
                                                                : 'password'
                                                        }
                                                        maxLength={1}
                                                        inputMode="numeric"
                                                        className="h-12 w-10 rounded border border-gray-300 text-center text-xl"
                                                        value={digit}
                                                        onChange={(e) =>
                                                            handleDigitChange(
                                                                e,
                                                                idx,
                                                                true
                                                            )
                                                        }
                                                        onKeyDown={(e) =>
                                                            handleKeyDown(
                                                                e,
                                                                idx,
                                                                true
                                                            )
                                                        }
                                                        onPaste={(e) =>
                                                            handlePaste(e, true)
                                                        }
                                                        autoComplete="off"
                                                    />
                                                )
                                            )}
                                            <Button
                                                type="text"
                                                size="small"
                                                icon={
                                                    confirmPinVisible ? (
                                                        <EyeInvisibleOutlined />
                                                    ) : (
                                                        <EyeOutlined />
                                                    )
                                                }
                                                onClick={() =>
                                                    setConfirmPinVisible(
                                                        !confirmPinVisible
                                                    )
                                                }
                                            />
                                        </div>
                                    </div>

                                    {error && (
                                        <span className="text-red-500">
                                            {error}
                                        </span>
                                    )}
                                </div>
                            ),
                        },
                    ]}
                />
            </Space>

            <div className="flex flex-row justify-end gap-5 lg:w-[60vw] @sm:justify-center">
                <div className="mt-10 flex">
                    <Button
                        className="border-1 border-[#1A3353] py-7 text-xl text-[#1A3353] lg:px-20 @sm:px-10"
                        onClick={onClose}
                    >
                        Cancel
                    </Button>
                </div>
                <div className="mt-10 flex gap-2">
                    <Button
                        className="bg-purpleLight py-7 text-xl lg:px-20 @sm:px-10"
                        type="primary"
                        loading={loading}
                        onClick={handleSubmit}
                    >
                        Confirm
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default ChangePin;
