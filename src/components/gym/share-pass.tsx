import { Button, ConfigProvider, Pagination, Table } from 'antd';
import { useEffect, useState } from 'react';
import FullLoader from '~/components/library/loader/full-loader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { SharedPassList } from '~/redux/actions/customer-action';
import SharePassModal from '~/screens/customers/share-pass-modal';
import Alertify from '~/services/alertify';
import { getQueryParams } from '~/utils/getQueryParams';

const columns: any = [
    {
        title: 'Client ID',
        dataIndex: 'clientID',
    },
    {
        title: 'Shared To',
        dataIndex: 'sharedTo',
    },
    {
        title: 'Package Name',
        dataIndex: 'packageName',
    },
    {
        title: 'No. of Sessions Transferred',
        dataIndex: 'transferredSessions',
        align: 'center',
    },
    {
        title: 'Location',
        dataIndex: 'location',
    },
    { title: 'Date', dataIndex: 'date' },
    { title: 'Time', dataIndex: 'time' },
];

const SharePass = () => {
    const [isSharePassModalVisible, setIsSharePassModalVisible] =
        useState<boolean>(false);
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const params = getQueryParams();
    const userId = params.userId;
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    const store = useAppSelector((state) => ({
        clientDetails: state.customer_store.customerDetails,
        sharedPassList: state.customer_store.sharedPassList,
        sharedPassListCount: state.customer_store.sharedPassListCount,
        clientOnboarding: state.settings_store.clientOnboarding,
    }));

    useEffect(() => {
        startLoader();
        if (userId) {
            dispatch(
                SharedPassList({
                    userId,
                    page: currentPage,
                    pageSize: pageSizes,
                })
            )
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [currentPage, pageSizes]);

    const dataSource = store.sharedPassList?.map((pass: any, i) => ({
        key: i,
        clientID: pass.clientId,
        sharedTo: pass.sharedTo,
        packageName: pass.packageName,
        transferredSessions: pass.transferredSessions,
        location: pass.cityName,
        date: pass.date,
        time: pass.time,
    }));

    const handleSharePassClose = (isSubmit = false) => {
        setIsSharePassModalVisible(false);
        if (isSubmit)
            dispatch(
                SharedPassList({
                    userId,
                    page: currentPage,
                    pageSize: pageSizes,
                })
            ).unwrap();
    };

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
    }

    return (
        <div className="pt-20">
            <div className="lg:w-[100%] lg:rounded-3xl lg:bg-[#f8f8f8f8] lg:px-10 lg:pb-10 lg:pt-8 @sm:p-4">
                <div className="flex flex-row items-center justify-between pb-10">
                    <div className="w-fit border-b-2 border-primary text-[15px] font-semibold text-[#1a3353]">
                        Shared Pass History
                    </div>
                    <Button
                        onClick={() => {
                            if (!store.clientDetails.isActive) {
                                Alertify.error(
                                    "Client is deactive, can't share pass."
                                );
                            } else {
                                setIsSharePassModalVisible(true);
                            }
                        }}
                        className=" fw-500 flex h-14 w-[100px] items-center   rounded-2xl  
                                     bg-purpleLight text-xl text-white "
                    >
                        + Share Pass
                    </Button>
                </div>
                {loader ? (
                    <FullLoader state={true} />
                ) : (
                    <>
                        <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                            <ConfigProvider
                                theme={{
                                    components: {
                                        Table: {
                                            borderColor: '#0000001A',
                                            cellFontSize: 13,
                                            headerBg: '#fff',
                                            headerColor: '#1A3353',
                                            colorText: '#455560',
                                        },
                                    },
                                }}
                            >
                                <Table
                                    id=""
                                    className="m-2 rounded-[6px] border-1 "
                                    columns={columns}
                                    dataSource={dataSource}
                                    pagination={false}
                                />
                            </ConfigProvider>
                        </div>
                        <div className="flex justify-center py-10">
                            <Pagination
                                current={currentPage}
                                total={store.sharedPassListCount}
                                pageSize={pageSizes}
                                onChange={paginate}
                            />
                        </div>
                    </>
                )}
            </div>
            {isSharePassModalVisible && (
                <SharePassModal
                    visible={isSharePassModalVisible}
                    onClose={handleSharePassClose}
                    clientId={userId}
                    clientName={`${store.clientDetails?.firstName} ${store.clientDetails?.lastName}`}
                />
            )}
        </div>
    );
};

export default SharePass;
