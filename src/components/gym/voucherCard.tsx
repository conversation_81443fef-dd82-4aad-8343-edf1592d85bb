import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>ton, Collapse, Pagination } from 'antd';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { clientVoucherList } from '~/redux/actions/coupon.action';
import { formatDate } from '../common/function';
import { getQueryParams } from '~/utils/getQueryParams';
import { navigate } from 'wouter/use-location';
import { PendingButtonChip } from '../common/chip-component';

interface Voucher {
    clientId?: string;
}

const VoucherCard: React.FC<Voucher> = ({ clientId }) => {
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const topRef = useRef<HTMLDivElement>(null);
    const params = getQueryParams();
    const [voucherList, setVoucherList] = useState<any>([]);
    const [pagination, setPagination] = useState<any>({});
    const [inactiveVoucherList, setInactiveVoucherList] = useState<any>([]);
    const [inactivePagination, setInavtivePagination] = useState<any>({});
    const activePageParam = Number(params.activePage);
    const activePageSizeParam = Number(params.activePageSize);
    const inActivePageParam = Number(params.inActivePage);
    const inActivePageSizeParam = Number(params.inActivePageSize);

    const [activeCurrentPage, setActiveCurrentPage] = useState(
        !isNaN(activePageParam) ? activePageParam : 1
    );
    const [inActiveCurrentPage, setInActiveCurrentPage] = useState(
        !isNaN(inActivePageParam) ? inActivePageParam : 1
    );
    const [activePageSize, setActivePageSize] = useState(
        !isNaN(activePageSizeParam) ? activePageSizeParam : 10
    );
    const [inActivePageSize, setInActivePageSize] = useState(
        !isNaN(inActivePageSizeParam) ? inActivePageSizeParam : 10
    );

    useEffect(() => {
        startLoader();
        if (clientId) {
            const payload = {
                page: activeCurrentPage,
                pageSize: activePageSize,
                isActive: true,
            };
            dispatch(clientVoucherList({ userId: clientId, payload }))
                .unwrap()
                .then((res: any) => {
                    console.log('Res--------------', res);
                    setVoucherList(res?.data?.data);
                    setPagination(res?.data?._metadata?.pagination);
                })
                .finally(endLoader);
        }
    }, [clientId, activeCurrentPage, activePageSize]);

    useEffect(() => {
        startLoader();
        if (clientId) {
            const inactivePayload = {
                page: inActiveCurrentPage,
                pageSize: inActivePageSize,
                isActive: false,
            };
            dispatch(
                clientVoucherList({
                    userId: clientId,
                    payload: inactivePayload,
                })
            )
                .unwrap()
                .then((res: any) => {
                    setInactiveVoucherList(res?.data?.data);
                    setInavtivePagination(res?.data?._metadata?.pagination);
                })
                .finally(endLoader);
        }
    }, [clientId, inActiveCurrentPage, inActivePageSize]);

    function paginate(page: number, pageSize: number, isActive: boolean) {
        if (isActive) {
            setActiveCurrentPage(page);
            setActivePageSize(pageSize);
        } else {
            setInActiveCurrentPage(page);
            setInActivePageSize(pageSize);
        }
        topRef.current?.scrollIntoView({ behavior: 'smooth' });
        navigate(
            `?activePage=${
                isActive ? page : activeCurrentPage
            }&activePageSize=${
                isActive ? pageSize : activePageSize
            }&inActivePage=${
                !isActive ? page : inActiveCurrentPage
            }&inActivePageSize=${!isActive ? pageSize : inActivePageSize}`,
            { replace: true }
        );
    }

    console.log('Voucher-------------------', voucherList);

    const renderVoucherCard = (v: any) => {
        const isPending = String(v?.paymentStatus).toLowerCase() === 'pending';

        const goToOrderHistory = () => {
            if (v?.invoiceId) {
                navigate(`/order-detail?orderId=${v?.invoiceId || ''}`);
            }
        };
        return (
            <div className="mt-4 rounded-2xl border bg-white px-6 py-6">
                <div className="flex flex-row   ">
                    {/* First Column - 3 items */}
                    <div className="flex  w-1/3 flex-col gap-4 ">
                        <div className="flex flex-row items-center  gap-5  ">
                            <div className="flex w-1/2 flex-row items-center justify-between">
                                <p className="   text-xl font-medium text-[#1A3353]">
                                    Name
                                </p>
                                <p>:</p>
                            </div>
                            <p className=" text-xl text-[#455560]">{v.name}</p>
                        </div>
                        <div className="flex  flex-row items-center  gap-5  ">
                            <div className="flex w-1/2 flex-row items-center justify-between">
                                <p className="  text-xl  font-medium text-[#1A3353]">
                                    Purchase Date
                                </p>
                                <p>:</p>
                            </div>
                            <p className=" text-xl text-[#455560]">
                                {formatDate(v.purchaseDate)}
                            </p>
                        </div>
                        <div className="flex flex-row items-center  gap-5 ">
                            <div className="flex w-1/2 flex-row items-center justify-between">
                                <p className="  text-xl  font-medium text-[#1A3353]">
                                    Expiry
                                </p>
                                <p>:</p>
                            </div>
                            <p className=" text-xl text-[#455560]">
                                {formatDate(v.endDate)}
                            </p>
                        </div>
                    </div>

                    {/* Second Column - 2 items */}
                    <div className="flex w-1/3 flex-col gap-4">
                        <div className="flex  flex-row items-center  gap-5 ">
                            <div className="flex w-1/2 flex-row items-center justify-between">
                                <p className="  text-xl  font-medium text-[#1A3353]">
                                    Price
                                </p>
                                <p>:</p>
                            </div>
                            <p className=" text-xl text-[#455560]">{v.price}</p>
                        </div>
                        {isPending ? (
                            <div className="flex  flex-row items-center  gap-5 ">
                                <div className="flex w-1/2 flex-row items-center justify-between">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Payment Status
                                    </p>
                                    <p>:</p>
                                </div>
                                <PendingButtonChip />
                            </div>
                        ) : (
                            <div className="flex  flex-row items-center  gap-5 ">
                                <div className="flex w-1/2 flex-row items-center justify-between">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Voucher Code
                                    </p>
                                    <p>:</p>
                                </div>
                                <p className=" text-xl text-[#455560]">
                                    {v.voucherCode}
                                </p>
                            </div>
                        )}
                    </div>

                    {/* Third Column - QR Code */}

                    <div className="flex w-1/3 items-center justify-center ">
                        {isPending ? (
                            <div className="flex h-full  items-center justify-center ">
                                <Button
                                    onClick={goToOrderHistory}
                                    className="bg-purpleLight px-20 py-7 text-2xl text-white"
                                    htmlType="submit"
                                >
                                    Order History
                                </Button>
                            </div>
                        ) : (
                            <div className="flex w-1/3 flex-col items-center justify-between">
                                <img
                                    src={v.qrCodeUrl}
                                    alt={`QR for ${v.code}`}
                                    className="h-56 w-56 rounded border border-slate-300"
                                    loading="lazy"
                                />
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="w-full lg:w-[100%] lg:py-10 lg:pr-10">
            <div ref={topRef} className="flex flex-col gap-5">
                {/* Active Vouchers */}
                <Collapse
                    className="custom-collapse client-profile-collapse mt-10 w-full rounded-2xl"
                    bordered={false}
                    defaultActiveKey={['1']}
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-primary text-2xl font-semibold">
                                    Active Vouchers
                                </div>
                            ),
                            children:
                                voucherList?.length > 0 ? (
                                    <>
                                        <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                            {voucherList?.map((v: any) => (
                                                <div key={v.id}>
                                                    {renderVoucherCard(v)}
                                                </div>
                                            ))}
                                        </div>
                                        <div className="flex justify-center py-10">
                                            <Pagination
                                                current={activeCurrentPage}
                                                total={pagination?.total}
                                                pageSize={activePageSize}
                                                onChange={(page, pageSize) =>
                                                    paginate(
                                                        page,
                                                        pageSize,
                                                        true
                                                    )
                                                }
                                                // hideOnSinglePage
                                            />
                                        </div>
                                    </>
                                ) : (
                                    <div className="rounded-2xl bg-[#f5f5f5] py-20 text-center text-xl text-[#455560]">
                                        No active vouchers
                                    </div>
                                ),
                        },
                    ]}
                    expandIconPosition="right"
                />

                <Collapse
                    className="custom-collapse client-profile-collapse mt-10 w-full rounded-2xl"
                    bordered={false}
                    defaultActiveKey={['']}
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-primary text-2xl font-semibold">
                                    Inactive Vouchers
                                </div>
                            ),
                            children:
                                inactiveVoucherList?.length > 0 ? (
                                    <>
                                        <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                            {inactiveVoucherList?.map(
                                                (v: any) => (
                                                    <div key={v.id}>
                                                        {renderVoucherCard(v)}
                                                    </div>
                                                )
                                            )}
                                        </div>
                                        <div className="flex justify-center py-10">
                                            <Pagination
                                                current={inActiveCurrentPage}
                                                total={
                                                    inactivePagination?.total
                                                }
                                                pageSize={inActivePageSize}
                                                onChange={(page, pageSize) =>
                                                    paginate(
                                                        page,
                                                        pageSize,
                                                        false
                                                    )
                                                }
                                                // hideOnSinglePage
                                            />
                                        </div>
                                    </>
                                ) : (
                                    <div className="rounded-2xl bg-[#f5f5f5] py-20 text-center text-xl text-[#455560]">
                                        No inactive vouchers
                                    </div>
                                ),
                        },
                    ]}
                    expandIconPosition="right"
                />
            </div>
        </div>
    );
};

export default VoucherCard;
