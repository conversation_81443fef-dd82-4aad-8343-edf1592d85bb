import React, { useState } from 'react';
import { Collapse, Space, Form, Input, Button, Select } from 'antd';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { ClientsDetails, UpdateClient } from '~/redux/actions/customer-action';
import { useLoader } from '~/hooks/useLoader';
const { TextArea } = Input;

const Notes = ({ clientDetails, clientId }: any) => {
    const dispatch = useAppDispatch();
    const [loader, showLoader, hideLoader] = useLoader();
    const [notes, setNotes] = useState<any>(clientDetails?.notes || '');

    console.log('clientDetails------------', clientDetails);
    const onChange = (key: string | string[]) => {
        console.log(key);
    };
    const customExpandIcon = (panelProps: any) =>
        panelProps.isActive ? <MinusOutlined /> : <PlusOutlined />;

    const handleSave = () => {
        showLoader();
        const payload = {
            firstName: clientDetails?.firstName,
            lastName: clientDetails?.lastName,
            dob: clientDetails?.dob,
            gender: clientDetails?.gender,
            activityLevel: clientDetails?.activityLevel,
            mobile: clientDetails?.mobile,
            email: clientDetails?.email ? clientDetails?.email : undefined,
            countryCode: clientDetails?.countryCode,
            address: {
                addressLine1: clientDetails?.addressLine1,
                addressLine2: clientDetails?.addressLine2,
                postalCode: clientDetails?.postalCode
                    ? Number(clientDetails?.postalCode)
                    : null,
                city: clientDetails?.city,
                state: clientDetails?.state,
                country: 'India',
                isDefault: clientDetails?.isPrimaryAddress,
            },
            businessAddress: {
                gstNumber: clientDetails?.B2bGST,
                businessName: clientDetails?.B2bBusinessName,
                addressLine1: clientDetails?.B2bAddress1,
                addressLine2: clientDetails?.B2bAddress2,
                city: clientDetails?.B2bcity,
                state: clientDetails?.B2bstate,
                postalCode: clientDetails?.B2bpostalCode
                    ? Number(clientDetails?.B2bpostalCode)
                    : null,
                isDefault: clientDetails?.isPrimaryB2BAddress,
            },
            isBusiness: clientDetails?.isBusiness,
            emergencyContactPerson: clientDetails?.emergencyContactPerson,
            emergencyContactPhone: clientDetails?.emergencyContactPhone,
            policies: clientDetails?.policies,
            facilityId: clientDetails?.facilityId,
            photo: clientDetails?.photo,
            notes: notes,
        };

        console.log('Payload-------------', payload);

        dispatch(UpdateClient({ reqData: payload, clientId: clientId }))
            .then((res: any) => {
                if (
                    res?.payload?.status === 200 ||
                    res?.payload?.status === 201
                ) {
                    dispatch(ClientsDetails({ clientId: clientId }));
                }
            })
            .catch((error: any) => {
                console.error('Error updating client:', error);
                hideLoader();
            })
            .finally(() => {
                hideLoader();
            });
    };

    return (
        <div className="w-full  lg:py-20 lg:pr-10">
            <Space className="w-full " direction="vertical" size="large">
                <Collapse
                    className="custom-collapse client-profile-collapse w-full rounded-2xl"
                    bordered={false}
                    defaultActiveKey={['1', '2']}
                    onChange={onChange}
                    expandIcon={customExpandIcon}
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-primary font-semibold">
                                    Messages
                                </div>
                            ),
                            children: (
                                <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                    <div className="py-5">
                                        <TextArea
                                            showCount
                                            maxLength={100}
                                            placeholder="Enter a Note"
                                            style={{
                                                height: 120,
                                                resize: 'none',
                                            }}
                                            onChange={(e) =>
                                                setNotes(e.target.value)
                                            }
                                            value={notes}
                                        />
                                    </div>
                                </div>
                            ),
                        },
                    ]}
                    // className="custom-collapse"
                    expandIconPosition="right"
                />
            </Space>

            <div className="flex flex-row justify-end gap-5">
                {/* <Form.Item>
                    <div className="mt-10" style={{ display: 'flex' }}>
                        <Button
                            className="border-1 border-[#1A3353] px-20 py-7 text-2xl"
                            htmlType="submit"
                        >
                            Cancel
                        </Button>
                    </div>
                </Form.Item> */}
                <Form.Item>
                    <div
                        className="mt-10"
                        style={{ display: 'flex', gap: '10px' }}
                    >
                        <Button
                            onClick={handleSave}
                            className="bg-purpleLight px-20 py-7 text-2xl"
                            type="primary"
                            htmlType="submit"
                            loading={loader}
                        >
                            Save
                        </Button>
                    </div>
                </Form.Item>
            </div>
        </div>
    );
};

export default Notes;
