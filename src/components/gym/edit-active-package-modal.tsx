import { Button, DatePicker, Form, Input, Modal } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useEffect, useCallback, useState } from 'react';
import {
    ActivePurchasePricingPackagesByClient,
    InActivePurchasePricingPackagesByClient,
    UpdatePurchaseData,
} from '~/redux/actions/pricing-actions';

const EditActivePackageModal = ({
    isModalVisible,
    handleCancel,
    purchaseData,
    isFullAccess,
    accessUserId, //use later
}: {
    isModalVisible: boolean;
    handleCancel: () => void;
    purchaseData: any;
    isFullAccess: boolean;
    accessUserId: string | undefined;
}) => {
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const clientDetails = useAppSelector(
        (state) => state.customer_store.customerDetails
    );

    useEffect(() => {
        if (purchaseData && clientDetails) {
            const {
                packageName,
                startDate,
                expiryDate,
                totalSessions,
                sessionConsumed,
                sessionType,
                dayPassLimit,
            } = purchaseData;

            form.setFieldsValue({
                clientName: `${clientDetails?.firstName} ${clientDetails?.lastName}`,
                packageName,
                startDate: dayjs(startDate).utc().startOf('day'),
                endDate: dayjs(expiryDate).utc().startOf('day'),
                addedSessions: 0,
                remainingSessions:
                    (sessionType === 'day_pass'
                        ? dayPassLimit
                        : totalSessions) - sessionConsumed,
            });
        }
    }, [purchaseData, clientDetails, form]);

    const handleStartDateChange = useCallback(
        (newStartDate: Dayjs | null) => {
            if (
                !newStartDate ||
                !purchaseData?.startDate ||
                !purchaseData?.expiryDate
            )
                return;

            const originalStartDate = dayjs(purchaseData.startDate)
                .utc()
                .startOf('day');
            const originalEndDate = dayjs(purchaseData.expiryDate)
                .utc()
                .startOf('day');

            const diffDays = newStartDate.diff(originalStartDate, 'day');
            const newEndDate = originalEndDate.add(diffDays, 'day');

            form.setFieldsValue({ endDate: newEndDate });
        },
        [form, purchaseData]
    );

    const handleSessionCount = useCallback(
        (type: 'increment' | 'decrement') => {
            if (
                !['multiple', 'day_pass', 'single'].includes(purchaseData?.sessionType) ||
                !isFullAccess
            )
                return;

            const values = form.getFieldsValue();
            const currentAdded = Number(values.addedSessions);
            const currentRemaining = Number(values.remainingSessions);

            if (type === 'increment' && (purchaseData.sessionType !== 'single' || currentRemaining === 0)) {
                form.setFieldsValue({
                    addedSessions: currentAdded + 1,
                    remainingSessions: currentRemaining + 1,
                });
            } else if (type === 'decrement' && currentRemaining > 0) {
                form.setFieldsValue({
                    addedSessions: currentAdded - 1,
                    remainingSessions: currentRemaining - 1,
                });
            }
        },
        [form, purchaseData]
    );

    const onFinish = async (values: any) => {
        try {
            setIsSubmitting(true);
            const { _id: purchaseId, userId } = purchaseData;

            await dispatch(
                UpdatePurchaseData({
                    purchaseId,
                    startDate: dayjs(values.startDate)
                        .startOf('day')
                        .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                    endDate: dayjs(values.endDate)
                        .startOf('day')
                        .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                    addedSessions: values.addedSessions,
                })
            );

            await dispatch(
                ActivePurchasePricingPackagesByClient({
                    userId,
                    page: 1,
                    pageSize: 10,
                })
            );
            await dispatch(
                InActivePurchasePricingPackagesByClient({
                    userId,
                    page: 1,
                    pageSize: 10,
                })
            );
            handleCancel();
        } catch (error) {
            console.error('Failed to update purchase:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Modal
            title="Edit Package"
            open={isModalVisible}
            onCancel={handleCancel}
            footer={false}
            centered
        >
            <Form
                layout="horizontal"
                className="pt-5"
                form={form}
                onFinish={onFinish}
            >
                {/* <div className="mb-8 flex w-full items-center">
                    <p className="m-0 w-[25%] text-start text-[13px] font-semibold text-[#1a3353]">
                        Client Name
                    </p>
                    <Form.Item name="clientName" className="mb-0 w-[75%]">
                        <Input disabled className="w-full" />
                    </Form.Item>
                </div> */}
                <div className="mb-8 flex w-full items-center">
                    <p className="m-0 w-[25%] text-start text-[13px] font-semibold text-[#1a3353]">
                        Package Name
                    </p>
                    <Form.Item name="packageName" className="mb-0 w-[75%]">
                        <Input disabled className="w-full" />
                    </Form.Item>
                </div>
                <div className="mb-8 flex w-full items-center">
                    <p className="m-0 w-[25%] text-start text-[13px] font-semibold text-[#1a3353]">
                        Start Date <span className="text-red-500">*</span>
                    </p>
                    <Form.Item
                        name="startDate"
                        className="mb-0 w-[75%]"
                        rules={[
                            {
                                required: true,
                                message: 'Start date is required',
                            },
                        ]}
                    >
                        <DatePicker
                            className="w-full"
                            format="DD/MM/YYYY"
                            onChange={handleStartDateChange}
                        />
                    </Form.Item>
                </div>
                <div className="mb-8 flex w-full items-center">
                    <p className="m-0 w-[25%] text-start text-[13px] font-semibold text-[#1a3353]">
                        End Date <span className="text-red-500">*</span>
                    </p>
                    <Form.Item
                        name="endDate"
                        className="mb-0 w-[75%]"
                        rules={[
                            { required: true, message: 'End date is required' },
                        ]}
                    >
                        <DatePicker
                            className="w-full"
                            format="DD/MM/YYYY"
                            disabled={!isFullAccess}
                        />
                    </Form.Item>
                </div>

                {['multiple', 'day_pass', 'single'].includes(
                    purchaseData?.sessionType
                ) && (
                    <div className="mb-8 flex w-full items-center">
                        <p className="m-0 w-[25%] text-start text-[13px] font-semibold text-[#1a3353]">
                            Remaining Sessions
                            <span className="text-red-500">*</span>
                        </p>
                        <div className="flex w-[75%] gap-4">
                            {isFullAccess && (
                                <>
                                    <div
                                        className="flex cursor-pointer items-center rounded-xl border px-6"
                                        onClick={() =>
                                            handleSessionCount('decrement')
                                        }
                                    >
                                        -
                                    </div>
                                    <Form.Item
                                        name="addedSessions"
                                        className="mb-0 w-min"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please enter remaining sessions',
                                            },
                                        ]}
                                    >
                                        <Input
                                            type="number"
                                            className="w-[60px]"
                                            disabled
                                        />
                                    </Form.Item>
                                    <div
                                        className="flex cursor-pointer items-center rounded-xl border px-6"
                                        onClick={() =>
                                            handleSessionCount('increment')
                                        }
                                    >
                                        +
                                    </div>
                                </>
                            )}
                            <Form.Item
                                name="remainingSessions"
                                className="mb-0 w-min"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please enter remaining sessions',
                                    },
                                ]}
                            >
                                <Input
                                    type="text"
                                    className="w-[60px]"
                                    disabled
                                />
                            </Form.Item>
                        </div>
                    </div>
                )}

                <div className="mt-4 flex w-[90%] justify-end gap-5">
                    <Form.Item>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={isSubmitting}
                            className="bg-purpleLight px-7 py-7 text-xl"
                        >
                            Save
                        </Button>
                    </Form.Item>
                    <Form.Item>
                        <Button
                            htmlType="button"
                            onClick={handleCancel}
                            disabled={isSubmitting}
                            className="border border-[#1A3353] bg-white px-7 py-7 text-xl text-[#1A3353]"
                        >
                            Cancel
                        </Button>
                    </Form.Item>
                </div>
            </Form>
        </Modal>
    );
};

export default EditActivePackageModal;
