import React, { useEffect, useState } from 'react';
import { ConfigProvider, Pagination, Table } from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useParams } from 'wouter';
import { clientBookingList } from '~/redux/actions/customer-action';
import { getQueryParams } from '~/utils/getQueryParams';
import { navigate } from 'wouter/use-location';
import { useLoader } from '~/hooks/useLoader';
import { capitalizeFirstLetter, formatDate } from '../common/function';

// Column Definitions
const columns: any = [
    {
        title: 'Package Name',
        dataIndex: 'packageName',
    },
    {
        title: 'Date',
        dataIndex: 'date',
        render: (text: any) => {
            return formatDate(text);
        },
    },
    {
        title: 'Start Time',
        dataIndex: 'from',
    },
    {
        title: 'End Time',
        dataIndex: 'to',
    },
    {
        title: 'Location',
        dataIndex: 'facilityName',
    },
    {
        title: 'Service Category',
        dataIndex: 'serviceCategoryName',
    },
    {
        title: 'Booking Status',
        dataIndex: 'scheduleStatus',
        render: (text: any) => {
            const statusClass =
                text === 'booked'
                    ? 'bg-green-100 bg-opacity-50 w-fit text-green-500 py-1 px-3 rounded'
                    : text === 'checked-in'
                    ? 'bg-purple-100 text-primary py-1 w-fit px-3 rounded'
                    : text === 'canceled'
                    ? 'bg-red-100 text-primary py-1 w-fit px-3 rounded'
                    : 'py-1 px-3';
            return (
                <div className={statusClass}>{capitalizeFirstLetter(text)}</div>
            );
        },
    },
];

const ClientBookingTable = () => {
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const userId = params.userId;

    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;
    const [loader, startLoader, endLoader] = useLoader();

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    const store = useAppSelector((state) => ({
        customerSchedlingList: state.customer_store.customerSchedulingList,
        customerSchedulingListCount:
            state.customer_store.customerSchedulingListCount,
    }));

    console.log('Id----------------', userId, store);

    useEffect(() => {
        const reqData = {
            page: currentPage,
            pageSize: pageSizes,
            clientId: userId,
        };
        if (userId) {
            startLoader();
            dispatch(clientBookingList(reqData))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [userId, currentPage, pageSizes]);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?userId=${userId}&page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?userId=${userId}&page=${page}&pageSize=${pageSize}`, {
                replace: true,
            });
        }
    }

    if (store.customerSchedulingListCount === 0) {
        return (
            <div className="flex h-[60vh] items-center justify-center text-lg text-gray-500">
                No data available
            </div>
        );
    }

    return (
        <div className="mt-6">
            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            borderColor: '#0000001A',
                            cellFontSize: 13,
                            headerBg: '#fff',
                            headerColor: '#1A3353',
                            colorText: '#455560',
                        },
                    },
                }}
            >
                <div className="rounded-xl border bg-white px-4 pb-8 pt-4 shadow-md">
                    <Table
                        columns={columns}
                        dataSource={store.customerSchedlingList}
                        pagination={false}
                        // scroll={{ y: 240 }}
                        loading={loader}
                    />
                    <div className="flex justify-center  py-10">
                        <Pagination
                            current={currentPage}
                            total={store.customerSchedulingListCount}
                            pageSizeOptions={['10', '20', '50']}
                            pageSize={pageSizes}
                            onChange={paginate}
                            hideOnSinglePage
                        />
                    </div>
                </div>
            </ConfigProvider>
        </div>
    );
};

export default ClientBookingTable;
