import { Button, Collapse, Form, Pagination, Space } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    ActivePurchasePricingPackagesByClient,
    InActivePurchasePricingPackagesByClient,
} from '~/redux/actions/pricing-actions';
import { formatCamelCase, formatDate } from '../common/function';
import { getQueryParams } from '~/utils/getQueryParams';
import { navigate } from 'wouter/use-location';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '~/components/library/loader/full-loader';
import BookingSuspensionModal from '~/screens/bookings/bookingSuspension-modal';
import { EditOutlined } from '@ant-design/icons';
import EditActivePackageModal from './edit-active-package-modal';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { useSelector } from 'react-redux';
import ModulePinConfirmationModal from '~/components/modals/module-pin-confirmation-modal';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import Alertify from '~/services/alertify';

interface ActivePackageProps {
    clientId: string;
}

const ActivePackages: React.FC<ActivePackageProps> = ({ clientId }) => {
    const topRef = useRef<HTMLDivElement>(null);

    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const [loader, startLoader, endLoader] = useLoader();
    const [isSuspensionModalVisible, setIsSuspensionModalVisible] =
        useState<boolean>(false);
    const [purchaseData, setPurchaseData] = useState<any>({});

    const activePageParam = Number(params.activePage);
    const activePageSizeParam = Number(params.activePageSize);
    const inActivePageParam = Number(params.inActivePage);
    const inActivePageSizeParam = Number(params.inActivePageSize);

    const [activeCurrentPage, setActiveCurrentPage] = useState(
        !isNaN(activePageParam) ? activePageParam : 1
    );
    const [inActiveCurrentPage, setInActiveCurrentPage] = useState(
        !isNaN(inActivePageParam) ? inActivePageParam : 1
    );
    const [activePageSize, setActivePageSize] = useState(
        !isNaN(activePageSizeParam) ? activePageSizeParam : 10
    );
    const [inActivePageSize, setInActivePageSize] = useState(
        !isNaN(inActivePageSizeParam) ? inActivePageSizeParam : 10
    );
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(false);
    const [isFullAccess, setIsFullAccess] = useState<boolean>(false);
    const [accessUserId, setAccessUserId] = useState<string | undefined>();

    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        userId: state.auth_store.userId,
        purchasepricingPackageList:
            state.pricing_store.purchasepricingListByClient,
        purchasePricingCount:
            state.pricing_store.purchasepricingListByClientCount,
        inActivePurchasepricingListByClient:
            state.pricing_store.inActivePurchasepricingListByClient,
        inActivePurchasepricingListByClientCount:
            state.pricing_store.inActivePurchasepricingListByClientCount,
    }));

    const showModal = () => {
        setPinModalVisible(false);
        setIsModalVisible(true);
    };

    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );

    const hasEditPackagePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.PURCHASE_PURCHASE &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PURCHASE_PACKAGE_UPDATE_START
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const hasEditEndDatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.PURCHASE_PURCHASE &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PURCHASE_PACKAGE_UPDATE_SESSION
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const openPinModal = (data: any) => {
        setPurchaseData({
            ...data,
            userId: clientId,
        });
        if (store.role === RoleType.ORGANIZATION) {
            setIsFullAccess(true);
            setAccessUserId(store.userId);
            showModal();
            return;
        }

        dispatch(GetSettingActiveStatus({ settingKey: 'settings_pin' }))
            .unwrap()
            .then((response: any) => {
                const settingData = response?.data?.data;
                const isEnabled = settingData?.isEnabled;
                const isActive = settingData?.isActive;

                if (isEnabled && isActive) {
                    setPinModalVisible(true);
                } else if (hasEditPackagePermission) {
                    setIsFullAccess(hasEditEndDatePermission);
                    setAccessUserId(store.userId);
                    showModal();
                } else
                    Alertify.error(
                        "Sorry, you don't have the necessary permissions to perform this action"
                    );
            });
    };

    const handleConfirmPin = (userId: string | undefined, policies?: any) => {
        const moduleData = policies?.find(
            (data: any) => data.type === SUBJECT_TYPE.PURCHASE_PURCHASE
        );
        const subModuleData = moduleData?.permissions?.some(
            (data: any) =>
                data.type === PERMISSIONS_ENUM.PURCHASE_PACKAGE_UPDATE_SESSION
        );
        setIsFullAccess(!!subModuleData);
        setAccessUserId(userId);
        showModal();
    };

    const handleCancel = () => {
        setPinModalVisible(false);
        setIsModalVisible(false);
        setPurchaseData(null);
    };

    function paginate(page: number, pageSize: number, isActive: boolean) {
        if (isActive) {
            setActiveCurrentPage(page);
            setActivePageSize(pageSize);
        } else {
            setInActiveCurrentPage(page);
            setInActivePageSize(pageSize);
        }
        topRef.current?.scrollIntoView({ behavior: 'smooth' });
        navigate(
            `?activePage=${
                isActive ? page : activeCurrentPage
            }&activePageSize=${
                isActive ? pageSize : activePageSize
            }&inActivePage=${
                !isActive ? page : inActiveCurrentPage
            }&inActivePageSize=${!isActive ? pageSize : inActivePageSize}`,
            { replace: true }
        );
    }

    const handleClose = (refresh = false) => {
        if (refresh)
            dispatch(
                ActivePurchasePricingPackagesByClient({
                    userId: clientId,
                    page: activeCurrentPage,
                    pageSize: activePageSize,
                })
            ).unwrap();
        setPurchaseData(null);
        setIsSuspensionModalVisible(false);
    };

    const handleSupensionModalOpen = (data: any) => {
        setPurchaseData({
            ...data,
            endDate: data?.expiryDate,
        });
        setIsSuspensionModalVisible(true);
    };

    useEffect(() => {
        startLoader();
        if (clientId) {
            dispatch(
                ActivePurchasePricingPackagesByClient({
                    userId: clientId,
                    page: activeCurrentPage,
                    pageSize: activePageSize,
                })
            )
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [clientId, activeCurrentPage, activePageSize]);

    useEffect(() => {
        startLoader();
        if (clientId) {
            dispatch(
                InActivePurchasePricingPackagesByClient({
                    userId: clientId,
                    page: inActiveCurrentPage,
                    pageSize: inActivePageSize,
                })
            )
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [clientId, inActiveCurrentPage, inActivePageSize]);

    function isPackageSuspended(item: any) {
        const now = new Date();
        if (item.suspensions && item.suspensions.length > 0)
            return item.suspensions.some((suspension: any) => {
                const fromDate = new Date(suspension.fromDate);
                const suspensionEndDate = new Date(suspension.endDate);
                return now >= fromDate && now <= suspensionEndDate;
            });
        return false;
    }

    return (
        <>
            <div
                ref={topRef}
                className="w-full  lg:w-[100%] lg:py-10 lg:pr-10 "
            >
                <Space className="w-full" direction="vertical" size="large">
                    <div className="flex flex-col gap-5 ">
                        <Collapse
                            className="custom-collapse client-profile-collapse mt-10 w-full rounded-2xl"
                            bordered={false}
                            defaultActiveKey={['1', '2']}
                            items={[
                                {
                                    key: '1',
                                    label: (
                                        <div className="w-fit border-b-2 border-primary text-2xl font-semibold">
                                            Active Packages
                                        </div>
                                    ),
                                    children:
                                        // loader ? (
                                        //     <div className="flex justify-center items-center py-10">
                                        //         <FullLoader state={true}  />
                                        //     </div>
                                        // ) :
                                        store.purchasepricingPackageList
                                            ?.length > 0 ? (
                                            <>
                                                <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                                    {store.purchasepricingPackageList.map(
                                                        (
                                                            item: any,
                                                            index: number
                                                        ) => {
                                                            const isSuspended =
                                                                isPackageSuspended(
                                                                    item
                                                                );
                                                            return (
                                                                <div
                                                                    key={index}
                                                                    className="mt-4 flex flex-col gap-5 rounded-2xl border bg-white px-5 py-5"
                                                                >
                                                                    {/* <div className="flex flex-row justify-end">
                                                                        <Button
                                                                            onClick={() =>
                                                                                handleSupensionModalOpen(
                                                                                    item
                                                                                )
                                                                            }
                                                                            className="text-4 bg-purpleLight px-8 py-4"
                                                                            type="primary"
                                                                        >
                                                                            {isSuspended
                                                                                ? 'Resume Suspension'
                                                                                : 'Suspend'}
                                                                        </Button>
                                                                    </div> */}
                                                                    <div className=" text-right">
                                                                        <EditOutlined
                                                                            onClick={() =>
                                                                                openPinModal(
                                                                                    item
                                                                                )
                                                                            }
                                                                            className="cursor-pointer text-[2rem]"
                                                                        />
                                                                    </div>
                                                                    <div className="flex flex-row justify-between ">
                                                                        <div className="flex w-1/3 flex-row items-center gap-5  ">
                                                                            <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Package
                                                                                    Name
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="w-1/2 text-xl text-[#455560]">
                                                                                {
                                                                                    item?.packageName
                                                                                }
                                                                            </p>
                                                                        </div>
                                                                        <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                            <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Service
                                                                                    Type
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="w-1/2 text-xl text-[#455560]">
                                                                                {formatCamelCase(
                                                                                    item?.type
                                                                                )}
                                                                            </p>
                                                                        </div>

                                                                        {item?.isBundledPricing ? (
                                                                            <>
                                                                                <div className="flex w-1/3 flex-row items-center gap-5  ">
                                                                                    <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                        <p className="text-xl font-medium text-[#1A3353]">
                                                                                            Bundle
                                                                                            Name
                                                                                        </p>
                                                                                        <p>
                                                                                            :
                                                                                        </p>
                                                                                    </div>
                                                                                    <div className="w-1/2 rounded-md px-3 py-1">
                                                                                        <p className="truncated-text text-xl text-[#455560] ">
                                                                                            {
                                                                                                item.bundledPricingName
                                                                                            }
                                                                                        </p>
                                                                                    </div>
                                                                                </div>
                                                                            </>
                                                                        ) : (
                                                                            <>
                                                                                <div className="flex w-1/3 flex-row items-center gap-5  ">
                                                                                    <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                        <p className="text-xl font-medium text-[#1A3353]">
                                                                                            Price
                                                                                        </p>
                                                                                        <p>
                                                                                            :
                                                                                        </p>
                                                                                    </div>
                                                                                    <p className="w-1/2 text-xl text-[#455560]">
                                                                                        {/* {item.price?.toFixed(
                                                                                            2
                                                                                        )} */}
                                                                                        {Math.trunc(
                                                                                            item.price *
                                                                                                100
                                                                                        ) /
                                                                                            100}
                                                                                    </p>
                                                                                </div>
                                                                            </>
                                                                        )}

                                                                        {/* <div className="flex w-[40%] flex-row items-center gap-5 ps-10">
                                                                            <div className="flex flex-row items-center justify-between lg:w-[50%]">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Location
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560]">
                                                                                {
                                                                                    item?.location
                                                                                }
                                                                            </p>
                                                                        </div> */}
                                                                    </div>
                                                                    <div className="flex flex-row justify-between">
                                                                        <div className="flex w-1/3 flex-row items-center gap-5  ">
                                                                            <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Start
                                                                                    Date
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="w-1/2 text-xl text-[#455560]">
                                                                                {formatDate(
                                                                                    item?.startDate
                                                                                )}
                                                                            </p>
                                                                        </div>

                                                                        <div className="flex w-1/3 flex-row items-center gap-5  ">
                                                                            <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Service
                                                                                    Category
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="w-1/2 text-xl text-[#455560]">
                                                                                {item?.serviceNames?.join(
                                                                                    ', '
                                                                                )}
                                                                            </p>
                                                                        </div>

                                                                        <div className="flex w-1/3 flex-row items-center gap-5  ">
                                                                            <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Total
                                                                                    No.
                                                                                    of
                                                                                    Sessions
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="w-1/2 text-xl text-[#455560]">
                                                                                {item?.sessionType ===
                                                                                'day_pass'
                                                                                    ? `${item?.dayPassLimit} X Day Pass(es)`
                                                                                    : item?.sessionType ===
                                                                                      'unlimited'
                                                                                    ? 'Unlimited'
                                                                                    : item?.totalSessions}
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                    <div className="flex flex-row justify-between">
                                                                        <div className="flex w-1/3 flex-row items-center gap-5  ">
                                                                            <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Expiry
                                                                                    Date
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="w-1/2 text-xl text-[#455560]">
                                                                                {formatDate(
                                                                                    item?.expiryDate
                                                                                )}
                                                                            </p>
                                                                        </div>

                                                                        {/* <div className="flex w-[40%] flex-row items-center gap-5">
                                                                    <div className="flex flex-row items-center justify-between w-1/2">
                                                                        <p className="text-xl font-medium text-[#1A3353]">
                                                                            Bundle
                                                                            Name
                                                                        </p>
                                                                        <p>:</p>
                                                                    </div>
                                                                    <p className="truncated-text text-xl text-[#455560] ">
                                                                        Package
                                                                        Name
                                                                    </p>
                                                                </div> */}
                                                                        <div className="flex w-1/3 flex-row items-center gap-5  ">
                                                                            <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Sub
                                                                                    Type
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="w-1/2 text-xl text-[#455560]">
                                                                                {item?.appointmentTypeName?.join(
                                                                                    ', '
                                                                                )}
                                                                            </p>
                                                                        </div>

                                                                        {item.type ===
                                                                        'courses' ? (
                                                                            <div className="flex w-1/3 flex-row items-center gap-5  ">
                                                                                <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                    <p className="text-xl font-medium text-[#1A3353]">
                                                                                        Total
                                                                                        Enrollments
                                                                                    </p>
                                                                                    <p>
                                                                                        :
                                                                                    </p>
                                                                                </div>
                                                                                <p className="w-1/2 text-xl text-[#455560]">
                                                                                    {item.sessionConsumed
                                                                                        ? item.sessionConsumed
                                                                                        : '0'}
                                                                                </p>
                                                                            </div>
                                                                        ) : (
                                                                            <div className="flex w-1/3 flex-row items-center gap-5  ">
                                                                                <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                    <p className="text-xl font-medium text-[#1A3353]">
                                                                                        Remaining
                                                                                        No.
                                                                                        of
                                                                                        Sessions
                                                                                    </p>
                                                                                    <p>
                                                                                        :
                                                                                    </p>
                                                                                </div>
                                                                                <p className="w-1/2 text-xl text-[#455560]">
                                                                                    {item.remainingSession
                                                                                        ? item.remainingSession
                                                                                        : '0'}
                                                                                </p>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                    {item?.type !==
                                                                        'courses' && (
                                                                        <div className="flex flex-row justify-center pb-5">
                                                                            <Button
                                                                                onClick={() =>
                                                                                    handleSupensionModalOpen(
                                                                                        item
                                                                                    )
                                                                                }
                                                                                className="text-4 bg-purpleLight px-8 py-4"
                                                                                type="primary"
                                                                            >
                                                                                {isSuspended
                                                                                    ? 'Unfreeze'
                                                                                    : 'Freeze'}
                                                                            </Button>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            );
                                                        }
                                                    )}
                                                </div>
                                                <div className="flex justify-center py-10">
                                                    <Pagination
                                                        current={
                                                            activeCurrentPage
                                                        }
                                                        total={
                                                            store.purchasePricingCount
                                                        }
                                                        pageSize={
                                                            activePageSize
                                                        }
                                                        onChange={(
                                                            page,
                                                            pageSize
                                                        ) =>
                                                            paginate(
                                                                page,
                                                                pageSize,
                                                                true
                                                            )
                                                        }
                                                        // hideOnSinglePage
                                                    />
                                                </div>
                                            </>
                                        ) : (
                                            <div className="rounded-2xl bg-[#f5f5f5] py-20 text-center text-xl text-[#455560]">
                                                No active package
                                            </div>
                                        ),
                                },
                            ]}
                            expandIconPosition="right"
                        />
                        <Collapse
                            className="custom-collapse client-profile-collapse mt-10 w-full rounded-2xl"
                            bordered={false}
                            defaultActiveKey={[]}
                            items={[
                                {
                                    key: '1',
                                    label: (
                                        <div className="w-fit border-b-2 border-primary text-2xl font-semibold">
                                            Inactive Packages
                                        </div>
                                    ),
                                    children:
                                        // loader ? (
                                        //     <div className="flex justify-center items-center py-10">
                                        //         <FullLoader state={true}  />
                                        //     </div>
                                        // ) :
                                        store
                                            .inActivePurchasepricingListByClient
                                            ?.length > 0 ? (
                                            <>
                                                <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                                    {store.inActivePurchasepricingListByClient.map(
                                                        (
                                                            item: any,
                                                            index: number
                                                        ) => (
                                                            <div
                                                                key={index}
                                                                className="mt-4 flex flex-col gap-5 rounded-2xl border bg-white px-5 py-5"
                                                            >
                                                                <div className="text-right">
                                                                    <EditOutlined
                                                                        onClick={() =>
                                                                            openPinModal(
                                                                                item
                                                                            )
                                                                        }
                                                                        className="cursor-pointer text-[2rem]"
                                                                    />
                                                                </div>
                                                                <div className="flex flex-row justify-between">
                                                                    <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                        <div className="flex w-1/2 flex-row items-center justify-between">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Package
                                                                                Name
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="w-1/2 text-xl text-[#455560]">
                                                                            {
                                                                                item?.packageName
                                                                            }
                                                                        </p>
                                                                    </div>
                                                                    <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                        <div className="flex w-1/2 flex-row items-center justify-between">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Service
                                                                                Category
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="w-1/2 text-xl text-[#455560]">
                                                                            {item?.serviceNames?.join(
                                                                                ', '
                                                                            )}
                                                                        </p>
                                                                    </div>
                                                                    <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                        <div className="flex w-1/2 flex-row items-center justify-between">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Location
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="w-1/2 text-xl text-[#455560]">
                                                                            {
                                                                                item?.location
                                                                            }
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                                <div className="flex flex-row justify-between">
                                                                    <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                        <div className="flex w-1/2 flex-row items-center justify-between">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Expiry
                                                                                Date
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="w-1/2 text-xl text-[#455560]">
                                                                            {formatDate(
                                                                                item?.expiryDate
                                                                            )}
                                                                        </p>
                                                                    </div>
                                                                    <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                        <div className="flex w-1/2 flex-row items-center justify-between">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Sub
                                                                                Type
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="w-1/2 text-xl text-[#455560]">
                                                                            {item?.appointmentTypeName?.join(
                                                                                ', '
                                                                            )}
                                                                        </p>
                                                                    </div>
                                                                    <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                        <div className="flex w-1/2 flex-row items-center justify-between">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Total
                                                                                No.
                                                                                of
                                                                                Sessions
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="w-1/2 text-xl text-[#455560]">
                                                                            {item?.sessionType ===
                                                                            'day_pass'
                                                                                ? `${item?.dayPassLimit} X Day Pass(es)`
                                                                                : item?.sessionType ===
                                                                                  'unlimited'
                                                                                ? 'Unlimited'
                                                                                : item?.totalSessions}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                                <div className="flex flex-row justify-between">
                                                                    <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                        <div className="flex w-1/2 flex-row items-center justify-between">
                                                                            <p className="text-xl font-medium text-[#1A3353]">
                                                                                Service
                                                                                Type
                                                                            </p>
                                                                            <p>
                                                                                :
                                                                            </p>
                                                                        </div>
                                                                        <p className="w-1/2 text-xl text-[#455560]">
                                                                            {formatCamelCase(
                                                                                item?.type
                                                                            )}
                                                                        </p>
                                                                    </div>
                                                                    {/* <div className="flex w-[40%] flex-row items-center gap-5">
                                                                    <div className="flex flex-row items-center justify-between w-1/2">
                                                                        <p className="text-xl font-medium text-[#1A3353]">
                                                                            Bundle
                                                                            Name
                                                                        </p>
                                                                        <p>:</p>
                                                                    </div>
                                                                    <p className="truncated-text text-xl text-[#455560] ">
                                                                        Package
                                                                        Name
                                                                    </p>
                                                                </div> */}
                                                                    {item?.isBundledPricing ? (
                                                                        <>
                                                                            <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                                <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                    <p className="text-xl font-medium text-[#1A3353]">
                                                                                        Bundle
                                                                                        Name
                                                                                    </p>
                                                                                    <p>
                                                                                        :
                                                                                    </p>
                                                                                </div>
                                                                                <div className="w-1/2 rounded-md bg-green-200">
                                                                                    <p className="truncated-text text-xl text-[#455560] ">
                                                                                        {
                                                                                            item.bundledPricingName
                                                                                        }
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                                <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                    <p className="text-xl font-medium text-[#1A3353]">
                                                                                        Price
                                                                                    </p>
                                                                                    <p>
                                                                                        :
                                                                                    </p>
                                                                                </div>
                                                                                <div
                                                                                    className={
                                                                                        item.paymentStatus ===
                                                                                            'refund' ||
                                                                                        item.paymentStatus ===
                                                                                            'cancel'
                                                                                            ? 'w-1/2 rounded-md bg-red-200 px-3 py-1'
                                                                                            : ''
                                                                                    }
                                                                                >
                                                                                    <p className="truncated-text text-xl text-[#455560] ">
                                                                                        {item.paymentStatus ===
                                                                                        'refund'
                                                                                            ? 'Refunded'
                                                                                            : item.paymentStatus ===
                                                                                              'cancel'
                                                                                            ? 'Cancelled'
                                                                                            : item.price}
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </>
                                                                    )}
                                                                    {item.type ===
                                                                    'courses' ? (
                                                                        <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                            <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Total
                                                                                    Enrollments
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="text-xl text-[#455560]">
                                                                                {item.sessionConsumed
                                                                                    ? item.sessionConsumed
                                                                                    : '0'}
                                                                            </p>
                                                                        </div>
                                                                    ) : (
                                                                        <div className="flex w-1/3 flex-row items-center gap-5 ">
                                                                            <div className="flex w-1/2 flex-row items-center justify-between">
                                                                                <p className="text-xl font-medium text-[#1A3353]">
                                                                                    Remaining
                                                                                    No.
                                                                                    of
                                                                                    Sessions
                                                                                </p>
                                                                                <p>
                                                                                    :
                                                                                </p>
                                                                            </div>
                                                                            <p className="w-1/2 text-xl text-[#455560]">
                                                                                {item.remainingSession
                                                                                    ? item.remainingSession
                                                                                    : '0'}
                                                                            </p>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                                <div className="flex justify-center py-10">
                                                    <Pagination
                                                        current={
                                                            inActiveCurrentPage
                                                        }
                                                        total={
                                                            store.inActivePurchasepricingListByClientCount
                                                        }
                                                        pageSize={
                                                            inActivePageSize
                                                        }
                                                        onChange={(
                                                            page,
                                                            pageSize
                                                        ) =>
                                                            paginate(
                                                                page,
                                                                pageSize,
                                                                false
                                                            )
                                                        }
                                                        // hideOnSinglePage
                                                    />
                                                </div>
                                            </>
                                        ) : (
                                            <div className="rounded-2xl bg-[#f5f5f5] py-20 text-center text-xl text-[#455560]">
                                                No Inactive package
                                            </div>
                                        ),
                                },
                            ]}
                            expandIconPosition="right"
                        />
                    </div>
                </Space>
            </div>
            {isSuspensionModalVisible && (
                <BookingSuspensionModal
                    visible={isSuspensionModalVisible}
                    onClose={handleClose}
                    purchaseId={purchaseData?._id}
                    purchaseData={purchaseData}
                />
            )}

            {isModalVisible && (
                <EditActivePackageModal
                    isModalVisible={isModalVisible}
                    handleCancel={handleCancel}
                    isFullAccess={isFullAccess}
                    purchaseData={purchaseData}
                    accessUserId={accessUserId}
                />
            )}

            {pinModalVisible && (
                <ModulePinConfirmationModal
                    visible={pinModalVisible}
                    onConfirm={handleConfirmPin}
                    onCancel={handleCancel}
                    module={SUBJECT_TYPE.PURCHASE_PURCHASE}
                    subModule={PERMISSIONS_ENUM.PURCHASE_PACKAGE_UPDATE_START}
                />
            )}
        </>
    );
};

export default ActivePackages;
