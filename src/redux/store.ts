import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { persistReducer, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { APP_MODE } from '~/env';

import authSlice from './slices/auth-slice';
import customerSlice from './slices/customer-slice';
import gymSlice from './slices/gym-slice';
import commonSlice from './slices/common-slice';
import attributesSlice from './slices/attributes-slice';
import amenitiesSlice from './slices/amenities-slice';
import organizationSlice from './slices/organization-slice';
import serviceCategorySlice from './slices/serviceCategorySlice';

import staffSlice from '~/redux/slices/staff-slice';
import facilitySlice from './slices/facility-slice';
import settingsSlice from '~/redux/slices/settings-slice';
import appointmentSlice from './slices/appointment-slice';
import pricingSlice from './slices/pricing-slice';
import classSlice from './slices/class-slice';
import payRateSlice from './slices/pay-rate-slice';
import roomSlice from './slices/room-slice';
import scheduleSlice from './slices/scheduling-slice';
import featureSlice from './slices/feature-slice';
import membershipSlice from './slices/membership-slice';
import announcementSlice from './slices/announcement-slice';
import purchasedSlice from './slices/purchaged-slice';
import waitTimeSlice from './slices/wait-time-slice';
import membershipSuspensionSlice from './slices/membershipSuspension-slice';
import categorySlice from './slices/merchandise/category-slice';
import subAttributeSlice from './slices/merchandise/attributes-slice';
import containerSlice from './slices/merchandise/container-slice';
import productSlice from './slices/merchandise/product-slice';
import inventorySlice from './slices/merchandise/inventory-slice';
import paymentSlice from './slices/payment-method.slice';
import courseSlice from './slices/courses-slice';
import permissionReducer from './slices/permissions-slice';
import sidebarReducer from './slices/topBar-slice';

const persistConfig = {
    key: 'root',
    storage,
    whitelist: [
        'auth_store',
        'common_store',
        'purchased_store',
        'permission_store',
    ],
};

const reducers = combineReducers({
    auth_store: authSlice,
    customer_store: customerSlice,
    gym_store: gymSlice,
    common_store: commonSlice,
    attribute_store: attributesSlice,
    amenity_store: amenitiesSlice,
    pricing_store: pricingSlice,
    staff_store: staffSlice,
    organization_store: organizationSlice,
    facility_store: facilitySlice,
    settings_store: settingsSlice,
    appointment_store: appointmentSlice,
    service_category_store: serviceCategorySlice,
    class_store: classSlice,
    pay_rate_store: payRateSlice,
    room_store: roomSlice,
    scheduling_store: scheduleSlice,
    fetaureTab_store: featureSlice,
    membership_store: membershipSlice,
    announcement_store: announcementSlice,
    purchased_store: purchasedSlice,
    waittime_store: waitTimeSlice,
    membershipSuspension_store: membershipSuspensionSlice,
    category_store: categorySlice,
    attributes_store: subAttributeSlice,
    container_store: containerSlice,
    product_store: productSlice,
    inventory_store: inventorySlice,
    paymentmethod_store: paymentSlice,
    course_store: courseSlice,
    permission_store: permissionReducer,
    sidebar: sidebarReducer,
});

const persistedReducer = persistReducer(persistConfig, reducers);

const store = configureStore({
    reducer: persistedReducer,
    devTools: APP_MODE === 'development',
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            thunk: true,
            serializableCheck: false,
            immutableCheck: false,
        }),
});

export const persistor = persistStore(store);

//This line is for typescirpt (Javascript does't have them)
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
