// src/redux/widgetStore.ts
import { configureStore } from '@reduxjs/toolkit';
import signupReducer from './slices/widget/widget-signup.slice';
import selectedServiceReducer from './slices/widget/selectedService.slice';

const widgetStore = configureStore({
  reducer: {
    signup: signupReducer,
    selectedService: selectedServiceReducer,   // ⬅️ add this
  },
  devTools: false,
});

export type WidgetRootState = ReturnType<typeof widgetStore.getState>;
export type WidgetDispatch = typeof widgetStore.dispatch;

export default widgetStore;
