import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    createFeature,
    featureListing,
    getFeatureById,
} from '~/redux/actions/feature-action';

interface Feature {
    _id?: string;
    name: string;
    status: boolean;
}

interface InitialState {
    featureList: Feature[];
    featureDetails: any;
    featureCount: number;
    loading: boolean;
    error: string | null;
    listingLoading: boolean;
}

const initialState: InitialState = {
    featureList: [],
    featureDetails: null,
    featureCount: 0,
    loading: false,
    error: null,
    listingLoading: false,
};

const featureSlice = createSlice({
    name: 'feature',
    initialState,
    reducers: {
        resetFeatureState: () => initialState,
        clearFeatureDetails: (state) => {
            state.featureDetails = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(createFeature.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(
                createFeature.fulfilled,
                (state, action: PayloadAction<Feature>) => {
                    state.loading = false;
                    // state.featureDetails = action.payload;
                    state.error = null;
                }
            )
            .addCase(createFeature.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message || 'Something went wrong';
            })
            // Handling fetchFeatureList
            .addCase(featureListing.pending, (state) => {
                state.listingLoading = true;
                state.error = null;
            })
            .addCase(
                featureListing.fulfilled,
                (
                    state,
                    action: PayloadAction<{
                        features: Feature[];
                        count: number;
                    }>
                ) => {
                    console.log(action);
                    state.listingLoading = false;
                    state.featureList = action.payload.data;
                    state.featureCount = action.payload.count;
                    state.error = null;
                }
            )
            .addCase(featureListing.rejected, (state, action) => {
                state.listingLoading = false;
                state.error =
                    action.error.message || 'Failed to fetch features';
            })
            .addCase(getFeatureById.fulfilled, (state, { payload }) => {
                // console.log('Payload-------------', payload);
                state.featureDetails = payload;
            });
    },
});

export const { resetFeatureState, clearFeatureDetails } = featureSlice.actions;
export default featureSlice.reducer;
