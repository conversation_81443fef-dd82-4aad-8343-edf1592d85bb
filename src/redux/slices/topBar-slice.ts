import { createSlice } from '@reduxjs/toolkit';

const sidebarSlice = createSlice({
    name: 'sidebar',
    initialState: {
        collapsed: false,
    },
    reducers: {
        setCollapsed: (state, action) => {
            state.collapsed = action.payload;
        },
        toggleCollapsed: (state) => {
            state.collapsed = !state.collapsed;
        },
    },
});

export const { setCollapsed, toggleCollapsed } = sidebarSlice.actions;
export default sidebarSlice.reducer;
