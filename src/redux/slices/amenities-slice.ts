import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    AmenitiesList,
    AmenityDetails,
    AmenityGroupList,
} from '../actions/amenities-action';

interface AmenitiesState {
    amenitiesList: [];
    amenitiesListCount: number;
    amenitiesDetails: [];
    amenitiesGroupList: [];
}

const initialState: AmenitiesState = {
    amenitiesList: [],
    amenitiesListCount: 0,
    amenitiesDetails: [],
    amenitiesGroupList: [],
};

const amenitiesSlice = createSlice({
    name: 'amenities_store',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(
            AmenitiesList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log("Payload------------", payload)
                state.amenitiesList = payload?.data?.data?.list;
                state.amenitiesListCount = payload?.data?.data?.count;
            }
        );
        builder.addCase(
            AmenityDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log("Payload------------", payload)
                state.amenitiesDetails = payload?.data?.data;
            }
        );
        builder.addCase(
            AmenityGroupList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload------------', payload);
                state.amenitiesGroupList = payload?.data?.data;
            }
        );
    },
});

// export const { logout } = amenitiesSlice.actions;

export default amenitiesSlice.reducer;
