import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    BookedSchedulingDetailsRecurring,
    classesCustomerList,
    classesParticipantList,
    classSchedulingList,
    getClassSchedulingDetails,
} from '../actions/class-action';

interface ClassState {
    classCustomerList: [];
    classCustomerListCount: number;
    classSchedulingListCount: number;
    classSchedulingList: [];
    classParticipantList: [];
    classParticipantListCount: number;
    classSchedulingDetails: any;
}

const initialState: ClassState = {
    classCustomerList: [],
    classCustomerListCount: 0,
    classSchedulingList: [],
    classSchedulingListCount: 0,
    classParticipantList: [],
    classParticipantListCount: 0,
    classSchedulingDetails: {},
};

const classSlice = createSlice({
    name: 'class_store',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(classSchedulingList.fulfilled, (state, { payload }) => {
                state.classSchedulingList = payload?.data?.data;
                state.classSchedulingListCount =
                    payload?.data?._metadata?.pagination?.total;
            })
            .addCase(classesCustomerList.fulfilled, (state, { payload }) => {
                state.classCustomerList = payload?.data?.data;
                state.classCustomerListCount =
                    payload?.data?._metadata?.pagination?.total;
            })
            .addCase(classesParticipantList.fulfilled, (state, { payload }) => {
                state.classParticipantList = payload?.data?.data;
                state.classParticipantListCount =
                    payload?.data?._metadata?.pagination?.total;
            })
            .addCase(
                getClassSchedulingDetails.fulfilled,
                (state, { payload }) => {
                    state.classSchedulingDetails = payload?.data?.data;
                }
            )
            .addCase(
                BookedSchedulingDetailsRecurring.fulfilled,
                (state, { payload }) => {
                    state.classSchedulingDetails = payload?.data?.data;
                }
            );
    },
});

export default classSlice.reducer;
