import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { BillingCityList, CityList, CountryList } from '../actions/common-action';
import { LogoutUser } from '../actions/auth-actions';
import { ClientsDetails } from '../actions/customer-action';

interface AuthState {
    countryList: [];
    cityList: [];
    billingCityList : [],
    searchValue: string;
    showPinModal: boolean;
    clientDetails:any
}

const initialState: AuthState = {
    countryList: [],
    cityList: [],
    billingCityList : [],
    searchValue: '',
    showPinModal: false,
    clientDetails:{}
};

const commonSlice = createSlice({
    name: 'common_store',
    initialState,
    reducers: {
        setSearchValue: (state, { payload }) => {
            state.searchValue = payload;
        },
        clearSearchValue: (state) => {
            state.searchValue = '';
        },
        SetShowPinModal: (state, { payload }) => {
            state.showPinModal = payload;
        },
    },
    extraReducers: (builder) => {
        builder.addCase(
            CountryList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.countryList = payload?.data?.data;
            }
        );
        builder.addCase(
            CityList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.cityList = payload?.data?.data;
            }
        );
        builder.addCase(
            BillingCityList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.billingCityList = payload?.data?.data || [];
            }
        );
        builder.addCase(
            ClientsDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.clientDetails = payload?.data?.data;
            }
        );
        builder.addCase(LogoutUser.fulfilled, (state) => {
            return initialState;
        });
    },
});

export const { setSearchValue, clearSearchValue, SetShowPinModal } =
    commonSlice.actions;

export default commonSlice.reducer;
