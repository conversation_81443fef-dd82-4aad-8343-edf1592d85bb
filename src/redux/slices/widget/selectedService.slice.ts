import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { WidgetRootState } from '~/redux/widget-store';

export type SubType = {
  _id: string;
  name: string;
  durationInMinutes: number;
  onlineBookingAllowed: boolean;
  isActive: boolean;
  isFeatured?: boolean;
  image?: string;
};

export type MainType = {
  _id: string;
  name: string;
  description?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  image?: string;
  appointmentType?: SubType[];
};

type SelectedServiceState = {
  sub: SubType | null;
  parent: MainType | null;
};

const initialState: SelectedServiceState = {
  sub: null,
  parent: null,
};

const selectedServiceSlice = createSlice({
  name: 'selectedService',
  initialState,
  reducers: {
    setSelectedService(
      state,
      action: PayloadAction<{ sub: SubType; parent: MainType;  }>
    ) {
      state.sub = action.payload.sub;
      state.parent = action.payload.parent;
     
    },
    clearSelectedService(state) {
      state.sub = null;
      state.parent = null;
    },
     resetSelectedService: () => initialState,
  },
});

export const { setSelectedService, clearSelectedService ,resetSelectedService} = selectedServiceSlice.actions;
export default selectedServiceSlice.reducer;

// Optional selector
export const selectSelectedService = (s: WidgetRootState) => s.selectedService;