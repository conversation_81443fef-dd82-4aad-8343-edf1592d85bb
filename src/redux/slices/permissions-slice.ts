import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { GetAllPermissions } from '../actions/permission-action';
interface Permission {
    _id: string;
    type: string;
    description: string;
    isActive: boolean;
    isDelegated: boolean;
    name: string;
    permitted: boolean;
}
interface PermissionState {
    all_permissions_for_role: Permission[];
    loading: boolean;
    error: string | null;
}
const initialState: PermissionState = {
    all_permissions_for_role: [],
    loading: false,
    error: null,
};
const permissionSlice = createSlice({
    name: 'permission_store',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(GetAllPermissions.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(
                GetAllPermissions.fulfilled,
                (state, { payload }: PayloadAction<any>) => {
                    state.loading = false;
                    state.all_permissions_for_role = payload?.data?.data || [];
                }
            )
            .addCase(GetAllPermissions.rejected, (state, action) => {
                state.loading = false;
                state.error =
                    action.error.message || 'Failed to fetch permissions';
            });
    },
});

export default permissionSlice.reducer;
