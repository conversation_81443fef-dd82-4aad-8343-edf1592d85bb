import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AttributeListData, ManipulateAttributesLists, SubAttributeList,GetBrandsData,ActiveDynamicAttributeList } from '../../actions/merchandise/attribute-action';

// Define Attribute state interface
interface AttributeState {
    subAttributeList: any[];
    attributeList: any[];
    activeDynamicAttributeList: any[];
    brandList: [],
    subAttributeCount: number | null;
    attributeCount: number | null;
    loading: boolean;
    error: string | null;
}

// Initial state
const initialState: AttributeState = {
    attributeList: [],
    activeDynamicAttributeList: [],
    subAttributeList: [],
    brandList: [],
    subAttributeCount: null,
    attributeCount: null,
    loading: false,
    error: null,
};

// Create Redux slice
const subAttributeSlice = createSlice({
    name: 'attribute',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(AttributeListData.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
        builder.addCase(
            AttributeListData.fulfilled,
            (state, action: PayloadAction<{ data: any }>) => {
                console.log(action.payload.data);
                state.loading = false;
                state.attributeList = ManipulateAttributesLists(action.payload.data);
                state.attributeCount = state.attributeList.length;
            }
        );
        builder.addCase(AttributeListData.rejected, (state, action) => {
            state.loading = false;
            state.error = action.error.message || 'Failed to fetch attributes';
        })
        builder.addCase(SubAttributeList.pending, (state) => {
            state.loading = true;
            state.error = null;
        });
        builder.addCase(
            SubAttributeList.fulfilled,
            (state, action: PayloadAction<{ data: any }>) => {
                state.loading = false;
                state.subAttributeList = action.payload.data.list;
                state.subAttributeCount = action.payload.data.count;
            }
        )
        builder.addCase(SubAttributeList.rejected, (state, action) => {
            state.loading = false;
            state.error = action.error.message || 'Failed to fetch sub-attributes';
        })
        builder.addCase(GetBrandsData.fulfilled, (state, action: PayloadAction<{ data: any }>) => {
            state.brandList = action.payload.data.data;
        });
        builder.addCase(ActiveDynamicAttributeList.fulfilled, (state, action: PayloadAction<{ data: any }>) => {
            state.activeDynamicAttributeList = action.payload.data.data;
        })
        builder.addCase(ActiveDynamicAttributeList.rejected, (state, action) => {
            state.loading = false;
            state.error = action.error.message || 'Failed to fetch active dynamic attributes';
        })

    },
});

export default subAttributeSlice.reducer;
