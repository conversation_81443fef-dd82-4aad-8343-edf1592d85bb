import { createSlice } from '@reduxjs/toolkit';
import {
    SearchProductBySkuDetails,
    CreateNewInventory,
    InventoryList,
    updateInventory,
    storeInventory,
    _ManipulateInventoryList
} from '../../actions/merchandise/inventory-action';

// Initial State
const initialState = {
    inventoryList: [],
    storeInventoryList: [],
    loading: false,
    error: null,
};

const handlePending = (state: any) => {
    state.loading = true;
    state.error = null;
};

const handleRejected = (state: any, action: any) => {
    state.loading = false;
    state.error = action.payload || 'An error occurred';
};

const inventorySlice = createSlice({
    name: 'inventory',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(SearchProductBySkuDetails.pending, handlePending)
            .addCase(SearchProductBySkuDetails.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(SearchProductBySkuDetails.rejected, handleRejected)

            .addCase(CreateNewInventory.pending, handlePending)
            .addCase(CreateNewInventory.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(CreateNewInventory.rejected, handleRejected)

            .addCase(InventoryList.pending, handlePending)
            .addCase(InventoryList.fulfilled, (state, action) => {
                state.loading = false;
                state.inventoryList = action.payload || [];
            })
            .addCase(InventoryList.rejected, handleRejected)

            .addCase(updateInventory.pending, handlePending)
            .addCase(updateInventory.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(updateInventory.rejected, handleRejected)

            .addCase(storeInventory.pending, handlePending)
            .addCase(storeInventory.fulfilled, (state, action) => {
                state.loading = false;
                state.storeInventoryList = _ManipulateInventoryList(action.payload.data.list);
            })
            .addCase(storeInventory.rejected, handleRejected);
    }
});

export default inventorySlice.reducer;
