import { createSlice } from '@reduxjs/toolkit';
import {
    <PERSON><PERSON><PERSON>ser,
    LogoutUser,
    SetNewPassword,
    ValidatePin,
} from '../actions/auth-actions';
import { RoleType } from '~/types/enums';
import { GetSettings } from '~/redux/actions/settings-actions';

interface AuthState {
    is_Login: boolean;
    user: any | null;
    token: string | null;
    forgetEmail: string;
    otpVerificationCode: string;
    role: string;
    userId: string;
    pin: number;
    pinStandBy: number;
    organizationId: string;
    profileImage: string;
}

const initialState: AuthState = {
    is_Login: false,
    token: null,
    user: null,
    forgetEmail: '',
    otpVerificationCode: '',
    role: '',
    userId: '',
    pin: 0, //here the actual pin is set by the api
    pinStandBy: 30, //pin timer also set by the api
    organizationId: '',
    profileImage: '',
};

const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        logout: (state) => {
            localStorage.removeItem('token');
            return initialState;
        },
        SetForgetEmail: (state, { payload }) => {
            state.forgetEmail = payload;
        },
        ClearForgetEmail: (state) => {
            state.forgetEmail = '';
        },
        SetOrganizationId: (state, { payload }) => {
            state.organizationId = payload;
        },
        ClearOrganizationId: (state) => {
            state.organizationId = '';
        },
        SetOtpVerificationCode: (state, { payload }) => {
            state.otpVerificationCode = payload;
        },
        ClearOtpVerificationCode: (state) => {
            state.otpVerificationCode = '';
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(LoginUser.fulfilled, (state, { payload }) => {
                // console.log('Payload----------', payload);
                localStorage.setItem('token', payload.data.data.accessToken);
                state.token = payload.data.data.accessToken;
                state.is_Login = true;
                state.userId = payload.data.data.user._id;
                state.user = payload.data.data.user;
                state.role = payload.data.data.user.role.type;
                // state.role = payload.data.data.user.role;
                if (state.role === RoleType.ORGANIZATION) {
                    state.organizationId = payload.data.data.user._id;
                } else {
                    state.organizationId = payload.data.data.organizationId;
                }
            })
            .addCase(ValidatePin.fulfilled, (state, { payload }) => {
                localStorage.setItem('token', payload.data.data.accessToken);
                state.token = payload.data.data.accessToken;
                state.is_Login = true;
                state.userId = payload.data.data.user._id;
                state.user = payload.data.data.user;
                state.role = payload.data.data.user.role;
                if (state.role === RoleType.ORGANIZATION) {
                    state.organizationId = payload.data.data.user._id;
                } else {
                    state.organizationId = payload.data.data.organizationId;
                }
            })
            .addCase(SetNewPassword.fulfilled, (state, { payload }) => {
                state.is_Login = false;
                state.role = payload.response.data.data.role;
                // state.token = payload.response.data.data.token;
                state.userId = payload.response.data.data._id;
            })
            .addCase(GetSettings.fulfilled, (state, { payload }) => {
                state.pinStandBy =
                    payload.data.data?.staffOnboarding?.pinStandBy;
            })
            .addCase(LogoutUser.fulfilled, (state) => {
                return initialState;
            });
    },
});

export const {
    logout,
    SetForgetEmail,
    SetOrganizationId,
    ClearOrganizationId,
    ClearForgetEmail,
    SetOtpVerificationCode,
    ClearOtpVerificationCode,
} = authSlice.actions;

export default authSlice.reducer;
