import { createSlice, PayloadAction, createAction } from '@reduxjs/toolkit';
import {
    GetReconsiliation,
    OrderInvoiceDetails,
    OrderInvoiceList,
    PosReturnEligible,
    markOrderAsPaid,
} from '../actions/purchased-action';

export const ClearPosReturnList = createAction(
    'purchase_store/ClearPosReturnList'
);

interface PurchasedState {
    orderInvoiceList: [];
    orderInvoiceCount: number;
    orderInvoiceDetail: any;
    posPuchasedData: any;
    selectedIds: [];
    selectedData: [];
    calculation: {
        discount: number;
        returnDiscount: number;
        gst: number;
        subTotal: number;
        total: number;
        promoCode: string;
    };
    markedOrderAsPaid: any;
    reconciliationData: any;
    posReturnEligibleData?: any;
}

const initialState: PurchasedState = {
    orderInvoiceList: [],
    orderInvoiceCount: 0,
    orderInvoiceDetail: {},
    posPuchasedData: {},
    selectedIds: [],
    selectedData: [],
    calculation: {
        discount: 0,
        returnDiscount: 0,
        gst: 0,
        subTotal: 0,
        total: 0,
        promoCode: '',
    },
    markedOrderAsPaid: {},
    reconciliationData: {},
    posReturnEligibleData: {},
};

const purchasedSlice = createSlice({
    name: 'purchase_store',
    initialState,
    reducers: {
        SetPosPurchasedData: (state, { payload }) => {
            state.posPuchasedData = payload;
            // console.log(
            //     'payloadSetPosPurchasedDataSetPosPurchasedDataSetPosPurchasedData',
            //     payload
            // );
        },
        ClearPosPurchasedData: (state) => {
            state.posPuchasedData = {};
        },
        UpdatePosPurchasedData: (state, { payload }) => {
            state.posPuchasedData = { ...state.posPuchasedData, ...payload };
        },
    },
    extraReducers: (builder) => {
        builder.addCase(
            OrderInvoiceList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.orderInvoiceList = payload?.data?.data;
                state.orderInvoiceCount = payload?.data?.totalCount;
            }
        );
        builder.addCase(
            OrderInvoiceDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.orderInvoiceDetail = payload?.data?.data;
            }
        );
        builder.addCase(
            markOrderAsPaid.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.markedOrderAsPaid = payload?.data?.data;
            }
        );
        builder.addCase(
            GetReconsiliation.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload in update-------------', payload);
                state.reconciliationData = payload?.data?.data;
            }
        );
        builder.addCase(
            PosReturnEligible.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                // console.log('Payload in update-------------', payload);
                state.posReturnEligibleData = payload?.data?.data;
            }
        );
        builder.addCase(ClearPosReturnList, (state) => {
            state.posReturnEligibleData = [];
        });
    },
});

export const {
    SetPosPurchasedData,
    ClearPosPurchasedData,
    UpdatePosPurchasedData,
} = purchasedSlice.actions;

export default purchasedSlice.reducer;
