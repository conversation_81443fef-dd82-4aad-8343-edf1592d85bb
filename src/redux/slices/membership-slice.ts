import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    CreateMembership, MembershipList, getMembershipById
} from '~/redux/actions/membership-action';

interface Membership {
    _id?: string;
    name: string;
    status: boolean;
    prefix: string;
    counter: number;
}
interface InitialState {
    membershipList: Membership[];
    membershipDetails: Membership | null;
    membershipCount: number;
    loading: boolean;
    error: string | null;
    listingLoading: boolean;
}

const initialState: InitialState = {
    membershipList: [],
    membershipDetails: null,
    membershipCount: 0,
    loading: false,
    error: null,
    listingLoading: false,
};
const membershipSlice = createSlice({
    name: 'membership',
    initialState,
    reducers: {
        resetMembershipState: () => initialState,
        clearMembershipDetails: (state) => {
            state.membershipDetails = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(CreateMembership.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(
                CreateMembership.fulfilled,
                (state, action: PayloadAction<Membership>) => {
                    state.loading = false;
                    state.error = null;
                }
            )
            .addCase(CreateMembership.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message || 'Something went wrong';
            })
            .addCase(MembershipList.pending, (state) => {
                state.listingLoading = true;
                state.error = null;
            })
            .addCase(
                MembershipList.fulfilled,
                (
                    state,
                    action: PayloadAction<{
                        membership: Membership[];
                        count: number;
                        data: any;
                    }>
                ) => {
                    state.listingLoading = false;
                    state.membershipList = action.payload.data;
                    state.membershipCount = action.payload.count;
                    state.error = null;
                }
            )
            .addCase(MembershipList.rejected, (state, action) => {
                state.listingLoading = false;
                state.error =
                    action.error.message || 'Failed to fetch Membership';
            })

            .addCase(
                getMembershipById.fulfilled,
                (state, { payload }: PayloadAction<any>) => {
                    state.membershipDetails = payload?.data?.data;
                }
            );

    }
})
export const { resetMembershipState, clearMembershipDetails } = membershipSlice.actions;
export default membershipSlice.reducer;