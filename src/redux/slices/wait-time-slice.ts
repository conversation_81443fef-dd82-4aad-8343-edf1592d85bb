import { createSlice } from '@reduxjs/toolkit';

interface WaitTimeState {
    clientOnboarding: any,
    staffOnboarding: any,
}

const initialState : WaitTimeState = {
    clientOnboarding: {},
    staffOnboarding: {},
};

const waitTimeSlice = createSlice({
    name: 'waitTimeSlice',
    initialState,
    reducers: {},
    extraReducers(builder) {
        // builder.addCase(SaveSettings.fulfilled, (state, action) => {});
        // builder.addCase(GetSettings.fulfilled, (state, { payload }) => {
        //     // console.log('Payload--------------', payload);
        //     state.clientOnboarding = payload?.data?.data?.clientOnboarding;
        //     state.staffOnboarding = payload?.data?.data?.staffOnboarding;
        // });
    },
});

export default waitTimeSlice.reducer;
