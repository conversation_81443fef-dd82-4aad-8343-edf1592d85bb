// actions/shareClientListAction.ts
import { createAsyncThunk } from '@reduxjs/toolkit';
import { FAMILY_SHARE_CLIENT_LIST, FAMILY_SHARE_PACKAGE } from '~/constants/api-constants';
import { getApi, postApi } from '~/services/api-services';
interface FamilyShareClientDto {
    purchaseId: string;
    userId: string;
    page?: number;
    pageSize?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
    search?: string;
}
interface SharePackagePayload {
    shareTo: string[];
    purchaseId: string;
}

export const FamilyShareClientList = createAsyncThunk(
    'package/shareClientList',
    async (params: FamilyShareClientDto, thunkAPI) => {
        try {
            const {
                purchaseId,
                userId,
                page = 1,
                pageSize = 10,
                orderBy = 'name',
                orderDirection = 'asc',
                search = '',
            } = params;

            const queryParams = new URLSearchParams({
                purchaseId,
                userId,
                page: page.toString(),
                pageSize: pageSize.toString(),
                orderBy,
                orderDirection,
                search,
            });

            const response = await getApi(`${FAMILY_SHARE_CLIENT_LIST}?${queryParams.toString()}`);

            return response.data;
        } catch (error: any) {
            return thunkAPI.rejectWithValue(
                error.response?.data?.message || 'Failed to fetch client list'
            );
        }
    }
);
export const SharePackageToUsers = createAsyncThunk(
    'package/shareToUsers',
    async (payload: SharePackagePayload, thunkAPI) => {
        try {
            const response = await postApi(FAMILY_SHARE_PACKAGE, payload);
            return response.data;
        } catch (error: any) {
            console.log(error)
            return thunkAPI.rejectWithValue(
                error.response?.data?.message || 'Failed to share package'
            );
        }
    }
);