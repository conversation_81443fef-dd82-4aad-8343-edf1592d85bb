import { createAsyncThunk } from '@reduxjs/toolkit';
import { REVENUE_CATEGORY } from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { deleteApi, patchApi, postApi } from '~/services/api-services';

// Create Revenue Category
export const CreateRevenueCategory: any = createAsyncThunk(
    'create-revenue-category',
    async (reqData: any) => {
        try {
            const response = await postApi(
                `${REVENUE_CATEGORY}/create`,
                reqData
            );
            Alertify.success('Revenue Category created successfully');
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// Revenue Category list
export const RevenueCategoryList: any = createAsyncThunk(
    'revenue-category-list',
    async ({ page, pageSize, search }: any) => {
        try {
            const response = await postApi(`${REVENUE_CATEGORY}/list`, {
                page,
                pageSize,
                search,
            });
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// Update Revenue Category
export const UpdateRevenueCategory: any = createAsyncThunk(
    'update-revenue-category',
    async (reqData: any) => {
        try {
            const { id, ...restData } = reqData;
            const response = await patchApi(
                `${REVENUE_CATEGORY}/${id}/update`,
                restData
            );
            Alertify.success('Revenue Category updated successfully');
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// Delete Revenue Category
export const DeleteRevenueCategory: any = createAsyncThunk(
    'delete-revenue-category',
    async (reqData: any) => {
        try {
            const { id } = reqData;
            const response = await deleteApi(
                `${REVENUE_CATEGORY}/${id}/delete`
            );
            Alertify.success('Revenue Category deleted successfully');
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// Change Revenue Category status
export const ChangeRevenueCategoryStatus: any = createAsyncThunk(
    'change-revenue-category-status',
    async (reqData: any) => {
        try {
            const { id, ...restData } = reqData;
            const response = await patchApi(
                `${REVENUE_CATEGORY}/${id}/status`,
                restData
            );
            Alertify.success('Status changed successfully');
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
