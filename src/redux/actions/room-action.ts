import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    CREATE_ROOM,
    ROOM_LIST,
    ROOM_DETAIL,
    UPDATE_ROOM,
    UPDATE_ROOM_STATUS,
    ROOM_LIST_BY_SERVICE_CATEGORY,
    ROOM_LIST_BY_FACILITY_ID,
    ROOM_LIST_BY_SCHEDULING
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { deleteApi, getApi, patchApi, postApi } from '~/services/api-services';

interface roomListParams {
    page: number;
    pageSize: number;
    search?: string;
    classType?: any;
    status?: boolean;
    facilityId?: string;
}

export const roomListing: any = createAsyncThunk(
    'Room-list',
    async ({
        page = 1,
        pageSize = 10,
        search = undefined,
        classType,
        status,
        facilityId,
    }: roomListParams) => {
        try {
            const response = await postApi(ROOM_LIST, {
                page,
                pageSize,
                search,
                classType,
                status,
                facilityId,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const roomListingByServiceCategory: any = createAsyncThunk(
    'roomListingByServiceCategory',
    async (reqData) => {
        try {
            const response = await postApi(
                ROOM_LIST_BY_SERVICE_CATEGORY,
                reqData
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const roomListingByFacilityId: any = createAsyncThunk(
    'roomListingByFacilityId',
    async (reqData) => {
        try {
            const response = await postApi(ROOM_LIST_BY_FACILITY_ID, reqData);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const createRoom: any = createAsyncThunk(
    'createRoom',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(CREATE_ROOM, reqData);
            // dispatch(AmenitiesList({ page: 1, pageSize: 10 }));
            Alertify.success('Room created successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const getRoomDetails = createAsyncThunk(
    'getRoomDetails',
    async (roomId: any, { rejectWithValue }) => {
        try {
            const response = await getApi(`${ROOM_DETAIL}/${roomId.roomId}`);
            return response.data;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch Room detail'
            );
        }
    }
);
export const updateRoomDetails = createAsyncThunk(
    'updateRoom',
    async ({ reqData, roomId }: any, { dispatch, rejectWithValue }) => {
        try {
            const response = await patchApi(`${UPDATE_ROOM}/${roomId}`, {
                ...reqData,
            });
            Alertify.success('Room updated successfully');
            return response;
        } catch (error: any) {
            return rejectWithValue(
                error.response.data || 'Failed To update the Room'
            );
        }
    }
);
export const deleteRoom = createAsyncThunk(
    'Delete Room',
    async (id: string, { dispatch, rejectWithValue }) => {
        try {
            await deleteApi(`${ROOM_DETAIL}/${id}`);
            Alertify.success('Room deleted successfully');
            dispatch(roomListing({ page: 1, pageSize: 10 }));
            return id;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error.response?.data || 'Failed to delete Room'
            );
        }
    }
);
export const updateRoomStatus = createAsyncThunk(
    'Update status',
    async ({ isActive, roomId }: any, { dispatch, rejectWithValue }) => {
        try {
            const response = await patchApi(`${UPDATE_ROOM_STATUS}/${roomId}`, {
                isActive,
            });
            dispatch(roomListing({ page: 1, pageSize: 10 }));
            Alertify.success('Room  Status updated successfully');
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error.response?.data ||
                    'Failed to update the status of the Room'
            );
        }
    }
);

export const roomListingByScheduling: any = createAsyncThunk(
    'roomListingByScheduling',
    async (reqData) => {
        try {
            const response = await postApi(
                ROOM_LIST_BY_SCHEDULING,
                reqData
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
