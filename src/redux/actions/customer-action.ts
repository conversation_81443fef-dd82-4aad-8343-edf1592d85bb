import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    ADMIN_CUSTOMER_LIST,
    CLIENTS_DETAILS,
    CLIENTS_LIST,
    CREATE_CLIENTS,
    DELETE_CLIENT,
    SHARE_PASS_List,
    SHARE_PASS_TO_OTHER,
    UPDATE_BASICASSESSMENT,
    UPDATE_CLIENT_STATUS,
    UPDATE_CLIENTS,
    UPDATE_POLICIES,
    CLIENTS_LIST_v1,
    CLIENT_SCHEDULING_LIST,
    MUTLIPLE_SHARE_PASS,
    INSTANT_CHECK_IN_ORDER_PAGE,
    INSTANT_SINGLE_CHECK_IN,
    CLIENTS_UPLOAD_DOCUMENT,
    CLIENTS_DOCUMENTS_LISTING,
    CLIENT_CREATE_DOCUMENT,
    CLIENT_DELETE_DOCUMENT,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import {
    deleteApi,
    getApi,
    patchApi,
    postApi,
    putApi,
} from '~/services/api-services';

/*----------------Customer List ---------------- */

interface CustomerListParams {
    page: number;
    pageSize: number;
    search: string;
    locationId: string[];
    isForFilter: boolean;
    isActive: boolean;
    notIncludedClientId?: string;
    isParent?: boolean;
    includeUserIds?: string[];
}
interface CustomerListParamsV1 {
    page: number;
    pageSize: number;
    search: string;
    facilityIds: string[];
    isActive: boolean;
    clientId: string;
}

export const CustomerList: any = createAsyncThunk(
    'customer-list',
    async (
        {
            page,
            pageSize,
            search,
            locationId,
            isActive,
            notIncludedClientId,
            isParent,
            includeUserIds,
        }: CustomerListParams,
        { getState }
    ) => {
        try {
            const response = await postApi(CLIENTS_LIST, {
                page,
                pageSize,
                search,
                locationId,
                isActive,
                notIncludedClientId,
                isParent,
                includeUserIds,
            });
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create  Client ------------------ */

export const CreateClient: any = createAsyncThunk(
    'CreateClient',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(CREATE_CLIENTS, reqData);
            dispatch(CustomerList({ page: 1, pageSize: 10 }));
            Alertify.success('Client created successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create  Client ------------------ */

export const DeleteClient: any = createAsyncThunk(
    'DeleteClient',
    async (clientId: string) => {
        try {
            const response = await deleteApi(`${DELETE_CLIENT}/${clientId}`);
            Alertify.success('Client deleted successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const DeleteClientDocument: any = createAsyncThunk(
    'DeleteClientDocument',
    async (docId: string) => {
        try {
            const response = await deleteApi(
                `${CLIENT_DELETE_DOCUMENT}/${docId}`
            );
            Alertify.success('Client deleted successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Clients Details ------------------ */

export const ClientsDetails: any = createAsyncThunk(
    'ClientsDetails',
    async ({ clientId }: any) => {
        try {
            // console.log(clientId, 'client Id');
            const response = await getApi(`${CLIENTS_DETAILS}/${clientId}`);
            return response;
        } catch (error) {
            console.log('Error fetch client detail API', error);
            return Promise.reject(error);
        }
    }
);

export const ClientsDocumentListing: any = createAsyncThunk(
    'ClientsDocumentListing',
    async ({ userId }: any) => {
        try {
            // console.log(clientId, 'client Id');
            const response = await getApi(
                `${CLIENTS_DOCUMENTS_LISTING}/${userId}`
            );
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

/*------------------- Clients Status Update ------------------ */

export const ClientStatusUpdate: any = createAsyncThunk(
    'ClientStatusUpdate',
    async ({ clientId, isActive, page, pageSize }: any, { dispatch }) => {
        try {
            const response = await putApi(
                `${UPDATE_CLIENT_STATUS}/${clientId}`,
                {
                    isActive,
                }
            );
            dispatch(CustomerList({ page, pageSize }));
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update Facility ------------------ */

export const UpdateClient: any = createAsyncThunk(
    'UpdateClient',
    async ({ reqData, clientId }: any, { dispatch }) => {
        try {
            const response = await patchApi(`${UPDATE_CLIENTS}/${clientId}`, {
                ...reqData,
            });
            dispatch(CustomerList({ page: 1, pageSize: 10 }));
            Alertify.success('Client updated successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update basicAssessment ------------------ */

export const UpdateBasicAssessment: any = createAsyncThunk(
    'UpdateBasicAssessment',
    async ({ reqData, clientId }: any, { dispatch }) => {
        try {
            const response = await patchApi(
                `${UPDATE_BASICASSESSMENT}/${clientId}`,
                {
                    ...reqData,
                }
            );
            Alertify.success('Client basic assessment updated successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update policies ------------------ */

export const UpdatePolicies: any = createAsyncThunk(
    'UpdatePolicies',
    async ({ reqData, clientId }: any, { dispatch }) => {
        try {
            const response = await patchApi(`${UPDATE_POLICIES}/${clientId}`, {
                ...reqData,
            });
            Alertify.success('Client policies updated successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Share Pass ------------------ */

export const sharePassToOther: any = createAsyncThunk(
    'sharePassToOther',
    async ({ payload }: any, { dispatch, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await patchApi(`${SHARE_PASS_TO_OTHER}`, {
                ...payload,
                organizationId,
            });
            Alertify.success(
                `${payload?.noOfSessions} sessions shared successfully`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const SharedPassList: any = createAsyncThunk(
    'sharedPassList',
    async ({ userId, page, pageSize, search }: any) => {
        try {
            const response = await postApi(`${SHARE_PASS_List}`, {
                userId,
                page,
                pageSize,
                search,
            });
            return response;
        } catch (error: any) {
            console.error(error);
        }
    }
);
export const ClientUploadDocument: any = createAsyncThunk(
    'ClientUploadDocument',
    async ({ file }: any) => {
        try {
            const formData = new FormData();
            formData.append('file', file);
            // formData.append('documentName', documentName);

            const response = await postApi(
                `${CLIENTS_UPLOAD_DOCUMENT}`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                }
            );
            return response;
        } catch (error: any) {
            console.error(error);
        }
    }
);
export const customerListV1: any = createAsyncThunk(
    'client-list',
    async (
        { page, pageSize, search, facilityIds, clientId }: CustomerListParamsV1,
        { dispatch, getState }: any
    ) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(CLIENTS_LIST_v1, {
                page,
                pageSize,
                search,
                facilityIds,
                organizationId,
                clientId,
            });
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

/*------------------- Client's Booking ------------------ */

export const clientBookingList: any = createAsyncThunk(
    'clientBookingList',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(CLIENT_SCHEDULING_LIST, reqData);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Client's Multiple Share Pass ------------------ */

export const multipleSharePassTransfer: any = createAsyncThunk(
    'multipleSharePassTransfer',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(MUTLIPLE_SHARE_PASS, reqData);
            Alertify.success('Transfered and Check-In successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Client's Multi Check _IN ------------------ */

export const multipleCheckInOrder: any = createAsyncThunk(
    'multipleCheckInOrder',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(
                INSTANT_CHECK_IN_ORDER_PAGE,
                reqData
            );
            Alertify.success('Checked In successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Client's Multi Check _IN ------------------ */

export const singleCheckInOrder: any = createAsyncThunk(
    'singleCheckInOrder',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(INSTANT_SINGLE_CHECK_IN, reqData);
            Alertify.success('Checked In successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const ClientCreateDocument: any = createAsyncThunk(
    'ClientCreateDocument',
    async ({ createDocumentPayload }: any) => {
        try {
            const response = await postApi(
                `${CLIENT_CREATE_DOCUMENT}`, // You'll need to add this constant to your api-constants
                createDocumentPayload
            );

            return response;
        } catch (error: any) {
            console.error('Create document error:', error);
            throw error;
        }
    }
);
