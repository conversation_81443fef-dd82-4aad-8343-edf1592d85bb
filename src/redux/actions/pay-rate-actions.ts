import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    ALL_CLASS_TYPES,
    ALL_SERVICE_CATEGORIES,
    CREATE_PAY_RATE,
    PAY_RATE,
    PAY_RATE_LIST,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { deleteApi, getApi, handleApiError, postApi, putApi } from '~/services/api-services';

export const getAllPayRates = createAsyncThunk(
    'payRate/getAllPayRates',
    async (payload: any, { rejectWithValue }) => {
        try {
            const response = await postApi(`${PAY_RATE_LIST}`, payload);
            return response.data;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch  specializations'
            );
        }
    }
);

export const getPayRateDetails = createAsyncThunk(
    'payRate/getPayRateDetails',
    async (id: string, { rejectWithValue }) => {
        try {
            const response = await getApi(`${PAY_RATE}/${id}`);
            return response.data;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch specialization detail'
            );
        }
    }
);

export const createPayRate = createAsyncThunk(
    'payRate/createPayRate',
    async (payload: any, { rejectWithValue, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(`${CREATE_PAY_RATE}`, {
                ...payload,
                organizationId,
            });
            Alertify.success('Specialization created successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const updatePayRate = createAsyncThunk(
    'payRate/updatePayRate',
    async (
        { id, payload }: { id: string; payload: any },
        { rejectWithValue }
    ) => {
        try {
            const response = await putApi(`${PAY_RATE}/${id}`, payload);
            Alertify.success('Specialization updated successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const deletePayRate = createAsyncThunk(
    'payRate/deletePayRate',
    async (id: string, { rejectWithValue }) => {
        try {
            await deleteApi(`${PAY_RATE}/${id}`);
            Alertify.success('Specialization deleted successfully');
            return id;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const getAllClassTypeByStaffId = createAsyncThunk(
    'payRate/getAllClassTypeByStaffId',
    async ({ staffId }: any, { rejectWithValue }) => {
        try {
            const response = await getApi(`${ALL_CLASS_TYPES}/${staffId}`);
            return response.data;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch specialization detail'
            );
        }
    }
);

export const getAllServiceCategories = createAsyncThunk(
    'payRate/getAllServiceCategories',
    async ({ staffId, data = {} }: any, { rejectWithValue }) => {
        try {
            const response = await postApi(
                `${ALL_SERVICE_CATEGORIES}/${staffId}`,
                data
            );
            return response.data;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch specialization detail'
            );
        }
    }
);
