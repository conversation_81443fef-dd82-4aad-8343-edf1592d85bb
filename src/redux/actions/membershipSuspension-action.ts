import { createAsyncThunk, isRejectedWithValue } from '@reduxjs/toolkit';
import {
    MEMBERSHIP_SUSPENSION,
    MEMBERSHIP_SUSPENSION_REVOKE,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { postApi, handleApiError } from '~/services/api-services';

/* Membership Suspension */
export const MembershipSuspension: any = createAsyncThunk(
    'Membership suspension',
    async ({ payload, isPackage }: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(MEMBERSHIP_SUSPENSION, {
                ...payload,
            });

            Alertify.success(
                `${isPackage ? 'Package' : 'Membership'} Frozen successfully`
            );
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);
export const revokeMembershipSuspension: any = createAsyncThunk(
    'RevokeMembership Suspension',
    async ({ payload, isPackage }: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(MEMBERSHIP_SUSPENSION_REVOKE, {
                ...payload,
            });

            Alertify.success(
                `${isPackage ? 'Package' : 'Membership'} unfreezed successfully`
            );
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);
