import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    ATTRIBUTE_DETAILS,
    ATTRIBUTE_LIST,
    ATTRIBUTE_TYPE_LIST,
    CREATE_ATTRIBUTE,
    DELETE_ATTRIBUTE,
    PARENT_ATTRIBUTE_LIST,
    UPDATE_ATTRIBUTE,
    UPDATE_ATTRIBUTE_STATUS,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { deleteApi, getApi, patchApi, postApi } from '~/services/api-services';

/*----------------Customer List ---------------- */

interface AttributeListParams {
    page?: number;
    pageSize?: number;
    search?: string;
    attributeType?: string;
}

export const AttributeList: any = createAsyncThunk(
    'attribute-list',
    async ({ page, pageSize, search }: AttributeListParams) => {
        try {
            const response = await postApi(ATTRIBUTE_LIST, {
                page,
                pageSize,
                search,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*---------------------- Attribute Type List ----------------------- */

export const AttributeTypeList: any = createAsyncThunk(
    'attribute-type-list',
    async ({ page, pageSize, search, attributeType }: AttributeListParams) => {
        try {
            const response = await postApi(ATTRIBUTE_TYPE_LIST, {
                attributeType,
                // page,
                // pageSize,
                search,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const AttributeTypeListForDropdown: any = createAsyncThunk(
    'attribute-type-list-for-dropdown',
    async ({ page, pageSize, search, attributeType }: AttributeListParams) => {
        try {
            const response = await postApi(ATTRIBUTE_TYPE_LIST, {
                attributeType,
                // page,
                // pageSize,
                search,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create Attribute ------------------ */

export const CreateAttributeData: any = createAsyncThunk(
    'CreateAttributeData',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(CREATE_ATTRIBUTE, reqData);
            dispatch(AttributeList({ page: 1, pageSize: 10 }));
            Alertify.success('Attribute created successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Attribye Details ------------------ */

export const AttributeDetails: any = createAsyncThunk(
    'AttributeDetails',
    async ({ attributeId }: any) => {
        try {
            const response = await getApi(
                `${ATTRIBUTE_DETAILS}/${attributeId}`
            );
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

/*------------------- Attribye Details ------------------ */

export const AttributeParentList: any = createAsyncThunk(
    'AttributeParentList',
    async () => {
        try {
            const response = await getApi(`${PARENT_ATTRIBUTE_LIST}`);
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update Attribute ------------------ */

export const UpdateAttributeData: any = createAsyncThunk(
    'UpdateAttributeData',
    async ({ reqData, attributeId }: any, { dispatch }) => {
        try {
            const response = await patchApi(
                `${UPDATE_ATTRIBUTE}/${attributeId}`,
                {
                    ...reqData,
                }
            );
            dispatch(AttributeList({ page: 1, pageSize: 10 }));
            Alertify.success('Attribute updated successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update Attribute Status------------------ */

export const UpdateAttributeStatus: any = createAsyncThunk(
    'UpdateAttributeStatus',
    async ({ reqData, attributeId }: any) => {
        try {
            const response = await patchApi(
                `${UPDATE_ATTRIBUTE_STATUS}/${attributeId}`,
                {
                    ...reqData,
                }
            );
            Alertify.success(
                `Attribute ${
                    !reqData.isActive ? 'deactivated' : 'activated'
                } successfully`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Delete Attribute ------------------ */

export const DeleteAttributeData: any = createAsyncThunk(
    'DeleteAttributeData',
    async ({ attributeId }: any, { dispatch }) => {
        try {
            const response = await deleteApi(
                `${DELETE_ATTRIBUTE}/${attributeId}`
            );
            Alertify.success('Attribute deleted successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
