import { createAsyncThunk } from '@reduxjs/toolkit';
import { GET_SETTINGS, SAVE_SETTINGS } from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { getApi, patchApi } from '~/services/api-services';

export const SaveSettings: any = createAsyncThunk(
    'common/SaveSettings',
    async ({ state }: any) => {
        try {
            const response = await patchApi(`${SAVE_SETTINGS}`, {
                ...state,
            });
            Alertify.success('Settings saved successfully');
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

export const GetSettings: any = createAsyncThunk(
    'common/GetSettings',
    async (_, { getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await getApi(`${GET_SETTINGS}/${organizationId}`);
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

type SummarizedObject = {
    [key: string]: boolean;
};

type NestedObject = {
    [key: string]: boolean | NestedObject;
};

export function summarizeNestedObject(
    obj: NestedObject | null | undefined
): SummarizedObject {
    const result: SummarizedObject = {};
    if (!obj || typeof obj !== 'object') {
        return result;
    }

    for (const [key, value] of Object.entries(obj)) {
        if (key === '_id') continue;

        if (typeof value === 'boolean') {
            result[key] = value;
        } else if (Array.isArray(value)) {
            // Skip arrays like proficiencyLevel
            continue;
        } else if (typeof value === 'object' && value !== null) {
            if (key === 'policies' && Array.isArray(value.items)) {
                result[key] = value.items.some((item: any) => item.isShown === true);
            } else {
                result[key] = Object.values(value).some((v) => v === true);
            }
        }
    }

    return result;
}

