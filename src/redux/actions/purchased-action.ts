import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    CREATE_PAY_RATE,
    CREATE_RECONCILIATION,
    DOWNLOAD_INVOICE_DETAILS,
    GET_RECONCILIATION,
    ORDER_DETAILS_BY_ID,
    ORDER_LIST,
    ORDER_LIST_EXPORT,
    UPDATE_ORDER_PAYMNET_STATUS,
    INVOICE_CANCEL,
    CART_VALIDATE,
    POS_RETURN_ELIGIBLE_LIST,
    VOUCHER,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { getApi, patchApi, postApi } from '~/services/api-services';

/*------------------- Order iNvoive List ------------------ */

export const OrderInvoiceList: any = createAsyncThunk(
    'OrderInvoiceList',
    async (reqData: any) => {
        try {
            const response = await postApi(ORDER_LIST, reqData);
            return response;
        } catch (error: any) {
            console.log('Error CreatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Order iNvoive Details ------------------ */
export const OrderInvoiceDetails: any = createAsyncThunk(
    'OrderInvoiceDetails',
    async ({ orderId }: any) => {
        try {
            const response = await getApi(`${ORDER_DETAILS_BY_ID}/${orderId}`);
            return response;
        } catch (error: any) {
            console.log('Error CreatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Order iNvoive Details ------------------ */

export const DownloadInvoice = createAsyncThunk(
    'DownloadInvoice',
    async ({ orderId }: any) => {
        try {
            const response = await getApi(
                `${DOWNLOAD_INVOICE_DETAILS}/${orderId}/download`,
                {},
                {
                    responseType: 'blob',
                    headers: {
                        Accept: 'application/pdf',
                    },
                }
            );

            return response.data;
        } catch (error: any) {
            console.log('Error in DownloadInvoice API:', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/* Update the order paymant status mark as paid */
export const markOrderAsPaid: any = createAsyncThunk(
    'updatePaymentStatus',
    async (payload: any) => {
        try {
            const response = await patchApi(
                `${UPDATE_ORDER_PAYMNET_STATUS}`,
                payload
            );
            Alertify.success(response.data.message);
            return response;
        } catch (error: any) {
            console.log(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Order Export ------------------ */

export const orderExport: any = createAsyncThunk(
    'orderExport',
    async (payload: any) => {
        try {
            const response = await postApi(`${ORDER_LIST_EXPORT}`, payload);
            Alertify.success('Exported Successfully');
            return response;
        } catch (error: any) {
            console.log(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Cart Validate ------------------ */

export const cartValidate: any = createAsyncThunk(
    'cartValidate',
    async (payload: any, { rejectWithValue }) => {
        try {
            const response = await postApi(`${CART_VALIDATE}`, payload);
            return response.data;
        } catch (error: any) {
            console.log(error);
            Alertify.error(
                error?.data?.validationErrors?.[0] ||
                    error.message ||
                    'Something went wrong'
            );
            return rejectWithValue(error);
        }
    }
);

/*------------------- Reconciliation List ------------------ */

export const GetReconsiliation: any = createAsyncThunk(
    'GetReconsiliation',
    async (payload: any) => {
        try {
            const response = await postApi(`${GET_RECONCILIATION}`, payload);
            return response;
        } catch (error: any) {
            console.log(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create Reconciliation ------------------ */

export const CreateReconsiliation: any = createAsyncThunk(
    'CreateReconsiliation',
    async (payload: any) => {
        try {
            const response = await postApi(`${CREATE_RECONCILIATION}`, payload);
            return response;
        } catch (error: any) {
            console.log(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const cancelOrRefundOrder: any = createAsyncThunk(
    'updatePaymentStatus',
    async (payload: any) => {
        try {
            const response = await patchApi(`${INVOICE_CANCEL}`, payload);
            Alertify.success(response.data.message);
            return response;
        } catch (error: any) {
            console.log(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- return pos ------------------ */

export const PosReturnEligible: any = createAsyncThunk(
    'PosReturnEligible',
    async ({ userId, page, pageSize }: any) => {
        try {
            const response = await postApi(
                `${POS_RETURN_ELIGIBLE_LIST}/${userId}`,
                {
                    page,
                    pageSize,
                }
            );

            return response;
        } catch (error: any) {
            console.log(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const getUsersVoucher = createAsyncThunk(
    'getUsersVoucher',
    async (payload: any) => {
        try {
            return await getApi(`${VOUCHER}/${payload.userId}/list`, payload);
        } catch (error: any) {
            console.log(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
