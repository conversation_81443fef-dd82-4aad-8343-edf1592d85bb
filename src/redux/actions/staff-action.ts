import { createAsyncThunk } from '@reduxjs/toolkit';
import dayjs from 'dayjs';
import {
    COMPLETE_STAFF_PROFILE,
    CREATE_STAFF_ADD_AVAILABILITY,
    GENERATE_PIN,
    GET_STAFF_DETAILS,
    GET_STAFF_LISTS,
    REGISTER_STAFF,
    EMAIL_OR_MOBILE_VALIDATION,
    STAFF_AVAILABILITY_UPDATE_DETAILS,
    UPDATE_STAFF_PROFILE,
    UPDATE_STAFF_STATUS,
    STAFF_PERMISSIONS,
    STAFF_PIN_SET,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import {
    getApi,
    handleApiError,
    patchApi,
    postApi,
    putApi,
} from '~/services/api-services';
import { RoleType, Staff_Roles } from '~/types/enums';
import { StaffAvailabilityList } from './appointment-action';

export const GetStaffList = createAsyncThunk(
    'staff/GetStaffList',
    async (
        {
            page = 1,
            pageSize = 10,
            search = '',
            role = undefined,
            locationId = undefined,
            forDropdown = false,
        }: // isActive = true,
        any,
        { dispatch, getState, rejectWithValue }
    ) => {
        try {
            const res = await postApi(GET_STAFF_LISTS, {
                page,
                pageSize,
                search,
                role: role?.length > 0 ? role : undefined,
                locationId: locationId?.length > 0 ? locationId : undefined,
                // isActive,
            });
            return { res, page, pageSize, forDropdown };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const CreateStaff: any = createAsyncThunk(
    'staffs/CreateStaff',
    async ({ fields }: any, { rejectWithValue, dispatch, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(REGISTER_STAFF, {
                ...fields,
                organizationId,
            });
            dispatch(GetStaffList({ page: 1, pageSize: 10 }));
            Alertify.success('Staff created successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const GetStaffProfileDetails: any = createAsyncThunk(
    'staffs/GetStaffProfileDetails',
    async ({ id }: any, { rejectWithValue }: any) => {
        try {
            const response = await getApi(`${GET_STAFF_DETAILS}/${id}`);
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const CompleteStaffProfile = createAsyncThunk(
    'staff/CompleteStaffProfile',
    async ({ fields }: any, { rejectWithValue }) => {
        try {
            const res = await postApi(CompleteStaffProfile, { ...fields });
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const SetStaffPin = createAsyncThunk(
    'staff/SetStaffPin',
    async (fields: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await postApi(STAFF_PIN_SET, { ...fields });
            Alertify.success('Pin Updated Successfully');
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const UpdateStaffProfile = createAsyncThunk(
    'staff/UpdateStaffProfile',
    async ({ id, fields }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await putApi(`${UPDATE_STAFF_PROFILE}/${id}`, {
                ...fields,
            });
            Alertify.success('Staff Profile Updated Successfully');
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const UpdateStaffStatus = createAsyncThunk(
    'staff/UpdateStaffProfile',
    async ({ reqData, id }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await putApi(`${UPDATE_STAFF_STATUS}/${id}`, {
                isActive: reqData.status,
            });
            Alertify.success('Staff Status Updated Successfully');
            return res.data;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export function __ManipulateStaffList(data: any) {
    return data.map((item: any) => {
        return {
            ...item,
            fullName: `${item.firstName} ${item.lastName}`,
            facilityName: item.facilityNames,
            phoneNumber: item.mobile,
            status: item.isActive,
            role: Staff_Roles[item.role],
            staffRole: item.role,
        };
    });
}

export const GeneratePin = createAsyncThunk(
    'GeneratePin',
    async ({ userId }: any, { dispatch, getState, rejectWithValue }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const res = await postApi(GENERATE_PIN, { organizationId, userId });
            Alertify.success('Pin generated successfully');
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const EmailOrMobileValidation = createAsyncThunk(
    'GeneratePin',
    async ({ value, id }: any, { rejectWithValue }: any) => {
        try {
            const res = await postApi(EMAIL_OR_MOBILE_VALIDATION, {
                value,
                id,
            });
            return { res };
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/* --------------- Add Staff Availability ----------------- */

export const AddStaffAvailability: any = createAsyncThunk(
    'staffs/AddStaffAvailability',
    async (
        { payload, isStaffDetailsPage, view, date, isStaffDetails }: any,
        { rejectWithValue, dispatch, getState }: any
    ) => {
        console.log('isStaffDetails---------------', isStaffDetails);
        const { role, user } = getState().auth_store;
        const { facilityId } = getState().appointment_store;
        let startDate, endDate;

        if (view === 'day') {
            startDate = dayjs(date).startOf('day').toDate();
            endDate = dayjs(date).endOf('day').toDate();
        } else if (view === 'week') {
            startDate = dayjs(date).startOf('week').toDate();
            endDate = dayjs(date).endOf('week').toDate();
        } else {
            startDate = dayjs(date).startOf('day').toDate();
            endDate = dayjs(date).endOf('day').toDate();
        }

        const data = {
            startDate: startDate,
            endDate: endDate,
        } as any;

        if (role === RoleType.TRAINER && isStaffDetails) {
            data.userIds = [user?._id];
        }
        if (role === RoleType.TRAINER) {
            data.facilityIds = [facilityId];
        }

        try {
            // const { organizationId } = getState().auth_store;
            const response = await postApi(CREATE_STAFF_ADD_AVAILABILITY, {
                ...payload,
                // organizationId,
            });
            if (!isStaffDetailsPage) {
                dispatch(StaffAvailabilityList({ reqData: data }));
            }
            Alertify.success('Staff availability created successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/* --------------- update Staff Availability ----------------- */

export const UpdateStaffAvailability: any = createAsyncThunk(
    'staffs/UpdateStaffAvailability',
    async (
        { payload, view, date, isStaffDetails }: any,
        { rejectWithValue, dispatch, getState }: any
    ) => {
        const { role, user } = getState().auth_store;
        const { facilityId } = getState().appointment_store;
        let startDate, endDate;

        if (view === 'day') {
            startDate = dayjs(date).startOf('day').toDate();
            endDate = dayjs(date).endOf('day').toDate();
        } else if (view === 'week') {
            startDate = dayjs(date).startOf('week').toDate();
            endDate = dayjs(date).endOf('week').toDate();
        } else {
            startDate = dayjs(date).startOf('day').toDate();
            endDate = dayjs(date).endOf('day').toDate();
        }

        const data = {
            startDate: startDate,
            endDate: endDate,
        } as any;

        if (role === RoleType.TRAINER && isStaffDetails) {
            data.userIds = [user?._id];
        }
        if (role === RoleType.TRAINER) {
            data.facilityIds = [facilityId];
        }

        try {
            const response = await patchApi(STAFF_AVAILABILITY_UPDATE_DETAILS, {
                ...payload,
                // organizationId,
            });
            dispatch(StaffAvailabilityList({ reqData: data }));
            Alertify.success('Staff availability updated successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

// GET STAFF PERMISSIONS
export const GetStaffPermissions: any = createAsyncThunk(
    'get-staff-permissions',
    async (reqData: any) => {
        try {
            const { userId, ...restData } = reqData;
            const response: any = await postApi(
                `${STAFF_PERMISSIONS}${userId}/list`,
                restData
            );
            // console.log('GetStaffPermissions',response);
            return response;
        } catch (error: any) {
            console.log('Error GetStaffPermissions API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// UPDATE STAFF PERMISSIONS
export const UpdateStaffPermissions: any = createAsyncThunk(
    'update-staff-permissions',
    async (reqData: any) => {
        try {
            const { userId, ...restData } = reqData;
            const response: any = await postApi(
                `${STAFF_PERMISSIONS}${userId}/assign`,
                restData
            );
            console.log('UpdateStaffPermissions', response);
            return response;
        } catch (error: any) {
            console.log('Error UpdateStaffPermissions API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
