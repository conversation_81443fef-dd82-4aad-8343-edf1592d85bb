import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    CLASS_SCHEDULING_LISTING,
    CLASS_PARTICIPANT_LIST,
    CLASSES_CUSTOMER_LIST,
    UPDATE_CLASS_SCHEDULE,
    CLASS_SCHEDULING,
    CLASS_ENROLL_CUSTOMER,
    CREATE_CLASS_SCHEDULE,
    CANCEL_ENROLL_CUSTOMER,
    CLASS_CHECK_IN,
    CLASS_SCHEDULING_DETAILS,
    CLASS_SCHEDULING_CANCELLATION_DETAILS,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import {
    deleteApi,
    getApi,
    patchApi,
    postApi,
    putApi,
} from '~/services/api-services';

// Shared error handler
const handleThunkError = (error: any) => {
    console.error('API Error:', error);
    Alertify.error(error?.message || 'Something went wrong');
    return Promise.reject(error);
};

export const createClassScheduling = createAsyncThunk(
    'createClassScheduling',
    async (payload: any) => {
        try {
            return await postApi(CREATE_CLASS_SCHEDULE, payload);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classSchedulingList = createAsyncThunk(
    'classSchedulingList',
    async (payload: any) => {
        try {
            return await getApi(CLASS_SCHEDULING_LISTING, payload);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const updateClassScheduling = createAsyncThunk(
    'updateClassScheduling',
    async (payload: any) => {
        try {
            const response = await putApi(UPDATE_CLASS_SCHEDULE, payload);
            return response;
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classesCustomerList = createAsyncThunk(
    'classesCustomerList',
    async (payload: any) => {
        try {
            return await getApi(CLASSES_CUSTOMER_LIST, payload);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classesParticipantList = createAsyncThunk(
    'classesParticipantList',
    async (payload: any) => {
        try {
            return await getApi(CLASS_PARTICIPANT_LIST, payload);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classCancelScheduling = createAsyncThunk(
    'classCancelScheduling',
    async ({ schedulingId, payload }: any) => {
        try {
            const response = await patchApi(`${CLASS_SCHEDULING}/${schedulingId}/cancel`, payload);
            Alertify.success(`Class canceled successfully`);
            return response;
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classDeleteScheduling = createAsyncThunk(
    'classDeleteScheduling',
    async ({ schedulingId }: { schedulingId: string }) => {
        try {
            const response = await deleteApi(
                `${CLASS_SCHEDULING}/${schedulingId}/delete`
            );
            Alertify.success(`Class deleted successfully`);
            return response;
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classEnrollClient = createAsyncThunk(
    'classEnrollClient',
    async (payload: any) => {
        try {
            return await postApi(CLASS_ENROLL_CUSTOMER, payload);
        } catch (error) {
            console.error('API Error:', error);
            return error;
        }
    }
);

export const cancelEnrollClient = createAsyncThunk(
    'cancelEnrollClient',
    async (payload: any) => {
        try {
            const response = await postApi(CANCEL_ENROLL_CUSTOMER, payload);
            Alertify.success(`Client's enrollment deleted`);
            return response;
        } catch (error) {
            console.error('API Error:', error);
            return error;
        }
    }
);

export const getClassSchedulingDetails = createAsyncThunk(
    'getClassSchedulingDetails',
    async ({ schedulingId }: { schedulingId: string }) => {
        try {
            return await getApi(`${CLASS_SCHEDULING}/${schedulingId}/get`);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

/*-------------- Booked Scheduling Details Reccurring    -------------- */

export const BookedSchedulingDetailsRecurring: any = createAsyncThunk(
    'staffs/BookedSchedulingDetailsRecurring',
    async ({ scheduleId, dateRange, startDate, markType, endDate }: any) => {
        try {
            const response = await postApi(
                `${CLASS_SCHEDULING_DETAILS}/${scheduleId}`,
                { dateRange, startDate, markType, endDate }
            );
            return response;
        } catch (error: any) {
            return handleThunkError(error);
        }
    }
);
/*-------------- Booked Scheduling Cancellation Details    -------------- */

export const getSchedulingCancellationDetails: any = createAsyncThunk(
    'staffs/getSchedulingCancellationDetails',
    async (payload: any) => {
        try {
            const response = await postApi(`${CLASS_SCHEDULING_CANCELLATION_DETAILS}`, payload);
            return response;
        } catch (error: any) {
            return handleThunkError(error);
        }
    }
);

export const classCheckIn: any = createAsyncThunk(
    'classCheckIn',
    async (payload: any) => {
        try {
            const response = await patchApi(`${CLASS_CHECK_IN}`, payload);
            Alertify.success(
                `Client ${payload?.isCheckedIn ? 'checked-In' : 'mark un-arrived'
                } `
            );
            return response;
        } catch (error) {
            return handleThunkError(error);
        }
    }
);
