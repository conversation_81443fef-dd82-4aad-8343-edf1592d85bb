import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    CREATE_COUPON,
    COUPON_LIST,
    UPDATE_COUPON,
    VOUCHER,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import {
    deleteApi,
    getApi,
    patchApi,
    postApi,
    putApi,
} from '~/services/api-services';

// Get COUPONS LIST
export const CouponList: any = createAsyncThunk(
    'coupon-list',
    async (payload: any, { rejectWithValue }) => {
        try {
            return await getApi(`${COUPON_LIST}`, payload);
        } catch (error: any) {
            console.log('Error CouponList API', error);
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error?.response?.data || 'Something went wrong'
            );
        }
    }
);

export const CreateCoupon: any = createAsyncThunk(
    'create-coupon',
    async (payload: any, { rejectWithValue }) => {
        try {
            const response: any = await postApi(CREATE_COUPON, payload);
            return response;
        } catch (error: any) {
            console.log('Error CreateCoupon API', error);
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error?.response?.data || 'Something went wrong'
            );
        }
    }
);

// Get COUPONS details
export const CouponDetails: any = createAsyncThunk(
    'coupon-details',
    async (couponId: string, { rejectWithValue }) => {
        try {
            const response: any = await getApi(`${VOUCHER}/${couponId}/get`);
            return response;
        } catch (error: any) {
            console.log('Error CouponDetails API', error);
            return rejectWithValue(
                error?.response?.data || 'Something went wrong'
            );
        }
    }
);

// Update Coupon
export const UpdateCoupon: any = createAsyncThunk(
    'update-coupon',
    async (payload: any, { rejectWithValue }) => {
        try {
            const response: any = await patchApi(`${UPDATE_COUPON}`, payload);
            return response;
        } catch (error: any) {
            console.log('Error UpdateCoupon API', error);
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error?.response?.data || 'Something went wrong'
            );
        }
    }
);

// Update Coupon Status
export const UpdateCouponStatus: any = createAsyncThunk(
    'update-coupon-status',
    async (payload: any, { rejectWithValue }) => {
        try {
            const { couponId, ...restData } = payload;
            const response: any = await patchApi(
                `${VOUCHER}/${couponId}/status`,
                restData
            );
            console.log('UpdateCoupon', response);
            return response;
        } catch (error: any) {
            console.log('Error UpdateCouponStatus API', error);
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error?.response?.data || 'Something went wrong'
            );
        }
    }
);

// Delete Coupon
export const DeleteCoupon: any = createAsyncThunk(
    'delete-coupon',
    async (couponId: string, { rejectWithValue }) => {
        try {
            const response: any = await deleteApi(
                `${VOUCHER}/${couponId}/delete`
            );
            return response;
        } catch (error: any) {
            console.log('Error DeleteCoupon API', error);
            return rejectWithValue(
                error?.response?.data || 'Something went wrong'
            );
        }
    }
);

/* --------------------- Client's Voucher List. --------------------- */

export const clientVoucherList: any = createAsyncThunk(
    'coupon-clientVoucherList',
    async ({ userId, payload }: any, { rejectWithValue }) => {
        try {
            const response: any = await getApi(
                `${VOUCHER}/purchased/${userId}/list?page=${payload.page}&pageSize=${payload.pageSize}&orderBy=createdAt&orderDirection=desc&isActive=${payload.isActive}`
            );
            return response;
        } catch (error: any) {
            console.log('Error CouponDetails API', error);
            return rejectWithValue(
                error?.response?.data || 'Something went wrong'
            );
        }
    }
);
