import { createAsyncThunk } from '@reduxjs/toolkit';
import dayjs from 'dayjs';
import {
    CREATE_SCHEDULING,
    SCHEDULING_LIST,
    SCHEDULING_DETAILS,
    DELETE_SCHEDULING,
    UPDATE_SCHEDULING,
    SCHEDULING_STAFF_AVAILABILITY_LIST,
    CANCEL_SCHEDULING,
    CHECK_IN_SCHEDULING,
    PURCHASE_PRICING_LIST_BY_CLASS_TYPE,
    SCHEDULING_LIST_V1,
    CREATE_BOOKING_SCHEDULING,
    UPDATE_BOOKING_SCHEDULING,
    UPDATE_APPOINTMENT_SCHEDULING,
    CREATE_APPOINTMENT_SCHEDULING,
    SCHEDULING_DETAILS_RECURRING,
    MEMBERSHIP_LIST,
    QR_CODE_PURCHASE_DETAILS,
    SCHEDULING_LIST_EXPORT,
    EXPORT_CHECK_IN_HISTORY,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import {
    deleteApi,
    getApi,
    handleApiError,
    patchApi,
    postApi,
} from '~/services/api-services';

/*-------------- Staff Available Listing -------------- */

export const StaffAvailabilityList: any = createAsyncThunk(
    'staffs/StaffAvailabilityList',
    async ({ reqData }: any, { rejectWithValue, dispatch }: any) => {
        try {
            const response = await postApi(SCHEDULING_STAFF_AVAILABILITY_LIST, {
                ...reqData,
            });
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/* --------------- Add book Scheduling ----------------- */

export const CreateBookScheduling: any = createAsyncThunk(
    'staffs/CreateBookScheduling',
    async ({ payload }: any, { rejectWithValue, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(CREATE_BOOKING_SCHEDULING, {
                ...payload,
                organizationId,
            });
            Alertify.success('Scheduling booked successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Scheduling Listing -------------- */

export const BookedSchedulingList: any = createAsyncThunk(
    'staffs/BookedSchedulingList',
    async (reqData: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(SCHEDULING_LIST, {
                ...reqData,
            });
            return response;
        } catch (error: any) {
            // return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Scheduling Listing -------------- */

export const BookedSchedulingListV1: any = createAsyncThunk(
    'staffs/BookedSchedulingListV1',
    async (reqData: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(SCHEDULING_LIST_V1, {
                ...reqData,
            });
            return response;
        } catch (error: any) {
            // return handleApiError(error, rejectWithValue);
        }
    }
);
export const BookedSchedulingListExport: any = createAsyncThunk(
    'staffs/BookedSchedulingListExport',
    async (reqData: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(SCHEDULING_LIST_EXPORT, {
                ...reqData,
            });
            return response;
        } catch (error: any) {
            // return handleApiError(error, rejectWithValue);
        }
    }
);
export const ExportCheckInHistory: any = createAsyncThunk(
    'staffs/ExportCheckInHistory',
    async (reqData: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(EXPORT_CHECK_IN_HISTORY, {
                ...reqData,
            });
            return response;
        } catch (error: any) {
            // return handleApiError(error, rejectWithValue);
        }
    }
);

export const BookedCalendarData: any = createAsyncThunk(
    'staffs/BookedCalendarData',
    async (reqData: any, { rejectWithValue }: any) => {
        try {
            const response = await postApi(SCHEDULING_LIST, {
                ...reqData,
                page: 1,
                pageSize: 50,
            });
            return response;
        } catch (error: any) {
            // return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Scheduling Details -------------- */

export const BookedSchedulingDetails: any = createAsyncThunk(
    'staffs/BookedSchedulingDetails',
    async ({ scheduleId, dateRange }: any, { rejectWithValue }: any) => {
        try {
            const response = await getApi(
                `${SCHEDULING_DETAILS}/${scheduleId}`,
                { dateRange }
            );
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Scheduling Details Reccurring    -------------- */

export const BookedSchedulingDetailsRecurring: any = createAsyncThunk(
    'staffs/BookedSchedulingDetailsRecurring',
    async (
        { scheduleId, dateRange, startDate, markType, endDate }: any,
        { rejectWithValue }: any
    ) => {
        try {
            const response = await postApi(
                `${SCHEDULING_DETAILS_RECURRING}/${scheduleId}`,
                { dateRange, startDate, markType, endDate }
            );
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Cancel Scheduling -------------- */

export const CancelScheduling: any = createAsyncThunk(
    'staffs/CancelScheduling',
    async ({ scheduleId, payload }: any, { rejectWithValue }: any) => {
        try {
            const response = await patchApi(
                `${CANCEL_SCHEDULING}/${scheduleId}`,
                payload ?? {}
            );
            Alertify.success('Booking canceled successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- CheckIn Scheduling -------------- */

export const CheckInScheduling: any = createAsyncThunk(
    'staffs/CheckInScheduling',
    async ({ scheduleId }: any, { rejectWithValue }: any) => {
        try {
            const response = await patchApi(
                `${CHECK_IN_SCHEDULING}/${scheduleId}`
            );
            Alertify.success('Booking checked-in successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Scheduling Listing -------------- */

export const DeleteBookedScheduling: any = createAsyncThunk(
    'staffs/DeleteBookedScheduling',
    async ({ id }: any, { rejectWithValue }: any) => {
        try {
            const response = await deleteApi(`${DELETE_SCHEDULING}/${id}`);
            Alertify.success('Scheduling deleted successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Scheduling Listing -------------- */

export const UpdateBookedScheduling: any = createAsyncThunk(
    'staffs/UpdateBookedScheduling',
    async ({ payload }: any, { rejectWithValue, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await patchApi(
                `${UPDATE_BOOKING_SCHEDULING}`,
                payload
            );
            Alertify.success('Scheduling updated successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const CreateAppointmentScheduling: any = createAsyncThunk(
    'staffs/CreateAppointmentScheduling',
    async ({ payload }: any, { rejectWithValue, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(CREATE_APPOINTMENT_SCHEDULING, {
                ...payload,
                organizationId,
            });
            Alertify.success('Scheduling booked successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const UpdateAppointmentScheduling: any = createAsyncThunk(
    'staffs/UpdateAppointmentScheduling',
    async ({ payload }: any, { rejectWithValue, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await patchApi(
                `${UPDATE_APPOINTMENT_SCHEDULING}`,
                payload
            );
            Alertify.success('Scheduling updated successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);
/*------------------- Purchase pricing package List ----------------- */

export const PurchasePricingPackagesList: any = createAsyncThunk(
    'PurchasePricingPackagesList',
    async ({ reqData, classType }: any) => {
        try {
            const response = await postApi(
                `${PURCHASE_PRICING_LIST_BY_CLASS_TYPE}/${classType}`,
                { ...reqData }
            );
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const MemberShipListing: any = createAsyncThunk(
    'MemberShipListing',
    async ({ reqData }: any) => {
        try {
            const response = await postApi(`${MEMBERSHIP_LIST}`, {
                ...reqData,
            });
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- QR code purchase details ----------------- */

export const qrCodePurchaseDetails: any = createAsyncThunk(
    'qrCodePurchaseDetails',
    async ({ purchaseId }: any) => {
        try {
            const response = await postApi(
                `${QR_CODE_PURCHASE_DETAILS}/${purchaseId}`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export function ManipulateBookAppointmentData(item: any) {
    return {
        _id: item?._id,
        customerId: item?.customerId || '',
        clientName: item?.clientName,
        packageName: item?.packageName,
        clientId: item?.clientId,
        userClientId: item?.userClientId,
        facilityId: item?.facilityId,
        date: dayjs(item?.date).format('DD/MM/YYYY'),
        startTime: item?.from,
        endTime: item?.to,
        email: item?.clientEmail,
        location: item?.facilityName,
        serviceCategory: item?.serviceCategoryName,
        paymentStatus: item?.paymentStatus || 'Unpaid',
        bookingStatus: item?.scheduleStatus || 'Done',
        clientPhone: item?.clientPhone,
        sharedBy: item?.sharedBy,
    };
}

export function ManipulateCalendarData(item: any) {
    const date = new Date(item.date);
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    const [fromHour, fromMinute] = item.from.split(':').map(Number);
    const [toHour, toMinute] = item.to.split(':').map(Number);
    return {
        id: item._id,
        title: item.serviceCategoryName,
        trainerName: item.trainerName,
        clientName: item.clientName,
        packageName: item.packageName,
        packageId: item.packageId,
        clientId: item.clientId,
        facilityId: item.facilityId,
        email: item.clientEmail,
        location: item.facilityName,
        serviceCategory: item.serviceCategoryName,
        trainerId: item.trainerId,
        roomId: item.room,
        resourceId: item.trainerId,
        start: new Date(year, month, day, fromHour, fromMinute),
        end: new Date(year, month, day, toHour, toMinute),
        paymentStatus: item.paymentStatus,
        classType: item.classType,
    };
}
