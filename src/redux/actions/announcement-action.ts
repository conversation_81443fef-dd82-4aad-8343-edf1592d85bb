import { createAsyncThunk } from '@reduxjs/toolkit';
import { ANNOUNCEMENT } from '~/constants/api-constants';
import {
    deleteApi,
    getApi,
    patchApi,
    postApi,
    putApi,
} from '~/services/api-services';
import Alertify from '~/services/alertify';

export const CreateAnnouncement: any = createAsyncThunk(
    'createAnnouncement',
    async (payload: any, { dispatch }) => {
        try {
            const response = await postApi(ANNOUNCEMENT, payload);
            Alertify.success('Announcement created successfully');
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const AnnouncementList: any = createAsyncThunk(
    'announcementList',
    async ({ page, pageSize }: any, { getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(`${ANNOUNCEMENT}/get`, {
                page,
                pageSize,
                organizationId,
            });
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const UpdateAnnouncementStatus = createAsyncThunk(
    'Update status',
    async (
        { isActive, announcementId }: any,
        { dispatch, rejectWithValue }
    ) => {
        try {
            const response = await patchApi(
                `${ANNOUNCEMENT}/${announcementId}/status`,
                { isActive }
            );
            Alertify.success('Announcement  Status updated successfully');
            return response;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data ||
                    'Failed to update the status of the Announcement'
            );
        }
    }
);
export const GetAnnouncementById: any = createAsyncThunk(
    'Announcement-Detail',
    async ({ announcementId }: any, { rejectWithValue }) => {
        try {
            const response = await getApi(
                `${ANNOUNCEMENT}/${announcementId}/get`
            );
            return response;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch Announcement detail'
            );
        }
    }
);
export const UpdateAnnouncement: any = createAsyncThunk(
    'Update-Announcement',
    async ({ reqData }: any, { dispatch, rejectWithValue }) => {
        try {
            const response = await putApi(`${ANNOUNCEMENT}`, {
                ...reqData,
            });
            Alertify.success('Announcement updated successfully');
            return response;
        } catch (error: any) {
            return rejectWithValue(
                error.response?.data || 'Failed to fetch Announcement detail'
            );
        }
    }
);

export const DeleteAnnouncement: any = createAsyncThunk(
    'DeleteAttributeData',
    async ({ announcementId }: any, { dispatch }) => {
        try {
            const response = await deleteApi(
                `${ANNOUNCEMENT}/${announcementId}/delete`
            );
            Alertify.success('Announcement deleted successfully');
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
