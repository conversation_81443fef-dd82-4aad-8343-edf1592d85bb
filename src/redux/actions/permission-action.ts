import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    FOR_ROLES_PERMISSIONS_ON_SUPER_ADMIN,
    GET_ALL_POLICIES_LIST,
    GET_ALL_ROLES_LIST,
    GET_SUBJECT_ACTIONS,
    STAFF_PERMISSIONS,
    UPDATE_PERMISSIONS_ON_ACTION_ID,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { getApi, postApi, putApi } from '~/services/api-services';

// Get all permissions on organization dashboard for staff
export const GetAllPermissions: any = createAsyncThunk(
    'get-all-permissions',
    async (reqData:any) => {
        try {
            const {userId} = reqData;
            const response: any = await getApi(`${STAFF_PERMISSIONS}${userId}/assigned`);
            const allPermissions = response?.paylaod?.data?.data;
            // console.log('GetAllPermissions',response);
            return response;
        } catch (error: any) {
            console.log('Error GetAllPermissions API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
// Get all roles for user side
export const GetRolesList: any = createAsyncThunk(
    'get-roles-list',
    async () => {
        try {
            const response: any = await getApi(GET_ALL_ROLES_LIST);
            // console.log('GetRolesList',response);
            return response;
        } catch (error: any) {
            console.log('Error GetRolesList API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
// Get all roles on super admin
export const GetAllRolesList: any = createAsyncThunk(
    'get-all-roles-list',
    async (reqData:any) => {
        try {
            const response: any = await getApi(`${FOR_ROLES_PERMISSIONS_ON_SUPER_ADMIN}/list`,reqData);
            // console.log('GetAllRolesList',response);
            return response;
        } catch (error: any) {
            console.log('Error GetAllRolesList API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)

// Get all permissions (assigned or not assigned) on the basis of role on super admin dashboard
export const GetAllPermissionsOnRole: any = createAsyncThunk(
    'get-all-permissions-on-role',
    async(reqData:any)=>{
        try{
            const response: any = await getApi(`${FOR_ROLES_PERMISSIONS_ON_SUPER_ADMIN}/${reqData.roleId}/setting-policies`);
            // console.log('GetAllPermissionsOnRole',response);
            return response;
        } catch (error:any){
            console.log('Error GetAllPermissionsOnRole API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)

// Get all policies on super admin dashboard
export const GetAllPoliciesList: any = createAsyncThunk(
    'get-all-policies-list',
    async(reqData:any)=>{
        try{
            const response: any = await postApi(GET_ALL_POLICIES_LIST, reqData);
            console.log('GetAllPoliciesList',response);
            return response;
        } catch (error:any){
            console.log('Error GetAllPoliciesList API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)

// Update all permissions (assigned or not assigned) on the basis of role on super admin dashboard
export const UpdateAllPermissionsOnRole: any = createAsyncThunk(
    'update-all-permissions-on-role',
    async (reqData:any) => {
        try {
            const {roleId,...restData} = reqData;
            const response: any = await putApi(`${FOR_ROLES_PERMISSIONS_ON_SUPER_ADMIN}/${roleId}/add-policies`,restData);
            // console.log('UpdateAllPermissionsOnRole',response);
            return response;
        } catch (error: any) {
            console.log('Error UpdateAllPermissionsOnRole API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)

// Get subject details by subjectType
export const GetSubjectDetails: any = createAsyncThunk(
    'get-subject-details',
    async (reqData: any) => {
        try {
            const { subjectType } = reqData;
            const response: any = await getApi(`${GET_SUBJECT_ACTIONS}/${subjectType}/get-subject`);
            // console.log('GetSubjectDetails', response);
            return response;
        } catch (error: any) {
            console.log('Error GetSubjectDetails API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
// Get permissions on the basis of actionId
export const GetPermissionsByActionId: any = createAsyncThunk(
    'get-permissions-by-action-id',
    async (reqData: any) => {
        try {
            const { actionId } = reqData;
            const response: any = await getApi(`${GET_SUBJECT_ACTIONS}/${actionId}/permissions-setting`);
            // console.log('GetPermissionsByActionId', response);
            return response;
        } catch (error: any) {
            console.log('Error GetPermissionsByActionId API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)
// Get permissions without actionId
export const GetPermissionsWithoutActionId: any = createAsyncThunk(
    'get-permissions-without-action-id',
    async (reqData:any) => {
        try {
            console.log("reqData",reqData)
            const response: any = await getApi(`${UPDATE_PERMISSIONS_ON_ACTION_ID}/permission/list?pageSize=${reqData.pageSize}`);
            console.log('GetPermissionsWithoutActionId', response);
            return response;
        } catch (error: any) {
            console.log('Error GetPermissionsWithoutActionId API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)

// Update all permissions (assigned or not assigned) on the basis of role on super admin dashboard
export const UpdatePermissionsOnActionId: any = createAsyncThunk(
    'update-all-permissions-on-role',
    async (reqData:any) => {
        try {
            const {actionId,...restData} = reqData;
            const response: any = await putApi(`${UPDATE_PERMISSIONS_ON_ACTION_ID}/${actionId}/assign-permissions`,restData);
            console.log('UpdatePermissionsOnActionId',response);
            return response;
        } catch (error: any) {
            console.log('Error UpdatePermissionsOnActionId API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)

// Add new action to a policy on super admin dashboard
export const AddNewActionId: any = createAsyncThunk(
    'add-new-action-id',
    async (reqData:any) => {
        try {
            const response: any = await postApi(`${UPDATE_PERMISSIONS_ON_ACTION_ID}/create`,reqData);
            console.log('AddNewActionId',response);
            return response;
        } catch (error: any) {
            console.log('Error AddNewActionId API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)