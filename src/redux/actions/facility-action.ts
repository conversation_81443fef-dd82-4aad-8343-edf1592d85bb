//facility actions
import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    ADD_FACILITY_UNAVAILABILITY,
    CREATE_FACILITY,
    DELETE_FACILITY_UNAVAILABILITY,
    UPDATE_FACILITY_UNAVAILABILITY,
    FACILITY_AVAILABILITY_DETAILS,
    FACILITY_DETAILS,
    FACILITY_LIST,
    FACILITY_LIST_BY_STAFFID,
    GYM_DETAILS,
    UPDATE_FACILITY,
    UPDATE_ALL_FACILITY_UNAVAILABILITY,
    UPDATE_FACILITY_STATUS,
    UPDATE_FACILITY_STORE_STATUS,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import {
    deleteApi,
    handleApiError,
    patchApi,
    postApi,
} from '~/services/api-services';

/*----------------Customer List ---------------- */

interface FacilityListParams {
    page: number;
    pageSize: number;
    search: string;
    organizationId?: string;
}

export const FacilitiesList: any = createAsyncThunk(
    'facility/facilities-list',
    async ({
        page = 1,
        pageSize = 10,
        search,
        organizationId = undefined,
    }: FacilityListParams) => {
        try {
            const response = await postApi(FACILITY_LIST, {
                page,
                pageSize,
                search,
                organizationId,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// Facility List without page
export const FacilitiesListWithoutPage: any = createAsyncThunk(
    'facility/facilities-list',
    async () => {
        try {
            const response = await postApi(FACILITY_LIST);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            // return Promise.reject(error);
        }
    }
);
/*------------------- Create Gym List ------------------ */
export const CreateFacelityOnBoarding: any = createAsyncThunk(
    'CreateFacelityOnBoarding',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(CREATE_FACILITY, reqData);
            // dispatch(FacilitiesList({ page: 1, pageSize: 10 }));
            Alertify.success('Branch created successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Facility Details ------------------ */

export const FacilityDetails: any = createAsyncThunk(
    'FacilityDetails',
    async ({ facilityId }: any) => {
        try {
            const response = await postApi(`${FACILITY_DETAILS}`, {
                facilityId,
            });
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update Facility ------------------ */

export const UpdateFacilityOnboard: any = createAsyncThunk(
    'UpdateFacilityOnboard',
    async ({ reqData }: any, { dispatch }) => {
        try {
            const response = await patchApi(`${UPDATE_FACILITY}`, {
                ...reqData,
            });
            // dispatch(FacilitiesList({ page: 1, pageSize: 10 }));
            Alertify.success('Branch updated successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Facility Details by staff ID------------------ */
export const GetFacilityListByStaffId = createAsyncThunk(
    'facility/GetFacilityListByStaffId',
    async ({ staffId }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await postApi(FACILITY_LIST_BY_STAFFID, {
                staffId,
            });
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const AddFacilityUnavailabity = createAsyncThunk(
    'facility/AddFacilityUnavailabity',
    async ({ ...payload }: any, { rejectWithValue }) => {
        try {
            const res = await postApi(ADD_FACILITY_UNAVAILABILITY, {
                ...payload,
            });
            Alertify.success('Facility unavailability marked successfully');
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const UpdateFacilityUnavailability = createAsyncThunk(
    'facility/UpdateFacilityUnavailability',
    async ({ data, _id }: any, { rejectWithValue }) => {
        try {
            const response = await patchApi(
                `${UPDATE_FACILITY_UNAVAILABILITY}/${_id}`,
                data
            );
            Alertify.success('Facility unavailability updated successfully');
            return response;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const UpdateAllFacilityUnavailabilities = createAsyncThunk(
    'facility/UpdateAllFacilityUnavailabilities',
    async (payload: any, { rejectWithValue }) => {
        try {
            const response = await patchApi(
                UPDATE_ALL_FACILITY_UNAVAILABILITY,
                payload
            );
            Alertify.success('Facility unavailabilities updated successfully');
            return response;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const FacilityUnavailabilityDetails = createAsyncThunk(
    'facility/FacilityUnavailabilityDetails',
    async ({ payload }: any, { rejectWithValue }) => {
        try {
            const res = await postApi(FACILITY_AVAILABILITY_DETAILS, {
                ...payload,
            });
            // Alertify.success('Facility unavailability marked successfully');
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const DeleteFacilityUnavailability: any = createAsyncThunk(
    'staffs/DeleteFacilityUnavailability',
    async ({ _id, facility }: any, { rejectWithValue }: any) => {
        try {
            const response = await deleteApi(
                `${DELETE_FACILITY_UNAVAILABILITY}/${_id}`,
                { facility }
            );
            Alertify.success('Facility unavailability deleted successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const UpdateFacilityStatus: any = createAsyncThunk(
    'UpdateFacilityStatus',
    async ({ reqData, facilityId }: any) => {
        try {
            const response = await patchApi(
                `${UPDATE_FACILITY_STATUS}/${facilityId}`,
                {
                    ...reqData,
                }
            );
            Alertify.success(
                `Facility ${
                    !reqData.status ? 'deactivated' : 'activated'
                } successfully`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const UpdateFacilityStoreStatus: any = createAsyncThunk(
    'UpdateFacilityStoreStatus',
    async ({ reqData, facilityId }: any) => {
        try {
            const response = await patchApi(
                `${UPDATE_FACILITY_STORE_STATUS}/${facilityId}`,
                {
                    ...reqData,
                }
            );
            Alertify.success(response?.data?.data?.message);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
