import { createAsyncThunk } from '@reduxjs/toolkit';
import { postApi } from '~/services/api-services';
import Alertify from '~/services/alertify';
import { GET_TRAINER_AVAILABILITY, GET_COURSE_LIST, GET_COURSE_DETAILS, REGISTER_CLIENT, CREATE_PAYMENT_ORDER, VERIFY_PAYMENT, GET_TRAINER_SLOT, BOOK_PA_SCHEDULE, EMAIL_OR_MOBILE_VALIDATION, WIDGET_GET_ALL_SERVICE_TYPE, WIDGET_FACILITY_LIST, WIDGET_TRAINER_AVAILABILITY, WIDGET_BOOKING_ROOM_AVAILABILITY, WIDGET_PRICING, LOGIN_CLIENT } from '~/constants/api-constants';
export interface TrainerAvailabilityByTypeParams {
  facilityIds: string[];
  startDate: string;
  endDate: string;
  classType: string;
  serviceCategoryId?: string;
  subTypeId?: string;
}
export interface BookingRoomAvailabilityParams {
  organizationId: string;
  facilityId: string;
  serviceCategoryId: string;
  subTypeId: string;
  date: string;
}

export const getTrainerAvailability = createAsyncThunk(
  'widget/getTrainerAvailability',
  async (payload: any, thunkAPI) => {
    try {
      console.log('📡 API call starting...');
      const response = await postApi(GET_TRAINER_AVAILABILITY, payload);
      console.log(response, '🔥 API success');
      return response;
    } catch (error: any) {
      console.error('❌ API call failed in thunk:', error);
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
);
export const getCourseList = createAsyncThunk(
  'widget/getCourseList',
  async (_, thunkAPI) => {
    try {
      const response = await postApi(GET_COURSE_LIST);
      return response;
    } catch (error: any) {
      console.error('❌ Failed to fetch course list:', error);
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
);

// Get Course Detail by ID
export const getCourseDetails = createAsyncThunk(
  'widget/getCourseDetails',
  async (payload: { id: string }, thunkAPI) => {
    try {
      const response = await postApi(GET_COURSE_DETAILS, payload);
      return response;
    } catch (error: any) {
      console.error('❌ Failed to fetch course detail:', error);
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
);
export const registerClient = createAsyncThunk(
  'widget/registerClient',
  async (payload: any, thunkAPI) => {
    try {
      const orgId = payload.organizationId; // you already set this in SignupComponent
      console.log(orgId)
      const response = await postApi(REGISTER_CLIENT, payload, {
        headers: { 'x-organization': orgId },
      });
      Alertify.success('Registration successful');
      return response;
    } catch (error: any) {
      console.error('❌ Registration failed:', error);
      Alertify.error(error?.message || 'Registration failed');
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
);

export const createRazorpayOrder = createAsyncThunk(
  'widget/createRazorpayOrder',
  async (
    payload: {
      amount: number;
      packageId: string;
      widgetId: string;
      userId: string;
    },
    thunkAPI
  ) => {
    try {
      const response = await postApi(CREATE_PAYMENT_ORDER, payload);
      return response;
    } catch (error: any) {
      console.error('❌ Failed to create Razorpay order:', error);
      Alertify.error(error?.message || 'Payment initialization failed');
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
);
export const verifyRazorpayPayment = createAsyncThunk(
  'widget/verifyRazorpayPayment',
  async (
    payload: {
      razorpay_order_id: string;
      razorpay_payment_id: string;
      razorpay_signature: string;
      userId: string;
      packageId: string;
    },
    thunkAPI
  ) => {
    try {
      const response = await postApi(VERIFY_PAYMENT, payload);
      Alertify.success('Payment verified successfully');
      return response;
    } catch (error: any) {
      console.error('❌ Payment verification failed:', error);
      Alertify.error(error?.message || 'Payment verification failed');
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
);
export const getTrainerSlot = createAsyncThunk(
  'widget/getTrainerSlot',
  async (payload: any, thunkAPI) => {
    try {
      const response = await postApi(GET_TRAINER_SLOT, payload);
      return response;
    } catch (error: any) {
      console.error('❌ Failed to fetch trainer slot:', error);
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
);
export const bookpersonalAppointment = createAsyncThunk(
  'widget/bookpersonalAppointment',
  async (payload: any, thunkAPI) => {
    try {
      const response = await postApi(BOOK_PA_SCHEDULE, payload);
      Alertify.success('Appointment booked successfully');
      return response;
    } catch (error: any) {
      console.error('❌ Failed to book appointment:', error);
      Alertify.error(error?.message || 'Booking failed');
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
)
export const EmailOrMobileValidation = createAsyncThunk(
  'check email',
  async ({ value, id,organizationId }: any, thunkAPI) => {
    try {
      const res = await postApi(EMAIL_OR_MOBILE_VALIDATION, {
        value,
        id,
        organizationId
      });
      return { res };
    } catch (error: any) {
      console.error('❌ Failed to book appointment:', error);
      Alertify.error(error?.message || 'Booking failed');
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
);
export const widgetServiceType = createAsyncThunk(
  'get all the service type',
  async ({ organizationId, classType }: any, thunkAPI) => {
    try {
      const res = await postApi(WIDGET_GET_ALL_SERVICE_TYPE, {
        organizationId,
        classType
      });
      return { res };
    } catch (error: any) {
      Alertify.error(error?.message || 'Booking failed');
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
)
export const widgetFacilityList = createAsyncThunk(
  'get all the Facility List of the organization',
  async (organizationId: any, thunkAPI) => {
    try {
      const res = await postApi(`${WIDGET_FACILITY_LIST}/${organizationId}`);
      return res;
    } catch (error: any) {
      Alertify.error(error?.message || 'Booking failed');
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
)
export const widgetTrainerAvailabiltyByType = createAsyncThunk(
  'Trainer Availability',
  async (payload: any, thunkAPI) => {
    try {
      const res = await postApi(
        WIDGET_TRAINER_AVAILABILITY,
        payload,
      );

      return res;
    } catch (error: any) {
      Alertify.error(error?.message[0] || 'Booking failed');
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');

    }
  }
);
export const getBookingRoomAvailability = createAsyncThunk(
  'widget/getBookingRoomAvailability',
  async (payload: BookingRoomAvailabilityParams, thunkAPI) => {
    try {
      const response = await postApi(WIDGET_BOOKING_ROOM_AVAILABILITY, payload);
      return response;
    } catch (error: any) {
      console.error('❌ Failed to fetch booking room availability:', error);
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }

);
export const widgetPricingService = createAsyncThunk('widget/pricingListing',
  async (payload: any, thunkAPI) => {
    try {
      const response = await postApi(WIDGET_PRICING, payload);
      return response;
    } catch (error: any) {
      console.error('❌ Failed to fetch Pricing :', error);
      return thunkAPI.rejectWithValue(error?.message || 'Unknown error');
    }
  }
)
export const WidgetLoginUser: any = createAsyncThunk(
    'login-user',
    async ({ data, organizationId }: any, { getState }) => {
        try {
            const response = await postApi(LOGIN_CLIENT, data, {
                'x-organization': organizationId,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
