import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    AMENITIES_DETAILS,
    AMENITIES_GROUP_LIST,
    AMENITIES_LIST,
    CREATE_AMENITIES,
    DELETE_AMENITY,
    UPDATE_AMENITIES,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { deleteApi, getApi, patchApi, postApi } from '~/services/api-services';

/*----------------amenities List ---------------- */

interface AmenitiesListParams {
    page: number;
    pageSize: number;
    search?: string;
}

export const AmenitiesList: any = createAsyncThunk(
    'Amenities-list',
    async ({
        page = 1,
        pageSize = 10,
        search = undefined,
    }: AmenitiesListParams) => {
        try {
            const response = await postApi(AMENITIES_LIST, {
                page,
                pageSize,
                search,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create Amenities List ------------------ */

export const CreateAmenitiesData: any = createAsyncThunk(
    'CreateAmenitiesData',
    async (reqData: any, { dispatch }) => {
        try {
            const response = await postApi(CREATE_AMENITIES, reqData);
            dispatch(AmenitiesList({ page: 1, pageSize: 10 }));
            Alertify.success('Amenity created successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Amenities Details ------------------ */

export const AmenityDetails: any = createAsyncThunk(
    'AmenityDetails',
    async ({ amenityId }: any) => {
        try {
            const response = await getApi(`${AMENITIES_DETAILS}/${amenityId}`);
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update Amenities List ------------------ */

export const UpdateAmenitiesData: any = createAsyncThunk(
    'UpdateAmenitiesData',
    async ({ reqData, amenityId }: any, { dispatch }) => {
        try {
            const response = await patchApi(
                `${UPDATE_AMENITIES}/${amenityId}`,
                {
                    ...reqData,
                }
            );
            Alertify.success('Amenity updated successfully');
            dispatch(AmenitiesList({ page: 1, pageSize: 10 }));
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Amenities Group List ------------------ */

export const AmenityGroupList: any = createAsyncThunk(
    'AmenityGroupList',
    async () => {
        try {
            const response = await getApi(`${AMENITIES_GROUP_LIST}`);
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);

export const DeleteAmenity: any = createAsyncThunk(
    'amenity/DeleteAmenity',
    async ({ id }: any, { dispatch }) => {
        try {
            const response = await deleteApi(`${DELETE_AMENITY}/${id}`);
            Alertify.success('Amenity deleted successfully');
            dispatch(AmenitiesList({}));
            return response;
        } catch (error) {
            console.log('Error fetch brand API', error);
            return Promise.reject(error);
        }
    }
);
