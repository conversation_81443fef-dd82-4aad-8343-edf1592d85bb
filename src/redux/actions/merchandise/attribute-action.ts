
import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    ATTRIBUTES_LIST, ADD_NEW_SUB_ATTRIBUTES, UPDATE_SUB_ATTRIBUTE, GET_BRANDS_DATA, DELETE_SUB_ATTRIBUTE,
    DYNAMIC_ATTRIBUTE_LIST, ADD_NEW_DYNAMIC_ATTRIBUTES, DELETE_DYNAMIC_ATTRIBUTE, GET_DYNAMIC_ATTRIBUTE_DETAILS,
    UPDATE_DYNAMIC_ATTRIBUTE,UPDATE_DYNAMIC_ATTRIBUTE_STATUS,ACTIVE_DYNAMIC_ATTRIBUTE_LIST
} from '~/constants/api-constants';
import { postApi, deleteApi, getApi, patchApi } from '~/services/api-services';
import Alertify from '~/services/alertify';

/*------------------Get Attrributes List-------------*/

export const AttributeListData = createAsyncThunk(
    'attributes/attributes-list',
    async () => {
        try {
            const response = await getApi(DYNAMIC_ATTRIBUTE_LIST);
            console.log(response, "response")
            return response.data;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
);

/***************************Create Sub Attribute*********************************** */
export const CreateSubAttribute = createAsyncThunk(
    'attribute/create-subAttribute',
    async (payload: any) => {
        try {
            const response = await postApi(ADD_NEW_SUB_ATTRIBUTES, payload);
            Alertify.success("Attribute create successfully")
            return response

        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
)

export function ManipulateAttributesLists(data: any) {
    return Object.entries(data).map(([key, value]) => ({
        key,
        name: value?.name || '',
        slug: key || '-',
        isActive: value?.isActive || false,
    }));
}


export const SubAttributeList = createAsyncThunk(
    'attributes/sub-attributes-list',
    async (payLoad: any) => {
        try {
            const response = await postApi(ATTRIBUTES_LIST, payLoad);
            return response.data;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
);
export const AttributeValueListData = createAsyncThunk(
    'attributes/attributes-value-list',
    async (id: any) => {
        try {
            const response = await getApi(`${ATTRIBUTES_LIST}/${id}`);
            return response.data;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
);

export const UpdateAttribute = createAsyncThunk(
    'attribute/update-attribute',
    async ({ id, payload }: { id: string; payload: any }, { rejectWithValue }) => {
        try {
            const response = await patchApi(
                `${UPDATE_SUB_ATTRIBUTE}/${id}`,
                payload
            );
            Alertify.success("Attribute Update Successfully")
            return response;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
);
export const GetBrandsData = createAsyncThunk('listBrandData', async () => {
    try {

        const res = await getApi(GET_BRANDS_DATA);
        return res;
    } catch (error) {
        console.error(error);
        return Promise.reject(error);
    }
});
export const deleteAttribute = createAsyncThunk(
    'Delete Attribute',
    async (id: string, { dispatch, rejectWithValue }) => {
        try {
            await deleteApi(`${DELETE_SUB_ATTRIBUTE}/${id}`);
            Alertify.success('Attribute deleted successfully');
            return id;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error.response?.data || 'Failed to delete Room'
            );
        }
    }
);
export const createDynamicAttribute = createAsyncThunk(
    'attribute/create-dynamic-attribute',
    async (payload: any) => {
        try {
            const response = await postApi(ADD_NEW_DYNAMIC_ATTRIBUTES, payload);
            Alertify.success("Attribute create successfully")
            return response

        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    });
export const deleteDynamicAttribute = createAsyncThunk(
    'Delete Dynamic Attribute',
    async (id: string, { dispatch, rejectWithValue }) => {
        try {
            await deleteApi(`${DELETE_DYNAMIC_ATTRIBUTE}/${id}`);
            Alertify.success('Attribute deleted successfully');
            return id;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error.response?.data || 'Failed to delete Room'
            );
        }
    }
);
export const getDynamicAttributeDetails = createAsyncThunk(
    'getDynamicAttributeDetails',
    async (id: string) => {
        try {
            const response = await getApi(`${GET_DYNAMIC_ATTRIBUTE_DETAILS}/${id}`);
            return response.data;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
)
export const updateDynamicAttribute = createAsyncThunk(
    'attribute/update-dynamic-attribute',
    async ({ id, payload }: { id: string; payload: any }, { rejectWithValue }) => {
        try {
            const response = await patchApi(
                `${UPDATE_DYNAMIC_ATTRIBUTE}/${id}`,
                payload
            );
            Alertify.success("Attribute Update Successfully")
            return response;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    });

export const updateDynamicAttributeStatus = createAsyncThunk(
    'attribute/update-dynamic-attribute-status',
    async ({ id, payload }: { id: string; payload: any }, { rejectWithValue }) => {
        try {
            const response = await patchApi(
                `${UPDATE_DYNAMIC_ATTRIBUTE_STATUS}/${id}`,
                payload
            );
            Alertify.success("Attribute Update Successfully")
            return response;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
)
export const ActiveDynamicAttributeList = createAsyncThunk(
    'attributes/active-dynamic-attribute-list',
    async () => {
        try {
            const response = await getApi(ACTIVE_DYNAMIC_ATTRIBUTE_LIST);
            return response.data;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
);
