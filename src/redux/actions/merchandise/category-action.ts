import { createAsyncThunk } from '@reduxjs/toolkit';
import { postApi, deleteApi, getApi, patchApi } from '~/services/api-services';
import { CATEGORY_LIST, CREATE_CATEGORY, CATEGORY_DELETE, GET_CATEGORY,SUB_CATEGORY_LIST } from '~/constants/api-constants';
import Alertify from '~/services/alertify';



export const createCategory: any = createAsyncThunk(
    'category/create-category',
    async (reqData: any) => {
        try {
            const response = await postApi(CREATE_CATEGORY, reqData);
            Alertify.success('Category Created Successfully');
            return response;
        } catch (error: any) {
            console.error('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
)


export const PrimaryCategoryListData: any = createAsyncThunk(
    'category/primary-category-list',
    async (
        reqData,
        { rejectWithValue }
    ) => {
        try {
            const res = await postApi(CATEGORY_LIST, reqData);
            return res;
        } catch (error: any) {
            console.error('API Error:', error);
            return rejectWithValue(error?.message || 'Failed to fetch categories');
        }
    }
);

export const DeleteCategory = createAsyncThunk(
    'DeleteCategory',
    async (id: string, { dispatch, rejectWithValue }) => {
        try {
            const res = await deleteApi(`${CATEGORY_DELETE}/${id}`);
            Alertify.success('Category deleted successfully');
            dispatch(PrimaryCategoryListData({ page: 1, pageSize: 10 }));
            return { res };
        } catch (error: any) {
            console.error(error);
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error.response?.data || 'Failed to delete Room'
            );
        }
    }
);

export const CategoriesDetailsById = createAsyncThunk(
    'CategoriesDetailsById',
    async (id: string) => {
        try {
            const res = await getApi(`${GET_CATEGORY}/${id}`);
            return { res };
        } catch (error: any) {
            console.error(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }

);

export const EditCategoriesDetails = createAsyncThunk(
    "EditCategoriesDetails",
    async ({ id, reqData }: { id: string; reqData: any }, { rejectWithValue }) => {
        try {
            const response = await patchApi(`${GET_CATEGORY}/${id}`, reqData);
            return response;
        } catch (error: any) {
            console.error(error);

            const errorMessage = error.response?.data?.message || error.message || "An error occurred";

            Alertify.error(errorMessage);
            return rejectWithValue(errorMessage);
        }
    }
);

export const SubCategoryList: any = createAsyncThunk(
    'category/sub-category-list',
    async (
        reqData,
        { rejectWithValue }
    ) => {
        try {
            const res = await postApi(SUB_CATEGORY_LIST, reqData);
            return res;
        } catch (error: any) {
            console.error('API Error:', error);
            return rejectWithValue(error?.message || 'Failed to fetch categories');
        }
    }
);


