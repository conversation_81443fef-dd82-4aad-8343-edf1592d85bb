import { createAsyncThunk } from '@reduxjs/toolkit';
import { postApi, deleteApi, getApi, patchApi } from '~/services/api-services';
import {
    SEARCH_BY_SKU_DETAILS,
    CREATE_NEW_INVENTORY,
    INVENTORY_LIST,
    UPDATE_INVENTORY,
    STORE_INVENTORY_LIST,
    INVENTORY_EXPORT,
    BULK_UPLOAD_INVENTORY,
    INVENTORY_EXPORT_PRODUCTS_TEMPLATE,
    INVENTORY_HISTORY,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import dayjs from 'dayjs';

export const SearchProductBySkuDetails: any = createAsyncThunk(
    'products/searchBySku', // Better naming convention for action type
    async (reqData, { rejectWithValue }) => {
        try {
            const res = await postApi(SEARCH_BY_SKU_DETAILS, reqData);
            return res;
        } catch (error: any) {
            console.error('SearchProductBySkuDetails Error:', error);
            return rejectWithValue(error.message || 'Something went wrong');
        }
    }
);

export const CreateNewInventory: any = createAsyncThunk(
    'CreateNewInventory',
    async (reqData: any) => {
        try {
            const res = await postApi(CREATE_NEW_INVENTORY, reqData);
            Alertify.success('Inventory created successfully');
            return { res };
        } catch (error: any) {
            console.error(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const InventoryList = createAsyncThunk(
    'inventory/list',
    async (payLoad: any) => {
        try {
            const response = await postApi(INVENTORY_LIST, payLoad);
            return response.data;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
);
export const updateInventory: any = createAsyncThunk(
    'UpdateInventory',
    async ({ reqData, inventoryId }: any) => {
        try {
            const response = await patchApi(
                `${UPDATE_INVENTORY}/${inventoryId}`,
                {
                    ...reqData,
                }
            );
            Alertify.success('Inventory Update Successfully');
        } catch (error: any) {
            console.error(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const _ManipulateInventoryList = (data = []) => {
    let inventory_arr: any[] = [];

    data.forEach((item: any) => {
        let obj;
        if (item.productId.length === 0) {
            return;
        }
        if (item.productType === 'simple') {
            obj = {
                ...item,
                name: item.productDetails?.name,
                productId: item.productDetails?._id,
                hsn: item.productDetails?.hsn,
                sku: item.productDetails?.sku,
                productType: item?.productType,
                batchId: item?.batchId,
                salePrice: item?.salePrice,
                mrp: item?.mrp,
                tax: item.productDetails?.gst,
                quantity: item?.quantity,
                expiryDate: item?.expiryDate
                    ? dayjs(item?.expiryDate).format('DD/MM/YYYY')
                    : null,
                discount: item?.discount,
                _id: item?._id,
                price: item?.salePrice,
                inventoryId: item?._id,
                discountPrice: item?.discountPrice,
                discountedValue: (item.salePrice * item.discount) / 100,
            };
            inventory_arr = [...inventory_arr, obj];
        } else if (item.productType === 'variable') {
            obj = {
                ...item,
                name: item?.productVariantDetails?.name,
                productId: item?.productDetails?._id,
                hsn: item.productVariantDetails?.hsnCode,
                sku: item?.productVariantDetails?.sku,
                productVariantId: item?.productVariantDetails?._id,
                productType: item?.productType,
                batchId: item?.batchId,
                salePrice: item?.salePrice,
                mrp: item?.mrp,
                tax: item?.productDetails?.gst,
                quantity: item?.quantity,
                expiryDate: item?.expiryDate,
                discount: item?.discount,
                _id: item?._id,
                price: item?.salePrice,
                inventoryId: item?._id,
                discountPrice: item?.discountPrice,
                discountedValue: (item.salePrice * item.discount) / 100,
            };
            inventory_arr = [...inventory_arr, obj];
        }
    });
    return inventory_arr;
};
export const storeInventory: any = createAsyncThunk(
    'inventory/store-list',
    async (payLoad: any) => {
        try {
            const response = await postApi(STORE_INVENTORY_LIST, payLoad);
            return response.data;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
);
export const ExportInventory = createAsyncThunk(
    'Export Inventory',
    async (payload: any, { rejectWithValue }) => {
        try {
            const response = await postApi(
                INVENTORY_EXPORT,
                { ...payload },
                {
                    responseType: 'blob',
                    headers: {
                        'X-Timezone':
                            Intl.DateTimeFormat().resolvedOptions().timeZone,
                    },
                }
            );

            const contentDisposition = response.headers['content-disposition'];
            const contentType = response.headers['content-type'];

            // Build IST-based filename
            const now = new Date();
            const istOffsetMs = 330 * 60 * 1000; // UTC+5:30
            const istDate = new Date(now.getTime() + istOffsetMs);
            const istStamp = istDate
                .toISOString()
                .replace('T', '_')
                .replace(/:/g, '-')
                .split('.')[0];
            const base = `inventory_${istStamp}`;
            const ext = payload.fileType || 'csv';
            const filename = `${base}.${ext}`;

            // Validate content-type
            if (
                !contentType?.includes('application') &&
                !contentType?.includes('csv')
            ) {
                const reader = new FileReader();
                reader.onload = () => {
                    const text = reader.result as string;
                    Alertify.error(`Export failed: ${text}`);
                };
                reader.readAsText(response.data);
                return rejectWithValue('Export failed with unexpected content');
            }

            // Trigger browser download
            const blob = new Blob([response.data], { type: contentType });
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.setAttribute('download', filename);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(link.href);

            return { success: true };
        } catch (error: any) {
            console.error('ExportInventory Error:', error);
            Alertify.error(
                error?.response?.data?.message ||
                    'Failed to export the inventory'
            );
            return rejectWithValue(
                error?.response?.data || 'Failed to export the inventory'
            );
        }
    }
);
export const BulkUploadInventory = createAsyncThunk(
    'BulkUploadInventory',
    async (payload: any) => {
        try {
            const res = await postApi(BULK_UPLOAD_INVENTORY, payload, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            Alertify.success('Inventory uploaded successfully');
            return res;
        } catch (error: any) {
            console.error('BulkUploadInventory Error:', error);
            Alertify.error(error?.message?.[0] || 'Failed to upload inventory');
            return Promise.reject(error);
        }
    }
);
export const ExportProductsTemplate = createAsyncThunk(
    'Export Products Template',
    async (payload: any, { rejectWithValue }) => {
        try {
            const response = await postApi(
                INVENTORY_EXPORT_PRODUCTS_TEMPLATE, // new API constant
                { ...payload },
                {
                    responseType: 'blob',
                    headers: {
                        'X-Timezone':
                            Intl.DateTimeFormat().resolvedOptions().timeZone,
                    },
                }
            );

            const contentType = response.headers['content-type'];

            // Build IST-based filename
            const now = new Date();
            const istOffsetMs = 330 * 60 * 1000; // UTC+5:30
            const istDate = new Date(now.getTime() + istOffsetMs);
            const istStamp = istDate
                .toISOString()
                .replace('T', '_')
                .replace(/:/g, '-')
                .split('.')[0];
            const base = `products_template_${istStamp}`;
            const ext = payload.fileType || 'csv';
            const filename = `${base}.${ext}`;

            // Validate content-type
            if (
                !contentType?.includes('application') &&
                !contentType?.includes('csv')
            ) {
                const reader = new FileReader();
                reader.onload = () => {
                    const text = reader.result as string;
                    Alertify.error(`Export failed: ${text}`);
                };
                reader.readAsText(response.data);
                return rejectWithValue('Export failed with unexpected content');
            }

            // Trigger browser download
            const blob = new Blob([response.data], { type: contentType });
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.setAttribute('download', filename);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(link.href);

            return { success: true };
        } catch (error: any) {
            console.error('ExportProductsTemplate Error:', error);
            Alertify.error(
                error?.response?.data?.message ||
                    'Failed to export product template'
            );
            return rejectWithValue(
                error?.response?.data || 'Failed to export product template'
            );
        }
    }
);
export const GetInventoryHistory = createAsyncThunk(
  'inventory/getHistory',
  async (
    params: { inventoryId: string; page?: number; pageSize?: number },
    { rejectWithValue }
  ) => {
    try {
      const { inventoryId, page = 1, pageSize = 5 } = params;

      const qs = new URLSearchParams({
        page: String(Number(page) || 1),
        pageSize: String(Number(pageSize) || 5),
      }).toString();

      const res = await getApi(`/inventory/${inventoryId}/history?${qs}`);
      return res?.data
    } catch (err: any) {
      return rejectWithValue(
        err?.response?.data || { message: 'Failed to fetch history' }
      );
    }
  }
);
