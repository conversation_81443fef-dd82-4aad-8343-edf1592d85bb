import axios from 'axios';
import { ProjectUrl } from '~/env';
import { LogoutUser } from '~/redux/actions/auth-actions';
import store from '~/redux/store';
import Alertify from '~/services/alertify';

// Create an axios instance with the base URL
const api = axios.create({
    baseURL: ProjectUrl,
    withCredentials: true,
});

// Request interceptor to add the authorization token
api.interceptors.request.use(
    async (config) => {
        const { token, organizationId } = store.getState().auth_store;
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        if (!config.headers['x-organization'] && organizationId) {
            config.headers['x-organization'] = organizationId;
        }

        return config;
    },
    (error) => Promise.reject(error)
);

// Response interceptor to handle errors
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error?.response?.status === 401) {
            store.dispatch(LogoutUser());
            Alertify.error('Your session is expired');
            // window.location.href = '/signin';
        }
        return Promise.reject(error?.response?.data || error);
    }
);

// Centralized error handling function
export function handleApiError(error, rejectWithValue) {
    console.error(error);
    Alertify.error(error?.message?.[0] || 'An error occurred');
    return rejectWithValue(error);
}

// Helper function to handle API requests
const handleApiRequest = async (
    method,
    path,
    data = {},
    headers = {},
    cancelToken
) => {
    try {
        const response = await api({
            method,
            url: path,
            headers: {
                Accept: 'application/json',
                'ngrok-skip-browser-warning': '69420',
                ...headers,
                ...(headers['X-Timezone'] && {
                    'X-Timezone':
                        Intl.DateTimeFormat().resolvedOptions().timeZone,
                }),
            },
            ...(method === 'get' ? { params: data } : { data }),
            responseType:
                headers?.responseType === 'arraybuffer'
                    ? headers?.responseType
                    : 'json',
            cancelToken,
        });
        return response;
    } catch (error) {
        console.error(`API ${method.toUpperCase()} error:`, error);
        throw error;
    }
};

// Exported API functions
export const postApi = (path, data, headers, cancelToken) =>
    handleApiRequest('post', path, data, headers, cancelToken);

export const getApi = (path, data = {}, headers, cancelToken) =>
    handleApiRequest('get', path, data, headers, cancelToken);

export const patchApi = (path, data, headers) =>
    handleApiRequest('patch', path, data, headers);

export const putApi = (path, data, headers) =>
    handleApiRequest('put', path, data, headers);

export const deleteApi = (path, data = {}, headers, cancelToken) =>
    handleApiRequest('delete', path, data, headers, cancelToken);
