import React from 'react';
import {
    CollapseProps,
    Collapse,
    Space,
    Form,
    Input,
    Button,
    Select,
} from 'antd';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';

const { Option } = Select;

const text1 = (
    <>
        <Form
            name="personal-info"
            layout="vertical"
            size="large"
            autoComplete="off"
            initialValues={{ gender: 'select' }}
            // form={form}
        >
            {/* Full Name Input  */}

            <Form.Item
                label="Full Name"
                name="fullName"
                rules={[
                    {
                        required: true,
                        message: 'Please input full name',
                    },
                ]}
            >
                <Input placeholder="Enter Full Name" />
            </Form.Item>

            {/* Gender Input */}

            <Form.Item
                label="Gender"
                name="Gender"
                rules={[
                    {
                        required: true,
                        message: 'Please select gender',
                    },
                ]}
            >
                <Select placeholder="Select Gender">
                    <Option value="male">Male</Option>
                    <Option value="female">Female</Option>
                    <Option value="other">Other</Option>
                </Select>
            </Form.Item>

            {/* D.O.B Input */}

            <Form.Item
                label="D.O.B"
                name="dob"
                rules={[
                    {
                        required: true,
                        message: 'Please input D.O.B',
                    },
                ]}
            >
                <Input type="date" placeholder="Enter your D.O.B" />
            </Form.Item>

            {/* Email Input */}

            <Form.Item
                label="Email"
                name="email"
                rules={[
                    {
                        required: true,
                        message: 'Please input email',
                    },
                    {
                        type: 'email',
                        message: 'The input is not valid E-mail!',
                    },
                ]}
            >
                <Input type="email" placeholder="Enter Email" />
            </Form.Item>
            <Form.Item
                label="Activity Level"
                name="activityLevel"
                rules={[
                    {
                        required: true,
                        message: 'Please input activity level',
                    },
                ]}
            >
                <Input placeholder="Enter Activity Level" />
            </Form.Item>
            <Form.Item
                label="Mobile"
                name="mobile"
                rules={[
                    {
                        required: true,
                        message: 'Please input Mobile',
                    },
                ]}
            >
                <Input type="number" placeholder="Enter Mobile" />
            </Form.Item>
        </Form>
    </>
);

const text2 = (
    <>
        <Form
            name="address"
            layout="vertical"
            size="large"
            autoComplete="off"
            initialValues={{ gender: 'select' }}
            // form={form}
        >
            {/* Street Input  */}

            <Form.Item
                label="Address 1"
                name="street"
                rules={[
                    {
                        required: true,
                        message: 'Please input street',
                    },
                ]}
            >
                <Input placeholder="Enter Street Name" />
            </Form.Item>
            <Form.Item
                label="Address 2"
                name="street"
                rules={[
                    {
                        required: true,
                        message: 'Please input street',
                    },
                ]}
            >
                <Input placeholder="Enter Street Name" />
            </Form.Item>

            {/* City Input  */}

            <Form.Item
                label="City"
                name="city"
                rules={[
                    {
                        required: true,
                        message: 'Please input City',
                    },
                ]}
            >
                <Input placeholder="Enter City Name" />
            </Form.Item>

            {/* Pin Code Input */}

            <Form.Item
                label="Postal Code"
                name="Postal"
                rules={[
                    {
                        required: true,
                        message: 'Please input Postal Code',
                    },
                ]}
            >
                <Input type="number" placeholder="Enter Mobile" />
            </Form.Item>

            {/* State Input */}

            <Form.Item
                label="State"
                name="state"
                rules={[
                    {
                        required: true,
                        message: 'Please select state',
                    },
                ]}
            >
                <Select placeholder="Select state">
                    <Option value="delhi">Delhi</Option>
                </Select>
            </Form.Item>

            {/* Country Input */}
            <Form.Item
                label="Country"
                name="country"
                rules={[
                    {
                        required: true,
                        message: 'Please select country',
                    },
                ]}
            >
                <Select placeholder="Select state">
                    <Option value="india">India</Option>
                </Select>
            </Form.Item>
            <Form.Item
                label="Cell Phone"
                name="mobile"
                rules={[
                    {
                        required: true,
                        message: 'Please input Cell Phone',
                    },
                ]}
            >
                <Input type="number" placeholder="Enter Mobile" />
            </Form.Item>
            <Form.Item
                label="Emergency Contact Person"
                name="Contact Person"
                rules={[
                    {
                        required: true,
                        message: 'Please input Emergency Contact Person',
                    },
                ]}
            >
                <Input
                    type="number"
                    placeholder="Enter Emergency Contact Person"
                />
            </Form.Item>
            <Form.Item
                label="Emergency Contact"
                name="mobile"
                rules={[
                    {
                        required: true,
                        message: 'Please input Emergency Contact',
                    },
                ]}
            >
                <Input type="number" placeholder="Enter Mobile" />
            </Form.Item>
        </Form>
    </>
);

const PersonalDetails = () => {
    const onChange = (key: string | string[]) => {
        console.log(key);
    };
    const customExpandIcon = (panelProps: any) =>
        panelProps.isActive ? <MinusOutlined /> : <PlusOutlined />;
    return (
        <div className="w-full lg:py-10 lg:pr-10 ">
            <Space className="w-full" direction="vertical" size="large">
                <Collapse
                    bordered={false}
                    defaultActiveKey={['1', '2']}
                    onChange={onChange}
                    expandIcon={customExpandIcon}
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-[#406CF9] font-semibold">
                                    Personal Details
                                </div>
                            ),
                            children: <p>{text1}</p>,
                        },
                    ]}
                    className="custom-collapse w-full rounded-2xl bg-[F2F2F280]"
                    // className="custom-collapse"
                    expandIconPosition="right"
                />
                <Collapse
                    bordered={false}
                    defaultActiveKey={['1', '2']}
                    onChange={onChange}
                    expandIcon={customExpandIcon}
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-[#406CF9] font-semibold">
                                    Address
                                </div>
                            ),
                            children: <p className="w-auto">{text2}</p>,
                        },
                    ]}
                    className="custom-collapse w-full bg-[F2F2F280]"
                    // className="custom-collapse"
                    expandIconPosition="right"
                />
            </Space>

            <div className="flex flex-row justify-end gap-5">
                <Form.Item>
                    <div className="mt-10" style={{ display: 'flex' }}>
                        <Button
                            className="border-2 border-[#1A3353] px-20 py-7 text-2xl"
                            htmlType="submit"
                        >
                            Cancel
                        </Button>
                    </div>
                </Form.Item>
                <Form.Item>
                    <div
                        className="mt-10"
                        style={{ display: 'flex', gap: '10px' }}
                    >
                        <Button
                            className="px-20 py-7 text-2xl"
                            type="primary"
                            htmlType="submit"
                        >
                            Confirm
                        </Button>
                    </div>
                </Form.Item>
            </div>
        </div>
    );
};

export default PersonalDetails;
