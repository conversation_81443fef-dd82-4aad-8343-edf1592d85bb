import { ConfigProvider, Tabs, TabsProps } from 'antd';
import Title from 'antd/es/typography/Title';
import React from 'react';
import ImageUpload from '~/components/common/image-upload-comp';
import PersonalDetails from './personalDetailsTab';
function goBack() {
    window.history.back();
}
const data = [
    {
        label: 'ID :',
        value: 'P-9GFD',
    },
    {
        label: 'DOB :',
        value: 'Staff',
    },
    {
        label: 'Gender :',
        value: 'Male',
    },
    {
        label: 'Weight :',
        value: 'Knox Studio Gurugram',
    },
    {
        label: 'Goal :',
        value: '12-08-2024',
    },
    {
        label: 'Activity Level :',
        value: 'S-12346',
    },
    {
        label: 'Height:',
        value: '12-08-2024',
    },
    {
        label: 'User Type :',
        value: 'S-12346',
    },
];
const items: TabsProps['items'] = [
    {
        key: '1',

        label: (
            <div className="flex gap-5">
                <div className="px-8 font-semibold ">Personal Details</div>
            </div>
        ),
        children: <PersonalDetails />,
    },
];
const UserProfile = () => {
    const handleImageUpload = (file: File) => {
        console.log('Uploaded file:', file);
        // You can now send the file to a server or perform any other actions needed
    };
    const onChange = (key: string) => {
        console.log(key);
    };
    return (
        <>
            <div className="flex items-center gap-4 ">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title level={4}>User Profile</Title>
            </div>
            <div className="my-10 flex w-full rounded-lg   py-10 shadow-md">
                <div className="w-[15%] @sm:hidden ">
                    <ImageUpload onUpload={handleImageUpload} />
                </div>
                <div className="w-[85%]">
                    <Title className="text-[#8143D1] @sm:ps-5 " level={4}>
                        Ravi
                    </Title>
                    <div className="w-full justify-start gap-x-10 lg:flex lg:flex-wrap ">
                        {data.map((item, index) => (
                            <div
                                key={index}
                                className={`mt-5 flex items-center gap-3 lg:w-[31%] @sm:ps-5   `}
                            >
                                <div
                                    className={` w-auto text-lg font-bold text-[#1A3353]`}
                                >
                                    <p className="text-2xl font-semibold">
                                        {item.label}
                                    </p>
                                </div>
                                <div className="overflow-wrap  break-word w-auto break-words text-xl text-[#72849A]">
                                    <p>{item.value}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* --------------tabs-------------- */}
            <div className="">
                <ConfigProvider
                    theme={{
                        components: {
                            Tabs: {},
                        },
                    }}
                >
                    <Tabs
                        defaultActiveKey="1"
                        items={items}
                        onChange={onChange}
                        tabPosition="top"
                        tabBarStyle={
                            {
                                // backgroundColor: '#f5f5f5',
                            }
                        }
                    />
                </ConfigProvider>
            </div>
        </>
    );
};

export default UserProfile;
