import { ConfigProvider, Pagination, Switch } from 'antd';
import clsx from 'clsx';
import { title } from 'process';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'wouter';
import { navigate } from 'wouter/use-location';
import CommonTable from '~/components/common/commonTable';
import { capitalizeFirstLetter } from '~/components/common/function';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import {
    AttributeList,
    AttributeTypeList,
    DeleteAttributeData,
    UpdateAttributeStatus,
} from '~/redux/actions/attribute-action';
import { GymList } from '~/redux/actions/gym-action';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';

const columns = [
    {
        title: 'ATTRIBUTE NAME',
        dataIndex: 'name',
        // width: "25%",
    },
];

const AttributeTypeListing: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();
    const [loader, startLoader, endLoader] = useLoader();
    const { attributeTypeList, attributeTypeListCount } = useSelector(
        (state: any) => state.attribute_store
    );

    console.log('customerList------------', attributeTypeList);

    const params = getQueryParams();
    const TypeOfAttribute = params.type;
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [isDeleting, setIsDeleting] = useState(false);
    const [attributeData, setAttributeData] = useState<any>(null);
    // const debouncedRequest = useDebounce((callback) => callback(), 300);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
    }

    useEffect(() => {
        startLoader();
        dispatch(
            AttributeTypeList({
                attributeType: TypeOfAttribute,
            })
        )
            .unwrap()
            .then(() => {
                console.log('first');
            })
            .finally(endLoader);
    }, []);

    const openConfirmationModal = (record: any, isDelete: boolean = false) => {
        setIsDeleting(isDelete);
        setAttributeData(record);
        setConfirmationModalVisible(true);
    };

    const handleConfirmStatusChange = () => {
        if (isDeleting)
            dispatch(DeleteAttributeData({ attributeId: attributeData._id }));
        else
            dispatch(
                UpdateAttributeStatus({
                    attributeId: attributeData._id,
                    reqData: {
                        isActive: !attributeData.isActive,
                        attributeType: TypeOfAttribute,
                    },
                })
            );
        setIsDeleting(false);
        setConfirmationModalVisible(false);
    };

    const handleCancelConfirmation = () => {
        setConfirmationModalVisible(false);
        setIsDeleting(false);
    };

    const selectColumn = [
        {
            title: 'STATUS',
            dataIndex: '',
            key: '',
            render: (record: any) => {
                // console.log("Record--------------", record)
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    colorTextQuaternary: 'gray',
                                    colorFillQuaternary: 'gray',
                                },
                            },
                        }}
                    >
                        <Switch
                            className={clsx(
                                'rounded-full transition-colors',
                                record.status ? 'bg-switch-on' : 'bg-switch-off'
                            )}
                            id="swtich-off"
                            checkedChildren="ON"
                            unCheckedChildren="OFF"
                            onChange={() =>
                                openConfirmationModal(record, false)
                            }
                            checked={record.isActive || false}
                        />
                    </ConfigProvider>
                );
            },
        },
        {
            title: 'ACTIONS',
            dataIndex: '',
            width: '90px',
            key: 'action',
            render: (record: any) => {
                console.log('Record: ', record);
                return (
                    <>
                        <span className="flex gap-2">
                            <Link
                                to={`/attribute/create-attribute/${record?._id}`}
                            >
                                <img
                                    src="/icons/common/edit.svg"
                                    alt="edit"
                                    className="h-[20px] cursor-pointer"
                                />
                            </Link>
                            <div
                                className="cursor-pointer"
                                onClick={() =>
                                    openConfirmationModal(record, true)
                                }
                            >
                                <img
                                    src="/icons/common/delete.svg"
                                    alt="delete"
                                    className="h-[20px]"
                                />
                            </div>
                        </span>
                    </>
                );
            },
        },
    ];

    const combinedColumns = [...columns, ...selectColumn];

    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={combinedColumns}
                dataSource={attributeTypeList}
                addNewLink="/attribute/create-attribute/0"
                bulkAction={false}
                loading={loader}
                addNewTitle="Create Attribute"
                heading={
                    capitalizeFirstLetter(TypeOfAttribute) + ' ' + 'Listing'
                }
            />
            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={attributeTypeListCount}
                    pageSize={pageSizes}
                    onChange={paginate}
                    // hideOnSinglePage
                />
            </div>

            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelConfirmation}
                message={
                    isDeleting
                        ? 'Are you sure you want to delete this attribute?'
                        : 'Are you sure you want to change the status?'
                }
            />
        </>
    );
};

export default AttributeTypeListing;
