import React, { useEffect, useState } from 'react';
import {
    ConfigProvider,
    Typography,
    Row,
    Col,
    Form,
    Input,
    Button,
    InputNumber,
} from 'antd';
import { useParams, useLocation } from 'wouter';
import { useLoader } from '~/hooks/useLoader';
import {
    CreateMembership,
    getMembershipById,
    updateMembership,
} from '~/redux/actions/membership-action';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
const { Title } = Typography;
function goBack() {
    window.history.back();
}
const createMembership: React.FC = () => {
    const [form] = Form.useForm();
    const { id } = useParams<{ id: string }>();
    const [loader, startLoader, endLoader] = useLoader();
    const dispatch = useDispatch<AppDispatch>();
    const [location, setLocation] = useLocation();

    const onFinish = async (values: any) => {

        console.log("Value------------", values)
        startLoader();
        const payLoad = {
            name: values.name,
            prefix: values.prefix,
            counter: values.counter,
        };
        console.log("Paylaod------------", payLoad)
        try {
            if (id === '0') {
                await dispatch(CreateMembership(payLoad)).unwrap();
            } else {
                await dispatch(
                    updateMembership({ reqData: payLoad, membershipId: id })
                ).unwrap();
            }
            setLocation('/setting/membership');
        } catch (error) {
            console.error('Error submitting form:', error);
        } finally {
            endLoader();
        }
    };
    const fetchMembershipDetail = async () => {
        startLoader();
        try {
            console.log(id);
            const membershipDetail = await dispatch(
                getMembershipById({ memberShipId: id })
            ).unwrap();
            console.log(membershipDetail, 'membershipDetail');
            form.setFieldsValue({
                name: membershipDetail.name,
                prefix: membershipDetail.prefix,
                counter: membershipDetail.counter,
            });
        } catch (error) {
            console.error('Error fetching room data:', error);
        } finally {
            endLoader();
        }
    };
    useEffect(() => {
        if (id && id !== '0') {
            fetchMembershipDetail();
        }
    }, [id]);
    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Typography: {
                            titleMarginBottom: 0,
                            titleMarginTop: 0,
                        },
                        Input: {
                            colorPrimary: '#E6EBF1',
                            colorPrimaryActive: '#E6EBF1',
                            colorPrimaryHover: '#E6EBF1',
                        },
                        Select: {
                            colorPrimary: '#E6EBF1',
                            colorPrimaryActive: '#E6EBF1',
                            colorPrimaryHover: '#E6EBF1',
                        },
                    },
                }}
            >
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="Back"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353]" level={4}>
                        {id === '0' ? 'Create Membership' : 'Membership'}
                    </Title>
                </div>
                <div className="mt-16">
                    <div className="flex  flex-col rounded-3xl border  p-16 lg:w-[75%] @sm:w-[80%]">
                        <Form
                            name="attributeCreate"
                            layout="vertical"
                            size="large"
                            form={form}
                            onFinish={onFinish}
                            autoComplete="off"
                        >
                            <div className="">
                                <Form.Item
                                    label="Name"
                                    name="name"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter a Membership name.',
                                        },
                                    ]}
                                >
                                    <Input
                                        placeholder="Enter Membership name"
                                        maxLength={50}
                                        className="w-[100%]"
                                        disabled={id !== '0' ? true : false}
                                    />
                                </Form.Item>
                            </div>

                            <div className="flex flex-row items-center gap-5">
                                <Form.Item
                                    className="w-[50%]"
                                    label="Prefix"
                                    name="prefix"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please enter a Prefix',
                                        },
                                    ]}
                                >
                                    <Input
                                        placeholder="Enter Prefix"
                                        maxLength={50}
                                        // className="w-[40%]"
                                        disabled={id !== '0' ? true : false}
                                    />
                                </Form.Item>
                                <Form.Item
                                    className="w-[50%]"
                                    label="Counter Position"
                                    name="counter"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter a Counter Value',
                                        },
                                        {
                                            pattern: /^[0-9]+$/,
                                            message: 'Please enter only numbers',
                                        }
                                    ]}
                                >
                                    <Input
                                        min={0}
                                        placeholder="Enter counter"
                                        maxLength={50}
                                        className="w-[100%]"
                                        onKeyDown={(e) => {
                                            // Allow only numbers, backspace, and delete
                                            if (
                                                !/[0-9]/.test(e.key) &&
                                                e.key !== 'Backspace' &&
                                                e.key !== 'Delete'
                                            ) {
                                                e.preventDefault();
                                            }
                                        }}
                                        disabled={id !== '0' ? true : false}
                                    />
                                </Form.Item>
                            </div>
                            <Form.Item className="mt-8 flex justify-end gap-8 @sm:hidden ">
                                <Button
                                    // loading={loader}
                                    onClick={() => goBack()}
                                    htmlType="button"
                                    className="w-[110px] border-[#1A3353] bg-[#fff]  text-black "
                                >
                                    <p> Cancel </p>
                                </Button>
                                {id === '0' && (
                                    <>
                                        <Button
                                            // loading={loader}
                                            htmlType="submit"
                                            className="w-[110px] bg-purpleLight text-white lg:ms-7"
                                        >
                                            <p> Save </p>
                                        </Button>
                                    </>
                                )}
                            </Form.Item>
                        </Form>
                    </div>
                </div>
            </ConfigProvider>
        </>
    );
};
export default createMembership;
