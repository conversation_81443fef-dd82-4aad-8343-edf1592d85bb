import { MoreOutlined } from '@ant-design/icons';
import { Menu, Dropdown, Pagination, Tag, Tooltip, Modal, Select } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import { Link, useLocation } from 'wouter';
import { navigate } from 'wouter/use-location';
import FullLoader from '~/components/library/loader/full-loader';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { FacilitiesList } from '~/redux/actions/facility-action';
import {
    BookedSchedulingListV1,
    CancelScheduling,
    CheckInScheduling,
    MemberShipListing,
} from '~/redux/actions/scheduling-action';
import { ServiceCategoryList } from '~/redux/actions/serviceCategoryAction';
import BookingModal from '~/screens/appointment/booking-modal';
import {
    ClassType,
    PERMISSIONS_ENUM,
    RoleType,
    SUBJECT_TYPE,
} from '~/types/enums';
import { formatDate } from '~/utils/formatDate';
import { getQueryParams } from '~/utils/getQueryParams';
import SharePassModal from '~/screens/customers/share-pass-modal';
import Alertify from '~/services/alertify';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import { useSelector } from 'react-redux';
import { GetSettings } from '~/redux/actions/settings-actions';
import ClientBookingTable from '~/components/gym/client-booking';
import ClientBookingCheckInModal from '~/components/common/client-book-check-modal';
import SharePassTypeModal from '../customers/share-pass-type-modal';
import BookingSuspensionModal from '../bookings/bookingSuspension-modal';
import CustomTable from '~/components/common/customTable';

function getStatus(record: any) {
    const now = new Date();
    const startDate = new Date(record.startDate);
    const endDate = new Date(record.endDate);

    if (!record.isActive) return 'Inactive';

    if (now < startDate || now > endDate) {
        return 'Inactive';
    }

    if (record.remainingSessions < 1) return 'Inactive';

    if (record.suspensions && record.suspensions.length > 0) {
        const isCurrentlySuspended = record.suspensions.some(
            (suspension: any) => {
                const fromDate = new Date(suspension.fromDate);
                const suspensionEndDate = new Date(suspension.endDate);
                return now >= fromDate && now <= suspensionEndDate;
            }
        );

        if (isCurrentlySuspended) return 'Suspended';
    }

    return 'Active';
}

const toCamelCase = (str: string): string => {
    return str
        .toLowerCase()
        .split(' ')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
};

// Helper to compute simple status
const getSimpleStatus = (
    record: any
): {
    label: 'Active' | 'Inactive' | 'Suspended' | 'Expired';
    color: string;
    note?: string;
} => {
    const isSuspended = !!record?.isSuspended;
    const suspensions = Array.isArray(record?.suspensions)
        ? record.suspensions
        : [];

    if (isSuspended && suspensions.length > 0) {
        const now = Date.now();
        const activeSusp = suspensions.find((s: any) => {
            const from = new Date(s.fromDate).setHours(0, 0, 0, 0);
            const to = new Date(s.endDate).setHours(23, 59, 59, 999);
            return now >= from && now <= to;
        });
        if (activeSusp) {
            return {
                label: 'Suspended',
                color: 'red',
                note: activeSusp?.notes,
            };
        }
    }

    if (record?.endDate) {
        const endOfEndDate = new Date(record.endDate).setHours(23, 59, 59, 999);
        if (Date.now() > endOfEndDate) {
            return { label: 'Expired', color: 'red' };
        }
    }

    return record?.isActive
        ? { label: 'Active', color: 'green' }
        : { label: 'Inactive', color: 'purple' };
};

const SimpleStatusTag: React.FC<{ record: any }> = ({ record }) => {
    const status = getSimpleStatus(record);
    const tag = <Tag color={status.color}>{status.label}</Tag>;
    return status.note ? <Tooltip title={status.note}>{tag}</Tooltip> : tag;
};

const columns = [
    {
        title: 'Customer ID',
        dataIndex: 'customerId',
        hidden: true,
    },
    {
        title: 'Client Name',
        dataIndex: 'clientName',
    },
    {
        title: 'Membership ID',
        dataIndex: 'membershipId',
        align: 'center',
        render: (membershipId: string) => {
            return membershipId && membershipId.trim() !== '' ? (
                membershipId
            ) : (
                <div className="text-center">-</div>
            );
        },
    },
    {
        title: 'Package Name',
        dataIndex: 'packageName',
        ellipses: true,
    },
    {
        title: 'Start Date',
        dataIndex: '',
        align: 'center',
        render: (record: any) => {
            return formatDate(record?.startDate);
        },
    },
    {
        title: 'End Date',
        dataIndex: '',
        align: 'center',
        render: (record: any) => {
            return formatDate(record?.endDate);
        },
    },
    {
        title: 'Phone Number',
        dataIndex: 'mobile',
        align: 'center',
    },
    {
        title: 'Location',
        dataIndex: 'location',
    },
    {
        title: 'Status',
        dataIndex: '',
        align: 'center',
        render: (record: any) => <SimpleStatusTag record={record} />,
    },
];

const MembershipListing: React.FC = () => {
    const [activeKey, setActiveKey] = useState(1);
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [selectedClients, setSelectedClients] = useState([]);
    const [selectedDateRange, setSelectedDateRange] = useState([]);
    const [selectedBookingStatus, setSelectedBookingStatus] = useState([]);
    const [isCheckIn, setIsCheckIn] = useState(false);
    const [scheduleData, setScheduleData] = useState<any>({});
    const [selectedServiceCategories, setSelectedServiceCategories] = useState(
        []
    );
    const [selectedCity, setSelectedCity] = useState([]);
    const [loader, startLoader, endLoader] = useLoader(true);
    const [listLoader, startListLoader, endListLoader] = useLoader(true);
    const [filterLoader, startFilterLoader, endFilterLoader] = useLoader(false);
    const params = getQueryParams();
    const [search, setSearch] = useState(params.search);
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
    const [isSuspensionModalVisible, setIsSuspensionModalVisible] =
        useState<boolean>(false);
    const [isSharePassModalVisible, setIsSharePassModalVisible] =
        useState<boolean>(false);

    const [openClientBookingModal, setOpenClientBookingModal] = useState(false);
    const [clientId, setClientId] = useState<string>('');
    const store = useAppSelector((state) => ({
        memberShipList: state.scheduling_store.memberShipList,
        memberShipListCount: state.scheduling_store.memberShipListCount,
        clientOnboarding: state.settings_store.clientOnboarding,
        role: state.auth_store.role,
    }));

    const dispatch = useAppDispatch();

    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    // for share pass option(should be visible or not)
    const [sharePassOption, setSharePassOption] = useState(false);
    const [sharePassEnabled, setSharePassEnabled] = useState(false);
    const [statusFilter, setStatusFilter] = useState<string>();
    const [statusChanged, setStatusChanged] = useState<boolean>(false);
    const debouncedRequest = useDebounce((callback) => callback(), 300);
    const handleClose = () => {
        const payload = {
            page: 1,
            search: search || undefined,
            pageSize: 10,
            facilityId: selectedCity,
            // serviceCategory: selectedServiceCategories,
            startDate: selectedDateRange[0],
            endDate: selectedDateRange[1],
            // classType: 'bookings',
            ...(statusFilter != '' && { status: statusFilter }),
        };
        dispatch(
            MemberShipListing({
                reqData: payload,
            })
        );
        setStatusChanged(false);
        setIsModalVisible(false);
        setScheduleData(null);
        setIsSuspensionModalVisible(false);
    };

    const getList = () => {
        if (activeKey === 1) {
            if (statusChanged) {
                setCurrentPage(1);
            }
            const payload = {
                page: currentPage,
                pageSize: pageSizes,
                ...(statusFilter != '' && { status: statusFilter }),
                ...(search && { search: search }),
                ...(selectedCity?.length > 0 && {
                    facilityIds: selectedCity,
                }),
                ...(selectedServiceCategories?.length > 0 && {
                    serviceCategoryIds: selectedServiceCategories,
                }),
                ...(selectedDateRange?.length > 0 && {
                    startDate: selectedDateRange[0],
                    endDate: selectedDateRange[1],
                }),
            };
            startListLoader();
            debouncedRequest(() => {
                dispatch(
                    MemberShipListing({
                        reqData: payload,
                        // classType: 'bookings',
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        // console.log('Res is:', res);
                        setStatusChanged(false);
                    })
                    .finally(() => {
                        endFilterLoader();
                        endListLoader();
                    });
            });
        }
    };

    useEffect(() => {
        if (
            search ||
            currentPage > 1 ||
            selectedCity.length ||
            selectedServiceCategories.length ||
            // (selectedDateRange[0] && selectedDateRange[1]) ||
            (Array.isArray(selectedDateRange) &&
                selectedDateRange.length === 2) ||
            selectedBookingStatus.length
        )
            startFilterLoader();
        getList();
        endLoader();
    }, [
        search,
        currentPage,
        pageSizes,
        selectedCity,
        selectedServiceCategories,
        statusFilter,
        selectedDateRange,
        activeKey,
        selectedBookingStatus,
        isSuspensionModalVisible,
    ]);

    useEffect(() => {
        dispatch(FacilitiesList({ page: 1, pageSize: 10 })).unwrap();
        dispatch(GetSettings({})).unwrap();
        dispatch(
            ServiceCategoryList({
                page: 1,
                pageSize: 10,
                classType: 'bookings',
            })
        ).unwrap();
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_scheduling_sharepass',
            })
        )
            .unwrap()
            .then((response: any) => {
                setSharePassOption(response?.data?.data?.isActive);
                setSharePassEnabled(response?.data?.data?.isEnabled);
            })
            .catch((error: any) =>
                Alertify.error('Error in fetching setting status')
            );
    }, []);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
    }

    function handleSearch(value: string) {
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    }

    const openConfirmationModal = (record: any, isCheckIn: boolean = false) => {
        setIsCheckIn(isCheckIn);
        setScheduleData(record);
        setConfirmationModalVisible(true);
    };

    const handleSharePassClose = (refresh = false) => {
        if (refresh) getList();
        setIsSharePassModalVisible(false);
    };

    const actionColumn = [
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            render: (record: any) => {
                const isDisabled =
                    getStatus(record) === 'Inactive' &&
                    new Date() > new Date(record.endDate);
                const isBooking = record.classType === ClassType.BOOKING;

                const menu = (
                    <Menu>
                        {!record.isSuspended ? (
                            <>
                                {isBooking && (
                                    <Menu.Item
                                        className={` ${
                                            isDisabled
                                                ? 'cursor-not-allowed bg-[#f5f5f5]'
                                                : 'text-[#455560]'
                                        }`}
                                        onClick={() => {
                                            setScheduleData({
                                                ...record,
                                                isEdit: false,
                                            });
                                            setIsModalVisible(true);
                                        }}
                                        key="1"
                                        disabled={isDisabled}
                                    >
                                        Add Booking
                                    </Menu.Item>
                                )}
                                <Menu.Item
                                    className={` ${
                                        isDisabled
                                            ? 'cursor-not-allowed bg-[#f5f5f5]'
                                            : 'text-[#455560]'
                                    }`}
                                    onClick={() => {
                                        setScheduleData({
                                            ...record,
                                            isEdit: false,
                                        });
                                        setIsSuspensionModalVisible(true);
                                    }}
                                    key="2"
                                    disabled={isDisabled}
                                >
                                    Freeze
                                </Menu.Item>
                            </>
                        ) : (
                            <>
                                <Menu.Item
                                    className={` ${
                                        isDisabled
                                            ? 'cursor-not-allowed bg-[#f5f5f5]'
                                            : 'text-[#455560]'
                                    }`}
                                    onClick={() => {
                                        setScheduleData({
                                            ...record,
                                            isEdit: false,
                                        });
                                        setIsSuspensionModalVisible(true);
                                    }}
                                    key="3"
                                    disabled={isDisabled}
                                >
                                    Unfreeze
                                </Menu.Item>
                            </>
                        )}
                        {/* {sharePassOption && sharePassEnabled && ( */}
                        {/* {sharePassEnabled &&
                            store.clientOnboarding?.sharePass && (
                                <Menu.Item
                                    className={` ${
                                        isDisabled
                                            ? 'cursor-not-allowed bg-[#f5f5f5]'
                                            : 'text-[#455560]'
                                    }`}
                                    onClick={() => {
                                        setScheduleData({
                                            ...record,
                                        });
                                        if (
                                            !['multiple', 'day_pass'].includes(
                                                record.sessionType
                                            )
                                        )
                                            Alertify.error(
                                                'Share pass can only be done when session type is multiple or day pass.'
                                            );
                                        else setIsSharePassModalVisible(true);
                                    }}
                                    key="share-pass"
                                    disabled={isDisabled}
                                >
                                    Share Pass
                                </Menu.Item>
                            )} */}
                        <Menu.Item
                            className={` ${'text-[#455560]'}`}
                            key="check-in"
                            // disabled={isDisabled}
                            onClick={() => {
                                setOpenClientBookingModal(true);
                                setClientId(record.clientId);
                            }}
                        >
                            Check-In History
                        </Menu.Item>
                    </Menu>
                );

                return (
                    <Dropdown
                        overlay={menu}
                        trigger={['click']}
                        visible={openDropdownKey === record.key}
                        onOpenChange={(visible) => {
                            setOpenDropdownKey(visible ? record.key : null);
                        }}
                    >
                        <MoreOutlined
                            style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                            }}
                        />
                    </Dropdown>
                );
            },
        },
    ];

    const combinedColumns = [...columns, ...actionColumn];
    // console.log('store.memberShipList', store.memberShipListCount);
    return loader ? (
        <FullLoader state={true} />
    ) : (
        <>
            <div className="">
                <CustomTable
                    className="min-w-min"
                    columns={combinedColumns}
                    dataSource1={store.memberShipList?.map((item: any) => ({
                        ...item,
                        membershipId: item.membershipId || '-',
                    }))}
                    membershipStatus={true}
                    statusFilter={statusFilter}
                    setStatusFilter={setStatusFilter}
                    setStatusChanged={setStatusChanged}
                    heading="Membership"
                    onSearch={handleSearch}
                    showBooking={true}
                    showPackages={true}
                    showDateRange={true}
                    loading={listLoader}
                    search={search}
                    showStaffLocation={true}
                    showClient={true}
                    showServiceCategory={false}
                    showBookingStatus={true}
                    showSearch={true}
                    showTabs={false}
                    showClearButton={true}
                    discountManagementButtonForInventory={false}
                    storeId={''}
                    {...{
                        selectedCity,
                        setSelectedCity,
                        selectedClients,
                        setSelectedClients,
                        selectedBookingStatus,
                        setSelectedBookingStatus,
                        selectedServiceCategories,
                        setSelectedServiceCategories,
                        selectedDateRange,
                        setSelectedDateRange,
                        setIsModalVisible,
                        setActiveKey,
                        activeKey,
                    }}
                />
                <div className="b flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={activeKey === 1 ? store.memberShipListCount : 0}
                        pageSize={pageSizes}
                        pageSizeOptions={['10', '20', '50']}
                        onChange={paginate}
                        hideOnSinglePage
                    />
                </div>
            </div>
            {confirmationModalVisible && (
                <CommonConfirmationModal
                    visible={confirmationModalVisible}
                    onConfirm={() => {
                        isCheckIn
                            ? dispatch(
                                  CheckInScheduling({
                                      scheduleId: scheduleData._id,
                                  })
                              )
                            : dispatch(
                                  CancelScheduling({
                                      scheduleId: scheduleData._id,
                                  })
                              );
                        setIsCheckIn(false);
                        setConfirmationModalVisible(false);
                        setScheduleData(null);
                    }}
                    onCancel={() => {
                        setIsCheckIn(false);
                        setConfirmationModalVisible(false);
                        setScheduleData(null);
                    }}
                    message={
                        isCheckIn
                            ? 'Are you sure you want to check-in?'
                            : 'Are you sure you want to cancel the booking?'
                    }
                />
            )}
            {isModalVisible && (
                <BookingModal
                    visible={isModalVisible}
                    onClose={handleClose}
                    tabValue={scheduleData.classType}
                    scheduleId={scheduleData?.isEdit ? scheduleData?._id : null}
                    clientId={scheduleData?.clientId}
                    facilityId={scheduleData?.facilityId}
                    packageId={activeKey === 1 ? scheduleData?.packageId : null}
                    purchaseId={activeKey === 1 ? scheduleData?._id : null}
                    isEdit={scheduleData?.isEdit || false}
                />
            )}
            {isSuspensionModalVisible && (
                <BookingSuspensionModal
                    visible={isSuspensionModalVisible}
                    onClose={handleClose}
                    purchaseId={scheduleData?._id}
                    purchaseData={scheduleData}
                />
            )}
            {openClientBookingModal && (
                <Modal
                    open={openClientBookingModal}
                    onCancel={() => setOpenClientBookingModal(false)}
                    footer={null}
                    width={900}
                    title="Bookings & Check-In History"
                >
                    <ClientBookingCheckInModal clientId={clientId} />
                </Modal>
            )}

            {isSharePassModalVisible && (
                <SharePassTypeModal
                    visible={isSharePassModalVisible}
                    onClose={handleSharePassClose}
                    clientId={scheduleData?.clientId}
                    clientName={scheduleData?.clientName}
                    purchaseId={scheduleData?._id}
                />
            )}
        </>
    );
};

export default MembershipListing;
