import React, { useEffect, useState } from 'react';
import CommonTable from '~/components/common/commonTable';
import { useLoader } from '~/hooks/useLoader';
import { Switch, ConfigProvider } from 'antd';
import clsx from 'clsx';
import { Link } from 'wouter';
import { capitalizeFirstLetter } from '~/components/common/function';
import {
    MembershipList,
    updateMembershipStatus,
} from '~/redux/actions/membership-action';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { useAppSelector } from '~/hooks/redux-hooks';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';

const columns = [
    {
        title: 'Names',
        dataIndex: '',
        render: (record: any) => {
            return (
                <Link
                    to={`/setting/membership/create-membership/${record._id}`}
                >
                    {capitalizeFirstLetter(record.name)}
                </Link>
            );
        },
    },
    {
        title: 'Prefix',
        dataIndex: 'prefix',
    },
    {
        title: 'Counter Position',
        dataIndex: 'counter',
    },
];
interface DataSourceItem {
    key: number;
    name: string;
    status: boolean;
    prefix: string;
    counter: number;
}

const MemberShipListingSettings: React.FC = () => {
    const [loader, startLoader, endLoader] = useLoader();
    const dispatch = useDispatch<AppDispatch>();
    const [membershipRecord, setMembershipRecord] = useState<any>(null);
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState<boolean>(false);

    const handleStatusChange = (record: any) => {
        setMembershipRecord(record);
        setConfirmationModalVisible(true);
    };
    useEffect(() => {
        startLoader();
        dispatch(MembershipList({}))
            .unwrap()
            .then(() => {
                console.log('first');
            })
            .finally(endLoader);
    }, []);
    const store = useAppSelector((state) => ({
        membershipList: state.membership_store.membershipList,
    }));
    const dataSource: DataSourceItem[] = store.membershipList.map(
        (rate: any, i: number) => ({
            key: i,
            _id: rate._id,
            name: rate.name,
            status: rate.isActive,
            prefix: rate.prefix,
            counter: rate.counter,
        })
    );
    const handleConfirmStatusChange = () => {
        if (membershipRecord)
            dispatch(
                updateMembershipStatus({
                    isActive: !membershipRecord.status,
                    membershipId: membershipRecord?._id,
                })
            );
        setConfirmationModalVisible(false);
    };

    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
    };
    const selectColumn = [
        {
            title: 'STATUS',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                // console.log("Record--------------", record)
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    handleBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Switch
                            className={clsx(
                                'rounded-full transition-colors',
                                record.status ? 'bg-switch-on' : 'bg-switch-off'
                            )}
                            id="swtich-off"
                            checkedChildren="ON"
                            // className="bg-[#D0D4D7]"
                            unCheckedChildren="OFF"
                            onChange={() => handleStatusChange(record)}
                            checked={record.status || false}
                        />
                    </ConfigProvider>
                );
            },
        },
    ];
    const combinedColumns = [...columns, ...selectColumn];
    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={combinedColumns}
                dataSource={dataSource}
                loading={loader}
                bulkAction={false}
                backButton={true}
                heading="Membership"
                addNewLink="/setting/membership/create-membership/0"
                addNewTitle="Create Membership"
            />
            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />
        </>
    );
};
export default MemberShipListingSettings;
