import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, ConfigProvider, Form, Input, Typography, Upload } from 'antd';
import Title from 'antd/es/typography/Title';
import React, { useEffect, useState } from 'react';
import ReactQuill from 'react-quill';
import { goBack } from '~/components/common/function';
import { useParams, useLocation } from 'wouter';
import { useLoader } from '~/hooks/useLoader';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import {
    GetAnnouncementById,
    CreateAnnouncement,
    UpdateAnnouncement,
} from '~/redux/actions/announcement-action';
import { UploadImage } from '~/redux/actions/common-action';

const CreateAnnouncements: React.FC = () => {
    const [form] = Form.useForm();
    const { id } = useParams<{ id: string }>();
    const [loader, startLoader, endLoader] = useLoader();
    const dispatch = useDispatch<AppDispatch>();
    const [location, setLocation] = useLocation();
    const [imageUrl, setImageUrl] = useState<string>('');
    const [loading, setLoading] = useState(false);

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? (
                <LoadingOutlined className="opacity-40" />
            ) : (
                <PlusOutlined className="opacity-40" />
            )}
            <div className="opacity-40">Upload</div>
        </button>
    );

    const onFinish = async (values: any) => {
        console.log('Value------------', values);
        startLoader();
        const payLoad: any = {
            title: values.title,
            subtitle: values.subtitle,
            description: values.description,
            imageUrl,
        };
        try {
            if (!id) {
                await dispatch(CreateAnnouncement(payLoad)).unwrap();
            } else {
                payLoad._id = id;
                await dispatch(
                    UpdateAnnouncement({ reqData: payLoad })
                ).unwrap();
            }
            setLocation('/setting/announcements');
        } catch (error) {
            console.error('Error submitting form:', error);
        } finally {
            endLoader();
        }
    };

    const handleImageUpload = (file: any) => {
        console.log('Uploaded file:', file);
        setLoading(true);
        dispatch(UploadImage({ file: file.file }))
            .then((res: any) => {
                console.log('Res------------------', res);
                setImageUrl(res?.payload?.res?.data?.data);
            })
            .finally(() => setLoading(false));
    };

    const fetchAnnouncementDetail = async () => {
        startLoader();
        try {
            await dispatch(GetAnnouncementById({ announcementId: id }))
                .unwrap()
                .then((res: any) => {
                    if (res?.status === 200 || res?.status === 201) {
                        const announcementData = res.data.data;
                        setImageUrl(announcementData.imageUrl);
                        form.setFieldsValue({
                            title: announcementData.title,
                            subtitle: announcementData.subtitle,
                            description: announcementData.description,
                        });
                    }
                });
        } catch (error) {
            console.error('Error fetching data:', error);
        } finally {
            endLoader();
        }
    };

    useEffect(() => {
        if (id) {
            fetchAnnouncementDetail();
        }
    }, [id]);

    return (
        <div>
            <div className="flex items-center gap-4 pb-12">
                <img
                    src="/icons/back.svg"
                    alt="Back"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title className="text-[#1A3353]" level={4}>
                    {`${id ? 'Update' : 'Create'} Announcement`}
                </Title>
            </div>

            <ConfigProvider
                theme={{
                    components: {
                        Typography: {
                            titleMarginBottom: 0,
                            titleMarginTop: 0,
                        },
                        Input: {
                            colorPrimary: '#E6EBF1',
                            colorPrimaryActive: '#E6EBF1',
                            colorPrimaryHover: '#E6EBF1',
                            borderRadius: 4,
                        },
                    },
                    token: {
                        borderRadius: 4,
                    },
                }}
            >
                <div className="flex flex-row items-start gap-6">
                    <div className="w-[65%] ">
                        <Form
                            name="gymCreate"
                            layout="vertical"
                            size="large"
                            initialValues={{
                                amenities: [],
                            }}
                            autoComplete="off"
                            onFinish={onFinish}
                            form={form}
                        >
                            <Form.Item
                                label="Banner Title"
                                name="title"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter Banner Title',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter Banner Title" />
                            </Form.Item>
                            <Form.Item
                                label="Sub Title"
                                name="subtitle"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter Sub Title',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter SubTitle" />
                            </Form.Item>
                            <Form.Item
                                label="Description"
                                name="description"
                                rules={[
                                    {
                                        required: false,
                                        message: 'Please enter description !',
                                    },
                                ]}
                            >
                                <ReactQuill
                                    theme="snow"
                                    // className="h-[20vh] rounded-lg"
                                />
                            </Form.Item>
                            <div className="flex items-end justify-end gap-5  lg:flex-row">
                                <Form.Item>
                                    <Button
                                        // type="default "

                                        className="h-14 w-36 border-1 border-[#1A3353] text-xl  text-[#1A3353]"
                                    >
                                        Cancel
                                    </Button>
                                </Form.Item>
                                <Form.Item>
                                    <Button
                                        type="primary"
                                        className="h-14 w-36  bg-purpleLight text-xl  "
                                        htmlType="submit"
                                    >
                                        Save
                                    </Button>
                                </Form.Item>
                            </div>
                        </Form>
                    </div>
                    <div className="mt-8 w-[35%] rounded-lg border lg:p-10 @sm:w-full @sm:p-5 ">
                        <div className="">
                            <Typography.Title level={5}>
                                <span className="text-primary ">
                                    UPLOAD ANNOUNCEMENT BANNER
                                </span>
                            </Typography.Title>
                            <Upload
                                id="banner-upload"
                                name="BANNER"
                                listType="picture-card"
                                className="avatar-uploader overflow-hidden lg:mt-8 @sm:mt-2"
                                showUploadList={false}
                                // action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                                // beforeUpload={beforeUpload}
                                // onChange={handleChange}
                                customRequest={handleImageUpload}
                                // disabled={!isEdit}
                            >
                                {imageUrl ? (
                                    <div className="relative h-full w-full">
                                        <img
                                            src={imageUrl}
                                            className="object-contain"
                                            alt="avatar"
                                            style={{
                                                width: '100%',
                                                height: '100%',
                                            }}
                                        />
                                        {loading && (
                                            <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/50">
                                                <LoadingOutlined
                                                    style={{
                                                        fontSize: 24,
                                                        color: '#8143D1',
                                                    }}
                                                    spin
                                                />
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    uploadButton
                                )}
                            </Upload>
                        </div>
                    </div>
                </div>
            </ConfigProvider>
        </div>
    );
};

export default CreateAnnouncements;
