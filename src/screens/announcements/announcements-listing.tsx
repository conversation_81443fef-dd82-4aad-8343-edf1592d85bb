import { ConfigProvider, Dropdown, <PERSON>u, Pagination, Switch } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'wouter';
import { navigate } from 'wouter/use-location';
import CommonTable from '~/components/common/commonTable';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '~/components/library/loader/full-loader';
import { MoreOutlined } from '@ant-design/icons';
import clsx from 'clsx';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import {
    AnnouncementList,
    DeleteAnnouncement,
    UpdateAnnouncementStatus,
} from '~/redux/actions/announcement-action';

const columns = [
    {
        title: 'Banner',
        dataIndex: 'imageUrl',
        render: (url: string) => (
            <img
                src={url}
                alt="Banner"
                style={{ width: 60, height: 30 }}
                className="object-cover"
            />
        ),
    },
    {
        title: 'Title',
        dataIndex: 'title',
        render: (title: string, record: any) => (
            <Link to={`/setting/update-announcements/${record._id}`}>
                {title}
            </Link>
        ),
    },
    { title: 'Sub Title', dataIndex: 'subtitle' },
];

const AnnouncementsListing: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();

    const { announcementList, announcementCount } = useSelector(
        (state: any) => state.announcement_store
    );
    const params = getQueryParams();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const [isDeleting, setIsDeleting] = useState(false);
    const [loader, startLoader, endLoader] = useLoader();
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [announcementData, setAnnouncementData] = useState<any>();
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
    }

    useEffect(() => {
        startLoader();
        const requestParams = {
            page: currentPage,
            pageSize: pageSizes,
        } as any;

        dispatch(
            AnnouncementList({
                ...requestParams,
            })
        )
            .unwrap()
            .finally(endLoader);
    }, [currentPage, pageSizes]);

    const openConfirmationModal = (record: any, isDelete: boolean = false) => {
        setIsDeleting(isDelete);
        setAnnouncementData(record);
        setConfirmationModalVisible(true);
    };

    const handleConfirmation = () => {
        if (isDeleting)
            dispatch(
                DeleteAnnouncement({
                    announcementId: announcementData._id,
                })
            );
        else
            dispatch(
                UpdateAnnouncementStatus({
                    announcementId: announcementData._id,
                    isActive: !announcementData.isActive,
                })
            );
        setIsDeleting(false);
        setConfirmationModalVisible(false);
    };

    const handleCancelConfirmation = () => {
        setConfirmationModalVisible(false);
        setIsDeleting(false);
    };

    const selectColumn = [
        {
            title: 'Status',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    handleBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Switch
                            id="swtich-off"
                            className={clsx(
                                'rounded-full transition-colors',
                                record.isActive
                                    ? 'bg-switch-on'
                                    : 'bg-switch-off'
                            )}
                            checkedChildren="ON"
                            unCheckedChildren="OFF"
                            onChange={() =>
                                openConfirmationModal(record, false)
                            }
                            checked={record.isActive || false}
                        />
                    </ConfigProvider>
                );
            },
        },
        {
            title: 'Action',
            dataIndex: '',
            width: '120px',
            align: 'center',
            key: 'action',
            render: (record: any) => {
                const menu = (
                    <Menu>
                        <Menu.Item key="edit">
                            <Link
                                to={`/setting/update-announcements/${record._id}`}
                            >
                                Edit
                            </Link>
                        </Menu.Item>
                        <Menu.Item key="assign-package">
                            <div
                                onClick={() =>
                                    openConfirmationModal(record, true)
                                }
                                className="text-xl text-[#1A3353]"
                            >
                                Delete
                            </div>
                        </Menu.Item>
                    </Menu>
                );
                return (
                    <>
                        <span className="flex justify-center gap-5 ">
                            <div>
                                <Dropdown overlay={menu} trigger={['click']}>
                                    <MoreOutlined
                                        style={{
                                            fontSize: '20px',
                                            cursor: 'pointer',
                                        }}
                                    />
                                </Dropdown>
                            </div>
                        </span>
                    </>
                );
            },
        },
    ];

    const combinedColumns = [...columns, ...selectColumn];

    return (
        <div className="">
            <div className="mt-6">
                <CommonTable
                    className="min-w-min"
                    columns={combinedColumns}
                    dataSource={announcementList}
                    loading={loader}
                    bulkAction={false}
                    backButton={true}
                    heading="Announcements"
                    addNewLink="/setting/create-announcements"
                    addNewTitle="Create Announcements"
                />
                <div className="flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={announcementCount}
                        pageSize={pageSizes}
                        onChange={paginate}
                        pageSizeOptions={['10', '20', '50']}
                        hideOnSinglePage
                    />
                </div>
            </div>
            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmation}
                onCancel={handleCancelConfirmation}
                message={
                    isDeleting
                        ? 'Are you sure you want to delete this announcement?'
                        : 'Are you sure you want to change the status?'
                }
            />
        </div>
    );
};
export default AnnouncementsListing;
