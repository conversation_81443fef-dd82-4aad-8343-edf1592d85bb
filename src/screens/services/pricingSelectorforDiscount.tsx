import { Checkbox, CheckboxProps, Tooltip } from 'antd';
import ConfigProvider, { ConfigConsumer } from 'antd/es/config-provider';

const PricingSelector = (props: any) => {
    const options = props.pricingList?.map((item: any) => ({
        label: (
            <Tooltip
                title={
                    item.price < props.discountValueState
                        ? `Price is less than the discounted amount ₹${props.discountValueState}`
                        : ''
                }
            >
                <div className="flex w-full items-center gap-4">
                    <p>{item.name}</p>
                    {/* <p>(₹{item.price})</p> */}
                    <p>(₹{item.price?.toFixed(2)})</p>
                </div>
            </Tooltip>
        ),
        value: item._id,
        disabled: item.price < props.discountValueState,
    }));

    // const checkAll = props.pricingList.length === props.selectedPackages.length;
    // const indeterminate =
    //     props.selectedPackages.length > 0 &&
    //     props.selectedPackages.length < props.pricingList.length;

    const onChange = (list: string[]) => {
        props.setSelectedPackages(list);
        props.form.setFieldsValue({ pricingPackages: list });
    };

    const onCheckAllChange: CheckboxProps['onChange'] = (e) => {
        // Filter out items with price less than discount value
        const selectableItems = props.pricingList?.filter(
            (item: any) => item.price >= props.discountValueState
        );
        const allIds = selectableItems.map((item: any) => item._id);
        const newList = e.target.checked ? allIds : [];
        props.setSelectedPackages(newList);
        props.form.setFieldsValue({ pricingPackages: newList });
    };
    const selectableItemsCount = props.pricingList?.filter(
        (item: any) => item.price >= props.discountValueState
    ).length;
    const checkAll = selectableItemsCount === props.selectedPackages.length;
    const indeterminate =
        props.selectedPackages.length > 0 &&
        props.selectedPackages.length < selectableItemsCount;

    console.log(
        'props.selectedPackages selectedPackages--------',
        props.selectedPackages
    );
    return (
        <>
            <ConfigProvider
                theme={{
                    token: {
                        // colorBorder: '#8143D1',
                        colorPrimary: '#8143D1',
                        colorPrimaryHover: '#8143D1',
                    },
                }}
            >
                <Checkbox
                    indeterminate={indeterminate}
                    onChange={onCheckAllChange}
                    checked={checkAll}
                    className="mb-4"
                >
                    Select All
                </Checkbox>
                <div className="h-64 overflow-y-scroll">
                    <Checkbox.Group
                        className="flex w-full flex-col gap-4"
                        options={options}
                        value={props.selectedPackages}
                        onChange={onChange}
                    />
                </div>
            </ConfigProvider>
        </>
    );
};
export default PricingSelector;
