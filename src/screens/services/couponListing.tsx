import { MoreOutlined } from '@ant-design/icons';
import { Dropdown, Pagination, Switch } from 'antd';
import { useEffect, useMemo, useState, useCallback } from 'react';
import CommonTable from '~/components/common/commonTable';
import {
    DeleteCoupon,
    CouponList,
    UpdateCouponStatus,
} from '~/redux/actions/coupon.action';
import { useDispatch, useSelector } from 'react-redux';
import { getQueryParams } from '~/utils/getQueryParams';
import Alertify from '~/services/alertify';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { useAppSelector } from '~/hooks/redux-hooks';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import CreateCouponModal from './createCoupon';

const CouponListing = () => {
    const dispatch = useDispatch();
    const [isCouponModalVisible, setIsCouponModalVisible] = useState(false);
    const [dataSource, setDataSource] = useState<any[]>([]);
    const [totalItems, setTotalItems] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [couponData, setCouponData] = useState<any>(null);
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);

    const params = getQueryParams();
    const pageParam = Number(params.page);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );

    const { role } = useAppSelector((state) => state.auth_store);
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );

    const checkPermission = useCallback(
        (permissionType: PERMISSIONS_ENUM) =>
            all_permissions_for_role?.some((module: any) =>
                module.subjects?.some(
                    (subject: any) =>
                        subject.type === SUBJECT_TYPE.PRICING_PRICING &&
                        subject.actions?.some((action: any) =>
                            action.permissions?.some(
                                (permission: any) =>
                                    permission.type === permissionType
                            )
                        )
                )
            ),
        [all_permissions_for_role]
    );

    const hasCouponReadPermission = checkPermission(
        PERMISSIONS_ENUM.COUPON_READ
    );
    const hasCouponWritePermission = checkPermission(
        PERMISSIONS_ENUM.COUPON_WRITE
    );
    const hasCouponUpdatePermission = checkPermission(
        PERMISSIONS_ENUM.COUPON_UPDATE
    );
    const hasCouponDeletePermission = checkPermission(
        PERMISSIONS_ENUM.COUPON_DELETE
    );

    /** Fetch coupons */
    const fetchCoupons = useCallback(
        async (page: number, limit: number) => {
            if (!(hasCouponReadPermission || role === RoleType.ORGANIZATION)) {
                return Alertify.error(
                    "Sorry, you don't have the necessary permissions to perform this action"
                );
            }

            try {
                const response: any = await dispatch(
                    CouponList({ page, perPage: limit })
                );

                setDataSource(response?.payload?.data?.data || []);
                setTotalItems(
                    response?.payload?.data?._metadata?.pagination?.total || 0
                );
            } catch {
                Alertify.error('Failed to fetch vouchers');
            }
        },
        [dispatch, hasCouponReadPermission, role]
    );

    const handleClose = (refresh = false) => {
        if (refresh) fetchCoupons(currentPage, perPage);
        setConfirmationModalVisible(false);
        setIsCouponModalVisible(false);
        setCouponData(null);
    };

    const handleConfirmStatusChange = async () => {
        if (!couponData?._id) return;
        try {
            const response = await dispatch(
                UpdateCouponStatus({
                    couponId: couponData._id,
                    isActive: !couponData.isActive,
                })
            ).unwrap();
            if ([200, 201].includes(response?.status)) {
                Alertify.success('Voucher status updated successfully');
            } else {
                Alertify.error(response?.message || 'Failed to update status');
            }
        } catch (error) {
            console.error('Error updating voucher status:', error);
            Alertify.error('Could not update status!');
        } finally {
            handleClose(false);
        }
    };

    const handleConfirmDelete = async () => {
        if (!couponData?._id) return;

        try {
            const response = await dispatch(
                DeleteCoupon(couponData._id)
            ).unwrap();

            if ([200, 201].includes(response?.status)) {
                Alertify.success('Voucher deleted!');
                await fetchCoupons(currentPage, perPage);
            } else {
                Alertify.error(response?.message || 'Failed to delete voucher');
            }
        } catch (error) {
            console.error('Error deleting voucher:', error);
            Alertify.error('Failed to delete voucher');
        } finally {
            handleClose(false);
        }
    };

    const columns = useMemo(
        () => [
            {
                key: 'name',
                title: 'Name',
                dataIndex: 'name',
            },
            {
                title: 'Price',
                key: 'price',
                dataIndex: 'price',
            },
            {
                title: 'Status',
                key: 'isActive',
                align: 'center',
                render: (record: any) => (
                    <Switch
                        checked={record.isActive}
                        className="rounded-full transition-colors"
                        disabled={
                            !(
                                hasCouponUpdatePermission ||
                                role === RoleType.ORGANIZATION
                            )
                        }
                        checkedChildren="ON"
                        unCheckedChildren="OFF"
                        onChange={() => {
                            setCouponData(record);
                            setConfirmationModalVisible(true);
                        }}
                    />
                ),
            },
            ...(hasCouponUpdatePermission ||
            hasCouponDeletePermission ||
            role === RoleType.ORGANIZATION
                ? [
                      {
                          title: 'Action',
                          key: 'action',
                          width: '120px',
                          align: 'center',
                          render: (record: any) => {
                              const menuItems = [
                                  ...(hasCouponUpdatePermission ||
                                  role === RoleType.ORGANIZATION
                                      ? [
                                            {
                                                key: 'edit',
                                                label: (
                                                    <div
                                                        className="text-xl text-[#1A3353]"
                                                        onClick={() => {
                                                            setCouponData(
                                                                record
                                                            );
                                                            setIsCouponModalVisible(
                                                                true
                                                            );
                                                        }}
                                                    >
                                                        Edit Voucher
                                                    </div>
                                                ),
                                            },
                                        ]
                                      : []),
                                  ...(hasCouponDeletePermission ||
                                  role === RoleType.ORGANIZATION
                                      ? [
                                            {
                                                key: 'delete',
                                                label: (
                                                    <div
                                                        className="text-xl text-[#1A3353]"
                                                        onClick={() => {
                                                            setCouponData({
                                                                ...record,
                                                                isDelete: true,
                                                            });
                                                            setConfirmationModalVisible(
                                                                true
                                                            );
                                                        }}
                                                    >
                                                        Delete Voucher
                                                    </div>
                                                ),
                                            },
                                        ]
                                      : []),
                              ];

                              return (
                                  <Dropdown
                                      menu={{ items: menuItems }}
                                      trigger={['click']}
                                  >
                                      <MoreOutlined
                                          style={{
                                              fontSize: 20,
                                              cursor: 'pointer',
                                          }}
                                      />
                                  </Dropdown>
                              );
                          },
                      },
                  ]
                : []),
        ],
        [
            hasCouponUpdatePermission,
            hasCouponDeletePermission,
            role,
            currentPage,
            perPage,
        ]
    );

    useEffect(() => {
        fetchCoupons(currentPage, perPage);
    }, [currentPage, perPage]);

    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={columns}
                backButton
                dataSource={dataSource}
                heading="Voucher(s)"
                addNewTitle="Create voucher"
                addNewModal={
                    hasCouponWritePermission || role === RoleType.ORGANIZATION
                }
                openModal={() => setIsCouponModalVisible(true)}
            />

            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={totalItems}
                    pageSize={perPage}
                    onChange={(page, pageSize) => {
                        setCurrentPage(page);
                        if (pageSize) setPerPage(pageSize);
                    }}
                    showSizeChanger
                    pageSizeOptions={['10', '20', '50', '100']}
                />
            </div>

            {isCouponModalVisible && (
                <CreateCouponModal
                    couponId={couponData?._id}
                    visible={isCouponModalVisible}
                    handleClose={handleClose}
                />
            )}

            {confirmationModalVisible && (
                <CommonConfirmationModal
                    visible={confirmationModalVisible}
                    onConfirm={
                        couponData?.isDelete
                            ? handleConfirmDelete
                            : handleConfirmStatusChange
                    }
                    onCancel={handleClose}
                    message={`Are you sure you want to ${
                        couponData?.isDelete
                            ? 'delete this voucher?'
                            : 'change the status?'
                    }`}
                />
            )}
        </>
    );
};

export default CouponListing;
