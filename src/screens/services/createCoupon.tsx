import {
    Button,
    ConfigProvider,
    Form,
    Input,
    InputNumber,
    Modal,
    Select,
    Typography,
} from 'antd';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useLoader } from '~/hooks/useLoader';
const { Text } = Typography;
import {
    CouponDetails,
    CouponList,
    CreateCoupon,
    UpdateCoupon,
} from '~/redux/actions/coupon.action';
interface CreateCouponModalProps {
    visible: boolean;
    handleClose: (refresh?: boolean) => void;
    couponId?: string;
    isPosScreen?: boolean;
    onVoucherCreated?: (voucher: any) => void;
}

const CreateCouponModal = ({
    visible,
    handleClose,
    couponId,
    isPosScreen,
    onVoucherCreated,
}: CreateCouponModalProps) => {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();

    const handleFinish = async (values: any) => {
        try {
            startSubmitLoader();
            const payload = {
                ...values,
                price: Number(values.price),
                isSellOnline: true,
                expiredInDays: Number(values.expiredInDays),
            };

            const res = couponId
                ? await dispatch(
                      UpdateCoupon({
                          voucherId: couponId,
                          ...payload,
                      })
                  )
                      .unwrap()
                      .finally(endSubmitLoader)
                : await dispatch(CreateCoupon(payload))
                      .unwrap()
                      .finally(endSubmitLoader);
            if (res?.status === 200 || res?.status === 201) {
                if (isPosScreen && !couponId && onVoucherCreated) {
                    try {
                        const voucherListRes = await dispatch(
                            CouponList({ page: 1, perPage: 15 })
                        ).unwrap();

                        const newVoucher = voucherListRes?.data?.data?.[0];

                        if (newVoucher) {
                            onVoucherCreated(newVoucher);
                        }
                    } catch (error) {
                        console.error('Error fetching voucher list:', error);
                    }
                }

                handleClose(true);
            }
        } catch (error) {
            console.error('Error creating voucher:', error);
        }
    };

    useEffect(() => {
        const fetchCoupon = async () => {
            const res: any = await dispatch(CouponDetails(couponId)).unwrap();
            if (res?.data?.data) {
                const data = res.data.data;
                form.setFieldsValue({
                    expiredInDays: data.expiredInDays,
                    durationUnit: data.durationUnit,
                    name: data.name,
                    price: data.price,
                });
            }
        };
        if (couponId) fetchCoupon();
    }, [couponId]);

    return (
        <Modal
            open={visible}
            closable
            centered
            onCancel={() => handleClose()}
            title={couponId ? 'Edit voucher' : 'Create voucher'}
            footer={null}
            width={800}
        >
            <ConfigProvider
                theme={{
                    components: {
                        Form: { itemMarginBottom: 0 },
                    },
                }}
            >
                <Form
                    form={form}
                    layout="horizontal"
                    size="large"
                    preserve={false}
                    className="create-coupon-form"
                    onFinish={handleFinish}
                >
                    <Form.Item
                        label="Name"
                        name="name"
                        rules={[
                            {
                                required: true,
                                message: 'Please enter voucher name',
                            },
                        ]}
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 20 }}
                    >
                        <Input placeholder="Enter voucher name" />
                    </Form.Item>

                    <Form.Item
                        label="Price"
                        name="price"
                        rules={[
                            {
                                required: true,
                                message: 'Please enter voucher price',
                            },
                        ]}
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 20 }}
                    >
                        <InputNumber
                            controls={false}
                            placeholder="Enter price"
                            style={{ width: '100%' }}
                            min={0}
                        />
                    </Form.Item>

                    <div className="flex w-full items-center justify-between gap-8">
                        <Text className="w-[15%] text-end font-medium text-[#1a3353]">
                            Expiry
                        </Text>
                        <div className="flex w-[80%] gap-8">
                            <Form.Item
                                name="expiredInDays"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Enter expiry duration',
                                    },
                                ]}
                                noStyle
                            >
                                <InputNumber
                                    controls={false}
                                    placeholder="Will expire in days"
                                    min={1}
                                    style={{ flex: 1 }}
                                />
                            </Form.Item>
                            <Form.Item
                                name="durationUnit"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select duration type!',
                                    },
                                ]}
                                noStyle
                            >
                                <Select
                                    placeholder="Select Duration"
                                    options={[
                                        { label: 'Days', value: 'days' },
                                        { label: 'Months', value: 'months' },
                                        { label: 'Years', value: 'years' },
                                    ]}
                                    style={{ flex: 1 }}
                                />
                            </Form.Item>
                        </div>
                    </div>

                    <div className="mt-6 flex justify-end gap-3">
                        <Button onClick={() => handleClose()}>Cancel</Button>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={submitLoader}
                            className="bg-purpleLight text-white"
                        >
                            Save
                        </Button>
                    </div>
                </Form>
            </ConfigProvider>
        </Modal>
    );
};

export default CreateCouponModal;
