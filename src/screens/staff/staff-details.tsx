import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Config<PERSON><PERSON><PERSON>,
    Tabs,
    TabsProps,
    Typography,
} from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useParams } from 'wouter';
import { Asset } from '~/assets';
import { formatStringWithSpaces } from '~/components/common/function';
import ImageUpload from '~/components/common/image-upload-comp';
import PasswordPin from '~/components/staff/password-pin';
import PersonalInfo from '~/components/staff/personal-info';
import ScheduleModal from '~/components/staff/scheduleModal';
import StaffProfile from '~/components/staff/staff-profile';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { UploadImage } from '~/redux/actions/common-action';
import { GetRolesList } from '~/redux/actions/permission-action';
import { summarizeNestedObject } from '~/redux/actions/settings-actions';
import {
    GetStaffProfileDetails,
    UpdateStaffProfile,
} from '~/redux/actions/staff-action';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { formatDate } from '~/utils/formatDate';

const { Title } = Typography;
function goBack() {
    window.history.back();
}

const StaffDetails = () => {
    const [_, setLocation] = useLocation();
    const dispatch = useAppDispatch();
    const { id } = useParams();
    const [activeTabKey, setActiveTabKey] = useState('1');
    const [showAvailabilityModal, setShowvailabilityModal] =
        useState<boolean>(false);

    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        staffRole: state.staff_store.personalInfo?.roleType,
        firstName: state.staff_store.personalInfo?.firstName,
        lastName: state.staff_store.personalInfo?.lastName,
        gender: state.staff_store.personalInfo?.gender,
        email: state.staff_store.personalInfo?.email,
        phone: state.staff_store.personalInfo?.phone,
        staffId: state.staff_store.additionalInfo?.staffId,
        employmentDate: state.staff_store.additionalInfo?.employmentDate,
        defaultLocation: state.staff_store.facilityInfo?.cityNames,
        imageUrl: state.staff_store.personalInfo?.profilePicture,
        staffOnboarding: state.settings_store.staffOnboarding,
    }));
    console.log('staffRole-------------', store.staffRole);
    const openAvailabilityModal = () => {
        setShowvailabilityModal(true);
    };
    const closeAvailabilityModal = () => {
        setShowvailabilityModal(false);
    };

    const ShowTabvalue = summarizeNestedObject(store.staffOnboarding);

    // console.log('staff onboading-----------', ShowTabvalue);
    const [rolesList, setRolesList] = useState([]);
    useEffect(() => {
        dispatch(GetRolesList()).then((res: any) => {
            // console.log('res', res?.payload?.data?.data);
            setRolesList(res?.payload?.data?.data || []);
        });
    }, []);
    const data = [
        {
            label: 'StaffID# :',
            value: `${store.staffId}`,
        },
        {
            label: 'Email :',
            value: `${store.email}`,
        },
        {
            label: 'Role :',
            value: `${
                store.staffRole === RoleType.TRAINER
                    ? 'Instructor'
                    : rolesList.find(
                          (role: any) => role._id === store.staffRole
                      )?.name || formatStringWithSpaces(store.staffRole)
            }`,
        },

        {
            label: 'Gender :',
            value: `${store.gender}`,
        },
        {
            label: 'Facility :',
            value: `${store.defaultLocation}`,
        },
        {
            label: 'Employment Date :',
            value: `${formatDate(store.employmentDate)}`,
        },
    ];

    const handleImageUpload = (file: File) => {
        console.log('Uploaded file:', file);
        dispatch(UploadImage({ file }))
            .unwrap()
            .then((data) => {
                dispatch(
                    UpdateStaffProfile({
                        fields: { profilePicture: data.res.data.data },
                        id,
                    })
                );
            });
    };

    useEffect(() => {
        dispatch(GetStaffProfileDetails({ id }));
    }, []);

    const items: TabsProps['items'] = [
        {
            key: '1',
            label: (
                <div className="flex gap-5">
                    <img src={Asset.profileIconTab} />
                    <div>Profile</div>
                </div>
            ),
            children: <StaffProfile />,
        },
    ];

    if (store.role === RoleType.ORGANIZATION) {
        items.push({
            key: '2',
            label: (
                <div className="flex gap-5">
                    <img src={Asset.passwordIconTab} />
                    <div>Password/ Pin</div>
                </div>
            ),
            children: <PasswordPin setParentActiveTab={setActiveTabKey} />,
        });
    }

    const Profile = [
        {
            key: '1',
            label: (
                <div className="flex gap-5">
                    <img src="../../../public/icons/profile_icon.svg" />
                    <div>Profile</div>
                </div>
            ),
            children: <PersonalInfo />,
        },
        {
            key: '2',
            label: (
                <div className="flex gap-5">
                    <img src="../../../public/icons/password_icon.svg" />
                    <div>Password/ Pin</div>
                </div>
            ),
            children:
                store.role === RoleType.ORGANIZATION ? <PasswordPin /> : null, // Conditionally render PasswordPin
        },
    ];

    // For permissions
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasAvailabilityPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_AVAILABILITY &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_AVAILABILITY_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasStaffUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_STAFF_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.STAFF_UPDATE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    return (
        <ConfigProvider>
            <div>
                <div className="flex items-center gap-4 ">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353]" level={4}>
                        Staff Profile
                    </Title>
                </div>

                <div className="my-10 flex w-full rounded-lg   py-10 shadow-md">
                    <div className="w-[15%] @sm:hidden ">
                        <ImageUpload
                            onUpload={handleImageUpload}
                            imageUrl={store.imageUrl}
                        />
                    </div>
                    <div className=" lg:w-[85%] @sm:w-full ">
                        <div className="flex lg:flex-row lg:items-center lg:justify-between lg:pb-5 lg:pe-10 @sm:flex-col ">
                            <Title
                                className="capitalize text-primary @sm:order-1 @sm:ps-5"
                                level={4}
                            >
                                {store.firstName} {store.lastName}
                            </Title>
                            <div className="flex flex-row gap-5 @sm:justify-between @sm:px-5 @sm:pb-4">
                                {(hasAvailabilityPermission ||
                                    store.role === RoleType.ORGANIZATION) &&
                                    store.staffRole === RoleType.TRAINER && (
                                        <Button
                                            onClick={openAvailabilityModal}
                                            className=" border  border-[#1A3353] bg-[#FAFAFB]    px-6 py-6 text-xl text-[#1A3353]"
                                            type="primary"
                                        >
                                            Appointment Availability
                                        </Button>
                                    )}
                                {/* {ShowTabvalue?.payRates && ( */}
                                {(hasStaffUpdatePermission ||
                                    store.role === RoleType.ORGANIZATION) &&
                                    store.staffRole === RoleType.TRAINER && (
                                        <Button
                                            onClick={() => {
                                                setLocation(
                                                    `/class-pay-rates/${id}`
                                                );
                                            }}
                                            className="bg-purpleLight px-6 py-6 text-xl"
                                            type="primary"
                                        >
                                            Staff Specialization
                                        </Button>
                                    )}
                                {/* )} */}
                            </div>
                        </div>

                        <div className="w-full justify-start gap-x-10 lg:flex lg:flex-wrap ">
                            {data.map((item, index) => (
                                <div
                                    key={index}
                                    className={`mt-5 flex items-center gap-3 lg:w-[31%] @sm:ps-5   `}
                                >
                                    <div
                                        className={` w-auto text-lg font-bold text-[#1A3353]`}
                                    >
                                        <p className="text-2xl font-semibold">
                                            {item.label}
                                        </p>
                                    </div>
                                    <div className="overflow-wrap  break-word w-auto break-words text-xl capitalize text-[#72849A]">
                                        <p>{item.value}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                <div className="bg-red-20 mt-10 @sm:hidden ">
                    <ConfigProvider
                        theme={{
                            components: {},
                        }}
                    >
                        <Tabs
                            activeKey={activeTabKey}
                            onChange={(key) => setActiveTabKey(key)}
                            items={items}
                            tabPosition="left"
                            renderTabBar={(props, DefaultTabBar) => (
                                <DefaultTabBar {...props}>
                                    {(node) => (
                                        <div
                                            style={{
                                                backgroundColor:
                                                    node.key === props.activeKey
                                                        ? 'rgba(129, 67, 209, 0.1)'
                                                        : 'transparent',
                                                marginBottom: '20px',
                                                borderRadius: '4px',
                                                transition:
                                                    'background-color 0.3s',
                                            }}
                                        >
                                            {node}
                                        </div>
                                    )}
                                </DefaultTabBar>
                            )}
                        />
                    </ConfigProvider>
                </div>

                <div className="bg-green-100 lg:hidden">
                    <Collapse items={Profile} bordered={false} />
                </div>
            </div>
            {showAvailabilityModal && (
                <ScheduleModal
                    visible={showAvailabilityModal}
                    onClose={closeAvailabilityModal}
                    isStaffDetails={true}
                    staffName={`${store.firstName} ${store.lastName}`}
                    staffId={id}
                    isStaffDetailsPage={true}
                />
            )}
        </ConfigProvider>
    );
};

export default StaffDetails;
