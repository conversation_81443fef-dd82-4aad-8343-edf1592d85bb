import { EditOutlined } from '@ant-design/icons';
import { Pagination } from 'antd';
import { title } from 'process';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'wouter';
import { navigate } from 'wouter/use-location';
import CommonTable from '~/components/common/commonTable';
import { capitalizeFirstLetter } from '~/components/common/function';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { GymList } from '~/redux/actions/gym-action';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';

const columns = [
    {
        title: 'BRANCH NAME',
        dataIndex: '',
        // width: "25%",
        render: (record: any) => {
            // console.log('Record--------', record);

            return (
                <Link to={`${'#'}`}>
                    {capitalizeFirstLetter(record.facilityName)}
                </Link>
            );
        },
    },
    {
        title: 'ADDRESS',
        dataIndex: '',
        render: (record: any) => {
            // console.log('Record--------', record);

            return <div>{record.address.addressLine1}</div>;
        },
    },

    {
        title: 'EMAIL',
        dataIndex: 'email',
        align: 'center',
        // width: "10%",
        render: (email: any) => {
            // console.log('Record--------', email);

            return <div>{email ? email : '-'}</div>;
        },
    },
];

const gymListing: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();
    const [loader, startLoader, endLoader] = useLoader();
    const { facilityListCount, facilityList } = useSelector(
        (state: any) => state.facility_store
    );

    const searchValue = useSelector(
        (state: any) => state.common_store.searchValue
    );

    // console.log('customerList------------', searchValue);
    const params = getQueryParams();

    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const debouncedRequest = useDebounce((callback) => callback(), 300);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
    }

    useEffect(() => {
        startLoader();
        if (searchValue) {
            debouncedRequest(() => {
                dispatch(
                    FacilitiesList({
                        page: currentPage,
                        pageSize: pageSizes,
                        search: searchValue,
                    })
                )
                    .unwrap()
                    .finally(endLoader);
            });
        } else {
            dispatch(FacilitiesList({ page: currentPage, pageSize: pageSizes }))
                .unwrap()
                .finally(endLoader);
        }
    }, [currentPage, pageSizes, searchValue]);

    const selectColumn = [
        {
            title: 'ACTION',
            dataIndex: '',
            key: 'action',
            render: (record: any) => (
                <Link
                    to={`/facility/create-facility/${record._id}?updateDetails=true`}
                >
                    <EditOutlined />
                </Link>
            ),
        },
    ];

    const combinedColumns = [...columns, ...selectColumn];
    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={combinedColumns}
                dataSource={facilityList}
                addNewLink=""
                bulkAction={true}
                loading={loader}
                addNewTitle=""
                heading="FACILITY"
            />
            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={facilityListCount}
                    pageSize={pageSizes}
                    onChange={paginate}
                    // hideOnSinglePage
                />
            </div>
        </>
    );
};

export default gymListing;
