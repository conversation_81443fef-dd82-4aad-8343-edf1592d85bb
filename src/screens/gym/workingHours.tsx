import React, { useState, useEffect } from 'react';
import {
    Collapse,
    TimePicker,
    Checkbox,
    Button,
    Space,
    Form,
    ConfigProvider,
    Modal,
} from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import type { Dayjs } from 'dayjs';
import Alertify from '~/services/alertify';

const { Panel } = Collapse;

interface TimeSlot {
    from: Dayjs | null;
    to: Dayjs | null;
}

interface WorkingHours {
    [key: string]: TimeSlot[];
}

interface WorkingHoursProps {
    workingHours?: WorkingHours;
    setWorkingHours?: React.Dispatch<React.SetStateAction<WorkingHours>>;
}

const WorkingHoursComponent: React.FC<WorkingHoursProps> = ({
    workingHours,
    setWorkingHours,
}) => {
    const [form] = Form.useForm();

    const [selectedDay, setSelectedDay] = useState<string>('Mon');
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [currentSlotIndex, setCurrentSlotIndex] = useState<number | null>(
        null
    );
    const [checkState, setCheckState] = useState(false);

    const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    const handleDayClick = (day: string) => {
        setSelectedDay(day);
        const slots = workingHours[day];
        const fieldsValue = slots?.reduce((acc, slot, index) => {
            acc[`from-${day}-${index}`] = slot.from;
            acc[`to-${day}-${index}`] = slot.to;
            return acc;
        }, {} as { [key: string]: Dayjs | null });
        console.log({ fieldsValue });
        form.setFieldsValue(fieldsValue);
    };

    const handleAddTimeSlot = () => {
        setWorkingHours((prev) => ({
            ...prev,
            [selectedDay]: [...prev[selectedDay], { from: null, to: null }],
        }));
    };

    const handleRemoveTimeSlot = (index: number) => {
        setWorkingHours((prev) => ({
            ...prev,
            [selectedDay]: prev[selectedDay].filter((_, i) => i !== index),
        }));
    };

    const handleTimeChange = (
        index: number,
        type: 'from' | 'to',
        time: Dayjs | null
    ) => {
        setCheckState(false);
        setWorkingHours((prev) => {
            const updatedHours = {
                ...prev,
                [selectedDay]: prev[selectedDay].map((slot, i) => {
                    if (i === index) {
                        if (
                            type === 'to' &&
                            slot.from &&
                            time &&
                            time.isBefore(slot.from)
                        ) {
                            Alertify.error(
                                "Closing Time can't be earlier than the opening time"
                            );
                            return slot; // Return the slot unchanged
                        }
                        return { ...slot, [type]: time };
                    }
                    return slot;
                }),
            };
            return updatedHours;
        });
    };

    const handleDuplicateForAllDays = (
        e: React.ChangeEvent<HTMLInputElement>,
        index: number
    ) => {
        if (e.target.checked) {
            setCurrentSlotIndex(index);
            setIsModalVisible(true);
        }
    };

    const handleOk = () => {
        if (currentSlotIndex !== null) {
            const slotToDuplicate = workingHours[selectedDay][currentSlotIndex];
            setWorkingHours((prev) => {
                const newWorkingHours = { ...prev };
                daysOfWeek.forEach((day) => {
                    if (day !== selectedDay) {
                        // Replace the existing slot instead of appending
                        newWorkingHours[day] = [{ ...slotToDuplicate }];
                    }
                });
                return newWorkingHours;
            });
        }
        setCheckState(true);
        setIsModalVisible(false);
    };

    const handleCancel = () => setIsModalVisible(false);

    useEffect(() => {
        if (workingHours[selectedDay]?.length === 0) {
            handleAddTimeSlot();
        }
    }, [selectedDay]);

    useEffect(() => {
        handleDayClick(selectedDay);
    }, [workingHours]);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Collapse: {
                        headerBg: '#ffffff',
                    },
                },
            }}
        >
            <Collapse className="w-full rounded-2xl" defaultActiveKey={['1']}>
                <Panel
                    header={
                        <p className="text-2xl font-medium text-[#1A3353]">
                            Working Hours
                        </p>
                    }
                    key="1"
                >
                    <Space direction="vertical" style={{ width: '100%' }}>
                        <Form
                            name="workingHours-form"
                            layout="vertical"
                            size="large"
                            form={form}
                        >
                            <div className="mb-8 flex flex-wrap gap-8">
                                {daysOfWeek.map((day) => (
                                    <div key={day}>
                                        <Button
                                            shape="circle"
                                            disabled={false}
                                            className={`p-2 hover:border-checkbox-checked  ${
                                                selectedDay === day
                                                    ? 'bg-checkbox-checked text-white'
                                                    : 'bg-white'
                                            }`}
                                            // type={
                                            //     selectedDay === day
                                            //         ? 'primary'
                                            //         : 'default'
                                            // }
                                            onClick={() => handleDayClick(day)}
                                        >
                                            {day}
                                        </Button>
                                    </div>
                                ))}
                            </div>
                            {workingHours[selectedDay]?.map((slot, index) => (
                                <div
                                    key={index}
                                    className="mb-7 flex flex-col border-1 px-8 py-4"
                                >
                                    {index > 0 && (
                                        <div className="flex justify-end">
                                            <div
                                                onClick={() =>
                                                    handleRemoveTimeSlot(index)
                                                }
                                            >
                                                <CloseOutlined />
                                            </div>
                                        </div>
                                    )}
                                    <div className="flex w-full lg:flex-row lg:justify-between lg:gap-8 @sm:flex-col">
                                        <div className="lg:w-[50%]">
                                            <Form.Item
                                                label="Opening Time"
                                                name={`from-${selectedDay}-${index}`}
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please enter opening time',
                                                    },
                                                ]}
                                            >
                                                <TimePicker
                                                    className="w-full"
                                                    format="HH:mm"
                                                    placeholder="Opening Time"
                                                    value={slot.from}
                                                    onChange={(time) =>
                                                        handleTimeChange(
                                                            index,
                                                            'from',
                                                            time
                                                        )
                                                    }
                                                />
                                            </Form.Item>
                                        </div>
                                        <div className="lg:w-[50%]">
                                            <Form.Item
                                                label="Closing Time"
                                                name={`to-${selectedDay}-${index}`}
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please enter closing time',
                                                    },
                                                ]}
                                            >
                                                <TimePicker
                                                    className="w-full"
                                                    format="HH:mm"
                                                    placeholder="Closing Time"
                                                    value={slot.to}
                                                    onChange={(time) =>
                                                        handleTimeChange(
                                                            index,
                                                            'to',
                                                            time
                                                        )
                                                    }
                                                />
                                            </Form.Item>
                                        </div>
                                    </div>
                                    <Checkbox
                                        onChange={(e) =>
                                            handleDuplicateForAllDays(e, index)
                                        }
                                        checked={checkState}
                                    >
                                        Duplicate this slot for all days
                                    </Checkbox>
                                </div>
                            ))}
                        </Form>
                    </Space>
                </Panel>
            </Collapse>
            <Modal
                title="Confirm Overwrite"
                open={isModalVisible}
                okText="Yes"
                cancelText="No"
                footer={false}
            >
                <p>Do you want to overwrite this time slot for all days?</p>

                <div className="mt-8 flex justify-end gap-8 @sm:hidden ">
                    <Button
                        onClick={handleCancel}
                        htmlType="button"
                        className="h-14 w-[90px]  border-[#1A3353] bg-[#fff] text-xl text-black "
                    >
                        <p> Cancel </p>
                    </Button>
                    <Button
                        onClick={handleOk}
                        htmlType="submit"
                        className="h-14  w-[90px]   bg-purpleLight text-xl text-white"
                    >
                        <p> Save </p>
                    </Button>
                </div>
            </Modal>
        </ConfigProvider>
    );
};

export default WorkingHoursComponent;
