import { DeleteOutlined } from '@ant-design/icons';
import Title from 'antd/es/typography/Title';
import { useEffect, useState, useCallback } from 'react';
import { goBack } from '~/components/common/function';
import { useDispatch } from 'react-redux';
import { GetSettings, SaveSettings } from '~/redux/actions/settings-actions';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '~/components/library/loader/full-loader';
import { Button, Checkbox, ConfigProvider, Form, Input, Select } from 'antd';
import Alertify from '~/services/alertify';
import { navigate } from 'wouter/use-location';
const { Option } = Select;
interface ProficiencyItem {
    id: number;
    value: string;
}
interface PolicyItem {
    id: number;
    isShown: boolean;
    name: string;
    required: boolean;
    expiry: number | null;
    expiryType: 'Days' | 'Months' | 'Years';
}
interface ClientOnboardingState {
    showPolicies: boolean;
    showAssessment: boolean;
    showProficiencyLevel: boolean;
    assessment: {
        weight: boolean;
        measurement: boolean;
    };
    policies: {
        items: PolicyItem[];
    };
    notes: boolean;
    sharePass: boolean;
    proficiencyLevel?: ProficiencyItem[];
    photoPolicy: boolean;
}

interface SettingsState {
    clientOnboarding: ClientOnboardingState;
}

const initialState: SettingsState = {
    clientOnboarding: {
        showPolicies: false,
        showAssessment: false,
        showProficiencyLevel: false,
        assessment: {
            weight: false,
            measurement: false,
        },
        policies: {
            items: [],
        },
        notes: false,
        sharePass: false,
        proficiencyLevel: [],
        photoPolicy: false,
    },
};

const optionsAssessment = [
    { label: 'Weight', value: 'weight' },
    { label: 'Measurement', value: 'measurement' },
];

const optionsPolicies = [
    { label: 'Facility Waiver', value: 'facilityWaiver' },
    { label: 'Safety Briefing Done', value: 'safetyBriefingDone' },
    { label: 'Check For ID', value: 'checkForID' },
];

const ClientOnboarding = () => {
    const dispatch = useDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const [state, setState] = useState<SettingsState>(initialState);

    const handleCheckboxChange =
        (section: keyof SettingsState, key: string, subKey?: string) =>
        (e: any) => {
            setState((prevState) => {
                const updatedSection = { ...prevState[section] };
                if (subKey) {
                    (updatedSection as any)[key] = {
                        ...(updatedSection as any)[key],
                        [subKey]: e.target.checked,
                    };
                } else {
                    (updatedSection as any)[key] = e.target.checked;
                }
                return { ...prevState, [section]: updatedSection };
            });
        };
    console.log(state, 'kasdkasjdk');
    const handleToggleVisibility = (key: keyof ClientOnboardingState) => () => {
        setState((prevState) => {
            const updatedValue = !prevState.clientOnboarding[key];
            const updated = {
                ...prevState.clientOnboarding,
                [key]: updatedValue,
            };

            if (
                key === 'showProficiencyLevel' &&
                updatedValue &&
                (!updated.proficiencyLevel ||
                    updated.proficiencyLevel.length === 0)
            ) {
                updated.proficiencyLevel = [{ id: Date.now(), value: '' }];
            }

            if (
                key === 'showPolicies' &&
                updatedValue &&
                (!updated.policies?.items ||
                    updated.policies.items.length === 0)
            ) {
                updated.policies = {
                    items: [
                        {
                            id: Date.now(),
                            isShown: false,
                            name: '',
                            required: false,
                            expiry: null,
                            expiryType: 'Months',
                        },
                    ],
                };
            }

            return { ...prevState, clientOnboarding: updated };
        });
    };

    const handleInputChange = (id: number, value: string) => {
        setState((prevState) => {
            const updated = prevState.clientOnboarding.proficiencyLevel!.map(
                (item) => (item.id === id ? { ...item, value } : item)
            );
            return {
                ...prevState,
                clientOnboarding: {
                    ...prevState.clientOnboarding,
                    proficiencyLevel: updated,
                },
            };
        });
    };

    const handleAddInput = () => {
        const lastItem = state.clientOnboarding.proficiencyLevel?.slice(-1)[0];

        if (!lastItem || lastItem.value.trim() === '') {
            Alertify.error(
                'Please fill the previous input before adding a new one'
            );
            return;
        }

        setState((prevState) => ({
            ...prevState,
            clientOnboarding: {
                ...prevState.clientOnboarding,
                proficiencyLevel: [
                    ...prevState.clientOnboarding.proficiencyLevel!,
                    { id: Date.now(), value: '' },
                ],
            },
        }));
    };

    const handleRemoveInput = (id: number) => {
        setState((prevState) => ({
            ...prevState,
            clientOnboarding: {
                ...prevState.clientOnboarding,
                proficiencyLevel:
                    prevState.clientOnboarding.proficiencyLevel!.filter(
                        (item) => item.id !== id
                    ),
            },
        }));
    };

    const fetchData = () => {
        dispatch(GetSettings({}))
            .unwrap()
            .then((data: any) => {
                if (data?.data?.data) {
                    const { _id, ...responseData } = JSON.parse(
                        JSON.stringify(data.data.data)
                    );

                    // Ensure proficiencyLevel has at least one item
                    const proficiencyList =
                        responseData.clientOnboarding.proficiencyLevel;
                    if (
                        Array.isArray(proficiencyList) &&
                        proficiencyList.length === 0
                    ) {
                        proficiencyList.push({ id: Date.now(), value: '' });
                    }

                    // Ensure policies.items exists and has at least one row
                    const policyList =
                        responseData.clientOnboarding.policies?.items;
                    if (!Array.isArray(policyList) || policyList.length === 0) {
                        responseData.clientOnboarding.policies = {
                            items: [
                                {
                                    id: Date.now(),
                                    name: '',
                                    required: false,
                                    expiry: 0,
                                },
                            ],
                        };
                    } else {
                        // Add id to each item if missing
                        responseData.clientOnboarding.policies.items =
                            policyList.map((item: any) => ({
                                ...item,
                                id: item.id || Date.now() + Math.random(),
                                name: item.name || '',
                                required: item.required || false,
                                expiry: item.expiry,
                            }));
                    }

                    setState(responseData);
                }
            })
            .finally(endLoader);
    };

    const saveSettings = async () => {
        const { clientOnboarding } = state;

        const newState = {
            ...state,
            clientOnboarding: {
                ...clientOnboarding,
                assessment: clientOnboarding.showAssessment
                    ? clientOnboarding.assessment
                    : { weight: false, measurement: false },

                policies: clientOnboarding.showPolicies
                    ? {
                          items: clientOnboarding.policies.items.map(
                              ({ id, ...rest }) => rest
                          ),
                      }
                    : {
                          items: [],
                      },

                proficiencyLevel: clientOnboarding.showProficiencyLevel
                    ? clientOnboarding.proficiencyLevel?.filter(
                          (item) => item.value.trim() !== ''
                      ) ?? []
                    : [],

                notes: clientOnboarding.notes,
                sharePass: clientOnboarding.sharePass,
                showAssessment: clientOnboarding.showAssessment,
                showPolicies: clientOnboarding.showPolicies,
                showProficiencyLevel: clientOnboarding.showProficiencyLevel,
            },
        };

        try {
            const res = await dispatch(SaveSettings({ state: newState }));
            const status = res?.payload?.status;
            if (status === 200 || status === 201) {
                fetchData();
                navigate('/setting');
            } else {
                Alertify.error('Failed to save settings. Please try again.');
            }
        } catch (err) {
            Alertify.error('Something went wrong. Please try again later.');
        }
    };

    useEffect(() => {
        startLoader();
        fetchData();
    }, []);
    const handlePolicyItemChange = (
        id: number,
        key: keyof PolicyItem,
        value: any
    ) => {
        setState((prev) => {
            const updatedItems = prev.clientOnboarding.policies.items.map(
                (item) => (item.id === id ? { ...item, [key]: value } : item)
            );
            return {
                ...prev,
                clientOnboarding: {
                    ...prev.clientOnboarding,
                    policies: { items: updatedItems },
                },
            };
        });
    };

    const handleAddPolicyItem = () => {
        const lastItem = state.clientOnboarding.policies.items.slice(-1)[0];
        if (!lastItem || lastItem.name.trim() === '') {
            Alertify.error(
                'Please fill the previous policy before adding a new one'
            );
            return;
        }

        setState((prev) => ({
            ...prev,
            clientOnboarding: {
                ...prev.clientOnboarding,
                policies: {
                    items: [
                        ...prev.clientOnboarding.policies.items,
                        {
                            id: Date.now(),
                            name: '',
                            required: false,
                            expiry: 0,
                            expiryType: 'Days',
                            isShown: true,
                        },
                    ],
                },
            },
        }));
    };

    const handleRemovePolicyItem = (id: number) => {
        setState((prev) => ({
            ...prev,
            clientOnboarding: {
                ...prev.clientOnboarding,
                policies: {
                    items: prev.clientOnboarding.policies.items.filter(
                        (i) => i.id !== id
                    ),
                },
            },
        }));
    };

    return (
        <div>
            <div className="flex items-center gap-4 @sm:mb-10">
                <img
                    src="/icons/back.svg"
                    alt="back"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title className="text-[#1A3353]" level={4}>
                    Client Onboarding
                </Title>
            </div>
            {loader ? (
                <FullLoader state={true} />
            ) : (
                <>
                    <div className="mt-10 flex flex-col gap-14 rounded-xl border px-16 py-10 lg:w-[70%]">
                        <div>
                            <div className="mb-5 flex items-center gap-12">
                                <ConfigProvider theme={{}}>
                                    <Checkbox
                                        onChange={handleToggleVisibility(
                                            'showAssessment'
                                        )}
                                        checked={
                                            state.clientOnboarding
                                                .showAssessment
                                        }
                                    >
                                        Assessment
                                    </Checkbox>
                                </ConfigProvider>
                            </div>
                            {state.clientOnboarding.showAssessment && (
                                <div className="flex flex-col gap-4 px-20">
                                    {optionsAssessment.map((option) => (
                                        <Checkbox
                                            key={option.value}
                                            checked={
                                                state.clientOnboarding
                                                    .assessment[
                                                    option.value as keyof typeof state.clientOnboarding.assessment
                                                ]
                                            }
                                            onChange={handleCheckboxChange(
                                                'clientOnboarding',
                                                'assessment',
                                                option.value
                                            )}
                                        >
                                            {option.label}
                                        </Checkbox>
                                    ))}
                                </div>
                            )}
                        </div>
                        <div>
                            <div className="mb-5 flex items-center gap-12">
                                <Checkbox
                                    onChange={handleToggleVisibility(
                                        'showProficiencyLevel'
                                    )}
                                    checked={
                                        state.clientOnboarding
                                            .showProficiencyLevel
                                    }
                                >
                                    Proficiency Level
                                </Checkbox>
                            </div>
                            {state.clientOnboarding.showProficiencyLevel && (
                                <div className="flex flex-col gap-5 px-20">
                                    {state.clientOnboarding.proficiencyLevel?.map(
                                        (item, index) => (
                                            <div
                                                key={item.id}
                                                className="flex items-center gap-5"
                                            >
                                                <Input
                                                    className="w-[30%]"
                                                    value={item.value}
                                                    onChange={(e) =>
                                                        handleInputChange(
                                                            item.id,
                                                            e.target.value
                                                        )
                                                    }
                                                />
                                                {index !== 0 && (
                                                    <DeleteOutlined
                                                        className="cursor-pointer text-3xl text-red-500"
                                                        onClick={() =>
                                                            handleRemoveInput(
                                                                item.id
                                                            )
                                                        }
                                                    />
                                                )}
                                            </div>
                                        )
                                    )}
                                    <Button
                                        className="h-8 w-10 border-1 border-[#1A3353] text-base text-[#1A3353]"
                                        onClick={handleAddInput}
                                    >
                                        Add
                                    </Button>
                                </div>
                            )}
                        </div>
                        <div>
                            <div className="mb-5 flex items-center gap-12">
                                <Checkbox
                                    onChange={handleToggleVisibility(
                                        'showPolicies'
                                    )}
                                    checked={
                                        state.clientOnboarding.showPolicies
                                    }
                                >
                                    Policies
                                </Checkbox>
                            </div>
                            {state.clientOnboarding.showPolicies && (
                                <div className="flex flex-col gap-4 px-20">
                                    {state.clientOnboarding.policies.items.map(
                                        (item) => (
                                            <div
                                                key={item.id}
                                                className="flex items-center gap-4"
                                            >
                                                <Checkbox
                                                    defaultChecked
                                                    checked={item.isShown}
                                                    onChange={(e) =>
                                                        handlePolicyItemChange(
                                                            item.id,
                                                            'isShown',
                                                            e.target.checked
                                                        )
                                                    }
                                                ></Checkbox>
                                                <Input
                                                    value={item.name}
                                                    onChange={(e) =>
                                                        handlePolicyItemChange(
                                                            item.id,
                                                            'name',
                                                            e.target.value
                                                        )
                                                    }
                                                    className="w-[200px]"
                                                    placeholder="Policy name"
                                                />
                                                <Checkbox
                                                    checked={item.required}
                                                    onChange={(e) =>
                                                        handlePolicyItemChange(
                                                            item.id,
                                                            'required',
                                                            e.target.checked
                                                        )
                                                    }
                                                >
                                                    Is Required ?
                                                </Checkbox>
                                                {/* <span>Expire:</span>
                                            <Input
                                                type="number"
                                                min={1}
                                                className="w-[80px]"
                                                value={item.expiry}
                                                onChange={(e) =>
                                                    handlePolicyItemChange(item.id, 'expiry', Number(e.target.value))
                                                }
                                            />
                                            <Select
                                                placeholder="Select Duration"
                                                value={item.expiryType}
                                                onChange={(value) =>
                                                    handlePolicyItemChange(item.id, 'expiryType', value)
                                                }
                                                className="w-[150px]"
                                            >
                                                <Option value="Days">Days</Option>
                                                <Option value="Months">Months</Option>
                                                <Option value="Years">Years</Option>
                                            </Select> */}
                                                <DeleteOutlined
                                                    onClick={() =>
                                                        handleRemovePolicyItem(
                                                            item.id
                                                        )
                                                    }
                                                    className="cursor-pointer text-3xl text-red-500"
                                                />
                                            </div>
                                        )
                                    )}
                                    <Button
                                        onClick={handleAddPolicyItem}
                                        className="w-[80px]"
                                    >
                                        Add +
                                    </Button>
                                </div>
                            )}
                        </div>
                        <div className="flex items-center gap-12">
                            <Checkbox
                                checked={state.clientOnboarding.notes}
                                onChange={handleCheckboxChange(
                                    'clientOnboarding',
                                    'notes'
                                )}
                            >
                                Notes
                            </Checkbox>
                        </div>
                        <div className="flex items-center gap-12">
                            <Checkbox
                                checked={state.clientOnboarding.sharePass}
                                onChange={handleCheckboxChange(
                                    'clientOnboarding',
                                    'sharePass'
                                )}
                            >
                                Share Pass
                            </Checkbox>
                        </div>
                        <div className="flex items-center gap-12">
                            <Checkbox
                                checked={state.clientOnboarding.photoPolicy}
                                onChange={handleCheckboxChange(
                                    'clientOnboarding',
                                    'photoPolicy'
                                )}
                            >
                                Is Client Photo compulsory ?
                            </Checkbox>
                        </div>
                    </div>

                    <Form>
                        <div className="flex items-end justify-end gap-5 py-16 lg:flex-row">
                            <Form.Item>
                                <Button
                                    type="primary"
                                    className="bg-purpleLight px-14 py-7 text-xl lg:w-[120px]"
                                    htmlType="submit"
                                    onClick={saveSettings}
                                >
                                    Save
                                </Button>
                            </Form.Item>
                        </div>
                    </Form>
                </>
            )}
        </div>
    );
};

export default ClientOnboarding;
