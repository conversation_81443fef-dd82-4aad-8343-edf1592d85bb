import { Button, Input, Modal, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { ArrowRightOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { StaffAvailabilityListV1 } from '~/redux/actions/appointment-action';
import { capitalizeFirstLetter } from '~/components/common/function';

const { Option } = Select;

interface Props {
    visible: boolean;
    onClose: () => void;
    onSubmit: any;
    selectedSchedule?: any;
    loading?: boolean;
    classType: string;
}

const SubstituteTrainerModal: React.FC<Props> = ({
    visible,
    onClose,
    onSubmit,
    selectedSchedule,
    loading,
    classType,
}) => {
    const dispatch = useAppDispatch();
    const [staffOption, setStaffOption] = useState<any>(null);
    const [toTrainer, setToTrainer] = React.useState<string>();

    useEffect(() => {
        if (!visible) {
            setToTrainer(undefined);
        }
    }, [visible]);

    useEffect(() => {
        if (selectedSchedule) {
            const reqData = {
                facilityId: selectedSchedule?.facilityId,
                classType,
                serviceId: selectedSchedule?.serviceCategoryId,
                subTypeId: selectedSchedule?.subTypeId,
                date: dayjs(selectedSchedule?.startDate).format(
                    'YYYY-MM-DDT00:00:00[Z]'
                ),
                startTime: selectedSchedule?.from,
                endTime: selectedSchedule?.to,
            };
            try {
                dispatch(StaffAvailabilityListV1({ reqData }))
                    .unwrap()
                    .then((res: any) => {
                        const staffOptions = res?.data?.data
                            ?.filter(
                                (item: any) =>
                                    item._id !== selectedSchedule?.trainerId
                            )
                            ?.map((item: any) => ({
                                value: item._id,
                                label: capitalizeFirstLetter(
                                    `${item.firstName} ${item.lastName}`
                                ),
                            }));

                        setStaffOption(staffOptions || []);
                    });
            } catch (error) {
                console.error('Error fetching available staff:', error);
                setStaffOption([]);
            }
        }
    }, [selectedSchedule]);

    return (
        <Modal
            title="Substitute Staff"
            open={visible}
            footer={null}
            onCancel={onClose}
        >
            <>
                <div className="flex items-center justify-between gap-4">
                    <Input
                        style={{ flex: 1 }}
                        value={selectedSchedule?.trainerName}
                        disabled
                    />

                    <ArrowRightOutlined className="text-xl" />

                    <Select
                        value={toTrainer}
                        onChange={setToTrainer}
                        style={{ flex: 1 }}
                        showSearch
                        placeholder="To (New Trainer)"
                        filterOption={(input, option) =>
                            String(option?.label ?? '')
                                .toLowerCase()
                                .includes(input.toLowerCase())
                        }
                        options={staffOption}
                    />
                </div>
                <div className="flex flex-row gap-5 lg:justify-end @sm:justify-center">
                    <div className="mt-10" style={{ display: 'flex' }}>
                        <Button
                            onClick={onClose}
                            className="border border-[#1A3353] px-20 py-7 text-2xl"
                        >
                            Cancel
                        </Button>
                    </div>

                    <div
                        className="mt-10"
                        style={{ display: 'flex', gap: '10px' }}
                    >
                        <Button
                            className="bg-purpleLight px-20 py-7 text-2xl"
                            type="primary"
                            htmlType="submit"
                            onClick={() => {
                                onSubmit(toTrainer);
                            }}
                            loading={loading}
                        >
                            Save
                        </Button>
                    </div>
                </div>
            </>
        </Modal>
    );
};

export default SubstituteTrainerModal;
