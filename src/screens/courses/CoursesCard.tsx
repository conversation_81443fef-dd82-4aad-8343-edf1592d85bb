import React, { useEffect, useMemo, useState } from 'react';
import { Button, Dropdown, Modal, Switch } from 'antd';
import { useLocation } from 'wouter';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { CopyPricingAPI } from '~/redux/actions/pricing-actions';
import { useDispatch, useSelector } from 'react-redux';
import { MoreOutlined } from '@ant-design/icons';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { useAppSelector } from '~/hooks/redux-hooks';
import Alertify from '~/services/alertify';

// Define Props Interface
interface CourseCardProps {
    workshopTitle: string;
    courseId: string;
    date: string | null;
    description: string;
    onEnroll?: () => void;
    isActive?: boolean;
    onToggleActive?: (newState: boolean) => void;
    isExpired?: boolean;
    seriviceCategory?: string;
    onCourseListRefresh?: () => void;
}

const CourseCard: React.FC<CourseCardProps> = ({
    workshopTitle,
    date,
    courseId,
    description,
    onEnroll,
    isActive,
    onToggleActive,
    isExpired,
    seriviceCategory,
    onCourseListRefresh,
}) => {
    const [location, setLocation] = useLocation();
    const [isActiveState, setIsActiveState] = React.useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [pendingState, setPendingState] = useState<boolean | null>(null);
    const [confirmationModal, setConfirmationModal] = useState(false);
    const [idToDuplicate, setIdToDuplicate] = useState<string>('');
    useEffect(() => {
        if (isActive) {
            setIsActiveState(isActive);
        }
    }, [isActive]);

    const handleToggle = (checked: boolean) => {
        setPendingState(checked);
        setShowConfirmModal(true); // show modal first
    };

    const handleConfirmToggle = () => {
        if (pendingState !== null) {
            setIsActiveState(pendingState); // update visual switch
            onToggleActive?.(pendingState); // call parent
        }
        setShowConfirmModal(false);
        setPendingState(null);
    };

    const handleCancelToggle = () => {
        setShowConfirmModal(false);
        setPendingState(null);
    };
    const dispatch = useDispatch();
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasPricingWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.PRICING_PRICING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PRICING_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const handleDuplicate = (course_Id: any) => {
        dispatch(CopyPricingAPI({ pricingId: course_Id })).then((res: any) => {
            console.log(res.payload);
            if (res?.payload?.status === 200 || res?.payload?.status === 201) {
                // Alertify.success('Course duplicated successfully');
                setLocation(
                    `/create-pricing/${res.payload.data?.data?._id}?duplicate=true`
                );
                // setConfirmationModal(false);
                // onCourseListRefresh?.();
            }
        });
    };
    const menuItems = [
        {
            key: 'edit',
            label: (
                <p
                    className="text-2xl text-[#000]"
                    onClick={() => {
                        setConfirmationModal(true);
                        // setIdToDuplicate(courseId);
                    }}
                >
                    Duplicate
                </p>
            ),
        },
    ];
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
    }));
    return (
        <>
            <div className="rounded-lg border bg-[#f5f5f5] px-14 py-14">
                <div className=" flex justify-between ">
                    <div>
                        <div className=" flex items-center justify-between gap-2">
                            <h2 className="text-3xl font-medium text-primary">
                                {workshopTitle}
                            </h2>
                        </div>
                        {/* <p className="mt-2 text-18 font-normal text-[#455560]">
                        {seriviceCategory}
                    </p> */}
                        <p className="mt-5 ">{description}</p>
                        {date && (
                            <div className="mb-10 mt-5 flex items-center space-x-4 ">
                                <span className="rounded border bg-white px-6 py-2 text-xl text-[#455560]">
                                    Date : {date}
                                </span>
                                {/* <span className="rounded border bg-white px-2 py-1 text-xl font-medium text-black">
                            From: {time}
                        </span> */}
                            </div>
                        )}

                        {/* <p className="mt-2 text-xl font-medium text-black">
                        Sign Up : {signUpCount}
                    </p> */}
                        <div className="mt-4 flex space-x-4">
                            <Button
                                type="default"
                                className="px-8 py-4 text-14"
                                onClick={() =>
                                    setLocation(
                                        `/courses-customer-list/${courseId}`
                                    )
                                }
                            >
                                Clients
                            </Button>
                            <Button
                                onClick={() =>
                                    setLocation(
                                        `/courses-card-listing/${courseId}`
                                    )
                                }
                                className="bg-purpleLight px-8 py-4 text-14"
                                type="primary"
                            >
                                Manage Schedules
                            </Button>
                            <Button
                                onClick={() => {
                                    setLocation(
                                        `/course-details/${courseId}?serviceType=courses`
                                    );
                                }}
                                className="px-8 py-4 text-14"
                            >
                                Course Details
                            </Button>
                        </div>
                    </div>
                    <div className="flex flex-col items-center justify-between text-right">
                        {(hasPricingWritePermission ||
                            store.role === RoleType.ORGANIZATION) && (
                            <Dropdown
                                menu={{ items: menuItems }}
                                trigger={['click']}
                            >
                                <MoreOutlined
                                    style={{
                                        fontSize: '20px',
                                        cursor: 'pointer',
                                    }}
                                />
                            </Dropdown>
                        )}

                        <Switch
                            className="mb-2"
                            checked={isActiveState && !isExpired}
                            onChange={handleToggle}
                            checkedChildren="Active"
                            disabled={isExpired}
                            unCheckedChildren="Inactive"
                        />
                    </div>
                    <CommonConfirmationModal
                        visible={showConfirmModal}
                        onConfirm={handleConfirmToggle}
                        onCancel={handleCancelToggle}
                        message={`Are you sure you want to ${
                            pendingState ? 'activate' : 'deactivate'
                        } this course?`}
                    />
                </div>
            </div>
            <Modal
                title={<div className="border-b-2">Confirmation</div>}
                open={confirmationModal}
                onOk={() => handleDuplicate(courseId)}
                onCancel={() => setConfirmationModal(false)}
                className="w-[30vw]"
                footer={[
                    <div className="mt-14">
                        <Button
                            className="border-1 border-[#1A3353]"
                            key="back"
                            onClick={() => setConfirmationModal(false)}
                        >
                            Cancel
                        </Button>
                        <Button
                            style={{ marginLeft: 10 }}
                            key="submit"
                            className="bg-purpleLight text-white"
                            onClick={() => handleDuplicate(courseId)}
                            // loading={loader ? loader : false}
                        >
                            Confirm
                        </Button>
                    </div>,
                ]}
            >
                <p className="mt-8">
                    Are you sure you want to duplicate this Course?
                </p>
            </Modal>
        </>
    );
};

export default CourseCard;
