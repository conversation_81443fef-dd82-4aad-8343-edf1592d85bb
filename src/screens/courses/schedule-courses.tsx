import { Pagination } from 'antd';
import React, { useEffect, useState } from 'react';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { courseList, courseUpdate } from '~/redux/actions/courses-action';
import { capitalizeFirstLetter } from '~/components/common/function';
import { getQueryParams } from '~/utils/getQueryParams';
import { navigate } from 'wouter/use-location';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '~/components/library/loader/full-loader';
import dayjs from 'dayjs';
import CourseCard from './CoursesCard';

function formatDateSlashWise(isoDate: string | null | undefined): string {
    if (!isoDate) return 'N/A';

    const date = new Date(isoDate);
    if (isNaN(date.getTime())) return 'N/A';

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
}
interface ScheduleCourses {
    type: string;
    dateRange?: any;
}
const ScheduledCourses: React.FC<ScheduleCourses> = ({ type, dateRange }) => {
    const params = getQueryParams();
    const dispatch = useAppDispatch();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [loader, startLoader, endLoader] = useLoader();
    const [statusChange, setStatusChange] = useState<boolean>(false);

    const store = useAppSelector((state) => ({
        courseList: state.course_store.coursesList,
        courseListCount: state.course_store.coursesListCount,
    }));

    console.log('Course lisiting --------', store.courseList);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    const fetchCourseList = () => {
        startLoader();
        const payload: any = {
            page: currentPage,
            pageSize: pageSizes,
        };

        if (dateRange.startDate && dateRange.endDate) {
            payload.startDate = dayjs(dateRange.startDate, 'DD/MM/YYYY')
                .startOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
            payload.endDate = dayjs(dateRange.endDate, 'DD/MM/YYYY')
                .endOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
        }

        console.log('Payload---------', payload);
        dispatch(courseList(payload))
            .unwrap()
            .then((res: any) => {
                console.log('Res-------', res);
            })
            .finally(endLoader);
    };

    // Refresh handler for course duplication
    const handleCourseListRefresh = () => {
        fetchCourseList();
    };

    useEffect(() => {
        fetchCourseList();
    }, [
        currentPage,
        pageSizes,
        dateRange.startDate,
        dateRange.endDate,
        // statusChange,
    ]);

    return (
        <div className="">
            {loader ? (
                <div className="flex items-center justify-center py-10">
                    <FullLoader state={true} />
                </div>
            ) : (
                <>
                    <div className="space-y-6 p-4">
                        {store.courseList?.map(
                            (course: any, courseIndex: number) => {
                                const appointmentBoxes: any = (
                                    <div className="flex flex-row flex-wrap gap-3">
                                        {course.appointmentTypes?.map(
                                            (type: any, idx: number) => (
                                                <div
                                                    key={idx}
                                                    className="rounded-lg border bg-white px-4 py-2 text-xl text-[#455560]"
                                                >
                                                    {course.serviceCategoryName}{' '}
                                                    - {type.name}
                                                </div>
                                            )
                                        )}
                                    </div>
                                );

                                return (
                                    <CourseCard
                                        seriviceCategory={
                                            course.serviceCategoryName
                                        }
                                        courseId={course._id}
                                        key={courseIndex + course?._id}
                                        workshopTitle={capitalizeFirstLetter(
                                            course.name
                                        )}
                                        date={
                                            course.startDate
                                                ? `${formatDateSlashWise(
                                                      course.startDate
                                                  )} - ${formatDateSlashWise(
                                                      course.endDate
                                                  )}`
                                                : null
                                        }
                                        isActive={course.isActive}
                                        isExpired={course.isExpired}
                                        description={appointmentBoxes}
                                        onCourseListRefresh={
                                            handleCourseListRefresh
                                        }
                                        onToggleActive={(
                                            newStatus: boolean
                                        ) => {
                                            setStatusChange(true);
                                            dispatch(
                                                courseUpdate({
                                                    courseId: course._id,
                                                    isActive: newStatus,
                                                    page: 1,
                                                    pageSize: 10,
                                                })
                                            );
                                        }}
                                    />
                                );
                            }
                        )}

                        <div className="flex justify-center  py-10">
                            <Pagination
                                current={currentPage}
                                total={store.courseListCount}
                                pageSizeOptions={['10', '20', '50']}
                                pageSize={pageSizes}
                                onChange={paginate}
                                hideOnSinglePage
                            />
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

export default ScheduledCourses;
