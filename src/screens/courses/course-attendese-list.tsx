import { MoreOutlined } from '@ant-design/icons';
import { Dropdown, Input, Menu, Pagination, Table } from 'antd';
import Title from 'antd/es/typography/Title';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { getQueryParams } from '~/utils/getQueryParams';
import { useParams } from 'wouter';
import { useLoader } from '~/hooks/useLoader';
import {
    courseDeleteEnrollMent,
    courseEnrollMentCheckIn,
    coursesSchedulingCustomerList,
} from '~/redux/actions/courses-action';
import {
    capitalizeFirstLetter,
    formatDate,
    formatTime,
} from '~/components/common/function';
import { navigate } from 'wouter/use-location';
import {
    CheckInButtonChip,
    PendingButtonChip,
} from '~/components/common/chip-component';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
function goBack() {
    window.history.back();
}

const columns = [
    {
        title: 'NAME',
        dataIndex: 'name',
        key: 'name',
        render: (text: string) => capitalizeFirstLetter(text),
    },
    {
        title: 'PHONE NO ',
        dataIndex: 'mobile',
        key: 'mobile',
    },
    {
        title: 'DATE',
        dataIndex: 'date',
        key: 'date',
        render: (text: string) => formatDate(text),
    },
    {
        title: 'TIME',
        dataIndex: 'date',
        key: 'TIME',
        render: (text: string) => formatTime(text),
    },
    {
        title: 'STATUS',
        dataIndex: 'isCheckedIn',
        key: 'isCheckedIn',
        render: (text: boolean) =>
            text ? <CheckInButtonChip /> : <PendingButtonChip />,
    },
    // {
    //     title: <p className="text-center">ACTION</p>,
    //     dataIndex: 'ACTION',
    //     key: 'ACTION',
    // },
];
const CourseAttendeesList = () => {
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const { id } = useParams<{ id: string }>();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;
    const [confirmModalVisible, setConfirmModalVisible] = useState(false);
    const [clientData, setClientData] = useState<any>();

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [loader, startLoader, endLoader] = useLoader();

    const store = useAppSelector((state) => ({
        courseScheduleCustomerList:
            state.course_store.courseSchedulingCustomerList,
        courseScheduleCustomerListCount:
            state.course_store.courseSchedulingCustomerListCount,
    }));

    console.log(
        'Store----------------------',
        store.courseScheduleCustomerList
    );

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    useEffect(() => {
        startLoader();
        if (id) {
            const payload = {
                page: currentPage,
                pageSize: pageSizes,
                // search: searchParam,
                schedulingId: id,
            };
            dispatch(coursesSchedulingCustomerList({ payload }))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [id, currentPage, pageSizes]);

    const handleEnrollmentAction = () => {
        if (clientData && id) {
            dispatch(courseDeleteEnrollMent({ enrollmentId: clientData?._id }))
                .unwrap()
                .then(() => {
                    dispatch(
                        coursesSchedulingCustomerList({
                            payload: {
                                page: currentPage,
                                pageSize: pageSizes,
                                schedulingId: id,
                            },
                        })
                    );
                    setConfirmModalVisible(false);
                    setClientData(null);
                });
        }
    };

    const handleEnrollmentCheckIn = (record: any) => {
        if (!record?._id || !id) return;

        if (record?._id) {
            const payload = {
                enrollmentId: record._id,
                isCheckedIn: !record.isCheckedIn,
            };
            dispatch(courseEnrollMentCheckIn({ payload }))
                .unwrap()
                .then(() => {
                    dispatch(
                        coursesSchedulingCustomerList({
                            payload: {
                                page: currentPage,
                                pageSize: pageSizes,
                                schedulingId: id,
                            },
                        })
                    );
                });
        }
    };

    const selectColumn = [
        {
            title: 'Action',
            dataIndex: '',
            width: '120px',
            align: 'center',
            key: 'action',
            render: (record: any) => {
                console.log('Record------------', record);
                const menu = (
                    <Menu>
                        <Menu.Item
                            key="edit"
                            onClick={() => handleEnrollmentCheckIn(record)}
                        >
                            <div className="text-14 text-[#1A3353]">
                                {record?.isCheckedIn
                                    ? 'Mark as unarrived'
                                    : 'Check-In'}
                            </div>
                        </Menu.Item>
                        <Menu.Item
                            key="delete"
                            onClick={() => {
                                setClientData(record);
                                setConfirmModalVisible(true);
                            }}
                        >
                            <div className="text-14 text-[#1A3353]">
                                Delete Enrollment
                            </div>
                        </Menu.Item>
                    </Menu>
                );
                return (
                    <>
                        <span className="flex justify-center gap-5 ">
                            <div>
                                <Dropdown overlay={menu} trigger={['click']}>
                                    <MoreOutlined
                                        style={{
                                            fontSize: '20px',
                                            cursor: 'pointer',
                                        }}
                                    />
                                </Dropdown>
                            </div>
                        </span>
                    </>
                );
            },
        },
    ];

    const combinedColumns = [...columns, ...selectColumn];

    return (
        <div className="flex flex-col gap-12">
            <div className="flex flex-row justify-between">
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353] " level={4}>
                        List of Clients
                    </Title>
                </div>
            </div>
            <div className="rounded-lg border px-8 py-6 lg:w-[100%]">
                <Table
                    dataSource={store.courseScheduleCustomerList}
                    columns={combinedColumns}
                    pagination={false}
                    loading={loader}
                />
            </div>
            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={store.courseScheduleCustomerListCount}
                    pageSizeOptions={['10', '20', '50']}
                    pageSize={pageSizes}
                    onChange={paginate}
                    hideOnSinglePage
                />
            </div>

            {confirmModalVisible && (
                <CommonConfirmationModal
                    visible={confirmModalVisible}
                    message={`Are you sure you want to delete ${clientData?.name} from enrolled users?`}
                    onConfirm={handleEnrollmentAction}
                    onCancel={() => {
                        setConfirmModalVisible(false);
                        setClientData(null);
                    }}
                />
            )}
        </div>
    );
};

export default CourseAttendeesList;
