import { DownOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
    Button,
    Checkbox,
    Col,
    ConfigProvider,
    Dropdown,
    Form,
    FormProps,
    Input,
    Menu,
    Row,
    Select,
    Typography,
    Upload,
} from 'antd';
import React, { useEffect, useState } from 'react';
import ReactQuill from 'react-quill';
import { useLocation, useParams } from 'wouter';
import { goBack } from '~/components/common/function';
import FullLoader from '~/components/library/loader/full-loader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { UploadImage } from '~/redux/actions/common-action';
import {
    courseUpdate,
    getCoursesDetails,
} from '~/redux/actions/courses-action';
import Alertify from '~/services/alertify';
import { getQueryParams } from '~/utils/getQueryParams';
const { Title } = Typography;

const CreateCourseDetails = () => {
    const [form] = Form.useForm();
    const [imageUrl, setImageUrl] = useState<string>();
    const [loading, setLoading] = useState(false);
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const params = getQueryParams();

    const serviceTypeParam = params.serviceType;
    const { id } = useParams<{ id: string }>();

    const store = useAppSelector((state) => ({
        courseDetails: state.course_store.courseDetails,
    }));

    console.log('serviceTypeParam----------------', serviceTypeParam);

    useEffect(() => {
        if (serviceTypeParam) {
            form.setFieldsValue({
                classType: serviceTypeParam,
            });
        }
    }, [serviceTypeParam, form]);

    useEffect(() => {
        if (id !== '0') {
            startLoader();
            dispatch(getCoursesDetails({ courseId: id }))
                .unwrap()
                .finally(endLoader);
        }
    }, [id]);

    useEffect(() => {
        if (id && id !== '0' && store.courseDetails) {
            console.log('store.ServiceCategoryDetailData', store.courseDetails);
            if (store.courseDetails) {
                form.setFieldsValue({
                    courseName: store.courseDetails.name,
                    description: store.courseDetails.description,
                    classType: 'courses',
                    featureOnline: store.courseDetails.isFeatured,
                });
                setImageUrl(store.courseDetails?.image);
            }
        }
    }, [id, store.courseDetails, form]);

    const handleImageUpload = (file: any) => {
        console.log('Uploaded file:', file);
        setLoading(true);
        dispatch(UploadImage({ file: file.file }))
            .then((res: any) => {
                setImageUrl(res?.payload?.res?.data?.data);
            })
            .finally(() => setLoading(false));
    };

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </button>
    );

    const onFinish: FormProps['onFinish'] = async (values) => {
        console.log('Received values of form:', values);
        if (!imageUrl) {
            Alertify.error('Image is required for feature a course');
            return;
        }
        if (id && id !== '0') {
            startSubmitLoader();

            try {
                await dispatch(
                    courseUpdate({
                        courseId: id,
                        description: values.description,
                        name: values.courseName,
                        image: imageUrl,
                        isFeatured: values?.featureOnline,
                    })
                )
                    .then((res: any) => {
                        if (
                            res?.payload?.status === 200 ||
                            res?.payload?.status === 201
                        ) {
                            goBack();
                            Alertify.success('Course updated successfully');
                        }
                    })
                    .finally(endSubmitLoader);
                // setLocation('/appointments-types');
            } catch (error) {
                console.error('Error updating service:', error);
            }
        }
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Input: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                        borderRadius: 4,
                    },
                    Select: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                        borderRadius: 4,
                    },
                },
                token: {
                    borderRadius: 4,
                },
            }}
        >
            {loader ? (
                <FullLoader state={true} />
            ) : (
                <>
                    <div className="flex justify-between">
                        <div className="flex items-center gap-4 lg:mb-16 @sm:mb-10 ">
                            <img
                                src="/icons/back.svg"
                                alt="edit"
                                className="h-[10px] cursor-pointer"
                                onClick={goBack}
                            />
                            <Title className="text-[#1A3353]" level={4}>
                                {id === '0'
                                    ? 'Add Course Details'
                                    : 'Course Details'}
                            </Title>
                        </div>
                        {/* <Dropdown
                            trigger={['click']}
                            overlay={moreMenu}
                            placement="bottomRight"
                        >
                            <Button>
                                More
                                <span>
                                    <DownOutlined />
                                </span>
                            </Button>
                        </Dropdown> */}
                    </div>
                    <Row className="justify-around @sm:gap-10 ">
                        <Col
                            className="rounded-lg border p-5  lg:p-10  @sm:w-full"
                            lg={15}
                        >
                            <Form
                                name="gymCreate"
                                layout="vertical"
                                size="large"
                                form={form}
                                initialValues={{
                                    classType: 'courses',
                                }}
                                onFinish={onFinish}
                                autoComplete="off"
                            >
                                <Form.Item
                                    label="Course Name"
                                    name="courseName"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter a course name',
                                        },
                                    ]}
                                >
                                    <Input
                                        // disabled={id !== '0' ? true : false}
                                        placeholder="Enter course name"
                                    />
                                </Form.Item>
                                <Form.Item
                                    label="Type"
                                    name="classType"
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please select Type',
                                        },
                                    ]}
                                >
                                    <Select
                                        placeholder="Select Type"
                                        disabled={
                                            serviceTypeParam || id !== '0'
                                                ? true
                                                : false
                                        }
                                        options={[
                                            {
                                                label: 'Personal Appointment',
                                                value: 'personalAppointment',
                                            },
                                            {
                                                label: 'Classes',
                                                value: 'classes',
                                            },
                                            {
                                                label: 'Courses',
                                                value: 'courses',
                                            },
                                            {
                                                label: 'Bookings',
                                                value: 'bookings',
                                            },
                                        ]}
                                        onChange={() => console.log('Value')}
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Description"
                                    name="description"
                                    rules={[
                                        {
                                            required: false,
                                            message:
                                                'Please enter description !',
                                        },
                                    ]}
                                >
                                    <ReactQuill
                                        theme="snow"
                                        // className="h-[20vh] rounded-lg"
                                    />
                                </Form.Item>

                                <Form.Item
                                    name="featureOnline"
                                    valuePropName="checked"
                                >
                                    <Checkbox className="text-[#455560]">
                                        Feature Online
                                    </Checkbox>
                                </Form.Item>

                                <Form.Item className="mt-8 flex justify-end gap-8 @sm:hidden ">
                                    <Button
                                        // loading={loader}
                                        onClick={() => goBack()}
                                        htmlType="button"
                                        className="w-[110px] border-[#1A3353] bg-[#fff] text-xl  text-[#1A3353] "
                                    >
                                        <p> Cancel </p>
                                    </Button>
                                    <Button
                                        loading={submitLoader}
                                        htmlType="submit"
                                        className="w-[110px] bg-purpleLight text-xl  text-white lg:ms-7"
                                    >
                                        <p> Save </p>
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Col>
                        <Col
                            className="rounded-lg border  lg:p-10  @sm:p-5 "
                            lg={8}
                        >
                            <div className="">
                                <Typography.Title level={5}>
                                    <span className="text-primary ">
                                        UPLOAD IMAGE
                                    </span>
                                </Typography.Title>
                                <Upload
                                    id="banner-upload"
                                    name="avatar"
                                    listType="picture-card"
                                    className="avatar-uploader overflow-hidden lg:mt-8 @sm:mt-2"
                                    showUploadList={false}
                                    // action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                                    // beforeUpload={beforeUpload}
                                    // onChange={handleChange}
                                    customRequest={handleImageUpload}
                                >
                                    {imageUrl ? (
                                        <div className="relative h-full w-full">
                                            <img
                                                src={imageUrl}
                                                className="object-contain"
                                                alt="avatar"
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                }}
                                            />
                                            {loading && (
                                                <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/50">
                                                    <LoadingOutlined
                                                        style={{
                                                            fontSize: 24,
                                                            color: '#8143D1',
                                                        }}
                                                        spin
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        uploadButton
                                    )}
                                </Upload>
                            </div>
                            <Form.Item className="mt-8 flex justify-center  lg:hidden ">
                                <Button
                                    onClick={() => goBack()}
                                    htmlType="button"
                                    className="me-6 w-[110px]  border-[#1A3353] bg-[#fff]  text-xl  text-[#1A3353] "
                                >
                                    Cancel
                                </Button>
                                <Button
                                    loading={submitLoader}
                                    htmlType="submit"
                                    className=" w-[110px] bg-purpleLight text-xl  text-white"
                                >
                                    Save
                                </Button>
                            </Form.Item>
                        </Col>
                    </Row>
                </>
            )}
        </ConfigProvider>
    );
};

export default CreateCourseDetails;
