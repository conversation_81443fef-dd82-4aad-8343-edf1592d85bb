import { Config<PERSON>rovider, Switch } from 'antd';
import { title } from 'process';
import React from 'react';
import CommonTable from '~/components/common/commonTable';

const columns = [
    { title: 'GYM NAME', dataIndex: 'gymName' },
    {
        title: 'SESSION NAME',
        dataIndex: 'sessionName',
        // width: "25%",
    },
    {
        title: 'CATEGORY',
        dataIndex: 'category',
        // width: "25%",
    },
    {
        title: 'PLAN TYPE',
        dataIndex: 'planType',
        // align: "center",
        // width: "10%",
    },
    {
        title: 'TIER',
        dataIndex: 'tier',
        // width: "25%",
    },
    {
        title: 'PRICE',
        dataIndex: 'price',
        // width: "25%",
    },
];

const dataSource = [
    {
        key: 0,
        gymName: 'KNOX',
        planType: 'Single',
        email: '<EMAIL>',
        city: 'Gurgaon',
        category: 'Training',
        sessionName: 'Yoga',
        tier: 'Plus',
        price: '1000 ',
    },
    {
        key: 1,
        gymName: 'My Fitness',
        planType: 'Duo',
        email: '<EMAIL>',
        city: 'Delhi',
        category: 'MMA',
        sessionName: 'Cardio Training',
        tier: 'Premium',
        price: '5000',
    },
    {
        key: 2,
        gymName: 'My Gym',
        planType: 'Group',
        email: '<EMAIL>',
        city: 'Gurgaon',
        category: 'MMA',
        sessionName: 'Body Building',
        tier: 'Elite',
        price: '2000',
    },
    {
        key: 3,
        gymName: 'CultFit',
        planType: 'Single',
        email: '<EMAIL>',
        city: 'Gurgaon',
        category: 'MMA',
        sessionName: 'Biceps',
        tier: 'Elite',
        price: '15000',
    },
];
const SessionListing: React.FC = () => {
    const selectColumn = [
        {
            title: 'STATUS',
            dataIndex: 'status',
            key: 'status',
            render: (record: any) => {
                // console.log("Record--------------", record)
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    colorPrimaryBorder: '#8143d1',
                                    colorPrimary: '#8143d1',
                                    colorTextQuaternary: 'gray',
                                    colorFillQuaternary: 'gray',
                                },
                            },
                        }}
                    >
                        <Switch
                            id="swtich-off"
                            checkedChildren="ON"
                            // className="bg-[#D0D4D7]"
                            unCheckedChildren="OFF"
                            defaultChecked
                        />
                    </ConfigProvider>
                );
            },
        },
    ];

    const combinedColumns = [...columns, ...selectColumn];

    return (
        <CommonTable
            id="swtichoff"
            className="min-w-min"
            columns={combinedColumns}
            dataSource={dataSource}
            addNewLink="/"
            addNewTitle="Create New Session"
            heading="Session"
        />
    );
};

export default SessionListing;
