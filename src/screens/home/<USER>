import React, { useState } from 'react';
import { Card, Select, Typography, Space, Button } from 'antd';
import {
    <PERSON><PERSON><PERSON>,
    Bar,
    XAxis,
    YAxis,
    Tooltip,
    ResponsiveContainer,
    CartesianGrid,
} from 'recharts';

const { Option } = Select;

const mockData = {
    weekly: [
        { day: 'Sun', sales: 25000, checkIns: 18000 },
        { day: 'Mon', sales: 42000, checkIns: 39000 },
        { day: 'Tue', sales: 62000, checkIns: 53000 },
        { day: 'Wed', sales: 41000, checkIns: 30000 },
        { day: 'Thu', sales: 43000, checkIns: 45000 },
        { day: 'Fri', sales: 22000, checkIns: 21000 },
        { day: 'Sat', sales: 31000, checkIns: 28000 },
    ],
    monthly: [
        { day: 'Week 1', sales: 160000, checkIns: 135000 },
        { day: 'Week 2', sales: 180000, checkIns: 148000 },
        { day: 'Week 3', sales: 175000, checkIns: 152000 },
        { day: 'Week 4', sales: 190000, checkIns: 160000 },
    ],
    quarterly: [
        { day: 'Jan', sales: 580000, checkIns: 500000 },
        { day: 'Feb', sales: 540000, checkIns: 470000 },
        { day: 'Mar', sales: 600000, checkIns: 530000 },
    ],
    yearly: [
        { day: 'Jan', sales: 1020000, checkIns: 1500000 },
        { day: 'Feb', sales: 1800000, checkIns: 1580000 },
        { day: 'Mar', sales: 1200000, checkIns: 1430000 },
        { day: 'Apr', sales: 1923000, checkIns: 1645000 },
        { day: 'May', sales: 1857000, checkIns: 1724000 },
        { day: 'Jun', sales: 1789000, checkIns: 1602000 },
        { day: 'Jul', sales: 1145000, checkIns: 1753000 },
        { day: 'Aug', sales: 1972000, checkIns: 1678000 },
        { day: 'Sep', sales: 1564000, checkIns: 1591000 },
        { day: 'Oct', sales: 1111000, checkIns: 1716000 },
        { day: 'Nov', sales: 1639000, checkIns: 1627000 },
        { day: 'Dec', sales: 1988000, checkIns: 1695000 },
    ],
};

const DashboardChart = () => {
    const [selectedMetric, setSelectedMetric] = useState('Sales');
    const [selectedRange, setSelectedRange] = useState('weekly');

    const data = mockData[selectedRange];

    return (
        <div className="h-full pb-16">
            <Space className="mb-4 flex justify-between" direction="horizontal">
                <Space>
                    <Button
                        style={{
                            backgroundColor:
                                selectedMetric === 'Sales'
                                    ? '#8143d1'
                                    : undefined,
                            color:
                                selectedMetric === 'Sales' ? '#fff' : undefined,
                            borderColor:
                                selectedMetric === 'Sales'
                                    ? '#8143d1'
                                    : undefined,
                        }}
                        onClick={() => setSelectedMetric('Sales')}
                    >
                        Sales
                    </Button>
                    <Button
                        style={{
                            backgroundColor:
                                selectedMetric === 'Check Ins'
                                    ? '#8143d1'
                                    : undefined,
                            color:
                                selectedMetric === 'Check Ins'
                                    ? '#fff'
                                    : undefined,
                            borderColor:
                                selectedMetric === 'Check Ins'
                                    ? '#8143d1'
                                    : undefined,
                        }}
                        onClick={() => setSelectedMetric('Check Ins')}
                    >
                        Check Ins
                    </Button>
                </Space>

                <Select
                    defaultValue="weekly"
                    style={{ width: 150 }}
                    onChange={setSelectedRange}
                >
                    <Option value="weekly">Daily</Option>
                    <Option value="monthly">Monthly</Option>
                    {/* <Option value="quarterly">Quarterly</Option> */}
                    <Option value="yearly">Yearly</Option>
                </Select>
            </Space>

            <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="day" />
                    <YAxis tickFormatter={(value) => `${value / 1000}k`} />
                    <Tooltip
                        formatter={(value) => `${value.toLocaleString()}`}
                    />
                    <Bar
                        dataKey={
                            selectedMetric === 'Sales' ? 'sales' : 'checkIns'
                        }
                        fill="#05f3af"
                        radius={[4, 4, 0, 0]}
                    />
                </BarChart>
            </ResponsiveContainer>
        </div>
    );
};

export default DashboardChart;
