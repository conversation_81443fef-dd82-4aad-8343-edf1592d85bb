import Title from 'antd/es/typography/Title';
import React, { useEffect, useState } from 'react';
import { Pagination } from 'antd';
import { useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import FullLoader from '~/components/library/loader/full-loader';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { PlusOutlined } from '@ant-design/icons';

const GymLocation = () => {
    const [_, setLocation] = useLocation();
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader(true);
    const searchValue = useSelector(
        (state: any) => state.common_store.searchValue
    );
    const debouncedRequest = useDebounce((callback) => callback(), 300);
    const { facilityListCount, facilityList } = useSelector(
        (state: any) => state.facility_store
    );

    const [page, setPage] = useState(1);

    const fetchFacilities = () => {
        startLoader();
        const requestParams = {
            page,
            pageSize: 10,
            search: searchValue || undefined,
        };

        dispatch(FacilitiesList(requestParams)).unwrap().finally(endLoader);
    };

    useEffect(() => {
        debouncedRequest(fetchFacilities);
    }, [page, searchValue]);

    const handlePageChange = (newPage: number) => {
        setPage(newPage);
    };

    return (
        <>
            <Title className=" text-[#1A3353]" level={4}>
                Locations and Marketplace Listing
            </Title>
            {loader ? (
                <FullLoader state={true} />
            ) : (
                <div className="flex flex-col gap-7 rounded-2xl border border-[#E6EBF1] py-14 ps-16 lg:my-10 lg:w-[90%]">
                    {facilityList?.map((item: any) => (
                        <div
                            onClick={() => {
                                setLocation(
                                    `/facility/create-facility/${item._id}?updateDetails=true&view=true`
                                );
                            }}
                            key={item._id}
                            className="flex cursor-pointer flex-row items-center justify-between rounded-2xl bg-[#F2F2F2] px-8 py-6 lg:w-[90%] @sm:h-[70px]"
                        >
                            <p className="cursor-pointer text-3xl font-semibold text-[#1A3353]">
                                {item.facilityName}
                                <span className="text-2xl font-semibold">
                                    &nbsp; {item?.address?.addressLine1}
                                </span>
                            </p>
                            <PlusOutlined className="text-18 text-[#1A3353]" />
                        </div>
                    ))}
                    <p className="text-2xl font-medium text-[#455560]">
                        Contact the Hop.Wellness team to onboard a new facility{' '}
                    </p>
                </div>
            )}
            <div className="flex justify-center lg:w-[80%]">
                <Pagination
                    current={page}
                    pageSize={10}
                    total={facilityListCount}
                    onChange={handlePageChange}
                />
            </div>
        </>
    );
};

export default GymLocation;
