import { ArrowUpOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, ConfigProvider, Select, Table, Typography } from 'antd';
import Search from 'antd/es/transfer/search';
import { title } from 'process';
import React, { act, useEffect } from 'react';
import Dashboard<PERSON>hart from './dashboardChart';
// import Dashboard<PERSON>hart from './dashbodar<PERSON>hart';
import PopularPackageChart from './popularPackageChart';
import { useAppSelector } from '~/hooks/redux-hooks';
import { RoleType } from '~/types/enums';
import { useLocation } from 'wouter';
const { Text, Title } = Typography;
const Dashboard = () => {
    const [activeColumns, setActiveColumns] = React.useState('mostActive');
    const mostVisitedColumns = [
        {
            title: 'Name',
            dataIndex: 'name',
        },
        {
            align: 'center',
            title: 'Monthly Visit',
            dataIndex: 'monthlyVisit',
        },
        {
            align: 'center',
            title: 'Last Visit',
            dataIndex: 'lastVisit',
        },
    ];
    const mostVisitedData = [
        {
            key: 0,
            name: '<PERSON><PERSON><PERSON>',
            monthlyVisit: '20(+5)',
            lastVisit: '09/07/2025',
        },
        {
            key: 1,
            name: 'Subham Sharma',
            monthlyVisit: '22(+2)',
            lastVisit: '08/07/2025',
        },
        {
            key: 2,
            name: 'Ravi Tanwer',
            monthlyVisit: '15(+10)',
            lastVisit: '06/07/2025',
        },
        {
            key: 3,
            name: 'Lakshay Jain',
            monthlyVisit: '10(+2)',
            lastVisit: '05/07/2025',
        },
        {
            key: 4,
            name: 'Rahul',
            monthlyVisit: '5(+1)',
            lastVisit: '05/07/2025',
        },
        {
            key: 5,
            name: 'Shivam Gupta',
            monthlyVisit: '25(+5)',
            lastVisit: '05/07/2025',
        },
        {
            key: 6,
            name: 'Shivam Dwivedi',
            monthlyVisit: '45(+10)',
            lastVisit: '05/07/2025',
        },
        {
            key: 7,
            name: 'Navneet',
            monthlyVisit: '10(+2)',
            lastVisit: '05/07/2025',
        },
        {
            key: 8,
            name: 'Abhinav Rana',
            monthlyVisit: '10(+2)',
            lastVisit: '04/07/2025',
        },
        {
            key: 9,
            name: 'Deepak Panghal',
            monthlyVisit: '5',
            lastVisit: '04/07/2025',
        },
    ];

    const popularPackagesColumns = [
        {
            title: 'ID',
            dataIndex: 'id',
        },
        {
            title: 'PACKAGE NAME',
            dataIndex: 'packageName',
        },
        {
            title: 'TIMES PURCHASED',
            dataIndex: 'totalSold',
            align: 'center',
        },
        {
            title: 'ACTION',
            dataIndex: '',
            align: 'center',
            width: '100px',
            render: () => {
                return (
                    <div className="flex justify-center gap-2">
                        <img
                            src="/icons/common/edit.svg"
                            alt="edit"
                            className="h-[20px] cursor-pointer"
                        />
                        <img
                            src="/icons/common/delete.svg"
                            alt="delete"
                            className="h-[20px] cursor-pointer"
                        />
                    </div>
                );
            },
        },
    ];
    const popularPackagesData = [
        {
            key: 0,
            id: 1,
            packageName: 'Basic Package',
            totalSold: 100,
        },
        {
            key: 1,
            id: 2,
            packageName: 'Premium Package',
            totalSold: 50,
        },
        {
            key: 2,
            id: 3,
            packageName: 'Gold Package',
            totalSold: 20,
        },
        {
            key: 3,
            id: 4,
            packageName: 'Platinum Package',
            totalSold: 10,
        },
        {
            key: 4,
            id: 5,
            packageName: 'Diamond Package',
            totalSold: 5,
        },
        {
            key: 5,
            id: 6,
            packageName: 'Ruby Package',
            totalSold: 2,
        },
        {
            key: 6,
            id: 7,
            packageName: 'Sapphire Package',
            totalSold: 1,
        },
        {
            key: 7,
            id: 8,
            packageName: 'Emerald Package',
            totalSold: 1,
        },
        {
            key: 8,
            id: 9,
            packageName: 'Onyx Package',
            totalSold: 1,
        },
        {
            key: 9,
            id: 10,
            packageName: 'Opal Package',
            totalSold: 1,
        },
    ];
    const mostInactiveColumns = [
        {
            title: 'Name',
            dataIndex: 'name',
        },
        // {
        //     align: 'center',
        //     title: 'Monthly Visit',
        //     dataIndex: 'monthlyVisit',
        // },
        {
            align: 'center',
            title: 'Last Visit',
            dataIndex: 'lastVisit',
        },
    ];
    const mostInactiveData = [
        {
            key: 0,
            name: 'Amit Kumar',
            lastVisit: '10/05/2025',
        },
        {
            key: 1,
            name: 'Priya Sharma',
            lastVisit: '12/05/2025',
        },
        {
            key: 2,
            name: 'Vikram Singh',
            lastVisit: '15/05/2025',
        },
        {
            key: 3,
            name: 'Neha Gupta',
            lastVisit: '18/05/2025',
        },
        {
            key: 4,
            name: 'Rajesh Verma',
            lastVisit: '20/05/2025',
        },
        {
            key: 5,
            name: 'Ananya Patel',
            lastVisit: '22/05/2025',
        },
        {
            key: 6,
            name: 'Karan Malhotra',
            lastVisit: '25/05/2025',
        },
        {
            key: 7,
            name: 'Meera Joshi',
            lastVisit: '27/05/2025',
        },
        {
            key: 8,
            name: 'Sanjay Kapoor',
            lastVisit: '29/05/2025',
        },
        {
            key: 9,
            name: 'Pooja Reddy',
            lastVisit: '30/05/2025',
        },
    ];
    const expiringSoonColumns = [
        {
            title: 'Name',
            dataIndex: 'name',
        },
        {
            title: 'Remaining Sessions',
            dataIndex: 'remainingSessions',
            align: 'center',
        },
        {
            align: 'center',
            title: 'Expiring In (Days)',
            dataIndex: 'expiringIn',
        },
        {
            align: 'center',
            title: 'Expiry Date',
            dataIndex: 'expiryDate',
        },
    ];
    const expiringSoonData = [
        {
            key: 0,
            name: 'Arjun Mehta',
            remainingSessions: 'Unlimited',
            expiringIn: '1',
            expiryDate: '13/07/2025',
        },
        {
            key: 1,
            name: 'Sneha Patel',
            remainingSessions: 3,
            expiringIn: '2',
            expiryDate: '14/07/2025',
        },
        {
            key: 2,
            name: 'Mohit Khanna',
            remainingSessions: 'Unlimited',
            expiringIn: '2',
            expiryDate: '20/07/2025',
        },
        {
            key: 3,
            name: 'Divya Sharma',
            remainingSessions: 0,
            expiringIn: '0',
            expiryDate: '05/07/2025',
        },
        {
            key: 4,
            name: 'Kunal Bajaj',
            remainingSessions: 2,
            expiryDate: '05/07/2025',
        },
        {
            key: 5,
            name: 'Nisha Agarwal',
            remainingSessions: 4,
            expiringIn: '4',
            expiryDate: '16/07/2025',
        },
        {
            key: 6,
            name: 'Varun Malhotra',
            remainingSessions: 'Unlimited',
            expiringIn: '5',
            expiryDate: '17/07/2025',
        },
        {
            key: 7,
            name: 'Anjali Desai',
            remainingSessions: 0,
            expiringIn: '0',
            expiryDate: '12/07/2025',
        },
        {
            key: 8,
            name: 'Rohit Choudhary',
            remainingSessions: 2,
            expiringIn: '2',
            expiryDate: '14/07/2025',
        },
        {
            key: 9,
            name: 'Tanvi Kapoor',
            remainingSessions: 1,
            expiringIn: '1',
            expiryDate: '13/07/2025',
        },
    ];
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
    }));
    const [location, setLocation] = useLocation();

    useEffect(() => {
        if (
            store.role === RoleType.FRONT_DESK_ADMIN ||
            store.role === RoleType.TRAINER
        ) {
            setLocation('/point-of-sales');
        }
    }, [store.role]);
    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Card: {
                        bodyPadding: 12,
                    },
                },
            }}
        >
            <Title className=" text-[#1a3353]" level={4}>
                Dashboard
            </Title>
            <div className="flex justify-between gap-8">
                <Card className="flex-1">
                    <Title level={5} className="mb-2 text-[1.1vw]">
                        Total Check-Ins
                    </Title>
                    <div className="mt-4 flex items-center gap-8">
                        <Title className="text-[1.6vw] font-semibold">
                            2,510
                        </Title>
                        <div className="flex items-center gap-2">
                            <Text className="text-[1.2vw] text-green-500">
                                +18
                                <ArrowUpOutlined />
                            </Text>
                        </div>
                    </div>
                    <Text>This month</Text>
                </Card>
                <Card className="flex-1">
                    <Title level={5} className="mb-2 text-[1.1vw]">
                        Total Customers
                    </Title>
                    <div className="mt-4 flex items-center gap-8">
                        <Title className="text-[1.6vw] font-semibold">
                            211
                        </Title>
                        <div className="flex items-center gap-2">
                            <Text className="text-[1.2vw] text-green-500">
                                +50
                                <ArrowUpOutlined />
                            </Text>
                        </div>
                    </div>
                    <Text>Added this month</Text>
                </Card>
                <Card className="flex-1">
                    <Title level={5} className="mb-2 text-[1.1vw]">
                        Total Earnings
                    </Title>
                    <div className="mt-4 flex items-center gap-8">
                        <Title className="text-[1.6vw] font-semibold">
                            ₹19,320
                        </Title>
                        <div className="flex items-center gap-2">
                            <Text className="text-[1.2vw] text-green-500">
                                +15.7%
                                <ArrowUpOutlined />
                            </Text>
                        </div>
                    </div>
                    <Text>Compare to last month</Text>
                </Card>
                <Card className="flex-1">
                    <Title level={5} className="mb-2 text-[1.1vw]">
                        Total Packages Sold
                    </Title>
                    <div className="mt-4 flex items-center gap-8">
                        <Title className="text-[1.6vw] font-semibold">65</Title>
                        <div className="flex items-center gap-2">
                            <Text className="text-[1.2vw] text-green-500">
                                +5.7
                                <ArrowUpOutlined />
                            </Text>
                        </div>
                    </div>
                    <Text>Compare to last month</Text>
                </Card>
                <Card className="flex-1">
                    <Title level={5} className="mb-2 text-[1.1vw]">
                        New Customers
                    </Title>
                    <div className="mt-4 flex items-center gap-8">
                        <Title className="text-[1.6vw] font-semibold">42</Title>
                        <div className="flex items-center">
                            <Text className="text-[1.2vw] text-green-500">
                                <span className="">+46</span>
                                <ArrowUpOutlined className="text-green-500" />
                            </Text>
                        </div>
                    </div>
                    <Text>This month</Text>
                </Card>
            </div>
            {/* <div className="mt-8 flex items-center justify-end gap-8">
                <Button className="bg-[#8143d1] text-white">Add Sales</Button>
                <Button>Share Pass</Button>
                <Button>Add Customer</Button>
                <Button>Add Enquiry</Button>
            </div> */}
            <div className="mt-8 flex gap-4">
                <div className="w-[60%] rounded-xl bg-gray-100 p-8">
                    <DashboardChart />
                </div>
                <div className="w-[40%] rounded-xl border-2 p-4">
                    <div className="mb-4 flex flex-wrap items-center justify-between">
                        <Title level={3}>Customers</Title>
                        <div className="flex gap-4">
                            <Select
                                className="w-[150px]"
                                defaultValue="mostActive"
                                onChange={(value: string) =>
                                    setActiveColumns(value)
                                }
                                options={[
                                    {
                                        label: 'Most Active',
                                        value: 'mostActive',
                                    },
                                    {
                                        label: 'Most Inactive',
                                        value: 'mostInactive',
                                    },
                                ]}
                            />
                            <Select
                                className="w-[150px]"
                                defaultValue="currentMonth"
                                options={[
                                    {
                                        label: 'This Month',
                                        value: 'currentMonth',
                                    },
                                    {
                                        label: 'Last Month',
                                        value: 'lastMonth',
                                    },
                                ]}
                            />
                        </div>
                    </div>
                    {/* <Search allowClear placeholder="Search" /> */}
                    <Table
                        // columns={mostVisitedColumns}
                        columns={
                            activeColumns === 'mostActive'
                                ? mostVisitedColumns
                                : mostInactiveColumns
                        }
                        dataSource={
                            activeColumns === 'mostActive'
                                ? mostVisitedData
                                : mostInactiveData
                        }
                        pagination={false}
                        scroll={{ y: 60 * 8 }}
                    />
                </div>
            </div>
            <div className="mt-8 flex gap-4">
                <div className="w-[60%] rounded-xl border-2 p-4">
                    <div className="my-4 flex items-center justify-between">
                        <Title level={3}>Popular Packages</Title>

                        {/* <div className="w-[300px]">
                            <Search allowClear placeholder="Search" />
                        </div> */}
                        <Select
                            className="w-[150px]"
                            defaultValue="currentMonth"
                            options={[
                                {
                                    label: 'This Month',
                                    value: 'currentMonth',
                                },
                                {
                                    label: 'Last Month',
                                    value: 'lastMonth',
                                },
                            ]}
                        />
                    </div>
                    <Table
                        columns={popularPackagesColumns}
                        dataSource={popularPackagesData}
                        pagination={false}
                        scroll={{ y: 55 * 6 }}
                    />
                </div>
                <div className="w-[40%] rounded-xl bg-gray-100 p-8">
                    {/* <PopularPackageChart /> */}
                    <div className="mb-4 flex items-center justify-between">
                        <Title level={3}>Expiring Soon</Title>
                        {/* <Select
                            className="w-[150px]"
                            defaultValue="currentMonth"
                            options={[
                                {
                                    label: 'This Month',
                                    value: 'currentMonth',
                                },
                                {
                                    label: 'Last Month',
                                    value: 'lastMonth',
                                },
                            ]}
                        /> */}
                    </div>
                    {/* <Search allowClear placeholder="Search" /> */}
                    <Table
                        columns={expiringSoonColumns}
                        dataSource={expiringSoonData}
                        pagination={false}
                        scroll={{ y: 55 * 6 }}
                    />
                </div>
            </div>
        </ConfigProvider>
    );
};

export default Dashboard;
