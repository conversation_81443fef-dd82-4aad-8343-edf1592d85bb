import {
    DownloadOutlined,
    EyeInvisibleOutlined,
    EyeOutlined,
    MoreOutlined,
} from '@ant-design/icons';
import { Menu, Dropdown, Pagination, Tag, Tooltip, Modal, Button } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useMemo, useState, useRef } from 'react';
import { useLocation } from 'wouter';
import { navigate } from 'wouter/use-location';
import CustomTable from '~/components/common/customTable';
import FullLoader from '~/components/library/loader/full-loader';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { FacilitiesList } from '~/redux/actions/facility-action';
import {
    BookedSchedulingListExport,
    BookedSchedulingListV1,
    CancelScheduling,
    CheckInScheduling,
    ExportCheckInHistory,
    PurchasePricingPackagesList,
} from '~/redux/actions/scheduling-action';
import { ServiceCategoryList } from '~/redux/actions/serviceCategoryAction';
import BookingModal from '~/screens/appointment/booking-modal';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { formatDate } from '~/utils/formatDate';
import { getQueryParams } from '~/utils/getQueryParams';
import BookingSuspensionModal from './bookingSuspension-modal';
import Alertify from '~/services/alertify';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import { useSelector } from 'react-redux';
import { GetSettings } from '~/redux/actions/settings-actions';
import ClientBookingCheckInModal from '~/components/common/client-book-check-modal';
import SharePassTypeModal from '../customers/share-pass-type-modal';
import { setCollapsed } from '~/redux/slices/topBar-slice';
import ClientPreview from '../customers/clientPreview';
import {
    multipleCheckInOrder,
    singleCheckInOrder,
} from '~/redux/actions/customer-action';
import ClientListModal from '../payment pages/payment components/group-checkin-modal';
import FamilySharePassModal from '../payment pages/payment components/family-share-modal';

function getStatus(record: any) {
    const now = new Date();
    const startDate = new Date(record.startDate);
    const endDate = new Date(record.endDate);
    if (now < startDate || now > endDate) {
        return 'Inactive';
    }

    if (record.suspensions && record.suspensions.length > 0) {
        const isCurrentlySuspended = record.suspensions.some(
            (suspension: any) => {
                const fromDate = new Date(suspension.fromDate);
                const suspensionEndDate = new Date(suspension.endDate);
                return now >= fromDate && now <= suspensionEndDate;
            }
        );

        if (isCurrentlySuspended) return 'Frozen';
    }
    if (record.remainingSessions < 1) return 'Inactive';
    if (!record.isActive) return 'Inactive';

    return 'Active';
}

const toCamelCase = (str: string): string => {
    return str
        .toLowerCase()
        .split(' ')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
};

const columns = [
    {
        title: 'Customer ID',
        dataIndex: 'customerId',
        hidden: true,
        // onCell: () => ({ style: { minWidth: 120, width: 120 } }),
    },
    {
        title: 'Client Name',
        dataIndex: 'clientName',
        // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    },
    // {
    //     title: 'Membership ID',
    //     dataIndex: 'membershipId',
    //     align: 'center',
    //     render: (membershipId: string) => {
    //         return membershipId && membershipId.trim() !== '' ? (
    //             membershipId
    //         ) : (
    //             <div className="text-center">-</div>
    //         );
    //     },
    //     // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    // },
    {
        title: 'Package Name',
        dataIndex: 'packageName',
        ellipses: true,
        // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
    },
    {
        title: 'Date',
        dataIndex: '',
        // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
        render: (record: any) => {
            return formatDate(record?.date);
        },
    },
    {
        title: 'Time',
        dataIndex: 'createdAt',
        // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
        render: (text: any) => {
            const time = dayjs(text).format('hh:mm A');
            return time;
        },
    },
    {
        title: 'Phone Number',
        dataIndex: 'mobile',
        // width: 80,
        // ellipses: true,
    },
    {
        title: 'Remaining Sessions',
        dataIndex: 'remainingSessions',
        align: 'center',
        render: (text: any, record: any) => {
            const status = getStatus(record);

            if (status === 'Inactive') return '0';

            if (
                typeof text === 'string' &&
                text.toLowerCase().includes('day pass')
            ) {
                return toCamelCase(text); // Use camel case instead of uppercase
            }

            if (text === 9999) {
                return 'Unlimited';
            }

            return text;
        },
    },

    {
        title: 'Location',
        dataIndex: 'location',
        // onCell: () => ({ style: { minWidth: 180, width: 180 } }),
    },
    {
        title: 'Service Category',
        dataIndex: 'serviceCategory',
        // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    },

    {
        title: 'Status',
        dataIndex: '',
        render: (record: any) => {
            const status = getStatus(record);
            const statusColors = {
                Active: 'green',
                Inactive: 'purple',
                Frozen: 'red',
            };

            if (status === 'Frozen' && record.suspensions.length > 0) {
                return (
                    <Tooltip title={`${record.suspensions[0].notes}`}>
                        <Tag color={statusColors[status]}>{status}</Tag>
                    </Tooltip>
                );
            }

            return <Tag color={statusColors[status]}>{status}</Tag>;
        },
    },
    {
        title: 'Shared By',
        dataIndex: 'sharedByName',
        align: 'center',
        width: 100,
        render: (text: string) => {
            if (!text) return '-';

            const truncated =
                text.length > 15 ? text.substring(0, 15) + '...' : text;

            return (
                <Tooltip title={text}>
                    <span>{truncated}</span>
                </Tooltip>
            );
        },
        // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    },
    // {
    //     title: 'Payment Status',
    //     dataIndex: 'paymentStatus',
    //     // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    // },
];

const bookingStatus: any = {
    booked: 'Booked',
    'checked-in': 'Checked In',
    canceled: 'Canceled',
};

// Update the parseParamsFromLocation function to handle all parameters
const parseParamsFromLocation = (location: string) => {
    const search = location.includes('?') ? location.split('?')[1] : '';
    const urlParams = new URLSearchParams(search);
    return {
        isDailyCheckin: urlParams.get('dailyCheckin') === 'true',
        urlActiveTab: urlParams.get('activeTab'),
        urlStartDate: urlParams.get('startDate'),
        urlEndDate: urlParams.get('endDate'),
        urlBookingStatus: urlParams.get('bookingStatus'),
        urlSearch: urlParams.get('search'),
        urlPage: urlParams.get('page'),
        urlPageSize: urlParams.get('pageSize'),
    };
};

const toAPIDateRange = (
    range: [Dayjs | null, Dayjs | null]
): { startDate?: string; endDate?: string } => {
    const [s, e] = range;
    return {
        ...(s && e
            ? {
                  startDate: s.format('YYYY-MM-DD'),
                  endDate: e.format('YYYY-MM-DD'),
              }
            : {}),
    };
};

const BookingListing: React.FC = () => {
    const params = getQueryParams();
    const [location, setLocation] = useLocation();
    const urlParams = parseParamsFromLocation(location);

    const [addBookingModal, setAddBookingModal] = useState(false);

    // const [activeKey, setActiveKey] = useState(Number(params.activeTab) || 1);
    const [activeKey, setActiveKey] = useState(
        urlParams.urlActiveTab
            ? Number(urlParams.urlActiveTab)
            : params.activeTab
            ? Number(params.activeTab)
            : 1
    );
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [selectedClients, setSelectedClients] = useState([]);
    const [selectedDateRange, setSelectedDateRange] = useState<
        [Dayjs | null, Dayjs | null]
    >([
        urlParams.urlStartDate
            ? dayjs(urlParams.urlStartDate)
            : params.startDate
            ? dayjs(params.startDate)
            : null,
        urlParams.urlEndDate
            ? dayjs(urlParams.urlEndDate)
            : params.endDate
            ? dayjs(params.endDate)
            : null,
    ]);

    const [selectedBookingStatus, setSelectedBookingStatus] = useState<any>(
        urlParams.urlBookingStatus
            ? urlParams.urlBookingStatus
            : params.bookingStatus
            ? params.bookingStatus
            : []
    );

    const pageParam = urlParams.urlPage
        ? Number(urlParams.urlPage)
        : Number(params.page);
    const pageSizeParam = urlParams.urlPageSize
        ? Number(urlParams.urlPageSize)
        : Number(params.pageSize);
    // const [selectedDateRange, setSelectedDateRange] = useState([]);
    // const [selectedBookingStatus, setSelectedBookingStatus] = useState([]);
    const [isCheckIn, setIsCheckIn] = useState(false);
    const [scheduleData, setScheduleData] = useState<any>({});
    const [purchaseData, setPurchaseData] = useState<any>({});
    const [selectedServiceCategories, setSelectedServiceCategories] = useState(
        []
    );

    const [selectedCity, setSelectedCity] = useState([]);
    const [loader, startLoader, endLoader] = useLoader(true);
    const [filterLoader, startFilterLoader, endFilterLoader] = useLoader(false);
    const [search, setSearch] = useState(params.search);
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
    const [isSuspensionModalVisible, setIsSuspensionModalVisible] =
        useState<boolean>(false);
    const [isSharePassModalVisible, setIsSharePassModalVisible] =
        useState<boolean>(false);

    const [openClientBookingModal, setOpenClientBookingModal] = useState(false);
    const [clientId, setClientId] = useState<string>('');
    const dispatch = useAppDispatch();

    const [clientDetails, setClientDetails] = useState<boolean>(false);
    const [clientUpdatePermission, setClientUpdatePermission] =
        useState<boolean>(false);
    const [selectedRowIndex, setSelectedRowIndex] = useState<number>(-1);

    const [multipleSharePassOpen, setMultipleSharePassOpen] =
        useState<boolean>(false);
    const [multisharePassData, setMultiSharePassData] = useState<any>(false);
    const [familyPassShare, setFamilyPassShare] = useState<any>(false);
    const [checkInModalVisible, setCheckInModalVisible] = useState(false);
    const [sharedClientsForCheckIn, setSharedClientsForCheckIn] = useState<any>(
        []
    );
    const [selectedClientData, setSelectedClientData] = useState<any>(null);
    const [confirmationVisible, setConfirmationVisible] = useState(false);
    const [selectedCheckInData, setSelectedCheckInData] = useState<any>(null);
    const [checkInSuccessModalVisible, setCheckInSuccessModalVisible] =
        useState(false);
    const [checkInSuccessData, setCheckInSuccessData] = useState<any[]>([]);
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();

    const handleSetCollapsed = (val: boolean) => {
        dispatch(setCollapsed(val));
    };

    console.log('Params:', params);

    const viewClient = (clientId: string) => {
        handleSetCollapsed(true);
        setClientId(clientId);
        setClientDetails(true);
    };
    const cancelViewClient = () => {
        handleSetCollapsed(false);
        setClientId('');
        setClientDetails(false);
    };
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasClientUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.CLIENTS_UPDATE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    // console.log('clientUpdatePermission', clientId);
    const containerRef = useRef<HTMLDivElement>(null);

    const columns2 = [
        {
            title: 'Client Name',
            dataIndex: 'clientName',
            // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
        },
        {
            title: 'Package Name',
            dataIndex: 'packageName',
            // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
        },
        {
            title: 'Date',
            dataIndex: 'date',
            // width: 150,
        },
        {
            title: 'Start Time',
            dataIndex: 'startTime',
            // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
        },
        // {
        //     title: 'End Time',
        //     dataIndex: 'endTime',
        //     // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
        // },
        {
            title: 'Phone',
            dataIndex: 'clientPhone',
            // width: 250,
        },
        {
            title: 'Location',
            dataIndex: 'location',
            // onCell: () => ({ style: { minWidth: 120, width: 120 } }),
        },
        {
            title: 'Service Category',
            dataIndex: 'serviceCategory',
            // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
        },

        {
            title: 'Shared By',
            dataIndex: 'sharedBy',
            // align: 'center',
            width: 100,
            render: (text: string) => {
                if (!text) return '-';

                const truncated =
                    text.length > 15 ? text.substring(0, 15) + '...' : text;

                return (
                    <Tooltip title={text}>
                        <span>{truncated}</span>
                    </Tooltip>
                );
            },
            // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
        },

        {
            title: 'Booking Status',
            dataIndex: 'bookingStatus',
            // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
            render: (text: any) => {
                const statusClass =
                    text === 'booked'
                        ? 'bg-green-100 bg-opacity-50 w-fit text-green-500 py-1 px-3 rounded'
                        : text === 'checked-in'
                        ? 'bg-purple-100 text-primary py-1 w-fit px-3 rounded'
                        : text === 'canceled'
                        ? 'bg-red-100 text-primary py-1 w-fit px-3 rounded'
                        : 'py-1 px-3';
                return <div className={statusClass}>{bookingStatus[text]}</div>;
            },
        },
        {
            title: 'View',
            key: 'view',
            align: 'center',
            render: (record: any, _: any, index: number) => {
                return (
                    <div
                        className="text-2xl"
                        onClick={() => {
                            setSelectedRowIndex(index);
                            // console.log('The current record is:::::', record);

                            viewClient(record.userClientId);
                            setClientUpdatePermission(
                                hasClientUpdatePermission
                            );
                        }}
                    >
                        {clientDetails && clientId === record.userClientId ? (
                            <EyeInvisibleOutlined />
                        ) : (
                            <EyeOutlined />
                        )}
                    </div>
                );
            },
        },
    ];
    const store = useAppSelector((state) => ({
        schedulingList: state.scheduling_store.schedulingListv1,
        schedulingCount: state.scheduling_store.schedulingCountV1,
        purchasedPriceList: state.scheduling_store.purchasePricingList,
        purchasedPriceListCount:
            state.scheduling_store.purchasePricingListCount,
        clientOnboarding: state.settings_store.clientOnboarding,
        role: state.auth_store.role,
        organizationId: state.auth_store.organizationId,
    }));

    // const pageParam = Number(params.page);
    // const pageSizeParam = Number(params.pageSize);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    // for share pass option(should be visible or not)
    const [sharePassOption, setSharePassOption] = useState(false);
    const [sharePassEnabled, setSharePassEnabled] = useState(false);
    const debouncedRequest = useDebounce((callback) => callback(), 300);

    const hasBookingWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_SCHEDULE_BOOKING
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const hasBookingReadPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_SCHEDULE_READ
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const hasBookingUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_UPDATE_BOOKING
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const hasBookingCheckInPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_CHECKIN
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const hasBookingCancelPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_CANCEL
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const handleConfirmCheckIn = async () => {
        if (!selectedCheckInData) return;

        startSubmitLoader();

        const payload = {
            packageId: selectedCheckInData?.packageId,
            userId: selectedCheckInData?.userId,
            invoiceId: selectedCheckInData?.invoiceId,
            organizationId: store.organizationId,
            facilityId: selectedCheckInData?.facilityId,
        };

        try {
            await dispatch(singleCheckInOrder(payload))
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;
                    if (status === 200 || status === 201) {
                        setConfirmationVisible(false);
                        setSelectedCheckInData(null);
                        const checkInDetails = res?.data?.data || [];
                        setCheckInSuccessData(checkInDetails);
                        setCheckInSuccessModalVisible(true);
                    }
                });
        } catch (error) {
            console.log('Check-In failed');
        } finally {
            endSubmitLoader();
        }
    };

    const handleCheckIn = async (clientsIds: any) => {
        const purchaseId = clientsIds;
        if (!purchaseId) return;

        startSubmitLoader();
        const payload = {
            purchaseIds: purchaseId,
            organizationId: store.organizationId,
            facilityId: purchaseData?.facilityId,
        };
        try {
            await dispatch(multipleCheckInOrder(payload))
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;
                    if (status === 200 || status === 201) {
                        setCheckInModalVisible(false);
                        const checkInDetails = res?.data?.data || [];
                        setCheckInSuccessData(checkInDetails);
                        setCheckInSuccessModalVisible(true);
                    }
                });
        } catch (error) {
            console.error('Check-In Failed:', error);
        } finally {
            endSubmitLoader();
        }
    };

    const handleClose = () => {
        const [rangeStart, rangeEnd] = selectedDateRange || [];

        console.log('first date', rangeStart);
        console.log('second date', rangeEnd);

        const startDateISO = rangeStart
            ? dayjs(rangeStart)
                  .startOf('day')
                  .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
            : undefined;

        const endDateISO = rangeEnd
            ? dayjs(rangeEnd).endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
            : undefined;
        dispatch(
            BookedSchedulingListV1({
                page: 1,
                search: search || undefined,
                pageSize: 10,
                facilityId: selectedCity,
                serviceCategory: selectedServiceCategories,
                startDate: startDateISO,
                endDate: endDateISO,
                classType: 'bookings',
            })
        );
        setIsModalVisible(false);
        setScheduleData(null);
        setPurchaseData(null);
        setIsSuspensionModalVisible(false);
    };
    const handleExport = () => {
        const [rangeStart, rangeEnd] = selectedDateRange || [];

        const startDateISO = rangeStart
            ? dayjs(rangeStart)
                  .startOf('day')
                  .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
            : undefined;

        const endDateISO = rangeEnd
            ? dayjs(rangeEnd).endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
            : undefined;
        dispatch(
            BookedSchedulingListExport({
                search: search || undefined,
                facilityId: selectedCity,
                serviceCategory: selectedServiceCategories,
                startDate: startDateISO,
                endDate: endDateISO,
                classType: 'bookings',
            })
        )
            .unwrap()
            .then((res: any) => {
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    const blob = new Blob([res.data], {
                        type: 'text/csv;charset=utf-8;',
                    });
                    const url = window.URL.createObjectURL(blob);

                    const link = document.createElement('a');
                    link.href = url;
                    const date = new Date();
                    const istOffset = 330 * 60 * 1000; // IST is UTC+5:30
                    const istDate = new Date(date.getTime() + istOffset);
                    const istTimestamp = istDate
                        .toISOString()
                        .replace(/T/, '_')
                        .replace(/:/g, '-')
                        .split('.')[0];

                    const filename = `bookings_Report_${istTimestamp}`;
                    link.setAttribute('download', `${filename}.csv`);

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            })
            .catch((error: any) => {
                console.error('Export failed:', error);
            });
    };
    const handleExportCheckInHistory = () => {
        // const [rangeStart, rangeEnd] = selectedDateRange || [];

        // const startDateISO = rangeStart
        //     ? dayjs(rangeStart)
        //           .startOf('day')
        //           .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
        //     : undefined;

        // const endDateISO = rangeEnd
        //     ? dayjs(rangeEnd).endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
        //     : undefined;
        dispatch(
            ExportCheckInHistory({
                clientId: clientId,
                responseType: 'stream',
            })
        )
            .unwrap()
            .then((res: any) => {
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    const blob = new Blob([res.data], {
                        type: 'text/csv;charset=utf-8;',
                    });
                    const url = window.URL.createObjectURL(blob);

                    const link = document.createElement('a');
                    link.href = url;
                    const date = new Date();
                    const istOffset = 330 * 60 * 1000; // IST is UTC+5:30
                    const istDate = new Date(date.getTime() + istOffset);
                    const istTimestamp = istDate
                        .toISOString()
                        .replace(/T/, '_')
                        .replace(/:/g, '-')
                        .split('.')[0];

                    const filename = `bookings_Report_${istTimestamp}`;
                    link.setAttribute('download', `${filename}.csv`);

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            })
            .catch((error: any) => {
                console.error('Export failed:', error);
            });
    };

    const getList = () => {
        if (activeKey === 2) {
            if (
                hasBookingReadPermission ||
                store.role === RoleType.ORGANIZATION
            ) {
                const [rangeStart, rangeEnd] = selectedDateRange || [];

                console.log('first date', rangeStart);
                console.log('second date', rangeEnd);

                const startDateISO = rangeStart
                    ? dayjs(rangeStart)
                          .startOf('day')
                          .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
                    : undefined;

                const endDateISO = rangeEnd
                    ? dayjs(rangeEnd)
                          .endOf('day')
                          .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
                    : undefined;
                debouncedRequest(() => {
                    dispatch(
                        BookedSchedulingListV1({
                            page: currentPage,
                            search: search || undefined,
                            pageSize: pageSizes,
                            facilityId: selectedCity,
                            serviceCategory: selectedServiceCategories,
                            ...(selectedBookingStatus.length && {
                                scheduleStatus: selectedBookingStatus,
                            }),
                            startDate: startDateISO,
                            endDate: endDateISO,
                            classType: 'bookings',
                        })
                    ).finally(endFilterLoader);
                });
            } else {
                Alertify.error(
                    "Sorry, you don't have the necessary permissions to perform this action"
                );
            }
        } else if (activeKey === 1) {
            const [rangeStart, rangeEnd] = selectedDateRange || [];

            console.log('first date', rangeStart);
            console.log('second date', rangeEnd);

            const startDateISO = rangeStart
                ? dayjs(rangeStart)
                      .startOf('day')
                      .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
                : undefined;

            const endDateISO = rangeEnd
                ? dayjs(rangeEnd)
                      .endOf('day')
                      .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
                : undefined;

            const payload = {
                page: currentPage,
                pageSize: pageSizes,
                ...(search && { search: search }),
                ...(selectedCity?.length > 0 && {
                    facilityIds: selectedCity,
                }),
                ...(selectedServiceCategories?.length > 0 && {
                    serviceCategoryIds: selectedServiceCategories,
                }),
                ...(selectedDateRange?.length > 0 && {
                    startDate: startDateISO,
                    endDate: endDateISO,
                }),
            };
            debouncedRequest(() => {
                if (
                    hasBookingReadPermission ||
                    store.role === RoleType.ORGANIZATION
                ) {
                    dispatch(
                        PurchasePricingPackagesList({
                            reqData: payload,
                            classType: 'bookings',
                        })
                    ).finally(endFilterLoader);
                } else {
                    Alertify.error(
                        "Sorry, you don't have the necessary permissions to perform this action"
                    );
                }
            });
        }
    };

    useEffect(() => {
        if (
            search ||
            currentPage > 1 ||
            selectedCity.length ||
            selectedServiceCategories.length ||
            (selectedDateRange?.[0] && selectedDateRange?.[1]) ||
            selectedBookingStatus.length
        )
            startFilterLoader();
        getList();
        endLoader();
    }, [
        search,
        currentPage,
        pageSizes,
        selectedCity,
        selectedServiceCategories,
        selectedDateRange,
        activeKey,
        selectedBookingStatus,
        isSuspensionModalVisible,
        isModalVisible,
    ]);

    useEffect(() => {
        dispatch(FacilitiesList({ page: 1, pageSize: 10 }));
        dispatch(GetSettings({}));
        dispatch(
            ServiceCategoryList({
                page: 1,
                pageSize: 10,
                classType: 'bookings',
            })
        );
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_scheduling_sharepass',
            })
        )
            .unwrap()
            .then((response: any) => {
                setSharePassOption(response?.data?.data?.isActive);
                setSharePassEnabled(response?.data?.data?.isEnabled);
            })
            .catch(() => Alertify.error('Error fetching setting status'));
    }, []);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        setSelectedRowIndex(-1); // Reset selected row when paginating
        navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        cancelViewClient();
    }

    function handleSearch(value: string) {
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    }

    const openConfirmationModal = (record: any, isCheckIn: boolean = false) => {
        setIsCheckIn(isCheckIn);
        setScheduleData(record);
        setConfirmationModalVisible(true);
    };

    const handleSharePassClose = (refresh = false) => {
        if (refresh) getList();
        setIsSharePassModalVisible(false);
    };

    const actionColumn = [
        ...(hasBookingUpdatePermission || store.role === RoleType.ORGANIZATION
            ? [
                  {
                      title: 'Action',
                      dataIndex: '',
                      key: 'action',
                      render: (record: any) => {
                          const isDisabled = getStatus(record) === 'Inactive';
                          const menu = (
                              <Menu>
                                  {(hasBookingWritePermission ||
                                      store.role === RoleType.ORGANIZATION) && (
                                      <Menu.Item
                                          className={` ${
                                              isDisabled
                                                  ? 'cursor-not-allowed bg-[#f5f5f5]'
                                                  : 'text-[#455560]'
                                          }`}
                                          onClick={() => {
                                              setPurchaseData({
                                                  ...record,
                                                  isEdit: false,
                                              });
                                              setIsModalVisible(true);
                                          }}
                                          key="1"
                                          disabled={isDisabled}
                                      >
                                          Add Booking
                                      </Menu.Item>
                                  )}

                                  {/* Group Check-In Option */}
                                  <Menu.Item
                                      className="text-[#455560]"
                                      onClick={() => {
                                          const transformedData = {
                                              ...record,
                                              product: record.packageName,
                                              userId: record.clientId,
                                              packageId: record.packageId,
                                              isBundledPricing: false,
                                              bundledPricingId: null,
                                          };
                                          setMultiSharePassData(
                                              transformedData
                                          );
                                          setMultipleSharePassOpen(true);
                                      }}
                                      key="group-checkin"
                                      disabled={isDisabled}
                                  >
                                      Group Check-In
                                  </Menu.Item>

                                  {/* Family Share Option */}
                                  <Menu.Item
                                      className="text-[#455560]"
                                      onClick={() => {
                                          const transformedData = {
                                              ...record,
                                              product: record.packageName,
                                              userId: record.clientId,
                                              packageId: record.packageId,
                                              purchaseIds: [record._id],
                                              isBundledPricing: false,
                                              bundledPricingId: null,
                                          };
                                          setFamilyPassShare({
                                              visible: true,
                                              record: transformedData,
                                          });
                                      }}
                                      key="family-share"
                                      disabled={isDisabled}
                                  >
                                      Family Share
                                  </Menu.Item>

                                  {!record.isSuspended ? (
                                      <>
                                          <Menu.Item
                                              className={` ${
                                                  isDisabled
                                                      ? 'cursor-not-allowed bg-[#f5f5f5]'
                                                      : 'text-[#455560]'
                                              }`}
                                              onClick={() => {
                                                  setPurchaseData({
                                                      ...record,
                                                      isEdit: false,
                                                  });
                                                  setIsSuspensionModalVisible(
                                                      true
                                                  );
                                              }}
                                              key="2"
                                              disabled={isDisabled}
                                          >
                                              Freeze
                                          </Menu.Item>
                                      </>
                                  ) : (
                                      <>
                                          <Menu.Item
                                              className={` ${
                                                  isDisabled
                                                      ? 'cursor-not-allowed bg-[#f5f5f5]'
                                                      : 'text-[#455560]'
                                              }`}
                                              onClick={() => {
                                                  setPurchaseData({
                                                      ...record,
                                                      isEdit: false,
                                                  });
                                                  setIsSuspensionModalVisible(
                                                      true
                                                  );
                                              }}
                                              key="3"
                                              disabled={isDisabled}
                                          >
                                              Unfreeze
                                          </Menu.Item>
                                      </>
                                  )}
                                  {/* {sharePassOption && sharePassEnabled && ( */}
                                  {sharePassEnabled &&
                                      store.clientOnboarding?.sharePass && (
                                          <Menu.Item
                                              className={` ${' bg-[#f5f5f5]x'}`}
                                              onClick={() => {
                                                  setPurchaseData({
                                                      ...record,
                                                  });
                                                  if (
                                                      ![
                                                          'multiple',
                                                          'day_pass',
                                                      ].includes(
                                                          record.sessionType
                                                      )
                                                  )
                                                      Alertify.error(
                                                          'Share pass can only be done when session type is multiple or day pass.'
                                                      );
                                                  else
                                                      setIsSharePassModalVisible(
                                                          true
                                                      );
                                              }}
                                              key="share-pass"
                                              //   disabled={isDisabled}
                                          >
                                              Share Pass
                                          </Menu.Item>
                                      )}
                                  <Menu.Item
                                      className={` ${' bg-[#f5f5f5]x'}`}
                                      key="check-in"
                                      //   disabled={isDisabled}
                                      onClick={() => {
                                          console.log(
                                              'Record-----------',
                                              record
                                          );
                                          setOpenClientBookingModal(true);
                                          setClientId(record.clientId);
                                      }}
                                  >
                                      Check-In History
                                  </Menu.Item>
                              </Menu>
                          );
                          return (
                              <Dropdown
                                  overlay={menu}
                                  trigger={['click']}
                                  visible={openDropdownKey === record.key}
                                  onOpenChange={(visible) => {
                                      setOpenDropdownKey(
                                          visible ? record.key : null
                                      );
                                  }}
                              >
                                  <MoreOutlined
                                      style={{
                                          fontSize: '20px',
                                          cursor: 'pointer',
                                      }}
                                  />
                              </Dropdown>
                          );
                      },
                  },
              ]
            : []),
    ];

    const actionColumn2 = [
        ...(hasBookingUpdatePermission ||
        hasBookingCheckInPermission ||
        hasBookingCancelPermission ||
        store.role === RoleType.ORGANIZATION
            ? [
                  {
                      title: 'Action',
                      dataIndex: '',
                      key: 'action',
                      render: (record: any) => {
                          const menu = (
                              <Menu>
                                  {(hasBookingUpdatePermission ||
                                      store.role === RoleType.ORGANIZATION) && (
                                      <Menu.Item
                                          onClick={() => {
                                              setScheduleData({
                                                  ...record,
                                                  isEdit: true,
                                              });
                                              setIsModalVisible(true);
                                          }}
                                          key="1"
                                      >
                                          Edit
                                      </Menu.Item>
                                  )}
                                  {(hasBookingCheckInPermission ||
                                      store.role === RoleType.ORGANIZATION) &&
                                      record.bookingStatus !== 'checked-in' &&
                                      record.bookingStatus !== 'canceled' && (
                                          <Menu.Item
                                              onClick={() =>
                                                  openConfirmationModal(
                                                      record,
                                                      true
                                                  )
                                              }
                                              key="4"
                                          >
                                              Check-In
                                          </Menu.Item>
                                      )}
                                  {(hasBookingCancelPermission ||
                                      store.role === RoleType.ORGANIZATION) &&
                                      record.bookingStatus !== 'canceled' && (
                                          <Menu.Item
                                              onClick={() =>
                                                  openConfirmationModal(record)
                                              }
                                              key="5"
                                          >
                                              {record.bookingStatus ===
                                              'checked-in'
                                                  ? 'Reverse Check-In'
                                                  : 'Cancel'}
                                          </Menu.Item>
                                      )}
                              </Menu>
                          );

                          return (
                              <Dropdown
                                  overlay={menu}
                                  trigger={['click']}
                                  visible={openDropdownKey === record.key}
                                  onOpenChange={(visible) => {
                                      setOpenDropdownKey(
                                          visible ? record.key : null
                                      );
                                  }}
                              >
                                  <MoreOutlined
                                      style={{
                                          fontSize: '20px',
                                          cursor: 'pointer',
                                      }}
                                  />
                              </Dropdown>
                          );
                      },
                  },
              ]
            : []),
    ];

    const combinedColumns = [...columns, ...actionColumn];
    const combinedColumns2 = [...columns2, ...actionColumn2];

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
                event.preventDefault();

                const currentDataSource =
                    activeKey === 1
                        ? store.purchasedPriceList
                        : store.schedulingList;
                const currentIndex = selectedRowIndex;
                let newIndex = currentIndex;

                if (event.key === 'ArrowUp' && currentIndex > 0) {
                    newIndex = currentIndex - 1;
                } else if (
                    event.key === 'ArrowDown' &&
                    currentIndex < currentDataSource.length - 1
                ) {
                    newIndex = currentIndex + 1;
                }

                if (
                    newIndex !== currentIndex &&
                    newIndex >= 0 &&
                    newIndex < currentDataSource.length
                ) {
                    setSelectedRowIndex(newIndex);
                    const record = currentDataSource[newIndex];
                    viewClient(record.userClientId);
                    setClientUpdatePermission(hasClientUpdatePermission);
                }
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [
        selectedRowIndex,
        store.purchasedPriceList,
        store.schedulingList,
        activeKey,
        hasClientUpdatePermission,
    ]);

    useEffect(() => {
        // Focus the container when component mounts
        if (containerRef.current) {
            containerRef.current.focus();
        }
    }, []);

    // add daily check-in button handling
    // Add useEffect to update URL when filters change
    useEffect(() => {
        const queryParams = new URLSearchParams();

        // Add activeTab to URL
        if (activeKey) {
            queryParams.set('activeTab', activeKey.toString());
        }

        // Add date range to URL - convert Dayjs to string format
        if (selectedDateRange?.[0]) {
            queryParams.set(
                'startDate',
                selectedDateRange?.[0].format('YYYY-MM-DD')
            );
        }
        if (selectedDateRange?.[1]) {
            queryParams.set(
                'endDate',
                selectedDateRange?.[1].format('YYYY-MM-DD')
            );
        }

        if (selectedBookingStatus) {
            queryParams.set('bookingStatus', selectedBookingStatus);
        }

        if (search) {
            queryParams.set('search', search);
        }

        if (currentPage !== 1) {
            queryParams.set('page', currentPage.toString());
        }
        if (pageSizes !== 10) {
            queryParams.set('pageSize', pageSizes.toString());
        }

        if (urlParams.isDailyCheckin) {
            queryParams.set('dailyCheckin', 'true');
        }

        const newUrl = queryParams.toString()
            ? `?${queryParams.toString()}`
            : '';
        if (newUrl !== location.split('?')[1]) {
            navigate(newUrl, { replace: true });
        }
    }, [
        activeKey,
        selectedDateRange,
        selectedBookingStatus,
        search,
        currentPage,
        pageSizes,
    ]);

    const handleClearAll = () => {
        // Reset all filter states to default values
        setSearch('');
        setSelectedDateRange([null, null]);
        setSelectedBookingStatus([]);
        setSelectedCity([]);
        setSelectedServiceCategories([]);
        setSelectedClients([]);
        setCurrentPage(1);
        setPageSize(10);
        setActiveKey(1); // Reset to first tab
        setSelectedRowIndex(-1); // Reset selected row

        // Close client details if open
        cancelViewClient();

        // Clear URL parameters
        navigate('', { replace: true });
    };

    return loader ? (
        <FullLoader state={true} />
    ) : (
        <>
            <div
                tabIndex={0}
                onFocus={() => console.log('Container focused')}
                ref={containerRef}
                className="outline-none focus:outline-none"
            >
                <div className="sidebar-scrollbar flex max-h-full gap-8 overflow-y-scroll">
                    <div
                        className={`${
                            clientDetails
                                ? '3xl:w-[80%] md:w-[73%] 2xl:w-[75%]'
                                : 'w-full'
                        } sidebar-scrollbar max-h-full overflow-y-scroll transition-all duration-300 ease-linear`}
                    >
                        <CustomTable
                            className="min-w-min"
                            columns={combinedColumns}
                            columns2={combinedColumns2}
                            dataSource1={store.purchasedPriceList?.map(
                                (item: any) => ({
                                    ...item,
                                    membershipId: item.membershipId || '-',
                                })
                            )}
                            dataSource2={store.schedulingList}
                            hasBookingWritePermission={
                                hasBookingWritePermission
                            }
                            heading="Booking"
                            onSearch={handleSearch}
                            showBooking={true}
                            onExportClick={handleExport}
                            showPackages={true}
                            showDateRange={true}
                            loading={filterLoader}
                            search={search}
                            showStaffLocation={true}
                            showClient={true}
                            showServiceCategory={true}
                            showBookingStatus={true}
                            showSearch={true}
                            showTabs={true}
                            showClearButton={true}
                            rowClassName={(record: any, index: number) =>
                                selectedRowIndex === index
                                    ? 'bg-blue-100 border-l-4 border-blue-500'
                                    : ''
                            }
                            {...{
                                selectedCity,
                                setSelectedCity,
                                selectedClients,
                                setSelectedClients,
                                selectedBookingStatus,
                                setSelectedBookingStatus,
                                selectedServiceCategories,
                                setSelectedServiceCategories,
                                selectedDateRange,
                                setSelectedDateRange,
                                setIsModalVisible,
                                activeKey,
                                setActiveKey,
                                setCurrentPage,
                            }}
                        />
                        <div className="b flex justify-center py-10">
                            <Pagination
                                current={currentPage}
                                total={
                                    activeKey === 1
                                        ? store.purchasedPriceListCount
                                        : store.schedulingCount
                                }
                                pageSize={pageSizes}
                                pageSizeOptions={['10', '20', '50']}
                                onChange={paginate}
                                hideOnSinglePage
                            />
                        </div>
                    </div>
                    <div
                        className={`3xl:w-[20%] max-h-full overflow-y-scroll rounded-xl border p-8 transition-all duration-300 ease-linear md:w-[27%] 2xl:w-[25%] ${
                            clientDetails ? '' : 'hidden'
                        }`}
                    >
                        <ClientPreview
                            cancelViewClient={cancelViewClient}
                            viewClientId={clientId}
                            clientUpdatePermission={clientUpdatePermission}
                        />
                    </div>
                </div>
            </div>

            {multipleSharePassOpen && (
                <ClientListModal
                    visible={multipleSharePassOpen}
                    data={multisharePassData}
                    onCancel={() => setMultipleSharePassOpen(false)}
                    orderData={{
                        userId: multisharePassData?.clientId,
                        _id:
                            multisharePassData?.invoiceId ||
                            multisharePassData?._id,
                        organizationId: store.organizationId,
                        facilityId: { _id: multisharePassData?.facilityId },
                    }}
                    checkInModalVisible={checkInModalVisible}
                    setCheckInModalVisible={setCheckInModalVisible}
                    sharedClientsForCheckIn={sharedClientsForCheckIn}
                    setSharedClientsForCheckIn={setSharedClientsForCheckIn}
                    setSelectedClientData={setSelectedClientData}
                />
            )}

            {/* Family Share Modal */}
            {familyPassShare?.visible && (
                <FamilySharePassModal
                    visible={familyPassShare?.visible}
                    onClose={() => setFamilyPassShare(null)}
                    orderData={familyPassShare?.record}
                    invoiceId={
                        familyPassShare?.record?.invoiceId ||
                        familyPassShare?.record?._id
                    }
                />
            )}

            {/* Confirmation Modal for Single Check-In */}
            {confirmationVisible && (
                <CommonConfirmationModal
                    visible={confirmationVisible}
                    onCancel={() => {
                        setConfirmationVisible(false);
                        setSelectedCheckInData(null);
                    }}
                    onConfirm={handleConfirmCheckIn}
                    message="Are you sure you want to check-in this client?"
                    loader={submitLoader}
                />
            )}

            {/* Check-In Success Modal */}
            {checkInSuccessModalVisible && (
                <Modal
                    title="Check-In Summary"
                    open={checkInSuccessModalVisible}
                    onCancel={() => setCheckInSuccessModalVisible(false)}
                    footer={[
                        <Button
                            key="close"
                            className="h-16 w-[110px] bg-purpleLight text-white"
                            onClick={() => setCheckInSuccessModalVisible(false)}
                        >
                            Close
                        </Button>,
                    ]}
                    width="60%"
                >
                    <CustomTable
                        dataSource={checkInSuccessData}
                        columns={[
                            {
                                title: 'Client Name',
                                dataIndex: 'clientName',
                                key: 'clientName',
                            },
                            {
                                title: 'Package Name',
                                dataIndex: 'packageName',
                                key: 'packageName',
                            },
                            {
                                title: 'Service Category',
                                dataIndex: 'serviceCategoryName',
                                key: 'serviceCategoryName',
                            },
                            {
                                title: 'Room Name',
                                dataIndex: 'roomName',
                                key: 'roomName',
                            },
                            {
                                title: 'Start Time',
                                dataIndex: 'from',
                                key: 'from',
                                render: (text: any) => text,
                            },
                            {
                                title: 'End Time',
                                dataIndex: 'to',
                                key: 'to',
                                render: (text: any) => text,
                            },
                        ]}
                        pagination={false}
                    />
                </Modal>
            )}

            {confirmationModalVisible && (
                <CommonConfirmationModal
                    visible={confirmationModalVisible}
                    onConfirm={() => {
                        isCheckIn
                            ? dispatch(
                                  CheckInScheduling({
                                      scheduleId: scheduleData._id,
                                  })
                              )
                                  .unwrap()
                                  .then((res: any) => {
                                      const status =
                                          res?.payload?.status ?? res?.status;
                                      if (status === 200 || status === 201) {
                                          getList();
                                      }
                                  })
                            : dispatch(
                                  CancelScheduling({
                                      scheduleId: scheduleData._id,
                                  })
                              )
                                  .unwrap()
                                  .then((res: any) => {
                                      const status =
                                          res?.payload?.status ?? res?.status;
                                      if (status === 200 || status === 201) {
                                          getList();
                                      }
                                  });
                        setIsCheckIn(false);
                        setConfirmationModalVisible(false);
                        setScheduleData(null);
                    }}
                    onCancel={() => {
                        setIsCheckIn(false);
                        setConfirmationModalVisible(false);
                        setScheduleData(null);
                    }}
                    message={
                        isCheckIn
                            ? 'Are you sure you want to check-in?'
                            : 'Are you sure you want to cancel the booking?'
                    }
                />
            )}
            {isModalVisible && (
                <BookingModal
                    visible={isModalVisible}
                    onClose={handleClose}
                    tabValue={'bookings'}
                    scheduleId={
                        scheduleData?.isEdit && activeKey === 2
                            ? scheduleData?._id
                            : null
                    }
                    clientId={
                        activeKey === 1
                            ? purchaseData?.clientId
                            : scheduleData?.clientId
                    }
                    facilityId={
                        activeKey === 1
                            ? purchaseData?.facilityId
                            : scheduleData?.facilityId
                    }
                    packageId={activeKey === 1 ? purchaseData?.packageId : null}
                    purchaseId={activeKey === 1 ? purchaseData?._id : null}
                    isEdit={scheduleData?.isEdit || false}
                />
            )}
            {isSuspensionModalVisible && (
                <BookingSuspensionModal
                    visible={isSuspensionModalVisible}
                    onClose={handleClose}
                    purchaseId={purchaseData?._id}
                    purchaseData={purchaseData}
                />
            )}
            {openClientBookingModal && (
                <Modal
                    open={openClientBookingModal}
                    onCancel={() => setOpenClientBookingModal(false)}
                    footer={null}
                    width={900}
                    title="Bookings & Check-In History"
                >
                    <>
                        <div className="mb-4 flex justify-end">
                            <Button
                                className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                onClick={handleExportCheckInHistory}
                            >
                                Export
                                <DownloadOutlined className="ml-2 text-16" />
                            </Button>
                        </div>
                        <ClientBookingCheckInModal clientId={clientId} />
                    </>
                </Modal>
            )}
            {isSharePassModalVisible && (
                <SharePassTypeModal
                    visible={isSharePassModalVisible}
                    onClose={handleSharePassClose}
                    clientId={purchaseData?.clientId}
                    clientName={purchaseData?.clientName}
                    purchaseId={purchaseData?._id}
                />
            )}
        </>
    );
};

export default BookingListing;
