import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Button, Checkbox, Typography } from 'antd';
import {
    GetPermissionsByActionId,
    GetPermissionsWithoutActionId,
    UpdatePermissionsOnActionId,
} from '~/redux/actions/permission-action';
import Alertify from '~/services/alertify';
import { LoaderIcon } from 'react-hot-toast';

const { Text } = Typography;

interface Permission {
    _id: string;
    type: string;
    description: string;
    isActive: boolean;
    isDelegated: boolean;
    name: string;
    permitted: boolean;
}

const AllPermissionsList = (props: { actionId: string }) => {
    const dispatch = useDispatch();
    const [permissions, setPermissions] = useState<Permission[]>([]);
    const [pageLoading, setPageLoading] = useState(false);
    useEffect(() => {
        setPageLoading(true);
        if (props.actionId) {
            dispatch(GetPermissionsByActionId({ actionId: props.actionId }))
                .then((res: any) => {
                    const permissionsData = res?.payload?.data?.data || [];
                    setPermissions(permissionsData);
                    setPageLoading(false);
                })
                .catch((error: any) => {
                    Alertify.error('Error GetPermissionsByActionId API', error);
                });
        }
    }, [dispatch, props.actionId]);

    const handleCheckboxChange = (permissionId: string, checked: boolean) => {
        setPermissions((prevPermissions) =>
            prevPermissions.map((permission) =>
                permission._id === permissionId
                    ? { ...permission, permitted: checked }
                    : permission
            )
        );
    };
    const [loading, setLoading] = useState(false);
    const updatePermissions = () => {
        setPageLoading(true);
        setLoading(true);
        const checkedPermissions = permissions
            .filter((permission) => permission.permitted)
            .map((permission) => permission._id);

        dispatch(
            UpdatePermissionsOnActionId({
                actionId: props.actionId,
                permissions: checkedPermissions,
            })
        )
            .then((response: any) => {
                Alertify.success('Permissions updated successfully');
                dispatch(GetPermissionsByActionId({ actionId: props.actionId }))
                    .then((res: any) => {
                        const permissionsData = res?.payload?.data?.data || [];
                        setPermissions(permissionsData);
                    })
                    .catch((error: any) => {
                        Alertify.error(
                            'Error GetPermissionsByActionId API',
                            error
                        );
                    });
                setLoading(false);
                setPageLoading(false);
            })
            .catch((error: any) => {
                Alertify.error('Error updating permissions', error);
            });
    };

    return (
        <>
            {pageLoading ? (
                <div className="flex min-h-[45vh] items-center justify-center">
                    <LoaderIcon className="h-[30px] w-[30px]" />
                </div>
            ) : (
                <div>
                    <div className="grid grid-cols-3 gap-4">
                        {permissions.map((permission) => (
                            <div
                                key={permission._id}
                                className="flex items-start p-2"
                            >
                                <Checkbox
                                    checked={permission.permitted}
                                    onChange={(e) =>
                                        handleCheckboxChange(
                                            permission._id,
                                            e.target.checked
                                        )
                                    }
                                >
                                    <div className="flex flex-col">
                                        <Text strong>{permission.name}</Text>
                                        <Text type="secondary">
                                            {permission.description}
                                        </Text>
                                    </div>
                                </Checkbox>
                            </div>
                        ))}
                    </div>

                    <div className="text-end">
                        <div>
                            <Button
                                type="primary"
                                className="invisible w-[150px] bg-primary text-white"
                                disabled={true}
                            ></Button>
                        </div>
                        <Button
                            type="primary"
                            className="absolute bottom-4 right-4 w-[150px] bg-primary text-white"
                            onClick={updatePermissions}
                            disabled={loading}
                        >
                            {loading ? 'Saving...' : 'SAVE CHANGES'}
                        </Button>
                    </div>
                </div>
            )}
        </>
    );
};

export default AllPermissionsList;
