import { useEffect, useState } from 'react';
import { Typography, Button } from 'antd';
import { useDispatch } from 'react-redux';
import Alertify from '~/services/alertify';
import { GetAllPoliciesList } from '~/redux/actions/permission-action';
import { EditOutlined } from '@ant-design/icons';
import { useLocation } from 'wouter';

const { Text, Title } = Typography;

interface Action {
    label: string;
    value: string;
    permitted: boolean;
}

interface Permission {
    name: string;
    subject: string;
    type: string;
    actionIds: string[];
    actions?: Array<{
        _id: string;
        isActive: boolean;
        subject: string;
        module: string;
        action: string;
        description?: string;
    }>;
}

interface PermissionSection {
    category: string;
    permissions: Permission[];
}

interface SubjectAction {
    _id: string;
    isActive: boolean;
    subject: string;
    module: string;
    action: string;
    description?: string;
    permitted?: boolean;
}

interface Subject {
    subject: string;
    type: string;
    _id: string;
    actions: SubjectAction[];
}

interface RawDataItem {
    module: string;
    subjects: Subject[];
}

const PoliciesList = () => {
    const dispatch = useDispatch();
    const [permissionsData, setPermissionsData] = useState<PermissionSection[]>(
        []
    );

    const [location, setLocation] = useLocation();

    const handleEditPolicy = (subject: string, type: string) => {
        if (subject && type) {
            // Navigate to edit policy page with type
            setLocation(`/edit-policy/${type}`);
        } else {
            Alertify.error('Subject or type not found');
        }
    };

    const fetchPermissions = () => {
        // localStorage.removeItem('editPolicyData');
        dispatch(GetAllPoliciesList())
            .then((res: any) => {
                const rawData: RawDataItem[] = res?.payload?.data?.data || [];
                const groupedPermissions: Record<string, PermissionSection> =
                    {};

                rawData.forEach((item: RawDataItem) => {
                    const category = item.module;

                    if (!groupedPermissions[category]) {
                        groupedPermissions[category] = {
                            category,
                            permissions: [],
                        };
                    }

                    item.subjects?.forEach((subject: Subject) => {
                        // Extract action IDs from the subject
                        const actionIds = subject.actions.map(
                            (action) => action._id
                        );

                        groupedPermissions[category].permissions.push({
                            name: subject.subject,
                            type: subject.type,
                            subject: subject.subject,
                            actionIds: actionIds,
                            actions: subject.actions,
                        });
                    });
                });

                const transformedData = Object.values(groupedPermissions);
                setPermissionsData(transformedData);
            })
            .catch((error: any) => {
                Alertify.error(error);
            });
    };

    useEffect(() => {
        fetchPermissions();
    }, [dispatch, location]);

    return (
        <div className="mx-auto w-full rounded-xl p-6">
            <div className="flex items-center gap-4">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={() => window.history.back()}
                />
                <Title level={4}>Policies</Title>
            </div>
            <div>
                {permissionsData.map((section, index) => (
                    <div
                        key={index}
                        className="my-8 rounded-lg bg-slate-50 p-8"
                    >
                        <Text strong>{section.category}</Text>
                        <div className="mt-4 rounded-lg border-1 border-b-0">
                            {section.permissions.map((perm, i) => (
                                <div
                                    key={i}
                                    className="my-0 flex items-center justify-between border-b-1 bg-white px-4 py-1"
                                >
                                    <Text>{perm.name}</Text>
                                    <Button
                                        type="text"
                                        icon={<EditOutlined />}
                                        onClick={() =>
                                            handleEditPolicy(
                                                perm.subject,
                                                perm.type
                                            )
                                        }
                                        className="text-blue-500"
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default PoliciesList;
