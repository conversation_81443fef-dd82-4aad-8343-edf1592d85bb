import React from 'react';
import { Modal } from 'antd';
import { CheckCircleOutlined } from '@ant-design/icons';

const PackageSharedModal = ({ visible, onClose }: any) => {
    console.log(visible,"visible")
    return (
        <Modal
            open={visible}
            footer={null}
            closable
            onCancel={onClose}
            centered
            width={400}
            bodyStyle={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                padding: '40px 24px',
                textAlign: 'center',
            }}
        >
            <CheckCircleOutlined style={{ fontSize: 48, color: '#9C4DCC', marginBottom: 20 }} />
            <p style={{ fontSize: 16, color: '#1E2B3C', margin: 0 }}>Your packages have been shared.</p>
        </Modal>
    );
};

export default PackageSharedModal;
