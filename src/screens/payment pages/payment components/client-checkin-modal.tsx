// components/ClientCheckInModal.tsx

import React, { useMemo, useState } from 'react';
import { Modal, Typography, Table, Checkbox, Button, Input } from 'antd';
import { capitalizeFirstLetter } from '~/components/common/function';

const { Title, Text } = Typography;

const ClientCheckInModal = ({
    visible,
    onClose,
    onCheckIn,
    selectedClientData,
    data,
    loading,
}: any) => {
    const [checkedClients, setCheckedClients] = useState<string[]>(
        selectedClientData.map((client: any) => client._id)
    );
    const [searchText, setSearchText] = useState<string>('');

    const handleCheckboxChange = (checked: boolean, userId: string) => {
        setCheckedClients((prev) =>
            checked ? [...prev, userId] : prev.filter((id) => id !== userId)
        );
    };

    console.log('checkedClients-----------', selectedClientData);

    const filteredClients = useMemo(() => {
        const search = searchText?.trim()?.toLowerCase();
        return [...selectedClientData]
            ?.filter((client: any) =>
                client.name?.toLowerCase()?.includes(search)
            )
            .sort((a, b) => (b.isOwner ? 1 : 0) - (a.isOwner ? 1 : 0));
    }, [selectedClientData, searchText]);

    const columns = [
        {
            title: '',
            render: (_: any, record: any) => (
                <Checkbox
                    checked={checkedClients.includes(record._id)}
                    onChange={(e) =>
                        handleCheckboxChange(e.target.checked, record._id)
                    }
                />
            ),
        },
        {
            title: 'NAME',
            dataIndex: 'name',
            render: (text: string, record: any) => {
                const ownerStyle = record.isOwner
                    ? { color: 'black', fontWeight: 'bold' }
                    : {};

                return (
                    <span style={ownerStyle}>
                        {capitalizeFirstLetter(text)}
                    </span>
                );
            },
        },
        {
            title: 'Sessions',
            dataIndex: 'totalSessions',
        },
    ];

    return (
        <Modal open={visible} onCancel={onClose} footer={null} width="60%">
            <Title level={4}>Client List</Title>
            <div className="mb-12 flex gap-10 ">
                <Text strong>Package Name:</Text> <Text>{data?.product}</Text>
                <br />
                {/* <Text strong>Remaining Session(s):</Text>{' '}
                <Text>{packageInfo?.remainingSessions}</Text> */}
            </div>

            <Input
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                placeholder="Search"
                className="mb-6 w-[30%]"
                allowClear
            />

            <Table
                dataSource={filteredClients}
                columns={columns}
                pagination={false}
                rowKey="_id"
            />

            <div className="flex justify-end pt-6">
                <Button
                    type="primary"
                    className="bg-purpleLight text-white"
                    onClick={() => onCheckIn(checkedClients)}
                    disabled={!checkedClients.length}
                    loading={loading}
                >
                    Check In
                </Button>
            </div>
        </Modal>
    );
};

export default ClientCheckInModal;
