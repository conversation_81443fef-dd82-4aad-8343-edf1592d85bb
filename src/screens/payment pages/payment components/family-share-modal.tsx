// components/ClientListModal.tsx

import React, { useEffect, useState } from 'react';
import {
    Modal,
    Table,
    Input,
    Button,
    Typography,
    Checkbox,
    Pagination,
    Space,
    ConfigProvider,
} from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { useDebounce } from '~/hooks/useDebounce';
import { formatDate } from '~/components/common/function';
import Alertify from '~/services/alertify';
import { FamilyShareClientList, SharePackageToUsers } from '~/redux/actions/family-share.action';
import PackageSharedModal from './successfulShareModal';
import { PricingListingForSharePass } from '~/redux/actions/appointment-action';

const { Title, Text } = Typography;

const FamilySharePassModal = ({
    visible,
    onClose,
    orderData,
    invoiceId

}: any) => {
    console.log(orderData, "rherhifheihfiewh")
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [searchText, setSearchText] = useState('');
    const dispatch = useAppDispatch();
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSizes, setPageSize] = useState(10);
    const [loader, startLoader, endLoader] = useLoader();
    const [submitLoader, starSubmittLoader, endSubmitLoader] = useLoader();
    const debouncedRequest = useDebounce((callback) => callback(), 300);
    const [selectedClients, setSelectedClients] = useState<any[]>([]);
    const [clientList, setClientList] = useState<any>([])
    const [total, setTotal] = useState(0);
    const [successfulShareModal, SetSuccessfulShareModal] = useState<boolean>(false);
    const [availableSessions, setAvailableSessions] = useState(0);
    useEffect(() => {
        if (orderData) {
            dispatch(
                PricingListingForSharePass({
                    userId: orderData?.userId,
                    // bundledPricingId: data?.bundledPricingId,
                    invoiceId: invoiceId,
                })
            )
                .unwrap()
                .then((res: any) => {
                    if (res?.status === 200 || res?.status === 201) {
                        const pricingList = res.data?.data?.map(
                            (item: any) => ({
                                value: item._id,
                                label: item.packageName,
                                id: item._id,
                                packageId: item.packageId,
                                bundledPricingId: item.bundledPricingId,
                                ...item,
                            })
                        );
                        let matchedItems: any[] = [];

                        if (orderData?.isBundledPricing) {
                            matchedItems = pricingList.filter(
                                (item: any) =>
                                    item.bundledPricingId ===
                                    orderData?.bundledPricingId
                            );
                        } else {
                            matchedItems = pricingList.filter(
                                (item: any) =>
                                    item.packageId === orderData?.packageId
                            );
                        }

                        const totalSessions = matchedItems.reduce(
                            (sum: any, item: any) =>
                                sum + (item.remainingSessions || 0),
                            0
                        );
                        console.log(totalSessions, "total session")
                        setAvailableSessions(totalSessions);
                    }
                });
        }
    }, [orderData]);
    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
    }
   const onSelectAllChange = (e: any) => {
    if (e.target.checked) {
        if (clientList.length > availableSessions) {
            Alertify.error(`You can only transfer to ${availableSessions} client(s)`);
            return;
        }

        const newKeys = clientList.map((item: any) => item._id);
        setSelectedRowKeys(newKeys);
        setSelectedClients(clientList);
    } else {
        setSelectedRowKeys([]);
        setSelectedClients([]);
    }
};


    const onSingleCheck = (record: any) => {
    const exists = selectedRowKeys.includes(record._id);

    if (!exists && selectedRowKeys.length >= availableSessions) {
        Alertify.error(`You can only transfer to ${availableSessions} client(s)`);
        return;
    }

    let newSelectedKeys = [];
    let newSelectedClients = [];

    if (exists) {
        newSelectedKeys = selectedRowKeys.filter((id) => id !== record._id);
        newSelectedClients = selectedClients.filter((item) => item._id !== record._id);
    } else {
        newSelectedKeys = [...selectedRowKeys, record._id];
        newSelectedClients = [...selectedClients, record];
    }

    setSelectedRowKeys(newSelectedKeys);
    setSelectedClients(newSelectedClients);
};

    useEffect(() => {
        // if (!visible || !orderData?.purchaseId || !orderData?.userId) return;
        debouncedRequest(() => {
            startLoader();
            dispatch(
                FamilyShareClientList({
                    purchaseId: orderData?.purchaseIds[0],
                    userId: orderData.userId,
                    page: currentPage,
                    pageSize: pageSizes,
                    orderBy: 'name',
                    orderDirection: 'asc',
                    search: searchText,
                })
            ).then((res: any) => {
                const result = res?.payload?.data || [];
                const filtered = result?.filter((item: any) => !item.isDefault);
                const formattedData = filtered?.map((item: any) => ({
                    _id: item._id,
                    name: `${item.firstName || ''} ${item.lastName || ''}`.trim(),
                    mobile: item.mobile || '',
                    email: item.email || '',
                    createdAt: item.createdAt || '',
                }));
                setClientList(formattedData);
                setTotal(res?.payload?._metadata?.pagination?.total)
            })
                .finally(endLoader);
        });
    }, [visible, searchText, currentPage, pageSizes, orderData]);
    const columns = [
        {
            title: (
                <ConfigProvider
                    theme={{
                        token: {
                            colorPrimary: '#8143D1',
                        },
                    }}
                >
                    <Checkbox
                        onChange={onSelectAllChange}
                        checked={clientList.length > 0 && selectedRowKeys.length === clientList.length}
                        indeterminate={
                            selectedRowKeys.length > 0 && selectedRowKeys.length < clientList.length
                        }
                    />
                </ConfigProvider>
            ),
            dataIndex: 'checkbox',
            render: (_: any, record: any) => (
                <Checkbox
                    checked={selectedRowKeys.includes(record._id)}
                    onChange={() => onSingleCheck(record)}
                />
            ),
            width: 50,
        },
        {
            title: 'NAME',
            dataIndex: 'name',
        },
        {
            title: 'PHONE NO',
            dataIndex: 'mobile',
        },
        {
            title: 'EMAIL',
            dataIndex: 'email',
        },
        {
            title: 'CREATED AT',
            dataIndex: 'createdAt',
            render: (text: string) => formatDate(text),
        },
    ];
    const handleShare = async () => {
        if (selectedClients.length === 0) {
            Alertify.error("Please select at least one client");
            return;
        }

        if (!orderData?.purchaseIds?.[0]) {
            Alertify.error("Missing purchase ID");
            return;
        }

        starSubmittLoader();

        try {

            const payload = {
                shareTo: selectedClients.map(client => client._id),
                purchaseId: orderData.purchaseIds[0],
                shareFrom: orderData.userId,
            };
            const res = await dispatch(SharePackageToUsers(payload)).unwrap();
            console.log(res?.data)
            if (res?.data) {
                SetSuccessfulShareModal(true)
                // setTimeout(() => {
                //     onClose(); 
                // }, 300);
                // Alertify.success("Package shared successfully");
            }

        } catch (error: any) {
            console.log(error)
            Alertify.error(error || "Failed to share package");
        } finally {
            endSubmitLoader();
        }
    };
    return (
        <>
            <Modal
                open={visible}
                onCancel={onClose}
                footer={null}
                className="w-[60%]"
            >
                <Title level={4}>Client List</Title>

                <div className="mb-12 flex gap-10 ">
                    <Text strong>Package Name: </Text>
                    <Text>{orderData?.product}</Text>
                    <br />
                    <Text strong>No. of Session(s) Remaining: </Text>
                    <Text>{availableSessions}</Text>
                </div>
                <Input
                    placeholder="Search"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="mb-6 w-[40%]"
                    allowClear
                />
                <Table
                    columns={columns}
                    dataSource={clientList}
                    pagination={false}
                    loading={loader}
                />
                <div className="flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={total}
                        pageSize={pageSizes}
                        onChange={paginate}
                        pageSizeOptions={['10', '20', '50']}
                    // hideOnSinglePage
                    />
                </div>
                <div className="flex justify-end gap-5">
                    <Button
                        htmlType="button"
                        loading={submitLoader}
                        className="h-16  w-[110px]  bg-purpleLight text-white "
                        onClick={handleShare}
                    >
                        <p>Share </p>
                    </Button>
                </div>
            </Modal>
            {
                successfulShareModal && (
                    <>
                        <PackageSharedModal
                            visible={successfulShareModal}
                            onClose={() => { onClose(); SetSuccessfulShareModal(false); }}
                        />
                    </>
                )
            }
        </>
    );
};

export default FamilySharePassModal;


