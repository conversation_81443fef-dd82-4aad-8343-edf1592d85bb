import React, { useEffect, useState } from 'react';
import { Modal, Table, Button, Typography } from 'antd';
import { PricingListingForSharePass } from '~/redux/actions/appointment-action';
import { useAppDispatch } from '~/hooks/redux-hooks';

const { Title, Text } = Typography;

const BundleShareSessionModal = ({
    visible,
    onClose,
    orderData,
    bundleData,
    openClientListModal,
}: any) => {
    const dispatch = useAppDispatch();
    const [pricingList, setPricingList] = useState([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (orderData && bundleData) {
            setLoading(true);
            dispatch(
                PricingListingForSharePass({
                    userId: orderData.userId,
                    invoiceId: orderData._id,
                })
            )
                .unwrap()
                .then((res: any) => {
                    console.log('Res---------', res);
                    const result = res?.data?.data || [];
                    // const filtered = result.filter(
                    //     (p: any) => p.packageId === bundleData.packageId
                    // );
                    setPricingList(result);
                })
                .finally(() => setLoading(false));
        }
    }, [orderData, bundleData]);

    const columns = [
        {
            title: 'Package Name',
            dataIndex: 'packageName',
        },
        {
            title: 'Remaining Sessions',
            dataIndex: 'remainingSessions',
        },
        {
            title: 'Actions',
            render: (_: any, record: any) => (
                <Button
                    htmlType="button"
                    className="h-10   bg-purpleLight  text-white "
                    onClick={() => {
                        openClientListModal({
                            ...bundleData,
                            _id: record._id,
                        });
                        onClose();
                    }}
                >
                    Group Check-In
                </Button>
            ),
        },
    ];

    return (
        <Modal
            open={visible}
            onCancel={onClose}
            footer={null}
            width="50%"
            title="Select Package for Sharing"
        >
            <div className="mb-4">
                <Text strong>Product:</Text> <Text>{bundleData?.product}</Text>
            </div>

            <Table
                dataSource={pricingList}
                columns={columns}
                rowKey="_id"
                loading={loading}
                pagination={false}
            />
        </Modal>
    );
};

export default BundleShareSessionModal;
