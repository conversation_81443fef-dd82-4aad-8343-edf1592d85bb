import React, { useEffect, useState } from 'react';
import { Table, Button, ConfigProvider, Input, Tooltip } from 'antd';
import { useLocation } from 'wouter';
import { useLoader } from '~/hooks/useLoader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import DenominationModal from './denomination-modal';
import { PurchasePricingPackages } from '~/redux/actions/pricing-actions';
import Alertify from '~/services/alertify';
import {
    ClearPosPurchasedData,
    SetPosPurchasedData,
} from '~/redux/slices/purchaged-slice';
import { getQueryParams } from '~/utils/getQueryParams';
import { markOrderAsPaid } from '~/redux/actions/purchased-action';
import { goBack } from '~/components/common/function';
import Paragraph from 'antd/es/typography/Paragraph';
import { useSelector } from 'react-redux';
import { InfoCircleOutlined } from '@ant-design/icons';

// Table columns
const columns: any = [
    {
        title: 'Item',
        dataIndex: 'name',
        key: 'name',
        render: (text: string, record: any) =>
            record.isReturn || record.isVoucherApply ? (
                <div className="flex items-center justify-between text-red-500">
                    <div>{text}</div>
                    <div className="rounded-md bg-red-100 px-4 py-2 text-red-500">
                        {record.isReturn ? 'Return' : 'Voucher'}
                    </div>
                </div>
            ) : (
                <div>{text}</div>
            ),
    },
    {
        title: 'Qty',
        dataIndex: 'quantity',
        align: 'center',
        key: 'quantity',
        width: 90,
        render: (text: string, record: any) => {
            if (record.isCustomPackage) {
                return <div>1</div>;
            }

            return (
                <div
                    className={
                        record.isReturn || record.isVoucherApply
                            ? 'text-red-500'
                            : ''
                    }
                >
                    {text}
                </div>
            );
        },
    },
    {
        title: 'Price',
        key: 'subTotal',
        dataIndex: 'price',
        align: 'center',
        // render: (value: any) => value?.toFixed(2),
        render: (value: any, record: any) => {
            // const unitPrice = record?.isInclusiveofGst
            //     ? record?.finalPrice
            //     : record?.price;
            return (
                <div
                    className={
                        record.isReturn || record.isVoucherApply
                            ? 'text-red-500'
                            : ''
                    }
                >
                    {record.isReturn || record.isVoucherApply
                        ? -Math.trunc(value * 100) / 100
                        : Math.trunc(record.price * 100) / 100}
                </div>
            );
        },
    },
    {
        title: 'Discount',
        key: 'discountedValue',
        align: 'center',
        dataIndex: 'discountedValue',
        render: (value: any, record: any) => (
            <div
                className={
                    record.isReturn || record.isVoucherApply
                        ? 'text-red-500'
                        : ''
                }
            >
                {Math.trunc((value || 0) * 100) / 100}
            </div>
        ),
    },
    {
        title: 'GST',
        dataIndex: 'tax',
        key: 'tax',
        align: 'center',
        render: (value: any, record: any) => (
            <div
                className={
                    record.isReturn || record.isVoucherApply
                        ? 'text-red-500'
                        : ''
                }
            >
                {Math.trunc((value || 0) * 100) / 100}
            </div>
        ),
    },
];

interface CashPaymentProps {
    isMarkedAsPaid?: boolean;
}

const CashPayment: React.FC<CashPaymentProps> = ({ isMarkedAsPaid }) => {
    const [_, setLocation] = useLocation();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [denominationData, setDenominationData] = useState<
        Record<number, number>
    >({});
    const [overallTotal, setOverallTotal] = useState(0);
    const [paymentStatus, setPaymentStatus] = useState<string | null>(null);

    // console.log('denominationData-------', denominationData);

    const [loader, startLoader, endLoader] = useLoader();
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const orderId = params.orderId;
    const createdBy = params.createdBy;

    const store = useAppSelector((state) => ({
        posPurchasedData: state.purchased_store.posPuchasedData,
        userId: state.auth_store.userId,
    }));
    const [dataSource, setDataSource] = useState<any>([]);
    const [blinkChangeDue, setBlinkChangeDue] = useState(false);

    // convenience
    const totalFloor = Math.floor(store.posPurchasedData?.total ?? 0);
    const showChangeDue = overallTotal > totalFloor;
    const changeDue =
        Math.trunc(((overallTotal ?? 0) - totalFloor) * 100) / 100;

    useEffect(() => {
        if (showChangeDue) {
            setBlinkChangeDue(true);
            const t = setTimeout(() => setBlinkChangeDue(false), 100);
            return () => clearTimeout(t);
        } else {
            setBlinkChangeDue(false);
        }
    }, [showChangeDue]);

    useEffect(() => {
        setDataSource([
            ...(store.posPurchasedData?.returnItems || []),
            ...(store.posPurchasedData?.voucherItems || []),
            ...(store.posPurchasedData.purchaseItems || []),
            ...(store.posPurchasedData.customPackageItems || []),
        ]);
    }, []);
    // console.log('Data-----------store--------', store.posPurchasedData);

    const showModal = () => {
        setIsModalVisible(true);
    };

    const handleDenominationChange = (data: Record<number, number>) => {
        setDenominationData(data);
    };

    const handleConfirmStatusChange = () => {
        startLoader();
        const totalDenomination = Object.entries(denominationData)?.reduce(
            (sum, [key, value]: any) => sum + key * value,
            0
        );

        if (Object.keys(denominationData)?.length === 0) {
            Alertify.error('Please provide cash denominations.');
            endLoader();
            return;
        }

        if (
            totalDenomination < (Math.floor(store.posPurchasedData?.total) ?? 0)
        ) {
            Alertify.error(
                'Cash denominations must match or exceed the total bill.'
            );
            endLoader();
            return;
        }

        const payload: Record<string, any> = {
            invoiceId: orderId,
            status: 'completed',
            amountPaid: store.posPurchasedData?.total,
            isCashPayment: true,
            // billingAddressId: store.posPurchasedData?.billingAddressId,
            paymentDetails: [
                {
                    paymentMethodId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethodId,
                    paymentMethod:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethod,
                    transactionId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.transactionId,
                    amount: Math.floor(store.posPurchasedData?.total),
                    paymentDate: new Date(),
                    paymentStatus: 'completed',
                    paymentGateway: 'Stripe',
                    description: 'Payment for January subscription',
                    denominations: denominationData,
                },
            ],
        };

        dispatch(markOrderAsPaid(payload))
            .unwrap()
            .then((res: any) => {
                if (res?.status === 200 || res?.status === 201) {
                    setLocation(`/order-confirmation/${orderId}`, {
                        replace: true,
                    });
                    Alertify.success('Payment completed successfully');
                    dispatch(ClearPosPurchasedData());
                }
            })
            .finally(endLoader);
    };

    const handleContinueToPay = () => {
        startLoader();
        const totalDenomination = Object.entries(denominationData)?.reduce(
            (sum, [key, value]: any) => sum + key * value,
            0
        );

        if (Object.keys(denominationData)?.length === 0) {
            Alertify.error('Please provide cash denominations.');
            endLoader();
            return;
        }

        if (
            totalDenomination < (Math.floor(store.posPurchasedData?.total) ?? 0)
        ) {
            Alertify.error(
                'Cash denominations must match or exceed the total bill.'
            );
            endLoader();
            return;
        }
        const payload = {
            cart: {
                facilityId: store.posPurchasedData?.facilityId,
                userId: store.posPurchasedData?.userId,
                items: [
                    ...(store.posPurchasedData?.purchaseItems
                        ?.filter((item: any) => item.isPackage)
                        ?.map((item: any) => ({
                            itemType:
                                item?.itemType ??
                                (item.isProduct
                                    ? 'product'
                                    : item.isPackage
                                    ? 'service'
                                    : 'custom_package'),
                            itemId: item._id,
                            quantity: item.quantity || 1,
                            promotionId: item.promotion?._id,
                            ...(!item?.promotion?._id && {
                                discount: item.discount,
                            }),
                        })) || []),
                    ...(store.posPurchasedData?.purchaseItems
                        ?.filter((item: any) => item.isProduct)
                        ?.map((item: any) => ({
                            itemType: 'product',
                            itemId: item.productId,
                            variantId: item.productVariantId,
                            quantity: item.quantity || 1,
                            promotionId:
                                item?.isProduct &&
                                typeof item?.promotion === 'object'
                                    ? item?.promotion?._id
                                    : item?.promotion,
                            discount: null,
                        })) || []),
                    ...(store.posPurchasedData?.customPackageItems?.map(
                        (item: any) => ({
                            itemType: 'custom_package',
                            itemId: item.customPackageId,
                            quantity: item.quantity || 1,
                            promotionId: null,
                            discount: {
                                type: item.discount?.type,
                                value: item.discount?.value,
                                discountedBy: store.userId,
                            },
                        })
                    ) || []),
                    ...(store.posPurchasedData?.purchaseItems
                        ?.filter((item: any) => item.isVoucher)
                        ?.map((item: any) => ({
                            itemType: 'voucher',
                            itemId: item._id,
                            quantity: item.quantity || 1,
                            discount: item.discount,
                        })) || []),
                ],
                returnItems: [
                    ...(store.posPurchasedData?.returnItems
                        ?.filter((item: any) => item.isReturn)
                        ?.map((item: any) => item?.purchaseId) || []),
                ],
                vouchers: [
                    ...(store.posPurchasedData?.voucherItems?.map(
                        (item: any) => item?._id
                    ) || []),
                ],
                promotionCode: store.posPurchasedData?.promoCode,
                discount: {
                    type: store.posPurchasedData?.cartDiscountType,
                    value: store.posPurchasedData?.cartDiscount,
                    discountedBy: store.posPurchasedData?.discountedBy,
                },
            },
            paymentDetails: [
                {
                    paymentMethodId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethodId,
                    paymentMethod:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethod,
                    transactionId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.transactionId,
                    amount: Math.floor(store.posPurchasedData?.total),
                    paymentDate: new Date(),
                    paymentStatus: 'completed',
                    paymentGateway: 'Stripe',
                    description: 'Payment for January subscription',
                    denominations: denominationData,
                },
            ],
            isSplittedPayment: false,
            isCashPayment: true,
            amountPaid: store.posPurchasedData?.total,
            platform: 'Web',
            billingAddressId: store.posPurchasedData?.billingAddressId,
            // paymentBy: store.userId,
            paymentBy: createdBy ? createdBy : store.userId,
        };

        dispatch(PurchasePricingPackages(payload))
            .unwrap()
            .then((res: any) => {
                // console.log('Res----------------', res);
                if (res?.status === 200 || res?.status === 201) {
                    setLocation(
                        `/order-confirmation/${res.data?.data?.invoiceId}`,
                        { replace: true }
                    );
                    Alertify.success('Payment completed successfully');
                    dispatch(ClearPosPurchasedData());
                }
            })
            .finally(endLoader);
    };

    const { role } = useSelector((state: any) => state.auth_store);

    const handleConfirm = (status = paymentStatus) => {
        if (isMarkedAsPaid) handleConfirmStatusChange();
        else if (status) handleContinueToPay();
    };

    const handleClick = (paymentStatus: any) => {
        setPaymentStatus(paymentStatus);
        handleConfirm(paymentStatus);
    };

    const handleClose = () => {
        setPaymentStatus(null);
        setIsModalVisible(false);
    };

    function handleTranscationId(value: any) {
        if (value)
            store.posPurchasedData = {
                ...store.posPurchasedData,
                paymentDetails: store.posPurchasedData.paymentDetails.map(
                    (payment: any) => ({
                        ...payment,
                        transactionId: value,
                    })
                ),
            };
        dispatch(SetPosPurchasedData(store.posPurchasedData));
    }
    console.log('Data Source is:', dataSource);
    return (
        <div className=" bg-white py-10">
            {/* Ant Design Table */}
            <div className="lg:pb-16">
                <ConfigProvider
                    theme={{
                        components: {
                            Table: {
                                cellPaddingBlock: 6,
                            },
                        },
                        token: {
                            boxShadowSecondary:
                                '0 25px 50px -12px rgb(0 0 0 / 0.25)',
                        },
                    }}
                >
                    <Table
                        // dataSource={[
                        //     ...(store.posPurchasedData?.purchaseItems || []),
                        //     ...(store.posPurchasedData?.customPackageItems ||
                        //         []),
                        // ]}
                        dataSource={dataSource}
                        columns={columns}
                        pagination={false}
                        bordered
                        // summary={() => (
                        //     <Table.Summary.Row>
                        //         <Table.Summary.Cell index={0} colSpan={2}>
                        //             <span className="font-semibold">Total</span>
                        //         </Table.Summary.Cell>
                        //         <Table.Summary.Cell
                        //             index={2}
                        //             className="text-center"
                        //         >
                        //             <span className="font-bold">6000</span>
                        //         </Table.Summary.Cell>
                        //     </Table.Summary.Row>
                        // )}
                    />
                </ConfigProvider>
            </div>

            <div className="pb-16">
                <Button
                    onClick={showModal}
                    className="h-16 border border-[#1a3353] text-[#1a3353]"
                >
                    Select Denomination
                </Button>
            </div>
            {/* <span> Notes/TXN ID</span>
            <Input
                placeholder={`Enter Transcation Id`}
                onChange={(e) =>
                    handleTranscationId(e.target.value)

                } /> */}
            <div className="mb-9 flex justify-between lg:flex-row lg:items-center @sm:flex-col">
                <div className="checkbox-custom">
                    <Paragraph className="ant-form-item-label mb-0">
                        <label>Notes/Txn ID</label>
                    </Paragraph>
                </div>
                <div className="flex items-center lg:w-[80%] lg:flex-row @sm:flex-col">
                    <Input
                        placeholder={`Enter Transcation Id`}
                        onChange={(e) => handleTranscationId(e.target.value)}
                    />
                </div>
            </div>
            {/* Payment Details */}
            <div className="mb-6">
                <h3 className="mb-4 text-24 font-normal text-[#1A3353]">
                    Payment Details
                </h3>
                {store.posPurchasedData?.cartDiscountAmount > 0 && (
                    <div className="mb-2 flex justify-between">
                        <span className="text-[#383838]">Cart Discount</span>
                        <span className="font-semibold">
                            ₹
                            {Math.trunc(
                                store.posPurchasedData?.cartDiscountAmount * 100
                            ) / 100 || 0.0}
                            /-
                        </span>
                    </div>
                )}
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Item Total Amount</span>
                    <span className="font-semibold">
                        ₹
                        {Math.trunc(store.posPurchasedData?.subTotal * 100) /
                            100}
                        /-
                    </span>
                </div>
                {/* {store.posPurchasedData?.returnDiscount > 0 && (
                    <div className="mb-2 flex justify-between">
                        <span className="text-[#383838]">Returns </span>
                        <span className="font-semibold">
                            ₹
                            {Math.trunc(
                                store.posPurchasedData?.returnDiscount * 100
                            ) / 100 || 0.0}
                            /-
                        </span>
                    </div>
                )} */}
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">GST</span>
                    <span className="font-semibold">
                        ₹
                        {Math.trunc(store.posPurchasedData?.gst * 100) / 100 ||
                            0.0}
                        /-
                    </span>
                </div>
                {store.posPurchasedData?.voucherDiscount > 0 && (
                    <div className="mb-2 flex justify-between">
                        <span className="text-[#383838]">Voucher Applied</span>
                        <span className="font-semibold">
                            ₹
                            {Math.trunc(
                                store.posPurchasedData?.voucherDiscount * 100
                            ) / 100 || 0.0}
                            /-
                        </span>
                    </div>
                )}
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Total Amount</span>
                    <span className="font-semibold">
                        ₹
                        {Math.trunc(
                            (store.posPurchasedData?.total +
                                store?.posPurchasedData?.roundOff || 0) * 100
                        ) / 100}
                        /-
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Round Off</span>
                    <span className="font-semibold">
                        ₹
                        {Math.trunc(
                            (store?.posPurchasedData?.roundOff || 0) * 100
                        ) / 100}
                        /-
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">
                        Tendered Amount
                        <Tooltip
                            title="Amount paid in cash"
                            placement="right"
                            trigger={['hover', 'focus']}
                        >
                            <InfoCircleOutlined
                                className="cursor-pointer align-middle text-gray-400 hover:text-gray-600"
                                aria-label="Info: Amount paid in cash"
                                tabIndex={0}
                            />
                        </Tooltip>
                    </span>
                    <span className="font-semibold">
                        ₹{Math.trunc(overallTotal * 100) / 100}/-
                    </span>
                </div>
                <div className="flex justify-between border-t pt-2">
                    <span className="font-semibold text-[#383838]">
                        Amount Pending
                    </span>
                    <span
                        className={`font-bold ${
                            overallTotal < (store.posPurchasedData?.total ?? 0)
                                ? 'text-red-600'
                                : 'text-green-600'
                        }`}
                    >
                        ₹{' '}
                        {overallTotal >=
                        Math.floor(store.posPurchasedData?.total ?? 0)
                            ? '0.00'
                            : Math.trunc(
                                  (Math.floor(
                                      store.posPurchasedData?.total ?? 0
                                  ) -
                                      (overallTotal ?? 0)) *
                                      100
                              ) / 100}
                        /-
                    </span>
                </div>
                {showChangeDue && (
                    <>
                        <style>
                            {`
        @keyframes blinkInline { 
          50% { opacity: 0; }
        }
      `}
                        </style>

                        <div className="flex justify-between border-t pt-2">
                            <span className="font-semibold">Change Due</span>
                            <span
                                style={{
                                    fontWeight: 'bold',
                                    color: 'red',
                                    // animation: "blinkInline 1s step-start infinite",   //if we want to blink then uncomment this line
                                }}
                            >
                                ₹ - {changeDue}/-
                            </span>
                        </div>
                    </>
                )}
            </div>

            {/* Footer Buttons */}
            <div className="flex justify-end gap-10">
                <Button
                    onClick={goBack}
                    className="rounded-lg border border-[#1A3353]        px-10 py-8"
                >
                    Back
                </Button>

                {/* {!isMarkedAsPaid && (
                                    <Button
                                        className="rounded-lg border border-[#1A3353] px-10 py-8"
                                        loading={payLaterLoader}
                                        onClick={() => handleClick('pending')}
                                    >
                                        Pay Later
                                    </Button>
                                )} */}

                <Button
                    loading={loader}
                    onClick={() => handleClick('completed')}
                    className="rounded-lg bg-purpleLight px-10 py-8 text-white"
                >
                    Confirm Payment
                </Button>
            </div>
            {isModalVisible && (
                <DenominationModal
                    onDenominationChange={handleDenominationChange}
                    visible={isModalVisible}
                    onClose={handleClose}
                    setOverallTotal={setOverallTotal}
                    overallTotal={overallTotal}
                    totalAmount={
                        Math.trunc(store.posPurchasedData?.total * 100) / 100
                    }
                    changeDue={
                        Math.trunc(
                            ((overallTotal ?? 0) -
                                Math.floor(
                                    store.posPurchasedData?.total ?? 0
                                )) *
                                100
                        ) / 100
                    }
                    pendingAmount={
                        overallTotal >=
                        Math.floor(store.posPurchasedData?.total ?? 0)
                            ? '0.00'
                            : Math.trunc(
                                  (Math.floor(
                                      store.posPurchasedData?.total ?? 0
                                  ) -
                                      (overallTotal ?? 0)) *
                                      100
                              ) / 100
                    }
                />
            )}
        </div>
    );
};

export default CashPayment;
