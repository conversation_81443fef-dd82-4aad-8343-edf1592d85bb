import { Button, Modal, Radio, Table, Tooltip } from 'antd';
import Title from 'antd/es/typography/Title';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useParams } from 'wouter';
import { formatDate } from '~/components/common/function';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import {
    DownloadInvoice,
    OrderInvoiceDetails,
} from '~/redux/actions/purchased-action';
import SharePassModal from '~/screens/customers/share-pass-modal';
import {
    ClassType,
    PaymentStatus,
    RoleType,
    SUBJECT_TYPE,
} from '~/types/enums';
import ClientListModal from './group-checkin-modal';
import ClientCheckInModal from './client-checkin-modal';
import {
    multipleCheckInOrder,
    singleCheckInOrder,
} from '~/redux/actions/customer-action';
import BundleShareSessionModal from './bundle-share-modal';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import FamilySharePassModal from './family-share-modal';
import BookAppointmentModal from '~/screens/appointment/bookAppointmentModal';
import {
    ConfimredButtonChip,
    PendingButtonChip,
    RejectedButtonChip,
    ReturnButtonChip,
} from '~/components/common/chip-component';
import { set } from 'date-fns';
import { InfoCircleOutlined } from '@ant-design/icons';

export const renderPaymentStatusChip = (status: string) => {
    switch (status?.toLowerCase()) {
        case PaymentStatus.PENDING:
            return <PendingButtonChip />;
        case PaymentStatus.COMPLETED:
            return <ConfimredButtonChip />;
        case PaymentStatus.FAILED:
            return <RejectedButtonChip />;
        default:
            return <ReturnButtonChip text={status?.toLowerCase()} />;
    }
};

const OrderConfirmation = () => {
    const [_, setLocation] = useLocation();
    const [sharePassData, setSharePassData] = useState<any>(false);
    const [multisharePassData, setMultiSharePassData] = useState<any>(false);
    const [packageList, setPackageList] = useState<any>();
    const [orderData, setOrderData] = useState<any>();
    const [loader, startLoader, endLoader] = useLoader(true);
    const [submitLoader, starSubmittLoader, endSubmitLoader] = useLoader();
    const { orderId } = useParams();
    const dispatch = useAppDispatch();
    const [multipleSharePassOpen, setMultipleSharePassOpen] =
        useState<boolean>(false);
    const [clientListModalVisible, setClientListModalVisible] = useState(false);
    const [checkInModalVisible, setCheckInModalVisible] = useState(false);
    const [sharedClientsForCheckIn, setSharedClientsForCheckIn] = useState<any>(
        []
    );
    const [selectedClientData, setSelectedClientData] = useState<any>(null);
    const [bundleModalVisible, setBundleModalVisible] = useState(false);
    const [bundleData, setBundleData] = useState<any>(null);
    const [confirmationVisible, setConfirmationVisible] = useState(false);
    const [selectedCheckInData, setSelectedCheckInData] = useState<any>(null);
    const [checkInSuccessModalVisible, setCheckInSuccessModalVisible] =
        useState(false);
    const [checkInSuccessData, setCheckInSuccessData] = useState<any[]>([]);
    const [familyPassShare, setFamilyPassShare] = useState<any>(false);
    const [personalAppointmnetModal, setPersonalAppointmentModal] =
        useState<boolean>(false);
    const [paData, setPAData] = useState<any>();
    const [invoiceModalOpen, setInvoiceModalOpen] = useState(false);
    const [selectedFormat, setSelectedFormat] = useState<'roll' | 'pdf'>('pdf');
    const [loading, setLoading] = useState(false);

    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
    }));
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasPricingPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) => subject.type === SUBJECT_TYPE.ORDER
            )
        );
    }, [all_permissions_for_role]);
    const handleClose = () => {
        setPAData(null);
        setPersonalAppointmentModal(false);
    };
    useEffect(() => {
        startLoader();
        if (orderId) {
            dispatch(OrderInvoiceDetails({ orderId: orderId }))
                .unwrap()
                .then((res: any) => {
                    console.log('response for order invoice is:', res);
                    const data = res.data.data;
                    const userId = data?.userId;
                    const returnItems = data?.returnPurchaseItems?.length
                        ? data?.returnPurchaseItems.map(
                              (item: any, i: number) => ({
                                  key: i + 1,
                                  srNo: i + 1,
                                  product: item.packageName,
                                  quantity: item.quantity,
                                  price: item.price,
                                  sessionType: item.sessionType,
                                  classType: item.classType,
                                  isReturn: true,
                                  bundledPricingId: item.isBundledPricing
                                      ? item.packageId
                                      : null,
                                  packageId: item.isBundledPricing
                                      ? null
                                      : item.packageId,
                                  userId,
                                  ...item,
                              })
                          )
                        : [];
                    const voucherItems = data?.appliedVoucherItems?.length
                        ? data?.appliedVoucherItems.map(
                              (item: any, i: number) => ({
                                  key: i + 1,
                                  srNo: i + 1,
                                  product: item.name,
                                  isVoucherApply: true,
                                  userId,
                                  ...item,
                              })
                          )
                        : [];
                    const purchaseItems = data?.purchaseItems?.length
                        ? data?.purchaseItems.map((item: any, i: number) => ({
                              key: i + 1,
                              srNo: i + 1,
                              product: item.name,
                              quantity: item.quantity,
                              price: item.price,
                              sessionType: item.sessionType,
                              classType: item.classType,
                              bundledPricingId: item.isBundledPricing
                                  ? item.packageId
                                  : null,
                              packageId: item.isBundledPricing
                                  ? null
                                  : item.packageId,
                              userId,
                              ...item,
                          }))
                        : [];
                    const productItems = data?.productItem?.length
                        ? data?.productItem.map((item: any, i: number) => ({
                              key: purchaseItems.length + i + 1,
                              srNo: purchaseItems.length + i + 1,
                              product: item.name,
                              quantity: item.quantity,
                              price: item.price,
                              isProduct: true,
                              userId,
                              ...item,
                          }))
                        : [];
                    const customPackageItems = data?.customPackageItems?.length
                        ? data?.customPackageItems.map(
                              (item: any, i: number) => ({
                                  key:
                                      purchaseItems.length +
                                      productItems.length +
                                      i +
                                      1,
                                  srNo:
                                      purchaseItems.length +
                                      productItems.length +
                                      i +
                                      1,
                                  product: item.name,
                                  quantity: item.quantity,
                                  price: item.price,
                                  userId,
                                  ...item,
                              })
                          )
                        : [];
                    setPackageList([
                        ...returnItems,
                        ...voucherItems,
                        ...purchaseItems,
                        ...productItems,
                        ...customPackageItems,
                    ]);
                    console.log(data, 'dataaaa');
                    setOrderData(data);
                })
                .finally(endLoader);
        }
    }, [orderId]);

    const handleDownloadInvoice = async () => {
        if (!orderId) return;

        try {
            const response = await dispatch(
                DownloadInvoice({ orderId })
            ).unwrap();

            if (!response) {
                throw new Error('No URL received from API.');
            }

            window.open(response, '_blank', 'noopener,noreferrer');
        } catch (error) {
            console.error('Error downloading invoice:', error);
        }
    };

    const handleConfirmCheckIn = async () => {
        if (!selectedCheckInData) return;

        starSubmittLoader();

        const payload = {
            packageId: selectedCheckInData?.packageId,
            userId: selectedCheckInData?.userId,
            invoiceId: selectedCheckInData?.invoiceId,
            organizationId: orderData?.organizationId,
            facilityId: orderData?.facilityId?._id,
        };

        try {
            await dispatch(singleCheckInOrder(payload))
                .unwrap()
                .then((res: any) => {
                    console.log('Res--------', res);
                    const status = res?.payload?.status ?? res?.status;
                    if (status === 200 || status === 201) {
                        setConfirmationVisible(false);
                        setSelectedCheckInData(null);
                        const checkInDetails = res?.data?.data || [];
                        setCheckInSuccessData(checkInDetails);
                        setCheckInSuccessModalVisible(true);
                    }
                });
        } catch (error) {
            console.log('Check-In failed');
        } finally {
            endSubmitLoader();
        }
    };
    useEffect(() => {
        if (paData) setPersonalAppointmentModal(true);
    }, [paData]);
    const columns = [
        {
            title: 'Sr. No.',
            dataIndex: 'srNo',
            key: 'srNo',
            // width: 60,
            render: (text: string, record: any) => (
                <div
                    className={
                        record.isReturn || record.isVoucherApply
                            ? 'text-red-500'
                            : ''
                    }
                >
                    {text}
                </div>
            ),
        },
        {
            title: 'Item',
            dataIndex: 'product',
            key: 'product',
            // width: 300,
            render: (text: string, record: any) => (
                <div
                    className={
                        record.isReturn || record.isVoucherApply
                            ? 'text-red-500'
                            : ''
                    }
                >
                    {text}
                </div>
            ),
        },
        {
            title: ' Quantity',
            dataIndex: 'quantity',
            key: 'quantity',
            // width: 80,
            render: (text: string, record: any) => (
                <div
                    className={
                        record.isReturn || record.isVoucherApply
                            ? 'text-red-500'
                            : ''
                    }
                >
                    {text}
                </div>
            ),
        },

        {
            title: 'Unit Price',
            dataIndex: 'price',
            key: 'price',
            // render: (value: any) => value?.toFixed(2),
            render: (value: any, record: any) => {
                // const unitPrice = record?.isInclusiveofGst
                //     ? record?.finalPrice
                //     : record?.price;
                return (
                    <div
                        className={
                            record.isReturn || record.isVoucherApply
                                ? 'text-red-500'
                                : ''
                        }
                    >
                        {record.isReturn || record.isVoucherApply
                            ? -(Math.trunc(value * 100) / 100)
                            : Math.trunc(value * 100) / 100}
                    </div>
                );
            },
        },
        {
            title: ' Discount',
            dataIndex: 'discountValue',
            key: 'discountValue',
            render: (text: string, record: any) => <div>{text || 0}</div>,
        },
        {
            title: 'Actions',
            key: 'actions',
            width: 250,
            render: (record: any) => {
                console.log(record, 'record');
                const showFamilyShare = [
                    ClassType.BOOKING,
                    ClassType.PERSONAL_APPOINTMENT,
                    ClassType.CLASSES,
                ].includes(record?.classType);

                return (
                    <>
                        {!record.isReturn && !record.isVoucherApply && (
                            <div className="flex gap-4">
                                {record?.classType === ClassType.BOOKING && (
                                    <>
                                        <Button
                                            htmlType="button"
                                            className="h-10 bg-purpleLight text-white"
                                            onClick={() => {
                                                setSelectedCheckInData({
                                                    packageId:
                                                        record?.packageId,
                                                    userId: record?.userId,
                                                    invoiceId: orderData?._id,
                                                });
                                                setConfirmationVisible(true);
                                            }}
                                        >
                                            <p className="text-lg">Check-In</p>
                                        </Button>
                                        <Button
                                            htmlType="button"
                                            className="h-10 bg-purpleLight text-white"
                                            onClick={() => {
                                                if (record.isBundledPricing) {
                                                    setBundleData(record);
                                                    setBundleModalVisible(true);
                                                } else {
                                                    setMultipleSharePassOpen(
                                                        true
                                                    );
                                                    setMultiSharePassData(
                                                        record
                                                    );
                                                }
                                            }}
                                        >
                                            <p className="text-lg">
                                                {record.isBundledPricing
                                                    ? 'All Packages'
                                                    : 'Group Check-In'}
                                            </p>
                                        </Button>
                                    </>
                                )}

                                {record?.classType ===
                                    ClassType.PERSONAL_APPOINTMENT &&
                                    ['day_pass', 'multiple'].includes(
                                        record.sessionType
                                    ) && (
                                        <Button
                                            htmlType="button"
                                            className="h-10 bg-purpleLight text-white"
                                            onClick={() =>
                                                setSharePassData({
                                                    visible: true,
                                                    bundledPricingId:
                                                        record?.bundledPricingId,
                                                    packageId:
                                                        record?.packageId,
                                                })
                                            }
                                        >
                                            <p className="text-lg">
                                                Share Pass
                                            </p>
                                        </Button>
                                    )}

                                {showFamilyShare && (
                                    <Button
                                        htmlType="button"
                                        className="h-10 bg-purpleLight text-white"
                                        onClick={() => {
                                            setFamilyPassShare({
                                                visible: true,
                                                record: record,
                                            });
                                        }}
                                    >
                                        <p className="text-lg">Family Share</p>
                                    </Button>
                                )}
                            </div>
                        )}
                        {/* {record?.classType === ClassType.PERSONAL_APPOINTMENT && (
                            <>
                                <Button
                                    htmlType="button"
                                    className="h-10 bg-purpleLight text-white"
                                    onClick={() => {
                                        setPAData(record)
                                        console.log("book Pa", record, orderData)
                                    }}
                                >
                                    <p className="text-lg">Book Session</p>
                                </Button>
                            </>
                        )

                        } */}
                    </>
                );
            },

            // !record.isProduct &&
            // record.classType !== 'courses' &&
            // ['day_pass', 'multiple'].includes(record.sessionType) && (
            //     <Button
            //         htmlType="button"
            //         className="h-10   bg-purpleLight  text-white "
            //         onClick={() =>
            //             setSharePassData({
            //                 visible: true,
            //                 bundledPricingId: record?.bundledPricingId,
            //                 packageId: record?.packageId,
            //             })
            //         }
            //     >
            //         <p className="text-lg"> Share Pass </p>
            //     </Button>
            // ),
        },
    ];

    const handleCheckIn = async (clientsIds: any) => {
        const purchaseId = clientsIds;
        if (!purchaseId) return;

        starSubmittLoader();
        const payload = {
            purchaseIds: purchaseId,
            organizationId: orderData?.organizationId,
            facilityId: orderData?.facilityId?._id,
        };
        try {
            await dispatch(multipleCheckInOrder(payload))
                .unwrap()
                .then((res: any) => {
                    console.log('Res--------', res);
                    const status = res?.payload?.status ?? res?.status;
                    if (status === 200 || status === 201) {
                        setCheckInModalVisible(false);
                        const checkInDetails = res?.data?.data || [];
                        setCheckInSuccessData(checkInDetails);
                        setCheckInSuccessModalVisible(true);
                    }
                });
        } catch (error) {
            console.error('Check-In Failed:', error);
        } finally {
            endSubmitLoader();
        }
    };

    const printInvoice80mm = (invoice: any) => {
        const INR = (n: number | null) =>
            n == null
                ? ''
                : `₹ ${(Math.round(Number(n) * 100) / 100).toFixed(2)}`;

        const dateIN = (d: string | number | Date) =>
            d
                ? new Date(d).toLocaleDateString('en-IN', {
                      year: 'numeric',
                      month: 'short',
                      day: '2-digit',
                  })
                : '--';

        // Payment method name
        const pmName =
            invoice?.updatedPaymentDetails?.[0]?.paymentMethodName ||
            invoice?.paymentDetails?.[0]?.paymentMethodName ||
            invoice?.paymentDetails?.[0]?.paymentMethod ||
            '-';

        // Check if same UT code for tax display
        const sameUT =
            invoice?.clientBillingDetails?.utCode ===
            invoice?.billingDetails?.utCode;

        // Combine all items from different arrays with proper classification
        const items = [
            // Purchase Items (positive values)
            ...(invoice?.purchaseItems || []).map(
                (item: {
                    name: any;
                    price: any;
                    finalPrice: any;
                    quantity: any;
                    discountValue: any;
                }) => ({
                    ...item,
                    itemType: 'purchase',
                    displayName: item?.name,
                    isNegative: false,
                    unitPrice: Number(item?.price || item?.finalPrice || 0),
                    quantity: item?.quantity || 1,
                    discountValue: Number(item?.discountValue || 0),
                })
            ),
            // Return Purchase Items (negative values)
            ...(invoice?.returnPurchaseItems || []).map(
                (item: { packageName: any; price: any; quantity: any }) => ({
                    ...item,
                    itemType: 'return',
                    displayName: `${item?.packageName} (Return)`,
                    isNegative: true,
                    unitPrice: Number(item?.price || 0),
                    quantity: item?.quantity || 1,
                    discountValue: 0, // Returns don't have additional item discounts
                })
            ),
            // Return Custom Package Items (negative values)
            ...(invoice?.returnCustomPackageItems || []).map(
                (item: {
                    name: any;
                    packageName: any;
                    price: any;
                    quantity: any;
                }) => ({
                    ...item,
                    itemType: 'returnCustom',
                    displayName: item?.name || item?.packageName,
                    isNegative: true,
                    unitPrice: Number(item?.price || 0),
                    quantity: item?.quantity || 1,
                    discountValue: 0,
                })
            ),
            // Applied Voucher Items (already accounted in voucherDiscount, but show for transparency)
            ...(invoice?.appliedVoucherItems || []).map(
                (item: { name: any; price: any; quantity: any }) => ({
                    ...item,
                    itemType: 'voucher',
                    displayName: `${item?.name} (Voucher Applied)`,
                    isNegative: true,
                    unitPrice: Number(item?.price || 0),
                    quantity: item?.quantity || 1,
                    discountValue: 0, // Vouchers don't have additional item discounts
                })
            ),
            // Custom Package Items (positive values)
            ...(invoice?.customPackageItems || []).map(
                (item: {
                    name: any;
                    price: any;
                    finalPrice: any;
                    quantity: any;
                    discountValue: any;
                }) => ({
                    ...item,
                    itemType: 'custom',
                    displayName: item?.name,
                    isNegative: false,
                    unitPrice: Number(item?.price || item?.finalPrice || 0),
                    quantity: item?.quantity || 1,
                    discountValue: Number(item?.discountValue || 0),
                })
            ),
            // Product Items (positive values)
            ...(invoice?.productItem || []).map(
                (item: {
                    name: any;
                    price: any;
                    quantity: any;
                    discountValue: any;
                }) => ({
                    ...item,
                    itemType: 'product',
                    displayName: item?.name,
                    isNegative: false,
                    unitPrice: Number(item?.price || 0),
                    quantity: item?.quantity || 1,
                    discountValue: Number(item?.discountValue || 0),
                })
            ),
        ];

        // Generate items HTML
        const generateItemsHTML = () => {
            return items
                .map((item) => {
                    const qty = item.quantity;
                    const rate = item.unitPrice;
                    const lineTotal = rate * qty;
                    const itemDiscount = item.discountValue;

                    // Apply negative sign for return items and vouchers
                    const displayRate = item.isNegative
                        ? -Math.abs(rate)
                        : rate;
                    const displayLineTotal = item.isNegative
                        ? -Math.abs(lineTotal)
                        : lineTotal;

                    return `
                <div class="row wrapline">
                    <div class="label" style="flex:1; text-align:left; ${
                        item.isNegative ? 'color: #dc2626;' : ''
                    }">${item.displayName || '-'}</div>
                </div>
                <div class="row tiny">
                    <div class="label" style="flex:1; text-align:left; ${
                        item.isNegative ? 'color: #dc2626;' : ''
                    }">
                        ${qty} @ ${INR(displayRate)}
                    </div>
                    <div class="val" style="text-align:right; ${
                        item.isNegative ? 'color: #dc2626;' : ''
                    }">
                        ${INR(displayLineTotal)}
                    </div>
                </div>
                ${
                    itemDiscount > 0 && !item.isNegative
                        ? `
                    <div class="row tiny">
                        <div class="label">Item Discount</div>
                        <div class="val">-${INR(itemDiscount)}</div>
                    </div>
                `
                        : ''
                }
                <div style="padding-top: 5px;"></div>
            `;
                })
                .join('');
        };

        // Company details
        const companyName =
            invoice?.billingDetails?.billingName || invoice?.facilityName || '';
        const address1 = invoice?.billingDetails?.addressLine1 || '';
        const address2 = invoice?.billingDetails?.addressLine2 || '';
        const city = invoice?.billingDetails?.cityName || '';
        const state = invoice?.billingDetails?.stateName || '';
        const pincode = invoice?.billingDetails?.postalCode || '';
        const email = invoice?.billingDetails?.email || '';
        const phone = invoice?.billingDetails?.phone || '';
        const gstin = (invoice?.billingDetails?.gstNumber || '').toUpperCase();

        const html = `
<!doctype html>
<html>
<head>
<meta charset="utf-8" />
<title>Receipt ${invoice?.invoiceNumber ?? ''}</title>
<style>
  @page { size: 80mm auto; margin: 0; }
  @media print { html, body { width: 80mm; } }
  html, body { margin: 0; width: 80mm; color: #000; }
  body { font: 12px/1.45 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif; }

  .rcpt   { width: 72mm; margin: 0 auto; padding: 8px 0; }
  .center { text-align: center; }
  .right  { text-align: right; }
  .bold   { font-weight: 700; }
  .muted  { color: #666; }
  .div    { border-top: 1px dashed #000; margin: 8px 0; }
  .row    { display: flex; justify-content: space-between; gap: 8px; }
  .row + .row { margin-top: 2px; }
  .h1     { font-size: 16px; font-weight: 800; }
  .tiny   { font-size: 11px; }
  .sp8    { margin-top: 8px; }
  .sp12   { margin-top: 12px; }
  .lh     { line-height: 1.3; }
  .addr a { color: #000; text-decoration: underline; }
  .label  { min-width: 50%; }
  .val    { flex: 1; text-align: right; }
  .pt-5   { padding-top: 5px; }

  /* Totals emphasis */
  .gtl    { font-size: 13px; font-weight: 800; }

  /* Make long item names wrap nicely */
  .wrapline { word-break: break-word; }

</style>
</head>
<body>
  <div class="rcpt">
    <!-- Header -->
    <div class="center h1">${companyName}</div>
    <div class="center tiny addr lh">
      ${address1 ? `${address1}<br/>` : ''}
      ${address2 ? `${address2}<br/>` : ''}
      ${
          city || state || pincode
              ? `${city ? city + ', ' : ''}${state || ''} ${pincode || ''}`
              : ''
      }
    </div>
    <div class="center tiny lh sp8">
      ${email ? `Email: ${email}<br/>` : ''}
      ${phone ? `Phone: ${phone}<br/>` : ''}
      ${gstin ? `GSTIN: ${gstin}` : ''}
    </div>

    <div class="div"></div>

    <!-- Invoice meta -->
    <div class="row tiny">
      <div class="label">Invoice No:</div>
      <div class="val">${invoice?.invoiceNumber ?? '-'}</div>
    </div>
    <div class="row tiny">
      <div class="label">Order Date:</div>
      <div class="val">${dateIN(invoice?.invoiceDate)}</div>
    </div>
    <div class="row tiny">
      <div class="label">Created By:</div>
      <div class="val">${invoice?.createdByName || '-'}</div>
    </div>

    <div class="div"></div>

    <!-- Customer -->
    <div class="row tiny">
      <div class="label">Customer Name:</div>
      <div class="val">${invoice?.clientDetails?.name || '-'}</div>
    </div>
    ${
        invoice?.clientDetails?.customerId
            ? `
    <div class="row tiny">
      <div class="label">Customer ID:</div>
      <div class="val">${invoice.clientDetails.customerId}</div>
    </div>
    `
            : ''
    }

    <div class="div"></div>

    <!-- Items -->
    ${generateItemsHTML()}

    <div class="div"></div>

    <!-- Cart Discount (if applied) -->
    ${
        invoice?.cartDiscountAmount > 0
            ? `
    <div class="row tiny">
      <div class="label">Cart Discount (${
          invoice?.cartDiscountType === 'Percentage'
              ? invoice?.cartDiscount + '%'
              : 'Fixed'
      })</div>
      <div class="val">-${INR(invoice?.cartDiscountAmount)}</div>
    </div>
    `
            : ''
    }

    <!-- Subtotal -->
    <div class="row">
      <div class="label">Sub Total:</div>
      <div class="val">${INR(invoice?.subTotal)}</div>
    </div>

    <!-- Tax Section -->
    ${
        sameUT
            ? `
      ${
          invoice?.cgst != null && invoice?.cgst > 0
              ? `<div class="row"><div class="label">CGST:</div><div class="val">${INR(
                    invoice?.cgst
                )}</div></div>`
              : ''
      }
      ${
          invoice?.sgst != null && invoice?.sgst > 0
              ? `<div class="row"><div class="label">SGST:</div><div class="val">${INR(
                    invoice?.sgst
                )}</div></div>`
              : ''
      }
    `
            : `
      ${
          invoice?.igst != null && invoice?.igst > 0
              ? `<div class="row"><div class="label">IGST:</div><div class="val">${INR(
                    invoice?.igst
                )}</div></div>`
              : ''
      }
    `
    }

    <!-- Voucher Discount (if applied) -->
    ${
        invoice?.voucherDiscount > 0
            ? `
    <div class="row tiny">
      <div class="label">Voucher Applied</div>
      <div class="val">-${INR(invoice?.voucherDiscount)}</div>
    </div>
    `
            : ''
    }

    <!-- Round Off -->
    ${
        invoice?.roundOff != null && invoice?.roundOff !== 0
            ? `
    <div class="row tiny">
      <div class="label">Round Off</div>
      <div class="val">${invoice?.roundOff > 0 ? '+' : ''}${INR(
                  invoice?.roundOff
              )}</div>
    </div>
    `
            : ''
    }

    <div class="div"></div>

    <!-- Grand Total -->
    <div class="row gtl">
      <div class="label">Grand Total</div>
      <div class="val">${INR(invoice?.grandTotal)}</div>
    </div>

    <div class="div"></div>

    <!-- Amount in words -->
    ${
        invoice?.amountInWords
            ? `
    <div class="tiny">${String(invoice.amountInWords)}</div>
    <div class="div"></div>
    `
            : ''
    }

    <!-- Payment Details -->
    <div class="row">
      <div class="label">Payment Method:</div>
      <div class="val">${pmName}</div>
    </div>
    <div class="row">
      <div class="label">Payment Status:</div>
      <div class="val">${
          invoice?.paymentStatus
              ? invoice.paymentStatus.charAt(0).toUpperCase() +
                invoice.paymentStatus.slice(1)
              : '-'
      }</div>
    </div>

    <!-- Cash payment details -->
    ${
        (pmName?.toLowerCase() === 'cash' || invoice?.isSplittedPayment) &&
        invoice?.tenderedAmount > 0
            ? `
      <div class="row">
        <div class="label">Tendered:</div>
        <div class="val">${INR(invoice?.tenderedAmount)}</div>
      </div>
      <div class="row">
        <div class="label">Change Due:</div>
        <div class="val">${INR(invoice?.changeDue)}</div>
      </div>
    `
            : ''
    }

    <!-- Footer notes -->
    <div class="div"></div>
    <div class="center tiny lh">
      ${invoice?.totalGstValue > 0 ? 'GST is included in the total.<br/>' : ''}
      This is a computer generated receipt.<br/>
      Disputes subject to ${
          invoice?.billingDetails?.cityName || 'local'
      } jurisdiction.
    </div>
    <div class="center bold sp8">Thank you for your visit!</div>
  </div>

  <script>
    window.onload = function () {
      window.print();
      setTimeout(function(){ window.close(); }, 400);
    };
  </script>
</body>
</html>
    `.trim();

        // Create and print via iframe
        const iframe = document.createElement('iframe');
        iframe.style.position = 'fixed';
        iframe.style.right = '0';
        iframe.style.bottom = '0';
        iframe.style.width = '0';
        iframe.style.height = '0';
        iframe.style.border = '0';
        iframe.setAttribute('aria-hidden', 'true');
        document.body.appendChild(iframe);

        const cleanup = () =>
            setTimeout(() => {
                try {
                    document.body.removeChild(iframe);
                } catch {
                    /* empty */
                }
            }, 800);

        iframe.srcdoc = html;
        iframe.addEventListener('load', cleanup);
    };

    const handleDownload = async () => {
        if (!orderId) return;

        try {
            setLoading(true);
            if (selectedFormat === 'roll') {
                printInvoice80mm(orderData);
            } else {
                // your existing PDF flow
                const response = await dispatch(
                    DownloadInvoice({ orderId })
                ).unwrap();
                if (!response) throw new Error('No URL received from API.');
                window.open(response, '_blank', 'noopener,noreferrer');
            }
        } catch (err) {
            console.error(err);
        } finally {
            setLoading(false);
            setInvoiceModalOpen(false);
        }
    };

    return (
        <div className="">
            <div className="flex flex-row justify-between">
                <div className="flex flex-row items-center gap-6 pb-12">
                    <Title className=" text-[#1a3353]" level={4}>
                        Order Detail
                    </Title>
                    <p className=" text-[#455560] lg:text-[16px]">
                        Order ID: {orderData?.orderId}
                    </p>
                </div>
                <div className="flex  flex-row items-center gap-4">
                    {['completed', 'refund'].includes(
                        orderData?.paymentStatus
                    ) && (
                        <Button
                            onClick={() => setInvoiceModalOpen(true)}
                            htmlType="button"
                            className="h-16  w-[110px] border-[#1A3353] bg-[#fff]  text-[#1A3353] "
                        >
                            <p> Print Receipt </p>
                        </Button>
                    )}
                    {(hasPricingPermission ||
                        store.role === RoleType.ORGANIZATION) && (
                        <Button
                            htmlType="button"
                            className="h-16  w-[110px]  bg-purpleLight text-white "
                            onClick={() => setLocation('/order-listing')}
                        >
                            <p>Order History </p>
                        </Button>
                    )}
                </div>
            </div>
            <div className="flex flex-row items-start gap-4">
                <div className="w-[70%] rounded-md border border-gray-200 px-5 py-5">
                    <p className=" font-semibold text-[#1a3353] lg:text-[16px]">
                        Order contents
                    </p>

                    <div>
                        <Table
                            dataSource={packageList}
                            columns={columns}
                            pagination={false}
                        />
                    </div>
                </div>
                <div className="w-[30%] rounded-md border border-gray-200 p-5">
                    <p className=" font-semibold  text-[#1a3353] lg:text-[16px]">
                        Order summary
                    </p>
                    <div className="flex flex-col gap-2">
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Client Name :</p>
                            <p className="text-[#455560]">
                                {orderData?.clientDetails.name}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Purchase Date :</p>
                            <p className="text-[#455560]">
                                {formatDate(orderData?.invoiceDate)}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Payment Status :</p>
                            <p className="text-[#455560]">
                                {renderPaymentStatusChip(
                                    orderData?.paymentStatus
                                )}
                            </p>
                        </div>
                        {orderData?.cartDiscountAmount > 0 && (
                            <div className="flex flex-row items-center gap-2 pt-3">
                                <p className="font-medium ">Cart Discount :</p>
                                <p className="text-[#455560]">
                                    ₹{' '}
                                    {Math.trunc(
                                        orderData?.cartDiscountAmount * 100
                                    ) / 100}
                                    {/* {orderData?.cartDiscountAmount.toFixed(2)} */}
                                </p>
                            </div>
                        )}
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Item Total :</p>
                            <p className="text-[#455560]">
                                ₹{orderData?.subTotal}
                            </p>
                        </div>
                        {/* <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">
                                Total Item Discount :
                            </p>
                            <p className="text-[#455560]">
                                -₹{orderData?.itemDiscount}
                            </p>
                        </div> */}

                        {/* {orderData?.returnDiscount > 0 && (
                            <div className="flex flex-row items-center gap-2 pt-3">
                                <p className="font-medium ">Returns :</p>
                                <p className="text-[#455560]">
                                    ₹{' '}
                                    {Math.trunc(
                                        orderData?.returnDiscount * 100
                                    ) / 100}
                                    
                                </p>
                            </div>
                        )} */}
                        {orderData?.clientBillingDetails?.utCode ===
                        orderData?.billingDetails?.utCode ? (
                            <>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">CGST :</p>
                                    <p className="text-[#455560]">
                                        ₹{orderData?.cgst}
                                    </p>
                                </div>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">SGST :</p>
                                    <p className="text-[#455560]">
                                        ₹{orderData?.sgst}
                                    </p>
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">IGST :</p>
                                    <p className="text-[#455560]">
                                        {orderData?.igst}
                                    </p>
                                </div>
                            </>
                        )}
                        {orderData?.voucherDiscount > 0 && (
                            <div className="flex flex-row items-center gap-2 pt-3">
                                <p className="font-medium ">
                                    Voucher Applied :
                                </p>
                                <p className="text-[#455560]">
                                    ₹{' '}
                                    {Math.trunc(
                                        orderData?.voucherDiscount * 100
                                    ) / 100}
                                </p>
                            </div>
                        )}
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Total Amount </p>
                            <p className="text-[#455560]">
                                ₹{orderData?.totalAmountAfterGst}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Round Off </p>
                            <p className="text-[#455560]">
                                ₹ -{orderData?.roundOff}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Grand Total :</p>
                            <p className="text-[#455560]">
                                ₹{Math.floor(orderData?.grandTotal)}
                            </p>
                        </div>
                        {orderData?.tenderedAmount !== 0 && (
                            <>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">
                                        Tendered Amount
                                        <Tooltip
                                            title="Amount Received"
                                            placement="right"
                                            trigger={['hover', 'focus']}
                                        >
                                            <InfoCircleOutlined
                                                className="cursor-pointer px-2 align-middle text-gray-400 hover:text-gray-600"
                                                aria-label="Info: Amount paid"
                                                tabIndex={0}
                                            />
                                        </Tooltip>
                                        :
                                    </p>
                                    <p className="text-[#455560]">
                                        ₹{Math.floor(orderData?.tenderedAmount)}
                                    </p>
                                </div>
                            </>
                        )}
                        {orderData?.changeDue !== 0 && (
                            <>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">Change Due :</p>
                                    <p className="text-[#455560]  text-red-600">
                                        ₹{Math.floor(orderData?.changeDue)}
                                    </p>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>
            {/* <div className=" mx-auto  flex w-[33%] flex-row items-center gap-4 pt-20">
                <Button
                    htmlType="button"
                    className="h-16  w-[110px] border-[#1A3353] bg-[#fff]  text-[#1A3353] "
                >
                    <p> Print Receipt </p>
                </Button>
                <Button
                    htmlType="button"
                    className="h-16  w-[110px] border-[#1A3353] bg-[#fff]  text-[#1A3353] "
                    onClick={() => setLocation('/order-listing')}
                >
                    <p>Order History </p>
                </Button>
            </div> */}
            {sharePassData?.visible && (
                <SharePassModal
                    visible={sharePassData?.visible}
                    onClose={() => setSharePassData(null)}
                    clientId={orderData?.userId}
                    clientName={orderData?.clientDetails.name}
                    invoiceId={orderData?._id}
                    bundledPricingId={sharePassData?.bundledPricingId}
                    packageId={sharePassData?.packageId}
                />
            )}
            {multipleSharePassOpen && (
                <ClientListModal
                    visible={multipleSharePassOpen}
                    data={multisharePassData}
                    onCancel={() => setMultipleSharePassOpen(false)}
                    orderData={orderData}
                    checkInModalVisible={checkInModalVisible}
                    setCheckInModalVisible={setCheckInModalVisible}
                    sharedClientsForCheckIn={sharedClientsForCheckIn}
                    setSharedClientsForCheckIn={setSharedClientsForCheckIn}
                    setSelectedClientData={setSelectedClientData}
                />
            )}

            {/* {checkInModalVisible && (
                <ClientCheckInModal
                    visible={checkInModalVisible}
                    data={multisharePassData}
                    loading={submitLoader}
                    onClose={() => setCheckInModalVisible(false)}
                    sharedClients={sharedClientsForCheckIn}
                    selectedClientData={selectedClientData}
                    setSelectedClientData={setSelectedClientData}
                    onCheckIn={(checkedInClients: any) => {
                        console.log('Check-in Clients:', checkedInClients);
                        handleCheckIn(checkedInClients);
                    }}
                />
            )} */}

            {bundleModalVisible && (
                <BundleShareSessionModal
                    visible={bundleModalVisible}
                    onClose={() => setBundleModalVisible(false)}
                    orderData={orderData}
                    bundleData={bundleData}
                    openClientListModal={(selectedPackage: any) => {
                        setMultiSharePassData(selectedPackage);
                        setMultipleSharePassOpen(true);
                    }}
                />
            )}

            {confirmationVisible && (
                <CommonConfirmationModal
                    visible={confirmationVisible}
                    onCancel={() => {
                        setConfirmationVisible(false);
                        setSelectedCheckInData(null);
                    }}
                    onConfirm={handleConfirmCheckIn}
                    message="Are you sure you want to check-in this client?"
                    loader={submitLoader}
                />
            )}
            {checkInSuccessModalVisible && (
                <Modal
                    title="Check-In Summary"
                    open={checkInSuccessModalVisible}
                    onCancel={() => setCheckInSuccessModalVisible(false)}
                    footer={[
                        <Button
                            key="close"
                            className="h-16  w-[110px]  bg-purpleLight text-white "
                            onClick={() => setCheckInSuccessModalVisible(false)}
                        >
                            Close
                        </Button>,
                    ]}
                    width="60%"
                >
                    <Table
                        dataSource={checkInSuccessData}
                        // rowKey={(record) =>
                        //     record?.clientName + record?.packageName
                        // }
                        columns={[
                            {
                                title: 'Client Name',
                                dataIndex: 'clientName',
                                key: 'clientName',
                            },
                            {
                                title: 'Package Name',
                                dataIndex: 'packageName',
                                key: 'packageName',
                            },
                            {
                                title: 'Service Category',
                                dataIndex: 'serviceCategoryName',
                                key: 'serviceCategoryName',
                            },
                            {
                                title: 'Room Name',
                                dataIndex: 'roomName',
                                key: 'roomName',
                            },
                            {
                                title: 'Start Time',
                                dataIndex: 'from',
                                key: 'from',
                                render: (text) => text,
                            },
                            {
                                title: 'End Time',
                                dataIndex: 'to',
                                key: 'to',
                                render: (text) => text,
                            },
                        ]}
                        pagination={false}
                    />
                </Modal>
            )}
            {familyPassShare?.visible && (
                <FamilySharePassModal
                    visible={familyPassShare?.visible}
                    onClose={() => setFamilyPassShare(null)}
                    orderData={familyPassShare?.record}
                    invoiceId={orderId}
                />
            )}
            {personalAppointmnetModal && paData && (
                <>
                    <Modal
                        title="Book Appointment"
                        onCancel={handleClose}
                        open={personalAppointmnetModal}
                        centered
                        footer={null}
                        className="lg:w-[75%]"
                    >
                        <BookAppointmentModal
                            clientId={orderData.userId}
                            facilityId={orderData?.facilityId?._id}
                            onClose={handleClose}
                            purchaseId={paData?.purchaseIds[0]}
                        />
                    </Modal>
                </>
            )}

            <Modal
                title="Download Report as"
                open={invoiceModalOpen}
                onCancel={() => setInvoiceModalOpen(false)}
                footer={null}
                centered
                maskClosable={false}
            >
                <div className="items-left flex flex-col gap-2">
                    <Radio.Group
                        value={selectedFormat}
                        onChange={(e) => setSelectedFormat(e.target.value)}
                        className="items-left flex flex-row gap-6"
                    >
                        <Radio value="roll">Thermal Print</Radio>
                        <Radio value="pdf">PDF</Radio>
                    </Radio.Group>

                    <div className="mt-8 flex gap-4">
                        <Button onClick={() => setInvoiceModalOpen(false)}>
                            Cancel
                        </Button>
                        <Button
                            className="fw-500 w-[100px] rounded-2xl bg-purpleLight py-3 text-xl text-white"
                            type="primary"
                            onClick={handleDownload}
                            loading={loading}
                        >
                            Download
                        </Button>
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default OrderConfirmation;
