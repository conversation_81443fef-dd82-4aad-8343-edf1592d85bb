import {
    Button,
    ConfigProvider,
    Table,
    Typography,
} from 'antd';
import React from 'react';


const { Title, Text } = Typography;


interface CommonTableProps {
    heading?: string;
    backButton?: boolean;
    headingContent?: string;
    bulkAction?: boolean;
    addNewLink?: string;
    addNewTitle?: string;
    addNew?: () => void;
    columns: any[];
    dataSource: any[];
    toggleDiv?: (record: any, rowIndex: any) => void;
    loading?: boolean;
    className?: string;
    addNewModal?: boolean;
    openModal?: any;

}


const PackageListingTable: React.FC<CommonTableProps> = (props) => {
    console.log(props.columns, "llll")

    const renderColorBox = (color: string) => {
        const isValidHex = /^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(color);
        return isValidHex ? (
            <div
                style={{
                    width: 20,
                    height: 20,
                    backgroundColor: color,
                    borderRadius: '50%',
                    border: '1px solid #ccc',
                }}
            />
        ) : (
            color
        );
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Table: {
                        borderColor: '#0000001A',
                        cellFontSize: 13,
                        headerBg: '#fff',
                        headerColor: '#1A3353',
                        colorText: '#455560',
                    },
                },
            }}
        >
            <div>
                {/* Header Section */}
                <div className="shadow-b-md flex items-center justify-between  pb-4 lg:mb-8 @sm:flex-col @sm:gap-4 @sm:pb-7">
                    {props.heading && (
                        <div className="items-center gap-4 @sm:w-full">
                            <div className="flex items-center gap-4">
                                {props.backButton && (
                                    <img
                                        src="/icons/back.svg"
                                        alt="edit"
                                        className="h-[10px] cursor-pointer"
                                        onClick={() => window.history.back()}
                                    />
                                )}

                                <Title className="text-[#1A3353] " level={4}>
                                    {props.heading}
                                </Title>
                            </div>
                            <div className="">
                                <Text className="text-[#455560]">
                                    {props.headingContent}
                                </Text>
                            </div>
                        </div>
                    )}
                    {/* Filters and Actions */}
                    <div className="flex items-center justify-end  lg:w-[85%] lg:gap-10 @sm:grid @sm:grid-cols-1 @sm:gap-4">

                        {props.addNewModal && (
                            <Button
                                className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                onClick={props.openModal}
                            >
                                <p>{props.addNewTitle}</p>
                                <span className="-translate-y-1 text-3xl">
                                    +
                                </span>
                            </Button>
                        )}
                    </div>
                </div>
                {/* Table Section */}
                <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                    <ConfigProvider
                        theme={{
                            components: {
                                Table: {
                                    selectionColumnWidth: 50,
                                },
                            },
                        }}
                    >
                        <Table
                            className="m-2 overflow-x-auto rounded-[6px] border-1"
                            pagination={false}
                            columns={props.columns.map((col) => ({
                                ...col,
                                render: col.dataIndex === "color"
                                    ? (text: any) => renderColorBox(text)
                                    : col.render
                                    ? col.render  
                                    : (text: any) => text,  
                            }))}
                            dataSource={props.dataSource?.map(
                                (row: any, index: number) => {
                                    // Process the row to handle arrays
                                    const processedRow = Object.entries(
                                        row
                                    ).reduce((acc: any, [key, value]) => {
                                        acc[key] = Array.isArray(value)
                                            ? value.join(', ')
                                            : value; // Convert arrays to comma-separated strings
                                        return acc;
                                    }, {});

                                    return {
                                        ...processedRow,
                                        key: index,
                                    };
                                }
                            )}
                            loading={props.loading}
                        />
                    </ConfigProvider>
                </div>
            </div>
        </ConfigProvider>
    );
};

export default PackageListingTable;
