import React, { useEffect, useState } from 'react';
import {
    Button,
    ConfigProvider,
    DatePicker,
    Form,
    Select,
    FormProps,
} from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { SchedulingReport } from '~/redux/actions/report.action';
import { capitalizeFirstLetter } from '~/components/common/function';
import { useWatch } from 'antd/es/form/Form';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { GetStaffList } from '~/redux/actions/staff-action';
import dayjs from 'dayjs';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { activeServiceCategoryList } from '~/redux/actions/serviceCategoryAction';
import { TrainerListing } from '~/redux/actions/appointment-action';

const SchedulingReports = () => {
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const [options, setOptions] = useState<any[]>([]);
    const [staffOptions, setStaffOptions] = useState<any[]>([]);
    const [serviceOption, setServcieOption] = useState<any[]>([]);
    const [selectedServices, setSelectedServices] = useState<any[]>([]);
    const SELECT_ALL_VALUE = 'All';

    useEffect(() => {
        dispatch(FacilitiesList({}));
        form.resetFields();
    }, []);
    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        trainerList: state.appointment_store.trainerList,
    }));
    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));
    const selectedClassTypes = useWatch('classType', form);

    const shouldShowStaff =
        selectedClassTypes?.includes('personalAppointment') ||
        selectedClassTypes?.includes('courses');

    const fetchStaffOption = async (search: string, page: number) => {
        const facilityId = form.getFieldValue('facilityId');
        if (!facilityId) return [];
        const res = await dispatch(
            TrainerListing({
                page,
                pageSize: 10,
                locationId: [facilityId],
            })
        ).unwrap();
console.log(res,"reeee")
        return [
            ...(res?.data?.data?.length
                ? [{ label: 'All', value: 'all' }]
                : []),
            ...(res?.data?.data?.map((staff: any) => ({
                label: capitalizeFirstLetter(
                    `${staff.firstName} ${staff.lastName}`
                ),
                value: staff.userId,
            })) || []),
        ];
    };

    const handleSelectChange = (selectedValues: string[]) => {
        console.log(selectedValues);
        let selectedData = [];
        if (selectedValues.includes('all')) {
            selectedData = options
                ?.filter((option) => option.value !== 'all')
                .map((option) => option.value);
            // form.setFieldsValue({
            //     staffIds: selectedData?.map((option) => option.value),
            // });
            setStaffOptions(selectedData);
        } else setStaffOptions(selectedValues);
    };

    const onFinish: FormProps['onFinish'] = async (values) => {
        const payload: any = {
            facilityIds: [values?.facilityId],
            startDate: values?.startDate?.format('YYYY-MM-DD HH:mm:ss'),
            endDate: values?.endDate?.format('YYYY-MM-DD HH:mm:ss'),
            responseType: 'stream',
            staffIds:
                shouldShowStaff && staffOptions?.length ? staffOptions : null,
            classType: values?.classType,
            scheduleStatus: values?.scheduleStatus,
            serviceCategoryIds: values?.serviceCategoryIds
        };
        dispatch(SchedulingReport(payload))
            .unwrap()
            .then((res: any) => {
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    const blob = new Blob([res.data], {
                        type: 'text/csv;charset=utf-8;',
                    });
                    const url = window.URL.createObjectURL(blob);

                    const link = document.createElement('a');
                    link.href = url;
                    const date = new Date();
                    const istOffset = 330 * 60 * 1000; // IST is UTC+5:30
                    const istDate = new Date(date.getTime() + istOffset);
                    const istTimestamp = istDate
                        .toISOString()
                        .replace(/T/, '_')
                        .replace(/:/g, '-')
                        .split('.')[0];

                    const filename = `scheduling_Report_${istTimestamp}`;
                    link.setAttribute('download', `${filename}.csv`);

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            })
            .catch((error: any) => {
                console.error('Export failed:', error);
            })
            .finally(() => {
                form.resetFields();
                setStaffOptions([]);
            });
    };

    const classTypes = [
        {
            label: 'Personal Appointment',
            value: 'personalAppointment',
        },
        {
            label: 'Classes',
            value: 'classes',
        },
        {
            label: 'Bookings',
            value: 'bookings',
        },
        {
            label: 'Courses',
            value: 'courses',
        },
    ];
    const handleServiceTypeChange = async (selectedClassTypes: any[]) => {
    if (selectedClassTypes.length === 0) {
        // ✅ If nothing is selected in Service Type, clear services
        setServcieOption([]);
        form.setFieldsValue({ serviceCategoryIds: [] });
        return;
    }

    const response = await dispatch(
        activeServiceCategoryList({
            reqData: selectedClassTypes,
        })
    ).unwrap();

    const newServiceOptions = response?.data?.data?.list?.map((item: any) => ({
        value: item._id,
        label: item.name,
        classTypes: item.classType,
    })) || [];

    setServcieOption(newServiceOptions);

    const currentServices = form.getFieldValue('serviceCategoryIds') || [];

    const validServiceIds = newServiceOptions.map((opt: any) => opt.value);
    const updatedServiceIds = currentServices.filter((serviceId: string) =>
        validServiceIds.includes(serviceId)
    );

    form.setFieldsValue({ serviceCategoryIds: updatedServiceIds });
};

    const handleServiceCategoryChange = (values: any[]) => {
        if (values.includes(SELECT_ALL_VALUE)) {
            // User clicked "Select All"
            const allValues: any = serviceOption.map(opt => opt.value);
            setSelectedServices(allValues);
            form.setFieldsValue({ serviceCategoryIds: allValues });
        } else {
            // Normal selection (remove select all if it's there)
            setSelectedServices(values);
            form.setFieldsValue({ serviceCategoryIds: values });
        }
    };

    return (
        <>
            <p className=" text-2xl font-semibold text-[#1a3353]">
                Scheduling at a Glance
            </p>
            <div className="py-10">
                <ConfigProvider
                    theme={{
                        token: { controlHeight: 40 },
                    }}
                >
                    <Form
                        layout="vertical"
                        className="w-full"
                        form={form}
                        onFinish={onFinish}
                        initialValues={{
                            startDate: dayjs().startOf('day'),
                            facilityId: FacilityOptions?.[0]?.value || null,
                        }}
                    >
                        <div className="flex w-[100%] flex-row items-center justify-between">
                            <div className="w-[48%]">
                                <Form.Item
                                    label="Start Date"
                                    name="startDate"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select a start date',
                                        },
                                    ]}
                                    className="w-full"
                                >
                                    <DatePicker
                                        className="w-[100%]"
                                        format="DD-MMM-YYYY"
                                    />
                                </Form.Item>
                            </div>
                            <div className="w-[48%]">
                                <Form.Item
                                    label="End Date"
                                    name="endDate"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select an end date',
                                        },
                                    ]}
                                    className="w-full"
                                >
                                    <DatePicker
                                        className="w-[100%]"
                                        format="DD-MMM-YYYY"
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className="flex w-[100%] flex-row items-center justify-between ">
                            <div className="w-[48%]">
                                <Form.Item
                                    label="Facility"
                                    name="facilityId"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select a location.',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="w-[100%]"
                                        placeholder="Select facility"
                                        filterOption={(input, option) =>
                                            String(
                                                (option &&
                                                    (
                                                        option as {
                                                            label: string;
                                                        }
                                                    ).label) ??
                                                ''
                                            )
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={FacilityOptions}
                                    />
                                </Form.Item>
                            </div>

                            <div className="w-[48%]">
                                <Form.Item
                                    label="Service Type"
                                    name="classType"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select a Service Type.',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="w-[100%]"
                                        mode="multiple"
                                        maxTagCount="responsive"
                                        showSearch
                                        placeholder="Service Type"
                                        filterOption={(input, option) =>
                                            String(
                                                (option &&
                                                    (
                                                        option as {
                                                            label: string;
                                                        }
                                                    )?.label) ??
                                                ''
                                            )
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={classTypes}
                                        onChange={(values) => handleServiceTypeChange(values)}
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className="flex w-full flex-wrap gap-y-4 justify-between">

                            <div className="w-[48%]">
                                <Form.Item
                                    label="Service Category"
                                    name="serviceCategoryIds"
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please select a Service',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="w-full"
                                        mode="multiple"
                                        showSearch
                                        maxTagCount="responsive"
                                        placeholder="Service Type"
                                        value={selectedServices}
                                        onChange={handleServiceCategoryChange}
                                        options={[
                                            ...(serviceOption.length > 0
                                                ? [{ label: 'All', value: SELECT_ALL_VALUE }]
                                                : []),
                                            ...serviceOption,
                                        ]}
                                        filterOption={(input, option) =>
                                            (option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        disabled={serviceOption.length === 0}
                                    />
                                </Form.Item>

                            </div>
                            {shouldShowStaff && (
                                <div className="w-[48%]">
                                    <p className="pb-2 ps-2 text-[13px] font-medium text-[#1a3353]">
                                        Select Staffs
                                        <span className="text-2xl text-red-400">
                                            *
                                        </span>
                                    </p>
                                    <InfiniteScrollSelect
                                        // className="w-[100%] "
                                        className="w-[100%] border-b border-[#d1d5db]"
                                        fetchOptions={fetchStaffOption}
                                        onChange={(value: any, option: any) =>
                                            handleSelectChange(value)
                                        }
                                        placeholder="Select staffs"
                                        mode="multiple"
                                        extractOptions={setOptions}
                                    />
                                </div>
                            )}
                            <div className="w-[48%]">
                                <Form.Item
                                    label="Schedule Status"
                                    name="scheduleStatus"
                                >
                                    <Select
                                        className="w-[100%]"
                                        mode="multiple"
                                        showSearch
                                        placeholder="Schedule Status"
                                        filterOption={(input, option) =>
                                            String(
                                                (option &&
                                                    (
                                                        option as {
                                                            label: string;
                                                        }
                                                    )?.label) ??
                                                ''
                                            )
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={[
                                            {
                                                label: 'Booked',
                                                value: 'booked',
                                            },
                                            {
                                                label: 'Checked In',
                                                value: 'checked-in',
                                            },
                                            {
                                                label: 'Canceled',
                                                value: 'canceled',
                                            },
                                        ]}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex flex-row justify-end">
                            <Button
                                className="mt-10 bg-purpleLight px-14 py-7 text-xl text-white  "
                                htmlType="submit"
                            >
                                Export as CSV
                            </Button>
                        </div>
                    </Form>
                </ConfigProvider>
            </div>
        </>
    );
};

export default SchedulingReports;
