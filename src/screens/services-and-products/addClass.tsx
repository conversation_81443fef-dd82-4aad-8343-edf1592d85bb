import { DownOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
    Button,
    Col,
    ConfigProvider,
    Dropdown,
    Form,
    FormProps,
    Input,
    Menu,
    Row,
    Select,
    Typography,
    Upload,
} from 'antd';
import React, { useEffect, useState } from 'react';
import ReactQuill from 'react-quill';
import { useLocation, useParams } from 'wouter';
import { goBack } from '~/components/common/function';
import FullLoader from '~/components/library/loader/full-loader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { UploadImage } from '~/redux/actions/common-action';
import {
    CreateNewServiceCategory,
    ServiceCategoryDetail,
    UpdateServiceCategory,
} from '~/redux/actions/serviceCategoryAction';
import { ClassType } from '~/types/enums';
const { Title } = Typography;

const AddClass = () => {
    const [form] = Form.useForm();
    const [imageUrl, setImageUrl] = useState<string>();
    const [loading, setLoading] = useState(false);
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const { id } = useParams<{ id: string }>(); // Explicitly typing id

    const store = useAppSelector((state) => ({
        ServiceCategoryDetailData:
            state.service_category_store.ServiceCategoryDetailData,
    }));

    useEffect(() => {
        if (id !== '0') {
            startLoader();
            dispatch(ServiceCategoryDetail({ serviceId: id }))
                .unwrap()
                .finally(endLoader);
        }
    }, [id]);

    useEffect(() => {
        if (id && id !== '0' && store.ServiceCategoryDetailData) {
            if (store.ServiceCategoryDetailData) {
                form.setFieldsValue({
                    className: store.ServiceCategoryDetailData.name,
                    description: store.ServiceCategoryDetailData.description,
                    classType: store.ServiceCategoryDetailData.classType,
                });
                setImageUrl(store.ServiceCategoryDetailData.image);
            }
        }
    }, [id, store.ServiceCategoryDetailData, form]);

    const handleImageUpload = (file: any) => {
        console.log('Uploaded file:', file);
        dispatch(UploadImage({ file: file.file })).then((res: any) => {
            console.log('Res------------------', res);
            setImageUrl(res?.payload?.res?.data?.data);
        });
    };

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </button>
    );
    const [_, setLocation] = useLocation();

    const onFinish: FormProps['onFinish'] = async (values) => {
        console.log('Received values of form:', values);
        if (id && id !== '0') {
            const payload = {
                serviceId: id,
                name: values.className,
                description: values.description,
                image: imageUrl,
                classType: ClassType.CLASSES,
            };
            try {
                await dispatch(UpdateServiceCategory(payload));
                setLocation('/classes');
            } catch (error) {
                console.error('Error updating service:', error);
            }
        } else {
            const payload = {
                name: values.className,
                description: values.description,
                image: imageUrl,
                classType: ClassType.CLASSES,
            };
            // dispatch(CreateNewServiceCategory(payload));
            try {
                await dispatch(CreateNewServiceCategory(payload));
                setLocation('/classes');
            } catch (error) {
                console.error('Error updating service:', error);
            }
        }
    };

    const moreMenu = (
        <Menu>
            <Menu.Item key="1">Create or Schedule a Class</Menu.Item>
            <Menu.Item key="2">Schedule a Closed Business Day</Menu.Item>
            <Menu.Item key="3">Set Up Auto Emails</Menu.Item>
            <Menu.Item key="4">Class and course Options</Menu.Item>
            <Menu.Item key="5">View Hidden Class Cancellations</Menu.Item>
        </Menu>
    );

    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Input: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                        borderRadius: 4,
                    },
                    Select: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                        borderRadius: 4,
                    },
                },
                token: {
                    borderRadius: 4,
                },
            }}
        >
            {loader ? (
                <FullLoader state={true} />
            ) : (
                <>
                    <div className="flex justify-between">
                        <div className="flex items-center gap-4 lg:mb-16 @sm:mb-10 ">
                            <img
                                src="/icons/back.svg"
                                alt="edit"
                                className="h-[10px] cursor-pointer"
                                onClick={goBack}
                            />
                            <Title className="text-[#1A3353]" level={4}>
                                {id === '0' ? 'Add Class' : 'Edit Class'}
                            </Title>
                        </div>
                        <Dropdown
                            trigger={['click']}
                            overlay={moreMenu}
                            placement="bottomRight"
                        >
                            <Button>
                                More
                                <span>
                                    <DownOutlined />
                                </span>
                            </Button>
                        </Dropdown>
                    </div>
                    <Row className="justify-around @sm:gap-10 ">
                        <Col
                            className="rounded-lg border p-5  lg:p-10  @sm:w-full"
                            lg={15}
                        >
                            <Form
                                name="gymCreate"
                                layout="vertical"
                                size="large"
                                form={form}
                                initialValues={{
                                    amenities: [],
                                }}
                                onFinish={onFinish}
                                autoComplete="off"
                            >
                                <Form.Item
                                    label="Class Name"
                                    name="className"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter a class name',
                                        },
                                    ]}
                                >
                                    <Input placeholder="Enter class name" />
                                </Form.Item>
                                <Form.Item
                                    label="Class Type"
                                    name="classType"
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please select Class Type',
                                        },
                                    ]}
                                >
                                    <Select
                                        placeholder="Select Class Type"
                                        defaultValue={'classes'}
                                        options={[
                                            {
                                                label: 'Personal Appointment',
                                                value: 'personalAppointment',
                                            },
                                            {
                                                label: 'Classes',
                                                value: 'classes',
                                            },
                                            {
                                                label: 'Bookings',
                                                value: 'bookings',
                                            },
                                        ]}
                                        onChange={() => console.log('Value')}
                                        disabled={true}
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Description"
                                    name="description"
                                    rules={[
                                        {
                                            required: false,
                                            message:
                                                'Please enter description !',
                                        },
                                    ]}
                                >
                                    <ReactQuill
                                        theme="snow"
                                        // className="h-[20vh] rounded-lg"
                                    />
                                </Form.Item>

                                <Form.Item className="mt-8 flex justify-end gap-8 @sm:hidden ">
                                    <Button
                                        // loading={loader}
                                        onClick={() => goBack()}
                                        htmlType="button"
                                        className="w-[110px] border-[#1A3353] bg-[#fff]  text-xl text-[#1A3353] "
                                    >
                                        <p> Cancel </p>
                                    </Button>
                                    <Button
                                        // loading={loader}
                                        htmlType="submit"
                                        className="w-[110px]  bg-purpleLight text-xl  text-white lg:ms-7"
                                    >
                                        <p> Save </p>
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Col>
                        <Col
                            className="rounded-lg border lg:p-10 @sm:w-full @sm:p-5 "
                            lg={8}
                        >
                            <div className="">
                                <Typography.Title level={5}>
                                    <span className="text-primary ">
                                        UPLOAD IMAGE
                                    </span>
                                </Typography.Title>
                                <Upload
                                    id="banner-upload"
                                    name="avatar"
                                    listType="picture-card"
                                    className="avatar-uploader overflow-hidden lg:mt-8 @sm:mt-2"
                                    showUploadList={false}
                                    // action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                                    // beforeUpload={beforeUpload}
                                    // onChange={handleChange}
                                    customRequest={handleImageUpload}
                                >
                                    {imageUrl ? (
                                        <img
                                            src={imageUrl}
                                            className="lg:object-contain @sm:rounded-3xl @sm:object-cover"
                                            alt="avatar"
                                            style={{
                                                width: '100%',
                                                height: '100%',
                                            }}
                                        />
                                    ) : (
                                        uploadButton
                                    )}
                                </Upload>
                            </div>
                            <Form.Item className="mt-8 flex justify-center  lg:hidden ">
                                <Button
                                    onClick={() => goBack()}
                                    htmlType="button"
                                    className="me-6 w-[110px] border-[#1A3353]  bg-[#fff]  text-[#1A3353] "
                                >
                                    Cancel
                                </Button>
                                <Button
                                    // loading={loader}
                                    htmlType="submit"
                                    className=" w-[110px] border-[#8143D1] bg-[#8143D1]  text-white"
                                >
                                    Save
                                </Button>
                            </Form.Item>
                        </Col>
                    </Row>
                </>
            )}
        </ConfigProvider>
    );
};

export default AddClass;
