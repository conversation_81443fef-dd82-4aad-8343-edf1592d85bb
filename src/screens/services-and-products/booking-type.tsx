import {
    ClockCircleOutlined,
    DeleteFilled,
    EyeOutlined,
    InfoCircleFilled,
    LoadingOutlined,
    SettingOutlined,
    StopOutlined,
    TagOutlined,
} from '@ant-design/icons';
import {
    Button,
    Col,
    Collapse,
    ConfigProvider,
    Dropdown,
    Input,
    Menu,
    Modal,
    Pagination,
    Row,
    Space,
    Switch,
    Tooltip,
} from 'antd';
import Title from 'antd/es/typography/Title';
import React, {
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import { useSelector } from 'react-redux';
import { Link, useLocation } from 'wouter';
import { capitalizeFirstLetter, goBack } from '~/components/common/function';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    AllServiceCategoryList,
    DeleteAppointmentType,
    UpdateAppointmentTypeStatus,
    UpdateServiceCategory,
} from '~/redux/actions/serviceCategoryAction';
import {
    ClassType,
    PERMISSIONS_ENUM,
    RoleType,
    SUBJECT_TYPE,
} from '~/types/enums';
import AssignStaffModal from './assignStaffModal';
import { useLoader } from '~/hooks/useLoader';
import {
    PriceListByServiceCategory,
    RemovePricingToSubType,
} from '~/redux/actions/pricing-actions';
import { getQueryParams } from '~/utils/getQueryParams';
import { navigate } from 'wouter/use-location';
import { getAllPayRates } from '~/redux/actions/pay-rate-actions';
import AssignPricingModal from '~/components/common/assign-pricing-modal';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import clsx from 'clsx';
const { Search } = Input;

const BookingTypes = () => {
    const [_, setLocation] = useLocation();
    const dispatch = useAppDispatch();
    const params = getQueryParams();

    const pageParam = Number(params.page);
    const searchParam = params.search;
    const pageSizeParam = Number(params.pageSize);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    const [search, setSearch] = useState('');
    const staffListRef = useRef<HTMLDivElement>(null);
    const [isAssignStaff, setIsAssignStaff] = useState(false);
    const [idForStaff, setIdForStaff] = useState(null);
    const [serviceIdForStaff, setServiceIdForStaff] = useState(null);
    const [firstPriceId, setFirstPriceId] = useState<string>('');
    const [hasMore, setHasMore] = useState(true);
    const [isAssignModal, setIsAssignVisible] = useState(false);

    const [loaderType, startLoaderType, endLoaderType] = useLoader();
    const [loader, startLoader, endLoader] = useLoader();
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [appointmentTypeData, setSelectedAppointmentTypeData] =
        useState<any>(null);
    const [
        serviceconfirmationModalVisible,
        setServiceConfirmationModalVisible,
    ] = useState(false);
    const [serviceId, setServiceId] = useState<any>(null);
    const [openedPanel, setOpenedPanel] = useState<{
        serviceCategoryId?: string;
        appointmentTypeId?: string;
    }>({});

    const store = useAppSelector((state) => ({
        pricingListByServiceCategory:
            state.pricing_store.pricingListByServiceCategory,
        AllServiceCategoryListData:
            state.service_category_store.AllServiceCategoryListData,
        AllServiceCategoryListCount:
            state.service_category_store.AllServiceCategoryListCount,
        staffPayrate: state.pay_rate_store.payRateList,
        staffPayrateCount: state.pay_rate_store.payRateListCount,
        role: state.auth_store.role,
    }));

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    const filteredData = store.pricingListByServiceCategory?.filter(
        (pricingItem: any) =>
            pricingItem.name.toLowerCase().includes(search.toLowerCase())
    );

    const handleOpenModal = () => {
        setIsAssignVisible(true);
    };

    const handleCloseModal = () => {
        setIsAssignVisible(false);
    };

    useEffect(() => {
        startLoaderType();
        dispatch(
            AllServiceCategoryList({
                page: currentPage,
                pageSize: pageSizes,
                classType: ClassType.BOOKING,
            })
        )
            .unwrap()
            .then(() => {})
            .catch(() => {})
            .finally(endLoaderType);
    }, [currentPage, pageSizes]);

    const loadMoreStaff = () => {
        if (openedPanel.serviceCategoryId && openedPanel.appointmentTypeId) {
            const payload = {
                page: currentPage + 1,
                pageSize: 10,
                serviceType: ClassType.BOOKING,
                serviceCategory: openedPanel.serviceCategoryId,
                appointmentType: openedPanel.appointmentTypeId,
            };

            startLoader();
            dispatch(getAllPayRates(payload))
                .unwrap()
                .then((response) => {
                    if (!response || response.length === 0) {
                        setHasMore(false);
                    } else {
                        setCurrentPage((prev) => prev + 1);
                    }
                })
                .catch((error: any) => {
                    console.error('Error fetching more staff:', error);
                })
                .finally(endLoader);
        }
    };

    const handleScroll = useCallback(() => {
        if (!staffListRef.current) return;

        const { scrollTop, clientHeight, scrollHeight } = staffListRef.current;

        // Check if user has scrolled to the bottom
        if (
            scrollHeight - scrollTop <= clientHeight + 1 &&
            hasMore &&
            !loader
        ) {
            loadMoreStaff();
        }
    }, [hasMore, loader]);

    useEffect(() => {
        const staffList = staffListRef.current;
        if (staffList) {
            staffList.addEventListener('scroll', handleScroll);
            return () => {
                staffList.removeEventListener('scroll', handleScroll);
            };
        }
    }, [handleScroll]);

    useEffect(() => {
        if (openedPanel.serviceCategoryId && openedPanel.appointmentTypeId) {
            startLoader();
            dispatch(
                PriceListByServiceCategory({
                    serviceId: openedPanel.serviceCategoryId,
                    appointmentId: openedPanel.appointmentTypeId,
                })
            )
                .unwrap()
                .then((res: any) => {
                    console.log('Res-------------', res);
                    setFirstPriceId(res?.data?.data?.[0]?._id);
                })
                .catch((error: any) => {
                    console.error('Error fetching price list:', error);
                })
                .finally(endLoader);
        }
    }, [openedPanel, dispatch]);

    useEffect(() => {
        if (openedPanel.serviceCategoryId && openedPanel.appointmentTypeId) {
            const payload = {
                page: 1,
                pageSize: 10,
                serviceType: ClassType.BOOKING,
                serviceCategory: openedPanel.serviceCategoryId,
                appointmentType: openedPanel.appointmentTypeId,
            };
            startLoader();
            dispatch(getAllPayRates(payload))
                .unwrap()
                .then(() => {
                    console.log('Fetched staff list successfully.');
                })
                .catch((error: any) => {
                    console.error('Error fetching price list:', error);
                })
                .finally(endLoader);
        }
    }, [openedPanel, dispatch]);

    const handleServiceCategoryChange = (serviceCategoryId: string) => {
        setOpenedPanel((prev) => ({
            serviceCategoryId:
                prev.serviceCategoryId === serviceCategoryId
                    ? undefined
                    : serviceCategoryId,
            appointmentTypeId: undefined, // Close all appointment types when switching categories
        }));
    };

    // Handle appointment type collapse changes
    const handleAppointmentTypeChange = (
        serviceCategoryId: string,
        appointmentTypeId: string
    ) => {
        setSearch('');
        setOpenedPanel((prev) => ({
            serviceCategoryId,
            appointmentTypeId:
                prev.appointmentTypeId === appointmentTypeId
                    ? undefined
                    : appointmentTypeId,
        }));
    };

    const handleCancelStatusChange = () => {
        setSelectedAppointmentTypeData(null);
        setConfirmationModalVisible(false);
        setIsDeleting(false);
    };

    const handleConfirmStatusChange = () => {
        dispatch(
            UpdateAppointmentTypeStatus({
                reqData: {
                    isActive: !appointmentTypeData.isActive,
                    serviceId: appointmentTypeData.serviceId,
                    appointmentTypeId: appointmentTypeData._id,
                },
            })
        );
        setConfirmationModalVisible(false);
        setSelectedAppointmentTypeData(null);
    };
    const handleServiceConfirmStatusChange = () => {
        const reqData = {
            isActive: !serviceId.isActive,
            serviceId: serviceId.serviceId,
            classType: 'bookings',
            name: serviceId.name,
        };
        dispatch(UpdateServiceCategory(reqData))
            .unwrap()
            .then(() => {
                dispatch(
                    AllServiceCategoryList({
                        page: currentPage,
                        pageSize: pageSizes,
                        classType: ClassType.BOOKING,
                    })
                );
            })
            .catch(() => {})
            .finally(setServiceConfirmationModalVisible(false));
    };
    const handleServiceCancelStatusChange = () => {
        setServiceId(null);
        setServiceConfirmationModalVisible(false);
    };
    const handleDelete = () => {
        dispatch(
            DeleteAppointmentType({
                serviceId: appointmentTypeData.serviceId,
                appointmentTypeId: appointmentTypeData._id,
            })
        );
        setConfirmationModalVisible(false);
        setIsDeleting(false);
        setSelectedAppointmentTypeData(null);
    };

    const openConfirmationModal = (record: any, isDelete: boolean = false) => {
        setSelectedAppointmentTypeData(record);
        setConfirmationModalVisible(true);
        setIsDeleting(isDelete);
    };
    const openServiceDecativeConfirmModal = (record: any) => {
        setServiceId(record);
        setServiceConfirmationModalVisible(true);
    };
    const handleRemovePricing = (pricingId: string) => {
        const payload = {
            subTypeId: openedPanel.appointmentTypeId,
            serviceCategoryId: openedPanel.serviceCategoryId,
            pricingId: pricingId,
        };

        dispatch(RemovePricingToSubType(payload))
            .unwrap()
            .then((res: any) => {
                console.log('Res------------', res);
                if (res?.status === 200 || res?.status === 201) {
                    dispatch(
                        PriceListByServiceCategory({
                            serviceId: openedPanel.serviceCategoryId,
                            appointmentId: openedPanel.appointmentTypeId,
                        })
                    );
                }
            });
    };

    const addEventMenu = (
        <Menu className=" pb-5">
            {/* <div className="flex  justify-end ">
                <CloseOutlined
                    className="cursor-pointer"
                />
            </div> */}

            <Menu.Item
                className="rounded-none border-b border-[#455560] bg-white text-[#455560] "
                key="1"
            >
                <span className="pe-5">
                    <ClockCircleOutlined />
                </span>
                Allow online scheduling
            </Menu.Item>

            <Menu.Item
                // onClick={() => openScheduleUpdateModal()}
                key="2"
                className="rounded-none border-b  border-[#455560] bg-white text-[#455560]"
            >
                <span className="pe-5">
                    <TagOutlined />
                </span>
                Create pricing relationships
            </Menu.Item>
            <Menu.Item
                key="4"
                className="rounded-none border-b  border-[#455560] bg-white text-[#455560]"
            >
                <span className="pe-5">
                    <EyeOutlined />
                </span>
                View inactive pricing options
            </Menu.Item>
            <Menu.Item
                key="5"
                className="rounded-none border-b  border-[#455560] bg-white text-[#455560]"
            >
                <span className="pe-5">
                    <StopOutlined />
                </span>
                Deactivation of service category
            </Menu.Item>
        </Menu>
    );

    // for permissions
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasServiceUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SERVICES_SERVICE_SETUP &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.ORGANIZATION_SERVICE_UPDATE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasServiceWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SERVICES_SERVICE_SETUP &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.ORGANIZATION_SERVICE_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    return (
        <>
            <div className="flex w-[100%] justify-between">
                <div className="flex items-center gap-4 @sm:w-full">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353] " level={4}>
                        Service Category and Services
                    </Title>
                </div>
            </div>

            <div className="flex flex-col py-12 sm:w-4/5 lg:w-[100%] lg:ps-5">
                <div className=" my-9  lg:w-[30%]  "></div>
                {(hasServiceWritePermission ||
                    store.role === RoleType.ORGANIZATION) && (
                    <div
                        className=" mb-14 mt-3 flex cursor-pointer gap-4 rounded-xl border p-6"
                        onClick={() =>
                            setLocation(
                                'add-appointment-service/0?serviceType=bookings'
                            )
                        }
                    >
                        <p className="text-[#455560]">
                            + Add new service category
                        </p>
                        <Tooltip title="prompt text">
                            <InfoCircleFilled />
                        </Tooltip>
                    </div>
                )}

                <Space direction="vertical" className="w-full">
                    <div>
                        {loaderType ? (
                            <div className="flex h-full items-center justify-center">
                                <LoadingOutlined className="text-[#8143d1]" />{' '}
                            </div>
                        ) : (
                            store.AllServiceCategoryListData?.map(
                                (item: any) => (
                                    <Collapse
                                        accordion
                                        className="mb-8"
                                        key={item._id}
                                        activeKey={
                                            openedPanel.serviceCategoryId ===
                                            item._id
                                                ? item._id
                                                : undefined
                                        }
                                        onChange={() =>
                                            handleServiceCategoryChange(
                                                item._id
                                            )
                                        }
                                        items={[
                                            {
                                                key: item._id,
                                                label: (
                                                    <div className="flex flex-wrap justify-between">
                                                        <div className="flex flex-wrap justify-between">
                                                            <div className="flex flex-row items-center sm:gap-10 @sm:gap-2">
                                                                <p className="text-16 text-[#1A3353]">
                                                                    {item.name}
                                                                </p>
                                                                {(hasServiceUpdatePermission ||
                                                                    store.role ===
                                                                        RoleType.ORGANIZATION) && (
                                                                    <span
                                                                        onClick={() =>
                                                                            setLocation(
                                                                                `add-appointment-service/${item._id}`
                                                                            )
                                                                        }
                                                                        className="text-primary"
                                                                    >
                                                                        Edit
                                                                    </span>
                                                                )}
                                                                {(item.isFeatured ||
                                                                    item.appointmentType?.some(
                                                                        (
                                                                            sub: any
                                                                        ) =>
                                                                            sub.isFeatured
                                                                    )) && (
                                                                    <div
                                                                        className={`rounded-md px-2 py-1.5 text-lg text-white ${
                                                                            item.isFeatured
                                                                                ? 'bg-purpleLight'
                                                                                : 'bg-purpleLight/70'
                                                                        }`}
                                                                    >
                                                                        <p>
                                                                            Featured
                                                                        </p>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <div className="flex justify-between gap-14">
                                                            <div
                                                                onClick={(e) =>
                                                                    e.stopPropagation()
                                                                }
                                                                className="flex items-center gap-3"
                                                            >
                                                                <ConfigProvider
                                                                    theme={{
                                                                        components:
                                                                            {
                                                                                Switch: {
                                                                                    colorTextQuaternary:
                                                                                        'gray',
                                                                                    colorFillQuaternary:
                                                                                        'gray',
                                                                                },
                                                                            },
                                                                    }}
                                                                >
                                                                    <Switch
                                                                        className={clsx(
                                                                            'rounded-full transition-colors'
                                                                        )}
                                                                        disabled={
                                                                            !hasServiceUpdatePermission &&
                                                                            store.role !=
                                                                                RoleType.ORGANIZATION
                                                                        }
                                                                        id="swtich-off"
                                                                        checkedChildren="ON"
                                                                        unCheckedChildren="OFF"
                                                                        checked={
                                                                            item?.isActive
                                                                        }
                                                                        onChange={() =>
                                                                            openServiceDecativeConfirmModal(
                                                                                {
                                                                                    serviceId:
                                                                                        item._id,
                                                                                    isActive:
                                                                                        item.isActive,
                                                                                    name: item.name,
                                                                                }
                                                                            )
                                                                        }
                                                                    />
                                                                </ConfigProvider>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ),
                                                children: (
                                                    <>
                                                        {item?.appointmentType?.map(
                                                            (subItem: any) => {
                                                                return (
                                                                    <div
                                                                        key={
                                                                            subItem._id
                                                                        }
                                                                    >
                                                                        <Collapse
                                                                            className="mb-3 bg-white"
                                                                            accordion
                                                                            key={
                                                                                subItem._id
                                                                            }
                                                                            activeKey={
                                                                                openedPanel.serviceCategoryId ===
                                                                                    item._id &&
                                                                                openedPanel.appointmentTypeId ===
                                                                                    subItem._id
                                                                                    ? subItem._id
                                                                                    : undefined
                                                                            }
                                                                            onChange={() =>
                                                                                handleAppointmentTypeChange(
                                                                                    item._id,
                                                                                    subItem._id
                                                                                )
                                                                            }
                                                                            items={[
                                                                                {
                                                                                    key: subItem._id,
                                                                                    label: (
                                                                                        <div className="flex flex-wrap justify-between">
                                                                                            <div className="flex flex-row items-center sm:gap-10 @sm:gap-2">
                                                                                                <p className="text-2xl text-[#1A3353]">
                                                                                                    {
                                                                                                        subItem.name
                                                                                                    }
                                                                                                </p>
                                                                                                {(hasServiceWritePermission ||
                                                                                                    store.role ===
                                                                                                        RoleType.ORGANIZATION) && (
                                                                                                    <>
                                                                                                        <span
                                                                                                            className="text-[#8143d1]"
                                                                                                            onClick={() =>
                                                                                                                setLocation(
                                                                                                                    `add-appointment-type/${item._id}/${subItem._id}`
                                                                                                                )
                                                                                                            }
                                                                                                        >
                                                                                                            Edit
                                                                                                        </span>
                                                                                                        <span
                                                                                                            className="text-[#8143d1]"
                                                                                                            onClick={() =>
                                                                                                                openConfirmationModal(
                                                                                                                    {
                                                                                                                        ...subItem,
                                                                                                                        serviceId:
                                                                                                                            item._id,
                                                                                                                    },
                                                                                                                    true
                                                                                                                )
                                                                                                            }
                                                                                                        >
                                                                                                            Delete
                                                                                                        </span>
                                                                                                    </>
                                                                                                )}
                                                                                                {subItem.isFeatured && (
                                                                                                    <div className="rounded-md bg-purpleLight px-2 py-1.5 text-lg text-white">
                                                                                                        <p>
                                                                                                            Featured
                                                                                                        </p>
                                                                                                    </div>
                                                                                                )}
                                                                                            </div>
                                                                                            {/* {...store.role !==
                                                                                            RoleType.FRONT_DESK_ADMIN
                                                                                                ? [ */}
                                                                                            <div
                                                                                                onClick={(
                                                                                                    e
                                                                                                ) => {
                                                                                                    e.stopPropagation();
                                                                                                }}
                                                                                            >
                                                                                                <ConfigProvider
                                                                                                    theme={{
                                                                                                        components:
                                                                                                            {
                                                                                                                Switch: {
                                                                                                                    // colorPrimaryBorder: '#8143d1',
                                                                                                                    // colorPrimary: '#8143d1',
                                                                                                                    colorTextQuaternary:
                                                                                                                        'gray',
                                                                                                                    colorFillQuaternary:
                                                                                                                        'gray',
                                                                                                                },
                                                                                                            },
                                                                                                    }}
                                                                                                >
                                                                                                    <Switch
                                                                                                        disabled={
                                                                                                            !hasServiceUpdatePermission &&
                                                                                                            store.role !=
                                                                                                                RoleType.ORGANIZATION
                                                                                                        }
                                                                                                        className={clsx(
                                                                                                            'rounded-full transition-colors'
                                                                                                        )}
                                                                                                        id="swtich-off"
                                                                                                        checkedChildren="ON"
                                                                                                        unCheckedChildren="OFF"
                                                                                                        checked={
                                                                                                            subItem?.isActive
                                                                                                        }
                                                                                                        onChange={() =>
                                                                                                            openConfirmationModal(
                                                                                                                {
                                                                                                                    ...subItem,
                                                                                                                    serviceId:
                                                                                                                        item._id,
                                                                                                                }
                                                                                                            )
                                                                                                        }
                                                                                                    />
                                                                                                </ConfigProvider>
                                                                                            </div>
                                                                                            {/* ]
                                                                                                : []} */}
                                                                                        </div>
                                                                                    ),
                                                                                    children:
                                                                                        (
                                                                                            <Row
                                                                                                gutter={{
                                                                                                    xs: 8,
                                                                                                    sm: 16,
                                                                                                    md: 24,
                                                                                                    lg: 32,
                                                                                                }}
                                                                                            >
                                                                                                <div
                                                                                                    className=" w-full px-4"
                                                                                                    // sm={
                                                                                                    //     12
                                                                                                    // }
                                                                                                    // xs={
                                                                                                    //     24
                                                                                                    // }
                                                                                                >
                                                                                                    <div className="mb-5 flex flex-row  items-center justify-between border-b-1 pb-5 @sm:px-4">
                                                                                                        <p className="text-3xl font-medium text-[#1a3353]">
                                                                                                            Pricing
                                                                                                            Options
                                                                                                        </p>
                                                                                                        <div className="flex justify-between gap-3">
                                                                                                            <Input
                                                                                                                className="w-40 sm:w-56"
                                                                                                                value={
                                                                                                                    search
                                                                                                                }
                                                                                                                placeholder="Search"
                                                                                                                onChange={(
                                                                                                                    e
                                                                                                                ) =>
                                                                                                                    setSearch(
                                                                                                                        e
                                                                                                                            .target
                                                                                                                            .value
                                                                                                                    )
                                                                                                                }
                                                                                                            />
                                                                                                            {...hasServiceUpdatePermission ||
                                                                                                            store.role ===
                                                                                                                RoleType.ORGANIZATION
                                                                                                                ? [
                                                                                                                      <>
                                                                                                                          <Button
                                                                                                                              onClick={() => {
                                                                                                                                  handleOpenModal();
                                                                                                                                  setIdForStaff(
                                                                                                                                      subItem._id
                                                                                                                                  );
                                                                                                                                  setServiceIdForStaff(
                                                                                                                                      item._id
                                                                                                                                  );
                                                                                                                              }}
                                                                                                                              // to={`/duplicate-create-pricing/${firstPriceId}?type=personalAppointment&s_id=${item._id}&t_id=${subItem._id}`}
                                                                                                                              className="rounded-lg border bg-purpleLight px-6 text-xl text-[white] sm:px-4 sm:py-3"
                                                                                                                          >
                                                                                                                              Assign
                                                                                                                              Pricing
                                                                                                                              +
                                                                                                                          </Button>
                                                                                                                      </>,
                                                                                                                  ]
                                                                                                                : []}
                                                                                                        </div>
                                                                                                    </div>

                                                                                                    {loader ? (
                                                                                                        <div className="flex h-full items-center justify-center">
                                                                                                            <LoadingOutlined className="text-[#8143d1]" />{' '}
                                                                                                        </div>
                                                                                                    ) : (
                                                                                                        filteredData?.map(
                                                                                                            (
                                                                                                                items: any
                                                                                                            ) => (
                                                                                                                <div
                                                                                                                    key={
                                                                                                                        items._id
                                                                                                                    }
                                                                                                                    className={`group relative flex items-center justify-between border-b-1 py-3 sm:px-10 @sm:pe-8 @sm:ps-5 
                                                                                                                        ${
                                                                                                                            items.isActive
                                                                                                                                ? 'hover:bg-gray-100'
                                                                                                                                : 'cursor-not-allowed bg-gray-200 text-gray-500'
                                                                                                                        }`}
                                                                                                                >
                                                                                                                    <p>
                                                                                                                        {
                                                                                                                            items.name
                                                                                                                        }
                                                                                                                    </p>
                                                                                                                    <p>
                                                                                                                        ₹{' '}
                                                                                                                        {items?.isInclusiveofGst
                                                                                                                            ? Number(
                                                                                                                                  items.finalPrice
                                                                                                                              ).toFixed(
                                                                                                                                  2
                                                                                                                              )
                                                                                                                            : Number(
                                                                                                                                  items.price
                                                                                                                              ).toFixed(
                                                                                                                                  2
                                                                                                                              )}
                                                                                                                    </p>
                                                                                                                    {store.role !==
                                                                                                                        RoleType.FRONT_DESK_ADMIN &&
                                                                                                                        items.isAssigned && (
                                                                                                                            <Button
                                                                                                                                onClick={() =>
                                                                                                                                    items.isAssigned &&
                                                                                                                                    handleRemovePricing(
                                                                                                                                        items._id
                                                                                                                                    )
                                                                                                                                }
                                                                                                                                className="absolute right-0 border-0 bg-transparent p-0 px-2 py-1 text-[#8143d1] opacity-0 transition-opacity duration-200 group-hover:opacity-100"
                                                                                                                            >
                                                                                                                                <DeleteFilled />
                                                                                                                            </Button>
                                                                                                                        )}
                                                                                                                </div>
                                                                                                            )
                                                                                                        )
                                                                                                    )}
                                                                                                </div>
                                                                                            </Row>
                                                                                        ),
                                                                                },
                                                                            ]}
                                                                        />
                                                                    </div>
                                                                );
                                                            }
                                                        )}
                                                        {/* {item.description} */}
                                                        {...hasServiceWritePermission ||
                                                        store.role ===
                                                            RoleType.ORGANIZATION
                                                            ? [
                                                                  <div className="mt-8 flex flex-wrap justify-between">
                                                                      <Button
                                                                          className="border border-black bg-transparent py-6 text-xl"
                                                                          onClick={() =>
                                                                              setLocation(
                                                                                  `add-appointment-type/${item._id}/0`
                                                                              )
                                                                          }
                                                                      >
                                                                          Add a
                                                                          Sub-Type
                                                                      </Button>
                                                                  </div>,
                                                              ]
                                                            : []}
                                                    </>
                                                ),
                                            },
                                        ]}
                                    />
                                )
                            )
                        )}
                    </div>
                </Space>
                <AssignPricingModal
                    isVisible={isAssignModal}
                    onClose={handleCloseModal}
                    subTypeId={idForStaff}
                    serviceId={serviceIdForStaff}
                    serviceType={'bookings'}
                    openedPanel={openedPanel}
                />
            </div>

            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={store.AllServiceCategoryListCount}
                    pageSize={pageSizes}
                    onChange={paginate}
                    hideOnSinglePage
                />
            </div>
            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={
                    isDeleting ? handleDelete : handleConfirmStatusChange
                }
                onCancel={handleCancelStatusChange}
                message={`Are you sure you want to ${
                    isDeleting ? 'delete the subtype' : 'change the status'
                }?`}
            />
            {serviceconfirmationModalVisible && (
                <>
                    <CommonConfirmationModal
                        visible={serviceconfirmationModalVisible}
                        onConfirm={handleServiceConfirmStatusChange}
                        onCancel={handleServiceCancelStatusChange}
                        message={`Are you sure you want to change the status ?`}
                    />
                </>
            )}
        </>
    );
};

export default BookingTypes;
