import React, { useState } from 'react';
import {
    Button,
    Checkbox,
    ConfigProvider,
    DatePicker,
    Form,
    Input,
    InputNumber,
    Modal,
    Radio,
    RadioChangeEvent,
    Select,
    TimePicker,
} from 'antd';
import Title from 'antd/es/typography/Title';
import dayjs from 'dayjs';

const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

interface ScheduleClassesModalProps {
    showModal?: boolean;
    visible?: boolean;
    onClose?: () => void;
    title?: string;
    content?: string;
}

const ScheduleClassesModal: React.FC<ScheduleClassesModalProps> = ({
    showModal,
    onClose,
    title,
}) => {
    const [value, setValue] = useState(1);
    const [selectedDay, setSelectedDay] = useState<string>('Mon');

    const onRadioChange = (e: RadioChangeEvent) => {
        console.log('radio checked', e.target.value);
        setValue(e.target.value);
    };

    const onDateChange = (date: any, dateString: any) => {
        console.log(date, dateString);
    };

    const handleDayClick = (day: string) => {
        setSelectedDay(day);
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Form: {
                        itemMarginBottom: 22,
                        verticalLabelMargin: -5,
                    },
                },
            }}
        >
            <Modal
                title={
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                        }}
                    >
                        <Title level={4} className="text-[#1a3353]">
                            {title}
                        </Title>
                    </div>
                }
                open={showModal}
                onCancel={onClose}
                footer={null}
                style={{ top: 0 }}
                className="lg:w-[50%]"
            >
                <div className="flex flex-row gap-5 py-5">
                    <Form
                        layout="vertical"
                        className="flex w-full flex-col gap-8"
                    >
                        <Radio.Group onChange={onRadioChange} value={value}>
                            <Radio value={1} className="text-xl">
                                Recurring class
                            </Radio>
                            <Radio value={2} className="text-xl">
                                Single class
                            </Radio>
                        </Radio.Group>

                        <div className="flex w-full flex-row gap-5 border-b border-[#45556080] border-opacity-50 pb-8">
                            <div className="flex flex-col">
                                <p className="text-[13px] font-semibold text-[#1A3353]">
                                    Start
                                </p>
                                <DatePicker
                                    popupClassName="custom-datepicker"
                                    onChange={onDateChange}
                                    className=""
                                />
                            </div>
                            <div className="flex flex-col">
                                <p className="text-[13px] font-semibold text-[#1A3353]">
                                    End
                                </p>
                                <DatePicker
                                    popupClassName="custom-datepicker"
                                    onChange={onDateChange}
                                    className=""
                                />
                                <p className="text-[13px] text-[#1A3353]">
                                    1 year from now
                                </p>
                            </div>
                        </div>

                        <div className="mb-8 flex flex-wrap lg:gap-8 @sm:gap-5">
                            {daysOfWeek.map((day) => (
                                <div key={day}>
                                    <Button
                                        onClick={() => handleDayClick(day)}
                                        shape="circle"
                                        className={`p-2  ${
                                            selectedDay === day
                                                ? 'bg-checkbox-checked  text-white'
                                                : 'border-checkbox-checked text-black'
                                        }`}
                                    >
                                        {day}
                                    </Button>
                                </div>
                            ))}
                        </div>

                        <div className="flex flex-col rounded-lg border p-4 shadow-lg ">
                            <div className="flex lg:flex-row   lg:gap-3 @sm:flex-col">
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="To"
                                        name="StartTime"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please enter Start Time',
                                            },
                                        ]}
                                    >
                                        <TimePicker
                                            className="w-full"
                                            defaultOpenValue={dayjs(
                                                '00:00:00',
                                                'HH:mm:ss'
                                            )}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="From"
                                        name="EndTime"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please enter End Time',
                                            },
                                        ]}
                                    >
                                        <TimePicker
                                            className="w-full"
                                            defaultOpenValue={dayjs(
                                                '00:00:00',
                                                'HH:mm:ss'
                                            )}
                                        />
                                    </Form.Item>
                                </div>

                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Repeats Every"
                                        name="Repeat"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please enter interval',
                                            },
                                        ]}
                                    >
                                        <Select
                                            placeholder="Select Type"
                                            showSearch
                                            filterOption={(input, option) =>
                                                (option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={[
                                                { label: '1', value: '1' },
                                                {
                                                    label: '2',
                                                    value: '2',
                                                },

                                                { label: '3', value: '3' },
                                            ]}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Frequency"
                                        name="Frequency"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please enter Frequency',
                                            },
                                        ]}
                                    >
                                        <Select
                                            placeholder="Select Type"
                                            showSearch
                                            filterOption={(input, option) =>
                                                (option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={[
                                                {
                                                    label: 'Days',
                                                    value: 'Days',
                                                },
                                                {
                                                    label: 'Weeks',
                                                    value: 'Weeks',
                                                },

                                                {
                                                    label: 'Months',
                                                    value: 'Months',
                                                },
                                            ]}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                            <div className="flex lg:flex-row   lg:gap-3 @sm:flex-col">
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Staff"
                                        name="Staff"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please select staff',
                                            },
                                        ]}
                                    >
                                        <Select
                                            placeholder="Select Staff"
                                            showSearch
                                            filterOption={(input, option) =>
                                                (option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={[
                                                { label: '1', value: '1' },
                                                {
                                                    label: '2',
                                                    value: '2',
                                                },

                                                { label: '3', value: '3' },
                                            ]}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" w-full px-2 md:w-1/2">
                                    <Form.Item
                                        label="Pay Rates"
                                        name="Pay Rates"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please select pay rates',
                                            },
                                        ]}
                                    >
                                        <Select
                                            placeholder="Select Pay Rates"
                                            showSearch
                                            filterOption={(input, option) =>
                                                (option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={[
                                                {
                                                    label: 'Days',
                                                    value: 'Days',
                                                },
                                                {
                                                    label: 'Weeks',
                                                    value: 'Weeks',
                                                },

                                                {
                                                    label: 'Months',
                                                    value: 'Months',
                                                },
                                            ]}
                                        />
                                    </Form.Item>
                                </div>
                            </div>

                            <div className=" w-full  px-2 ">
                                <Form.Item
                                    label="Room"
                                    name="Room"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please room',
                                        },
                                    ]}
                                >
                                    <Select
                                        placeholder="Select Room"
                                        className="lg:w-[49%]"
                                        showSearch
                                        filterOption={(input, option) =>
                                            (option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={[
                                            {
                                                label: 'Days',
                                                value: 'Days',
                                            },
                                            {
                                                label: 'Weeks',
                                                value: 'Weeks',
                                            },

                                            {
                                                label: 'Months',
                                                value: 'Months',
                                            },
                                        ]}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex justify-center">
                            <Button
                                className=" rounded-lg border border-[#1a3353]  text-xl  text-[#1a3353] lg:w-[20%]"
                                type="default"
                            >
                                Save and Add Another
                            </Button>
                        </div>

                        <div className="rounded-lg  border px-6 py-6 shadow-lg">
                            <p className="text-[13px] font-semibold text-[#455560]">
                                Class size
                            </p>
                            <div className="flex flex-col  py-5 ">
                                <Form.Item
                                    label="Total Capacity"
                                    name="Capacity"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please enter capacity',
                                        },
                                    ]}
                                >
                                    <Input
                                        type="number"
                                        className="sm:w-full lg:w-[25%] @sm:w-[100%]"
                                    />
                                </Form.Item>
                                <Form.Item
                                    label="How many people can waitlist"
                                    name="Waitlist"
                                    rules={[
                                        {
                                            required: true,
                                        },
                                    ]}
                                >
                                    <Input
                                        type="number"
                                        className="lg:w-[25%] @sm:w-[100%]"
                                    />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="rounded-lg  border p-6 shadow-lg">
                            <p className="text-[13px] font-semibold text-[#455560]">
                                Online Options
                            </p>
                            <div className="flex flex-col gap-8 py-5 ">
                                <Checkbox>
                                    Allow clients to signup for this online
                                    class
                                </Checkbox>
                                <Form.Item
                                    label="Online Capacity"
                                    name="Online"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter online capacity',
                                        },
                                    ]}
                                >
                                    <Input
                                        type="number"
                                        className="lg:w-[25%] @sm:w-[100%]"
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className="rounded-lg  border p-6 shadow-lg">
                            <p className="text-[13px] font-semibold text-[#455560]">
                                Pricing
                            </p>
                            <div className="flex flex-col gap-5 py-5 ">
                                <Checkbox>
                                    Allow clients to signup now and pay later
                                </Checkbox>
                                <Checkbox>
                                    Clients can attend this class for free
                                </Checkbox>
                            </div>
                        </div>

                        <div className="flex flex-row justify-end">
                            <Form.Item>
                                <div style={{ display: 'flex', gap: '10px' }}>
                                    <Button
                                        className=" w-[100px] rounded-lg  border border-[#1a3353]  text-xl text-[#1a3353]"
                                        type="default"
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        className="w-[100px] rounded-lg bg-purpleLight  px-14  text-xl text-white  "
                                        htmlType="submit"
                                    >
                                        Save
                                    </Button>
                                </div>
                            </Form.Item>
                        </div>
                    </Form>
                </div>
            </Modal>
        </ConfigProvider>
    );
};

export default ScheduleClassesModal;
