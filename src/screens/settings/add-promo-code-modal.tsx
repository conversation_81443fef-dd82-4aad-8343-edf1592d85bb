import { Button, Form, Input, Modal, Radio, Select } from 'antd';
import Paragraph from 'antd/es/typography/Paragraph';
import React from 'react';

const AddPromoCodeModal = ({ visible, onClose }: any) => {
    return (
        <div>
            <Modal
                title="Add Promo Code"
                open={visible}
                centered
                onCancel={onClose}
                footer={false}
                className="w-[55%]"
            >
                <Form
                    className="flex flex-col gap-8 pt-10 "
                    name="PricingCustomPricing"
                    layout="horizontal"
                    size="large"
                    initialValues={{
                        discountType: 'Flat',
                    }}
                    autoComplete="off"

                    // disabled={isViewOnly} // Disable all inputs when view === 'true'
                >
                    {/* Description */}
                    <div className="flex flex-row items-center">
                        <div className="w-[20%]">
                            <Paragraph className="ant-form-item-label mb-0">
                                <label>
                                    Name
                                    <span className="ms-1 text-2xl text-red-400">
                                        {' '}
                                        *
                                    </span>
                                </label>
                            </Paragraph>
                        </div>
                        <Form.Item
                            name="description"
                            className="organizationFilter w-[80%]"
                        >
                            <Input placeholder="Enter description" />
                        </Form.Item>
                    </div>

                    {/* Quantity */}

                    {/* Unit Price */}

                    {/* Discount */}
                    <div className="flex flex-row items-center">
                        <div className="w-[20%]">
                            <Paragraph className="ant-form-item-label mb-0">
                                <label>
                                    Discount
                                    <span className="ms-1 text-2xl text-red-400">
                                        {' '}
                                        *
                                    </span>
                                </label>
                            </Paragraph>
                        </div>
                        <div className="flex w-[80%] items-center gap-4">
                            <Form.Item name="discountValue" className="w-full">
                                <Input
                                    type="text"
                                    placeholder="Enter discount value"
                                    onInput={(e: any) => {
                                        // Allow only digits and one decimal point
                                        e.target.value = e.target.value.replace(
                                            /[^0-9.]/g,
                                            ''
                                        );

                                        const parts = e.target.value.split('.');
                                        if (parts.length > 2) {
                                            e.target.value =
                                                parts[0] + '.' + parts[1];
                                        }
                                    }}
                                />
                            </Form.Item>
                        </div>
                    </div>

                    {/* Total */}

                    {/* Taxable */}
                    <div className="flex flex-row items-center">
                        <div className="w-[20%]">
                            <Paragraph className="ant-form-item-label mb-0">
                                <label>
                                    Promo Code
                                    <span className="ms-1 text-2xl text-red-400">
                                        {' '}
                                        *
                                    </span>
                                </label>
                            </Paragraph>
                        </div>
                        <Form.Item className="organizationFilter w-full ">
                            <Input placeholder="Enter Promo Code" />
                        </Form.Item>
                    </div>

                    {/* Tax and HSN if taxable */}

                    {/* Buttons */}
                    <div className="flex w-[85%] flex-row gap-5 lg:justify-end @sm:justify-center">
                        <Form.Item>
                            <Button
                                onClick={onClose}
                                className="h-12 w-32 border border-[#1A3353] text-xl"
                            >
                                Cancel
                            </Button>
                        </Form.Item>
                        <Form.Item>
                            <Button
                                htmlType="submit"
                                className="h-12 w-32 bg-purpleLight text-xl text-white"
                            >
                                Ok
                            </Button>
                        </Form.Item>
                    </div>
                </Form>
            </Modal>
        </div>
    );
};

export default AddPromoCodeModal;
