import { Switch, Table, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { navigate } from 'wouter/use-location';
import CommonTable from '~/components/common/commonTable';
import { useLoader } from '~/hooks/useLoader';
import { GetSettings, SaveSettings } from '~/redux/actions/settings-actions';
import Alertify from '~/services/alertify';

const { Title } = Typography;
const SettingPricing = () => {
    const [loader, startLoader, endLoader] = useLoader();

    const dispatch = useDispatch();
    const [state, setState] = useState(false);
    const fetchData = () => {
        dispatch(GetSettings({}))
            .then((res: any) => {
                // console.log('Response is:', res?.payload?.data?.data);
                const data = res?.payload?.data?.data;
                if (data?.isInclusiveofGst) {
                    setState(data?.isInclusiveofGst || false);
                }
            })
            .finally(endLoader);
    };
    useEffect(() => {
        startLoader();

        fetchData();
    }, []);
    const saveSettings = async () => {
        try {
            const payload = {
                isInclusiveofGst: !state,
            };
            const res = await dispatch(SaveSettings({ state: payload }));
            const status = res?.payload?.status;
            if (status === 200 || status === 201) {
                // fetchData();
                navigate('/setting');
            } else {
                Alertify.error('Failed to save settings. Please try again.');
            }
        } catch (err) {
            Alertify.error('Something went wrong. Please try again later.');
        }
    };
    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
        },
        {
            title: 'Status',
            dataIndex: 'status',
            render: () => {
                return (
                    <Switch
                        checked={state}
                        checkedChildren="ON"
                        unCheckedChildren="OFF"
                        onChange={() => saveSettings()}
                    />
                );
            },
        },
    ];
    const dataSource = [
        {
            key: 0,
            name: 'Inclusive of GST',
            // status: render(),
        },
    ];
    return (
        <div className="h-full  items-center gap-4 @sm:w-full">
            <CommonTable
                className="min-w-min"
                columns={columns}
                dataSource={dataSource}
                loading={loader}
                bulkAction={false}
                backButton={true}
                heading="Pricing"
            />
        </div>
    );
};

export default SettingPricing;
