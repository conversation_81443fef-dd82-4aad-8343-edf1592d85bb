import React, { useEffect, useState } from 'react';
import CommonTable from '~/components/common/commonTable';
import { useLoader } from '~/hooks/useLoader';
import {
    Switch,
    ConfigProvider,
    Modal,
    Typography,
    Form,
    Input,
    Button,
    Dropdown,
    Pagination,
} from 'antd';
import clsx from 'clsx';
import { Link } from 'wouter';
import { capitalizeFirstLetter } from '~/components/common/function';
import {
    MembershipList,
    updateMembershipStatus,
} from '~/redux/actions/membership-action';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import {
    ChangeRevenueCategoryStatus,
    CreateRevenueCategory,
    DeleteRevenueCategory,
    RevenueCategoryList,
    UpdateRevenueCategory,
} from '~/redux/actions/revenue-category-action';
import Alertify from '~/services/alertify';
import { MoreOutlined } from '@ant-design/icons';
import { getQueryParams } from '~/utils/getQueryParams';

const columns = [
    {
        title: 'Names',
        dataIndex: '',
        render: (record: any) => {
            return <div>{capitalizeFirstLetter(record.name)}</div>;
        },
    },
];
interface DataSourceItem {
    key: number;
    name: string;
    status: boolean;
    prefix: string;
    counter: number;
}
const { Title } = Typography;
const RevenueCategory: React.FC = () => {
    const [loader, startLoader, endLoader] = useLoader();
    const dispatch = useDispatch<AppDispatch>();
    const [revenueRecord, setRevenueRecord] = useState<any>(null);
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState<boolean>(false);
    const [openModal, setOpenModal] = useState(false);
    const [deleteModal, setDeleteModal] = useState(false);
    const [revenueForm] = Form.useForm();
    const [dataSource, setDataSource] = useState<DataSourceItem[]>([]);
    const [editId, setEditId] = useState<string>();
    const [editName, setEditName] = useState<string>();
    const [totalItems, setTotalItems] = useState(0);
    const params = getQueryParams();
    const [perPage, setPerPage] = useState(10);
    const pageParam = Number(params.page);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );

    const fetchRevenueList = () => {
        // const queryParams = { page, perPage };

        dispatch(RevenueCategoryList({}))
            .unwrap()
            .then((response: any) => {
                console.log('Response is:', response?.data?.data);
                setDataSource(response?.data?.data);
            })
            .finally(endLoader);
    };
    useEffect(() => {
        startLoader();
        fetchRevenueList();
    }, []);
    const onFinish = (values: any) => {
        if (editId) {
            const payload = {
                id: editId,
                name: values.name,
                isActive: true,
            };
            dispatch(UpdateRevenueCategory(payload))
                .then((res: any) => {
                    fetchRevenueList();
                    setOpenModal(false);
                    setEditId('');
                    setEditName('');
                })
                .catch((error: any) => {
                    Alertify.error('Could not update Revenue Category');
                    console.log('Error occured:', error);
                });
        } else {
            const payload = {
                name: values.name,
                isActive: true,
            };
            dispatch(CreateRevenueCategory(payload))
                .then((res: any) => {
                    fetchRevenueList();
                    setOpenModal(false);
                    setEditId('');
                    setEditName('');
                })
                .catch((error: any) => {
                    Alertify.error('Could not create Revenue Category');
                    console.log('Error occured:', error);
                });
        }
    };
    const handleStatusChange = (record: any) => {
        setRevenueRecord(record);
        setConfirmationModalVisible(true);
    };
    const handleConfirmStatusChange = () => {
        if (revenueRecord)
            dispatch(
                ChangeRevenueCategoryStatus({
                    isActive: !revenueRecord.isActive,
                    id: revenueRecord?._id,
                })
            )
                .then(() => {
                    setConfirmationModalVisible(false);
                    fetchRevenueList();
                })
                .catch((error: any) => {
                    console.log('Error occured:', error);
                    Alertify.error('Could not update status!');
                });
    };
    const handleDelete = (record: any) => {
        setRevenueRecord(record);
        setDeleteModal(true);
    };
    const handleConfirmDelete = () => {
        if (revenueRecord)
            dispatch(
                DeleteRevenueCategory({
                    id: revenueRecord?._id,
                })
            )
                .then(() => {
                    setDeleteModal(false);
                    fetchRevenueList();
                })
                .catch((error: any) => {
                    console.log('Error occured:', error);
                    Alertify.error('Could not update status!');
                });
    };

    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
    };
    const selectColumn = [
        {
            title: 'Status',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                // console.log("Record--------------", record)
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    handleBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Switch
                            className={clsx(
                                'rounded-full transition-colors',
                                record.isActive
                                    ? 'bg-switch-on'
                                    : 'bg-switch-off'
                            )}
                            id="swtich-off"
                            checkedChildren="ON"
                            // className="bg-[#D0D4D7]"
                            unCheckedChildren="OFF"
                            onChange={() => handleStatusChange(record)}
                            checked={record.isActive || false}
                        />
                    </ConfigProvider>
                );
            },
        },
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            width: '120px',
            align: 'center',
            render: (record: any) => {
                const menuItems = [
                    {
                        key: 'Edit',
                        label: (
                            <div
                                className="p-2 px-4"
                                onClick={() => {
                                    setEditId(record._id);
                                    setEditName(record.name);
                                    setOpenModal(true);
                                }}
                            >
                                Edit
                            </div>
                        ),
                    },
                    {
                        key: 'Delete',
                        label: (
                            <div
                                className="p-2 px-4"
                                onClick={() => handleDelete(record)}
                            >
                                Delete
                            </div>
                        ),
                    },
                ];
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Dropdown: {
                                    paddingBlock: 0,
                                    controlPaddingHorizontal: 0,
                                },
                            },
                        }}
                    >
                        <Dropdown
                            menu={{ items: menuItems }}
                            trigger={['click']}
                        >
                            <MoreOutlined
                                style={{
                                    fontSize: '20px',
                                    cursor: 'pointer',
                                }}
                            />
                        </Dropdown>
                    </ConfigProvider>
                );
            },
        },
    ];
    const combinedColumns = [...columns, ...selectColumn];
    useEffect(() => {
        if (editName) {
            revenueForm.setFieldsValue({
                name: editName,
            });
        } else {
            revenueForm.setFieldsValue({
                name: '',
            });
        }
        // console.log('editName', editName);
    }, [editId, editName, revenueForm]);
    const handlePageChange = (page: number, perPage?: number) => {
        setCurrentPage(page);
        if (perPage) setPerPage(perPage);
    };
    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={combinedColumns}
                dataSource={dataSource}
                loading={loader}
                bulkAction={false}
                backButton={true}
                heading="Revenue Category"
                addNewModal={true}
                openModal={() => setOpenModal(true)}
                addNewTitle="Add New"
            />
            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={totalItems}
                    pageSize={perPage}
                    onChange={handlePageChange}
                    showSizeChanger
                    pageSizeOptions={['10', '20', '50', '100']}
                />
            </div>
            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />
            <Modal
                open={openModal}
                onCancel={() => {
                    setOpenModal(false);
                    setEditId('');
                    setEditName('');
                }}
                title={
                    editId
                        ? 'Edit Revenue Category'
                        : 'Add New Revenue Category'
                }
                centered
                className="p-4"
                footer={false}
            >
                <Form form={revenueForm} className="pt-5" onFinish={onFinish}>
                    <Form.Item label="Name" name="name">
                        <Input placeholder="New Revenue Category Name" />
                    </Form.Item>
                    <div className="flex items-center justify-end gap-4">
                        <Button
                            key="cancel"
                            onClick={() => {
                                setOpenModal(false);
                                setEditId('');
                                setEditName('');
                            }}
                        >
                            Cancel
                        </Button>

                        <Button
                            key="save"
                            // type="primary"
                            htmlType="submit"
                            className="bg-purpleLight text-white"
                        >
                            Save
                        </Button>
                    </div>
                </Form>
            </Modal>
            <Modal
                open={deleteModal}
                footer={false}
                onCancel={() => {
                    setDeleteModal(false);
                    setEditId('');
                    setEditName('');
                }}
                centered
            >
                <p className="pt-5 text-2xl font-medium text-[#1a3353]">
                    Are you sure to delete the Revenue Category?
                </p>
                <div className="flex items-center justify-end gap-4 pt-10">
                    <Button
                        key="cancel"
                        onClick={() => {
                            setDeleteModal(false);
                            setEditId('');
                            setEditName('');
                        }}
                    >
                        Cancel
                    </Button>

                    <Button
                        key="save"
                        onClick={() => handleConfirmDelete()}
                        className="bg-purpleLight    text-white"
                    >
                        Delete
                    </Button>
                </div>
            </Modal>
        </>
    );
};
export default RevenueCategory;
