import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
    Button,
    ConfigProvider,
    Form,
    Input,
    Radio,
    Select,
    Typography,
    Upload,
} from 'antd';
import FormItem from 'antd/es/form/FormItem';
import Title from 'antd/es/typography/Title';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useLocation } from 'wouter';
import { goBack } from '~/components/common/function';

const PromotionalMssgDetail = () => {
    const [location] = useLocation();
    const [templateName, setTemplateName] = useState('');
    const [templateMessage, setTemplateMessage] = useState('');
    const [imageUrl, setImageUrl] = useState<string>('/assets/placeholder.jpg');
    const [loading, setLoading] = useState(false);
    const [editableTemplateName, setEditableTemplateName] =
        useState(templateName);

    const [selectedAction, setSelectedAction] = useState('none');
    const [actions, setActions] = useState([
        {
            type: 'call',
            label: 'Call us',
            value: '**********',
        },
    ]);

    const beforeUpload = (file: File) => {
        const isImage = file.type.startsWith('image/');
        if (!isImage) {
            toast.error('You can only upload image files!');
        }
        return isImage || Upload.LIST_IGNORE;
    };

    const handleCustomUpload = async ({ file }: any) => {
        setLoading(true);

        // For demo preview: use local object URL
        const url = URL.createObjectURL(file);
        setImageUrl(url);
        setLoading(false);

        // For real backend upload:
        // const formData = new FormData();
        // formData.append('file', file);
        // const res = await axios.post('/upload', formData);
        // setImageUrl(res.data.url); // then set the uploaded URL
    };

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </button>
    );

    useEffect(() => {
        const queryParams = new URLSearchParams(window.location.search);
        const nameFromURL = queryParams.get('name') ?? '';

        const messageTemplates: Record<string, string> = {
            'Birthday Message':
                'Happy Birthday! May your year be filled with joy and success!',
            'Exclusive Offer Inside!':
                'Check out our latest deal just for you.',
            'Your next appointment is confirmed!': 'Thanks for booking!',
            'Celebrate Diwali with us!': 'Diwali specials are live now!',
            'Holi Special Offer!': 'Color your day with savings!',
            'New Year Offer!': 'Celebrate 2025 with our amazing deals!',
        };

        setTemplateName(nameFromURL);
        setTemplateMessage(messageTemplates[nameFromURL] ?? '');
    }, [location]);

    return (
        <>
            <div className="flex items-center gap-4 ">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title className="text-[#1a3353]" level={4}>
                    Promotional Messages Template
                </Title>
            </div>
            <div className="flex flex-row justify-between gap-4  pt-16">
                <div className="flex w-[65%] flex-col gap-4   ">
                    <ConfigProvider
                        theme={{
                            components: {
                                Form: {
                                    itemMarginBottom: 22,
                                    verticalLabelMargin: -2,
                                },

                                Input: {},
                                Select: {},
                            },
                            token: {
                                borderRadius: 5,
                            },
                        }}
                    >
                        <Form layout="vertical" className="flex flex-col gap-5">
                            <FormItem label="Template Name">
                                <Input
                                    value={templateName || ''}
                                    onChange={(e) =>
                                        setEditableTemplateName(e.target.value)
                                    }
                                    type="text"
                                    className="w-full rounded-lg border p-2"
                                />
                            </FormItem>
                            <FormItem label="Sample Message">
                                <Input.TextArea
                                    className="w-full rounded-lg border p-2"
                                    rows={4}
                                    value={templateMessage || ''}
                                />
                            </FormItem>
                            <Form.Item label="Interactive Action">
                                <Radio.Group
                                    value={selectedAction}
                                    onChange={(e) =>
                                        setSelectedAction(e.target.value)
                                    }
                                >
                                    <Radio value="none"> None </Radio>
                                    <Radio value="action">Call to action</Radio>
                                    <Radio value="reward">Rewards</Radio>
                                </Radio.Group>
                            </Form.Item>
                            {selectedAction === 'action' && (
                                <>
                                    {selectedAction === 'action' && (
                                        <>
                                            {actions.map((action, index) => {
                                                const isFirst = index === 0;
                                                const isLast =
                                                    index ===
                                                    actions.length - 1;

                                                return (
                                                    <div
                                                        key={index}
                                                        className="mb-2 flex flex-row items-center gap-4"
                                                    >
                                                        <p className=" text-right text-[13px] font-medium text-[#1a3353]">
                                                            Call to Action{' '}
                                                            {index + 1}
                                                        </p>

                                                        <Select
                                                            // disabled={isFirst}
                                                            className="w-[15%]"
                                                            placeholder="Action"
                                                            options={[
                                                                {
                                                                    label: 'Phone number',
                                                                    value: 'call',
                                                                },
                                                                {
                                                                    label: 'URL',
                                                                    value: 'url',
                                                                },
                                                            ]}
                                                            value={
                                                                action.type ??
                                                                null
                                                            }
                                                            onChange={(
                                                                value
                                                            ) => {
                                                                if (!isFirst) {
                                                                    const updated =
                                                                        [
                                                                            ...actions,
                                                                        ];
                                                                    updated[
                                                                        index
                                                                    ].type =
                                                                        value;
                                                                    updated[
                                                                        index
                                                                    ].label =
                                                                        value ===
                                                                        'call'
                                                                            ? 'Call us'
                                                                            : value ===
                                                                              'url'
                                                                            ? 'Visit our website'
                                                                            : '';
                                                                    setActions(
                                                                        updated
                                                                    );
                                                                }
                                                            }}
                                                        />

                                                        <Input
                                                            // disabled={isFirst}
                                                            className="w-[15%]"
                                                            placeholder="Enter label"
                                                            value={action.label}
                                                            onChange={(e) => {
                                                                if (!isFirst) {
                                                                    const updated =
                                                                        [
                                                                            ...actions,
                                                                        ];
                                                                    updated[
                                                                        index
                                                                    ].label =
                                                                        e.target.value;
                                                                    setActions(
                                                                        updated
                                                                    );
                                                                }
                                                            }}
                                                        />

                                                        <Input
                                                            // disabled={isFirst}
                                                            className="w-[15%]"
                                                            placeholder="Enter value"
                                                            value={action.value}
                                                            onChange={(e) => {
                                                                if (!isFirst) {
                                                                    const updated =
                                                                        [
                                                                            ...actions,
                                                                        ];
                                                                    updated[
                                                                        index
                                                                    ].value =
                                                                        e.target.value;
                                                                    setActions(
                                                                        updated
                                                                    );
                                                                }
                                                            }}
                                                        />

                                                        {isLast && (
                                                            <Button
                                                                onClick={() =>
                                                                    setActions(
                                                                        (
                                                                            prev
                                                                        ) => [
                                                                            ...prev,
                                                                            {
                                                                                type: '',
                                                                                label: '',
                                                                                value: '',
                                                                            },
                                                                        ]
                                                                    )
                                                                }
                                                                className="rounded-lg border border-[#1a3353] px-5 py-3 text-xl text-[#1a3353]"
                                                            >
                                                                Add more
                                                            </Button>
                                                        )}
                                                    </div>
                                                );
                                            })}
                                        </>
                                    )}
                                </>
                            )}
                            {selectedAction === 'reward' && (
                                <>
                                    <div className="">
                                        <Form.Item
                                            className="w-[62%]"
                                            label="Reward Type"
                                        >
                                            <Input placeholder="Enter reward type" />
                                        </Form.Item>
                                    </div>

                                    <div className="flex flex-row items-center gap-4">
                                        <Form.Item
                                            className="w-[30%]"
                                            label="No. of Sessions attended"
                                        >
                                            <Input
                                                placeholder="Enter sessions"
                                                type="number"
                                            />
                                        </Form.Item>

                                        <Form.Item
                                            className="w-[30%]"
                                            label="Sessions attended in months"
                                        >
                                            <Select
                                                placeholder="Reward Type"
                                                options={[
                                                    {
                                                        label: '1 Month',
                                                        value: '1',
                                                    },
                                                    {
                                                        label: '3 Months',
                                                        value: '3',
                                                    },
                                                    {
                                                        label: '6 Months',
                                                        value: '6',
                                                    },
                                                    {
                                                        label: '12 Months',
                                                        value: '12',
                                                    },
                                                ]}
                                            />
                                        </Form.Item>
                                    </div>
                                </>
                            )}
                            <div className="flex flex-row justify-end gap-4 pt-20">
                                <Button
                                    onClick={goBack}
                                    className="w-[18%] rounded-lg border border-[#1a3353] py-2 text-xl text-[#1a3353]"
                                >
                                    Cancel
                                </Button>

                                <Button
                                    onClick={goBack}
                                    className="w-[18%] rounded-lg bg-purpleLight py-2 text-xl text-white"
                                    htmlType="submit"
                                >
                                    Generate Template
                                </Button>
                            </div>
                        </Form>
                    </ConfigProvider>
                </div>
                <div className="-mt-4 w-[30%] ">
                    <Typography.Title level={5}>
                        <span className="text-[13px] font-medium text-[#1a3353]">
                            Upload Image
                        </span>
                    </Typography.Title>
                    <div className=" rounded-xl border border-gray-200 p-5">
                        <Upload
                            name="avatar"
                            listType="picture-card"
                            className="avatar-uploader overflow-hidden "
                            showUploadList={false}
                            beforeUpload={beforeUpload}
                            customRequest={handleCustomUpload}
                        >
                            {imageUrl ? (
                                <div className="relative h-full w-full">
                                    <img
                                        src={imageUrl}
                                        alt="uploaded"
                                        className="object-center lg:object-cover @sm:rounded-3xl @sm:object-cover"
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            objectFit: 'cover',
                                        }}
                                    />
                                    {loading && (
                                        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/50">
                                            <LoadingOutlined
                                                style={{
                                                    fontSize: 24,
                                                    color: '#8143D1',
                                                }}
                                                spin
                                            />
                                        </div>
                                    )}
                                </div>
                            ) : (
                                uploadButton
                            )}
                        </Upload>
                    </div>
                </div>
            </div>
        </>
    );
};

export default PromotionalMssgDetail;
