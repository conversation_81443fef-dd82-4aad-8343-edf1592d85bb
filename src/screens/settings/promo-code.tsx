import { useState } from 'react';
import { Switch, Tag } from 'antd';
import CommonTable from '~/components/common/commonTable';
import { EditFilled } from '@ant-design/icons';
import { Link } from 'wouter';
import AddPromoCodeModal from './add-promo-code-modal';

const PromoCode = () => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedRow, setSelectedRow] = useState<any>(null);
    const [tableData, setTableData] = useState([
        {
            key: 0,
            name: 'abc100',
            value: '10%',
            promoCode: 'BIRTHDAY10',
            message: true, // enabled
            status: true, // active
        },
        {
            key: 1,
            name: 'abhi200',
            value: '20%',
            promoCode: 'EXCLUSIVE20',
            message: false, // disabled
            status: false, // inactive
        },
        // {
        //     key: 2,
        //     name: '<PERSON><PERSON><PERSON>',
        //     message: true, // enabled
        //     status: true, // active
        // },
        {
            key: 3,
            name: 'Lucky200',
            value: '30%',
            promoCode: 'DIWALI30',
            message: true, // disabled
            status: true, // inactive
        },
        {
            key: 4,
            name: 'Promo99',
            value: '40%',
            promoCode: 'PILATES40',
            message: true, // enabled
            status: true, // active
        },
        {
            key: 5,
            name: 'Flat50',
            value: '50%',
            promoCode: 'NEWYEAR50',
            message: false, // disabled
            status: false, // inactive
        },
    ]);
    const openModal = (record: any) => {
        setSelectedRow(record); // optional, for edit
        setIsModalVisible(true);
    };

    const closeModal = () => {
        setIsModalVisible(false);
        setSelectedRow(null);
    };
    const handleToggle = (key: any, checked: any) => {
        const updatedData = tableData.map((item) =>
            item.key === key ? { ...item, message: checked } : item
        );
        setTableData(updatedData);
    };

    const columns = [
        {
            title: 'Names',
            dataIndex: 'name',
        },
        {
            title: 'Value',
            dataIndex: 'value',
        },
        {
            title: 'Promo Code',
            dataIndex: 'promoCode',
        },

        {
            title: 'Enable & Disable',
            dataIndex: 'message',
            render: (enabled: any, record: any) => (
                <Switch
                    checked={enabled}
                    onChange={(checked) => handleToggle(record.key, checked)}
                />
            ),
        },
        {
            title: 'Status',
            dataIndex: 'status',
            render: (status: any) => (
                <Tag color={status ? 'green' : 'blue'}>
                    {status ? 'Active' : 'Inactive'}
                </Tag>
            ),
        },
        {
            title: '',
            dataIndex: 'action',
            render: (_: any, record: any) => (
                <div className="flex flex-row justify-center">
                    <EditFilled
                        onClick={() => openModal(record)}
                        className="cursor-pointer text-2xl"
                    />
                </div>
            ),
        },
    ];

    return (
        <div>
            <div className="">
                <CommonTable
                    className="min-w-min"
                    columns={columns}
                    dataSource={tableData}
                    heading="Promo Code Listing"
                    // addNewTitle="Create Amenities"

                    addPromoCode={true}
                />
            </div>
            <AddPromoCodeModal visible={isModalVisible} onClose={closeModal} />
        </div>
    );
};

export default PromoCode;
