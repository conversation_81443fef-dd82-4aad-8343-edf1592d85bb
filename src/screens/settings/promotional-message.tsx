import { useState } from 'react';
import { Switch, Tag } from 'antd';
import CommonTable from '~/components/common/commonTable';
import { EditFilled } from '@ant-design/icons';
import { Link } from 'wouter';

const PromotionalMessage = () => {
    const [tableData, setTableData] = useState([
        {
            key: 0,
            name: 'Birthday Message',
            message: true, // enabled
            status: true, // active
        },
        {
            key: 1,
            name: 'Exclusive Offer Inside!',
            message: false, // disabled
            status: false, // inactive
        },
        // {
        //     key: 2,
        //     name: '<PERSON><PERSON><PERSON> Offer',
        //     message: true, // enabled
        //     status: true, // active
        // },
        {
            key: 3,
            name: '<PERSON><PERSON><PERSON>te <PERSON><PERSON><PERSON> with us!',
            message: true, // disabled
            status: true, // inactive
        },
        {
            key: 4,
            name: 'Pilates workshop',
            message: true, // enabled
            status: true, // active
        },
        {
            key: 5,
            name: 'New Year Offer!',
            message: false, // disabled
            status: false, // inactive
        },
    ]);

    const handleToggle = (key: any, checked: any) => {
        const updatedData = tableData.map((item) =>
            item.key === key ? { ...item, message: checked } : item
        );
        setTableData(updatedData);
    };

    const columns = [
        {
            title: 'Names',
            dataIndex: 'name',
        },
        {
            title: 'Enable & Disable',
            dataIndex: 'message',
            render: (enabled: any, record: any) => (
                <Switch
                    checked={enabled}
                    onChange={(checked) => handleToggle(record.key, checked)}
                />
            ),
        },
        {
            title: 'Status',
            dataIndex: 'status',
            render: (status: any) => (
                <Tag color={status ? 'green' : 'blue'}>
                    {status ? 'Active' : 'Inactive'}
                </Tag>
            ),
        },
        {
            title: '',
            dataIndex: 'action',
            render: (_: any, record: any) => (
                <div className="flex flex-row justify-center">
                    <Link
                        to={`/promotional-message-detail?name=${encodeURIComponent(
                            record.name
                        )}`}
                    >
                        <EditFilled className="cursor-pointer text-2xl" />
                    </Link>
                </div>
            ),
        },
    ];

    return (
        <div>
            <div className="">
                <CommonTable
                    className="min-w-min"
                    columns={columns}
                    dataSource={tableData}
                    heading="Promotional Message Listing"
                    addNewTitle="Create Amenities"
                    addPromotional={true}
                />
            </div>
        </div>
    );
};

export default PromotionalMessage;
