import { CloseCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import { Button, ConfigProvider, Form, Input, Select, Switch } from 'antd';
import Title from 'antd/es/typography/Title';
import { useEffect, useState } from 'react';
import { goBack } from '~/components/common/function';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { GetSettings, SaveSettings } from '~/redux/actions/settings-actions';

type ReminderItem = {
    value: string;
    timing: 'before' | 'after' | '';
    disabled?: boolean;
};

const TransactionalMessage = () => {
    const dispatch = useAppDispatch();
    const [showPaymentReminder, setShowPaymentReminder] = useState(false);
    const [showRenewalReminder, setShowRenewalReminder] = useState(false);
    const [showLimitedSession, setShowLimitedSession] = useState(false);

    const [reminderList, setReminderList] = useState<ReminderItem[]>([
        { value: '1', timing: 'before', disabled: true },
    ]);

    const [renewalReminderList, setRenewalReminderList] = useState<
        ReminderItem[]
    >([{ value: '1', timing: 'before', disabled: true }]);

    const [limitedSessionList, setLimitedSessionList] = useState<
        ReminderItem[]
    >([{ value: '1', timing: 'before', disabled: true }]);

    const [loader, startLoader, endLoader] = useLoader();
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const [settingsData, setSettingsData] = useState<any>(null);
    const [errorMessages, setErrorMessages] = useState<{
        [key: string]: string;
    }>({});

    // Helper function to generate day options based on timing
    const getDayOptions = (timing: 'before' | 'after' | '') => {
        if (timing === 'before') {
            return Array.from({ length: 7 }, (_, i) => ({
                label: `${i + 1} day${i + 1 > 1 ? 's' : ''}`,
                value: `${i + 1}`,
            }));
        } else if (timing === 'after') {
            return Array.from({ length: 3 }, (_, i) => ({
                label: `${i + 1} day${i + 1 > 1 ? 's' : ''}`,
                value: `${i + 1}`,
            }));
        }
        return [];
    };

    // Helper function to get timing options
    const getTimingOptions = () => [
        { label: 'Day(s) before', value: 'before' },
        { label: 'Day(s) after', value: 'after' },
    ];

    // Validation function
    const validateDayValue = (
        value: string,
        timing: 'before' | 'after' | ''
    ) => {
        const num = parseInt(value, 10);
        if (!value || Number.isNaN(num)) return 'Please enter a valid number';
        if (timing === 'before' && (num < 1 || num > 7)) {
            return `Value for "Days Before" must be between 1 and 7`;
        }
        if (timing === 'after' && (num < 1 || num > 3)) {
            return `Value for "Days After" must be between 1 and 3`;
        }
        return '';
    };

    const handleRenewalDayChange = (
        value: string,
        index: number,
        timing: 'before' | 'after'
    ) => {
        const updated = [...renewalReminderList];
        const err = validateDayValue(value, timing);
        if (err) {
            setErrorMessages((prev) => ({
                ...prev,
                [`${timing}_${index}`]: err,
            }));
        } else {
            setErrorMessages((prev) => {
                const next = { ...prev };
                delete next[`${timing}_${index}`];
                return next;
            });
        }
        updated[index] = { ...updated[index], value, timing };
        setRenewalReminderList(updated);
    };

    // Handle timing change for renewal reminders
    const handleRenewalTimingChange = (
        timing: 'before' | 'after',
        index: number
    ) => {
        const updated = [...renewalReminderList];
        const currentValue = updated[index].value;

        // Check if current value is valid for new timing
        const err = validateDayValue(currentValue, timing);
        if (err) {
            // Reset value if invalid for new timing
            updated[index] = { ...updated[index], timing, value: '1' };
            setErrorMessages((prev) => {
                const next = { ...prev };
                delete next[`before_${index}`];
                delete next[`after_${index}`];
                return next;
            });
        } else {
            updated[index] = { ...updated[index], timing };
            setErrorMessages((prev) => {
                const next = { ...prev };
                delete next[`before_${index}`];
                delete next[`after_${index}`];
                return next;
            });
        }
        setRenewalReminderList(updated);
    };

    // Handle payment reminder changes
    const handlePaymentReminderChange = (
        type: 'value' | 'timing',
        index: number,
        newValue: string
    ) => {
        const updated = [...reminderList];
        if (type === 'value') {
            updated[index].value = newValue;
        } else if (type === 'timing') {
            updated[index].timing = newValue as 'before' | 'after';
            // Reset value to 1 when timing changes to ensure validity
            updated[index].value = '1';
        }
        setReminderList(updated);
    };

    // Handle limited session changes
    const handleLimitedSessionChange = (
        type: 'value' | 'timing',
        index: number,
        newValue: string
    ) => {
        const updated = [...limitedSessionList];
        if (type === 'value') {
            updated[index].value = newValue;
        } else if (type === 'timing') {
            updated[index].timing = newValue as 'before' | 'after';
            // Reset value to 1 when timing changes
            updated[index].value = '1';
        }
        setLimitedSessionList(updated);
    };

    const uniqAsc = (nums: number[]) =>
        Array.from(new Set(nums)).sort((a, b) => a - b);

    useEffect(() => {
        startLoader();
        dispatch(GetSettings({}))
            .unwrap()
            .then((data: any) => {
                const payload = data?.data?.data;
                if (!payload) return;

                setSettingsData(payload);

                const renewal = payload.renewalReminder;
                const enabled: boolean = !!renewal?.enabled;
                setShowRenewalReminder(enabled);

                const before = (renewal?.daysBefore ?? []).map((n: number) => ({
                    value: String(n),
                    timing: 'before' as const,
                    disabled: false,
                }));
                const after = (renewal?.daysAfter ?? []).map((n: number) => ({
                    value: String(n),
                    timing: 'after' as const,
                    disabled: false,
                }));

                const combined = [...before, ...after];
                setRenewalReminderList(
                    combined.length
                        ? combined
                        : [{ value: '1', timing: 'before', disabled: true }]
                );
            })
            .finally(endLoader);
    }, [dispatch]);

    const handleRemoveRenewalReminder = (index: number) => {
        if (index === 0) return;
        setRenewalReminderList((prev) => prev.filter((_, i) => i !== index));
    };

    const handleRemovePaymentReminder = (index: number) => {
        if (index === 0) return;
        setReminderList((prev) => prev.filter((_, i) => i !== index));
    };

    const handleRemoveLimitedSessionReminder = (index: number) => {
        if (index === 0) return;
        setLimitedSessionList((prev) => prev.filter((_, i) => i !== index));
    };

    const saveSettings = () => {
        // collect and validate
        const daysBeforeNums: number[] = [];
        const daysAfterNums: number[] = [];

        for (let i = 0; i < renewalReminderList.length; i++) {
            const item = renewalReminderList[i];
            if (!item.timing) continue;
            const err = validateDayValue(item.value, item.timing);
            if (err) {
                setErrorMessages((prev) => ({
                    ...prev,
                    [`${item.timing}_${i}`]: err,
                }));
            } else {
                const num = Number(item.value);
                if (item.timing === 'before') daysBeforeNums.push(num);
                if (item.timing === 'after') daysAfterNums.push(num);
            }
        }

        const updatedSettings = {
            ...settingsData,
            renewalReminder: {
                enabled: showRenewalReminder,
                daysBefore: uniqAsc(daysBeforeNums),
                daysAfter: uniqAsc(daysAfterNums),
            },
        };

        startSubmitLoader();

        dispatch(SaveSettings({ state: updatedSettings }))
            .unwrap()
            .then(() => {})
            .catch(() => {})
            .finally(endSubmitLoader);
    };

    return (
        <>
            <div className="flex items-center gap-4 ">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title className="text-[#1a3353]" level={4}>
                    Transactional Messages Template
                </Title>
            </div>

            <div className="ps-8 pt-16">
                <ConfigProvider
                    theme={{
                        components: {
                            Form: {
                                itemMarginBottom: 22,
                                verticalLabelMargin: -2,
                            },
                            Input: {},
                            Select: {},
                        },
                        token: { borderRadius: 5 },
                    }}
                >
                    <div className="flex flex-col gap-24">
                        {/* Payment Reminder with Dynamic Select */}
                        <Form layout="vertical" className="flex flex-col">
                            <Form.Item label="Payment reminder">
                                <div className="mb-10 flex flex-row items-center gap-10">
                                    <Switch
                                        checked={showPaymentReminder}
                                        onChange={setShowPaymentReminder}
                                    />
                                    <p className="text-[#3a3353]">
                                        Send pending payment reminder to client
                                    </p>
                                </div>

                                {showPaymentReminder && (
                                    <>
                                        <p className="mb-2 text-[13px] font-medium text-[#1a3353]">
                                            When to send reminder?
                                        </p>
                                        {reminderList.map((item, index) => (
                                            <div
                                                key={index}
                                                className="flex w-full flex-row items-center gap-4 pb-4"
                                            >
                                                <Form.Item className="mb-0 w-[20%]">
                                                    <Select
                                                        value={
                                                            item.timing ||
                                                            'before'
                                                        }
                                                        onChange={(val) =>
                                                            handlePaymentReminderChange(
                                                                'timing',
                                                                index,
                                                                val
                                                            )
                                                        }
                                                        options={getTimingOptions()}
                                                        placeholder="Select timing"
                                                    />
                                                </Form.Item>
                                                <Form.Item className="mb-0 w-[15%]">
                                                    <Select
                                                        value={item.value}
                                                        onChange={(val) =>
                                                            handlePaymentReminderChange(
                                                                'value',
                                                                index,
                                                                val
                                                            )
                                                        }
                                                        options={getDayOptions(
                                                            item.timing
                                                        )}
                                                        placeholder="Select days"
                                                        disabled={!item.timing}
                                                    />
                                                </Form.Item>
                                                {index !== 0 && (
                                                    <DeleteOutlined
                                                        className="cursor-pointer text-3xl text-red-500"
                                                        onClick={() =>
                                                            handleRemovePaymentReminder(
                                                                index
                                                            )
                                                        }
                                                    />
                                                )}
                                            </div>
                                        ))}

                                        <Button
                                            onClick={() =>
                                                setReminderList((prev) => [
                                                    ...prev,
                                                    {
                                                        value: '1',
                                                        timing: 'before',
                                                        disabled: false,
                                                    },
                                                ])
                                            }
                                            className="w-[8%] rounded-lg border border-[#1a3353] py-3 text-xl text-[#1a3353]"
                                        >
                                            Add More
                                        </Button>
                                    </>
                                )}
                            </Form.Item>
                        </Form>

                        {/* Renewal Reminder with Dynamic Select */}
                        <Form layout="vertical" className="flex flex-col">
                            <Form.Item label="Renewal reminder">
                                <div className="mb-10 flex flex-row items-center gap-10">
                                    <Switch
                                        checked={showRenewalReminder}
                                        onChange={setShowRenewalReminder}
                                    />
                                    <p className="text-[#3a3353]">
                                        Auto-renewal client of upcoming expiry
                                    </p>
                                </div>

                                {showRenewalReminder && (
                                    <>
                                        <p className="mb-2 text-[13px] font-medium text-[#1a3353]">
                                            When to send reminder?
                                        </p>
                                        {renewalReminderList.map(
                                            (item, index) => (
                                                <div
                                                    key={index}
                                                    className="flex w-full flex-row items-center gap-4 pb-4"
                                                >
                                                    <Form.Item
                                                        className="mb-0 w-[20%]"
                                                        validateStatus={
                                                            errorMessages[
                                                                `${item.timing}_${index}`
                                                            ]
                                                                ? 'error'
                                                                : undefined
                                                        }
                                                    >
                                                        <Select
                                                            value={
                                                                item.timing ||
                                                                'before'
                                                            }
                                                            onChange={(val) =>
                                                                handleRenewalTimingChange(
                                                                    val as
                                                                        | 'before'
                                                                        | 'after',
                                                                    index
                                                                )
                                                            }
                                                            options={getTimingOptions()}
                                                            placeholder="Select timing"
                                                        />
                                                    </Form.Item>
                                                    <Form.Item
                                                        className="mb-0 w-[15%]"
                                                        validateStatus={
                                                            errorMessages[
                                                                `${item.timing}_${index}`
                                                            ]
                                                                ? 'error'
                                                                : undefined
                                                        }
                                                        help={
                                                            errorMessages[
                                                                `${item.timing}_${index}`
                                                            ]
                                                        }
                                                    >
                                                        <Select
                                                            value={item.value}
                                                            onChange={(val) =>
                                                                handleRenewalDayChange(
                                                                    val,
                                                                    index,
                                                                    (item.timing ||
                                                                        'before') as
                                                                        | 'before'
                                                                        | 'after'
                                                                )
                                                            }
                                                            options={getDayOptions(
                                                                item.timing
                                                            )}
                                                            placeholder="Select days"
                                                            disabled={
                                                                !item.timing
                                                            }
                                                        />
                                                    </Form.Item>
                                                    {index !== 0 && (
                                                        <DeleteOutlined
                                                            className="cursor-pointer text-3xl text-red-500"
                                                            onClick={() =>
                                                                handleRemoveRenewalReminder(
                                                                    index
                                                                )
                                                            }
                                                        />
                                                    )}
                                                </div>
                                            )
                                        )}

                                        <Button
                                            onClick={() =>
                                                setRenewalReminderList(
                                                    (prev) => [
                                                        ...prev,
                                                        {
                                                            value: '1',
                                                            timing: 'before',
                                                            disabled: false,
                                                        },
                                                    ]
                                                )
                                            }
                                            className="w-[8%] rounded-lg border border-[#1a3353] py-3 text-xl text-[#1a3353]"
                                        >
                                            Add More
                                        </Button>
                                    </>
                                )}
                            </Form.Item>
                        </Form>

                        {/* Limited Session with Dynamic Select */}
                        <Form layout="vertical" className="flex flex-col">
                            <Form.Item label="Limited session reminder">
                                <p className=" text-2xl text-[#455560]">
                                    Send reminder to client when they have
                                    limited session left
                                </p>
                                <div className="mb-10 mt-5 flex flex-row items-center gap-10">
                                    <Switch
                                        checked={showLimitedSession}
                                        onChange={setShowLimitedSession}
                                    />
                                    <p className="text-[#3a3353]">
                                        Auto-renewal client of upcoming expiry
                                    </p>
                                </div>

                                {showLimitedSession && (
                                    <>
                                        <p className="mb-2 text-[13px] font-medium text-[#1a3353]">
                                            When to send reminder?
                                        </p>
                                        {limitedSessionList.map(
                                            (item, index) => (
                                                <div
                                                    key={index}
                                                    className="flex w-full flex-row items-center gap-4 pb-4"
                                                >
                                                    <Form.Item className="mb-0 w-[20%]">
                                                        <Select
                                                            value={
                                                                item.timing ||
                                                                'before'
                                                            }
                                                            onChange={(val) =>
                                                                handleLimitedSessionChange(
                                                                    'timing',
                                                                    index,
                                                                    val
                                                                )
                                                            }
                                                            options={getTimingOptions()}
                                                            placeholder="Select timing"
                                                        />
                                                    </Form.Item>
                                                    <Form.Item className="mb-0 w-[15%]">
                                                        <Select
                                                            value={item.value}
                                                            onChange={(val) =>
                                                                handleLimitedSessionChange(
                                                                    'value',
                                                                    index,
                                                                    val
                                                                )
                                                            }
                                                            options={getDayOptions(
                                                                item.timing
                                                            )}
                                                            placeholder="Select days"
                                                            disabled={
                                                                !item.timing
                                                            }
                                                        />
                                                    </Form.Item>
                                                    {index !== 0 && (
                                                        <DeleteOutlined
                                                            className="cursor-pointer text-3xl text-red-500"
                                                            onClick={() =>
                                                                handleRemoveLimitedSessionReminder(
                                                                    index
                                                                )
                                                            }
                                                        />
                                                    )}
                                                </div>
                                            )
                                        )}

                                        <Button
                                            onClick={() =>
                                                setLimitedSessionList(
                                                    (prev) => [
                                                        ...prev,
                                                        {
                                                            value: '1',
                                                            timing: 'before',
                                                            disabled: false,
                                                        },
                                                    ]
                                                )
                                            }
                                            className="w-[8%] rounded-lg border border-[#1a3353] py-3 text-xl text-[#1a3353]"
                                        >
                                            Add More
                                        </Button>
                                    </>
                                )}
                            </Form.Item>
                        </Form>

                        <div className="flex flex-row justify-end gap-4 pt-20">
                            <Button
                                onClick={goBack}
                                className="w-[10%] rounded-lg border border-[#1a3353] py-2 text-xl text-[#1a3353]"
                            >
                                Cancel
                            </Button>

                            <Button
                                onClick={saveSettings}
                                loading={submitLoader}
                                className="w-[10%] rounded-lg bg-purpleLight py-2 text-xl text-white"
                                htmlType="submit"
                            >
                                Save
                            </Button>
                        </div>
                    </div>
                </ConfigProvider>
            </div>
        </>
    );
};

export default TransactionalMessage;
