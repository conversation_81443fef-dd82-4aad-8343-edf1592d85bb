import { Modal, DatePicker, Form, FormProps, Button, Input } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { cancelOrRefundOrder } from '~/redux/actions/purchased-action';

const { RangePicker } = DatePicker;
const { TextArea } = Input;

interface PaymentStatusModal {
    visible: boolean;
    onClose: () => void;
    paymentStatus: string;
    invoiceId: string;
}

const PaymentStatusModal: React.FC<PaymentStatusModal> = ({
    visible,
    onClose,
    paymentStatus,
    invoiceId,
}) => {
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const onFinish: FormProps['onFinish'] = async (values) => {
        const payload = {
            ...values,
            paymentStatus,
            invoiceId,
        };

        try {
            const response = await dispatch(
                cancelOrRefundOrder(payload)
            ).unwrap();
            onClose();
        } catch (error) {
            console.error('Error while submitting:', error);
        }
    };

    return (
        <Modal
            title={
                <div className="border-b pb-2  font-semibold text-[#1A3353]">
                    {`${
                        paymentStatus === 'refund'
                            ? 'Reason for refund'
                            : 'Reason for cancel'
                    } `}
                </div>
            }
            open={visible}
            onCancel={onClose}
            footer={null}
            centered
            className="lg:w-[55%]"
            style={{ top: 10 }}
        >
            <div className="">
                <Form
                    layout="vertical"
                    className="w-full space-y-4"
                    form={form}
                    onFinish={onFinish}
                >
                    <Form.Item
                        // label={
                        //     <span className=" font-medium text-[#1A3353]">
                        //         Reason
                        //     </span>
                        // }
                        name="reason"
                        rules={[
                            {
                                required: true,
                                message: 'Please enter a reason',
                            },
                        ]}
                        className="py-5"
                    >
                        <TextArea
                            autoSize={{ minRows: 4, maxRows: 5 }}
                            className="rounded-md border p-2"
                        />
                    </Form.Item>
                    <div className="flex justify-end space-x-4">
                        <Button
                            className="rounded-md border border-gray-500 px-6 py-2"
                            onClick={onClose}
                        >
                            Cancel
                        </Button>
                        <Button
                            className="rounded-md bg-[#B79DF9] px-6 py-2 text-white"
                            type="primary"
                            htmlType="submit"
                            onClick={() => console.log('sdlfhsdkfhksdh')}
                        >
                            Save
                        </Button>
                    </div>
                </Form>
            </div>
        </Modal>
    );
};

export default PaymentStatusModal;
