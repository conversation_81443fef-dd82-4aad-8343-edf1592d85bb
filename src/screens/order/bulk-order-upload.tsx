import {
    DeleteFilled,
    MinusOutlined,
    PlusCircleFilled,
    PlusOutlined,
} from '@ant-design/icons';
import { Button, DatePicker, Input, Select, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import CustomTable from '~/components/common/customTable';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { customerListV1 } from '~/redux/actions/customer-action';
import { uploadBulkOrder } from '~/redux/actions/merchandise/product-action';
import { AddedPaymentMethodList } from '~/redux/actions/payment-method.action';
import { PricingListByActiveStatus } from '~/redux/actions/pricing-actions';
import Alertify from '~/services/alertify';

interface Product {
    id: string;
    item: string;
    qty: number;
    startDate: string;
    endDate: string;
    packagePrice: number;
    amountCollected: number;
    paymentMethod: string;
    unitPrice?: number; // price per unit (auto from item)
    manualPackagePrice?: boolean; // user overrode total
    manualAmountCollected?: boolean; // user overrode collected
    itemName?: string;
    expiredInDays?: number | null;
    durationUnit?: 'days' | 'weeks' | 'months';
    paymentMethodId?: string;
    paymentMethodName?: string;
    flatDiscount?: number; // NEW
    lockStartDate?: boolean;
}

interface Client {
    id: string;
    name: string;
    clientUserId?: string;
    products: Product[];
}
type DataType = Product;

const BulkOrderUpload = () => {
    const dispatch = useAppDispatch();
    const { facilityList } = useSelector((state: any) => state.facility_store);

    // Fixed state management
    const [selectedLocation, setSelectedLocation] = useState<string>('');
    const [selectedFacilityId, setSelectedFacilityId] = useState<string>(''); // Added this state
    const [loader, startLoader, endLoader] = useLoader();
    const [facilitySearch, setFacilitySearch] = useState<string>('');
    const [paymentMethods, setPaymentMethods] = useState<
        { value: string; label: string }[]
    >([]);
    const [pmLoading, setPmLoading] = useState(false);
    const [_, setLocation] = useLocation();

    // At top-level (near other hooks)
    const DEFAULT_FACILITY_KEY = 'defaultFacilityId';

    const FacilityOptions = facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    // Fixed fetchClientList function
    const fetchClientList = async (searchText = '', page = 1) => {
        // Use selectedFacilityId instead of undefined facilityId
        if (!selectedFacilityId || selectedFacilityId.length === 0) return [];

        try {
            const response = await dispatch(
                customerListV1({
                    page,
                    pageSize: 10,
                    facilityIds: Array.isArray(selectedFacilityId)
                        ? selectedFacilityId
                        : [selectedFacilityId],
                    search: searchText,
                    isActive: true,
                })
            ).unwrap();

            return (
                response?.data?.data?.list?.map((item: any) => ({
                    value: item.userId,
                    label: `${item?.firstName || ''} ${
                        item?.lastName || ''
                    }`.trim(),
                    id: item._id,
                    phone: item?.mobile || '',
                    email: item?.email || '',
                    age: item?.age || '',
                    unpaidSessions: item?.unpaidSessions || '',
                    proficiencyLevel: item.proficiencyLevel || '',
                    lastVisited: item?.lastVisited || '',
                    photo: item.photo || undefined,
                    missingRequiredPolicies:
                        item?.missingRequiredPolicies || [],
                    visitsLast3Months: item?.visitsLast3Months
                        ? item?.visitsLast3Months
                        : 0,
                })) || []
            );
        } catch (error) {
            console.error('Error fetching client list:', error);
            return [];
        }
    };

    const [clients, setClients] = useState<Client[]>([
        {
            id: '1',
            name: '',
            products: [
                {
                    id: '1',
                    item: '',
                    qty: 1,
                    startDate: '',
                    endDate: '',
                    packagePrice: 0.0,
                    amountCollected: 0.0,
                    paymentMethod: '',
                    flatDiscount: 0,
                },
            ],
        },
    ]);

    const round2 = (n: number) => Math.round((n + Number.EPSILON) * 100) / 100;

    const findCashMethod = (methods: { value: string; label: string }[]) =>
        methods.find(
            (m) =>
                String(m.label ?? '')
                    .trim()
                    .toLowerCase() === 'cash'
        ) ?? null;

    const getCashDefaults = (methods: { value: string; label: string }[]) => {
        const cash = findCashMethod(methods);
        return cash
            ? {
                  paymentMethodId: String(cash.value),
                  paymentMethodName: String(cash.label),
                  paymentMethod: String(cash.label), // keep label for payload & UI
              }
            : {
                  paymentMethodId: '',
                  paymentMethodName: '',
                  paymentMethod: '',
              };
    };

    useEffect(() => {
        let alive = true;

        const load = async () => {
            if (!selectedFacilityId) {
                setPaymentMethods([]);
                return;
            }
            setPmLoading(true);
            try {
                const res = await dispatch(
                    AddedPaymentMethodList({ facilityId: selectedFacilityId })
                ).unwrap();

                const list =
                    res?.data?.data?.list ?? res?.data?.data ?? res?.data ?? [];

                const options = (list || [])
                    .filter((m: any) => m?.isActive !== false)
                    .map((m: any) => ({
                        value: m?._id ?? m?.id ?? m?.code ?? m?.name,
                        label:
                            m?.name ??
                            m?.methodName ??
                            m?.displayName ??
                            m?.code ??
                            'Payment Method',
                    }));

                if (!alive) return;

                setPaymentMethods(options);

                // ⬇️ Prefill "Cash" for any product with no payment method yet (first row included)
                const cash = findCashMethod(options);
                if (cash) {
                    setClients((prev) =>
                        prev.map((c) => ({
                            ...c,
                            products: c.products.map((p) =>
                                p.paymentMethodId || p.paymentMethod
                                    ? p
                                    : {
                                          ...p,
                                          paymentMethodId: String(cash.value),
                                          paymentMethodName: String(cash.label),
                                          paymentMethod: String(cash.label),
                                      }
                            ),
                        }))
                    );
                }
            } catch (e) {
                console.error('Failed to load payment methods:', e);
                if (alive) setPaymentMethods([]);
            } finally {
                if (alive) setPmLoading(false);
            }
        };

        load();
        return () => {
            alive = false;
        };
    }, [selectedFacilityId, dispatch]);

    // Handler for facility selection

    const handleFacilityChange = (value: string) => {
        setSelectedFacilityId(value);
        setSelectedLocation(value);

        // remember selection for next visit
        if (typeof window !== 'undefined') {
            localStorage.setItem(DEFAULT_FACILITY_KEY, value);
        }

        setClients((prev) =>
            prev.map((c) => ({
                ...c,
                name: '',
                products: c.products.map((p) => ({
                    ...p,
                    paymentMethod: '',
                    item: '',
                    itemName: '',
                    unitPrice: undefined,
                    packagePrice: 0,
                    amountCollected: 0,
                    manualPackagePrice: false,
                    manualAmountCollected: false,
                    startDate: '',
                    endDate: '',
                    expiredInDays: undefined,
                    durationUnit: undefined,
                })),
            }))
        );
    };

    useEffect(() => {
        if (!facilityList?.length) return;

        // previously chosen
        const persisted =
            typeof window !== 'undefined'
                ? localStorage.getItem(DEFAULT_FACILITY_KEY)
                : null;

        const initial =
            facilityList.find((f: any) => f._id === persisted)?._id ??
            facilityList[0]._id; // fallback to first

        if (!selectedFacilityId) {
            // reuse your same logic (also resets rows, etc.)
            handleFacilityChange(initial);
        }
    }, [facilityList]);

    const addNewClient = () => {
        const pmDefaults = getCashDefaults(paymentMethods);

        const newClient: Client = {
            id: Date.now().toString(),
            name: '',
            products: [
                {
                    id: Date.now().toString(),
                    item: '',
                    qty: 1,
                    startDate: '',
                    endDate: '',
                    packagePrice: 0.0,
                    amountCollected: 0.0,
                    ...pmDefaults, // ⬅️ prefill Cash if available
                },
            ],
        };
        setClients((prev) => [...prev, newClient]);
    };

    const addProductToClient = (clientId: string) => {
        const pmDefaults = getCashDefaults(paymentMethods);

        setClients((prev) =>
            prev.map((client) => {
                if (client.id !== clientId) return client;

                const firstStart = client.products[0]?.startDate || ''; // copy from first row

                const newProduct: Product = {
                    id: Date.now().toString(),
                    item: '',
                    qty: 1,
                    startDate: firstStart, // ⬅️ copy
                    endDate: '', // computed later after item select
                    packagePrice: 0.0,
                    amountCollected: 0.0,
                    lockStartDate: true, // ⬅️ lock editing
                    ...pmDefaults,
                };

                return {
                    ...client,
                    products: [...client.products, newProduct],
                };
            })
        );
    };

    const syncStartDateForClient = (
        clientId: string,
        newStartDDMMYY: string
    ) => {
        setClients((prev) =>
            prev.map((c) => {
                if (c.id !== clientId) return c;
                const start = dayjs(newStartDDMMYY, 'DD-MM-YY');
                return {
                    ...c,
                    products: c.products.map((p, idx) => {
                        if (!p.lockStartDate || idx === 0) return p; // skip first row
                        // update start date and recompute end date if we know duration
                        const updated: Product = {
                            ...p,
                            startDate: newStartDDMMYY,
                        };
                        const amount = Number(p.expiredInDays ?? 0);
                        const unit = (p.durationUnit as any) || 'days';
                        if (amount > 0) {
                            // eslint-disable-next-line no-use-before-define
                            const end = computeEndDate(start, amount, unit);
                            updated.endDate = end.format('DD-MM-YY');
                        } else {
                            updated.endDate = '';
                        }
                        return updated;
                    }),
                };
            })
        );
    };

    const removeProduct = (clientId: string, productId: string) => {
        setClients(
            clients
                .map((client) => {
                    if (client.id === clientId) {
                        const updatedProducts = client.products.filter(
                            (p) => p.id !== productId
                        );
                        return { ...client, products: updatedProducts };
                    }
                    return client;
                })
                .filter((client) => client.products.length > 0)
        );
    };

    const updateQuantity = (
        clientId: string,
        productId: string,
        change: number
    ) => {
        setClients((prev) =>
            prev.map((client) => {
                if (client.id !== clientId) return client;
                return {
                    ...client,
                    products: client.products.map((p) => {
                        if (p.id !== productId) return p;
                        const newQty = Math.max(1, (p.qty ?? 1) + change);
                        const unit = Number(p.unitPrice ?? 0);
                        const newTotal = p.manualPackagePrice
                            ? p.packagePrice
                            : round2(unit * newQty);
                        const newCollected = p.manualAmountCollected
                            ? p.amountCollected
                            : Math.min(newTotal, p.amountCollected); // ⬅️ Cap at new total
                        return {
                            ...p,
                            qty: newQty,
                            packagePrice: newTotal,
                            amountCollected: newCollected,
                        };
                    }),
                };
            })
        );
    };

    const fetchItemsList = async (searchText = '', page = 1) => {
        try {
            const res = await dispatch(
                PricingListByActiveStatus({
                    page,
                    pageSize: 20,
                    search: searchText,
                })
            ).unwrap();

            const list = res?.data?.data?.list ?? [];

            return list.map((it: any) => ({
                value: it?._id,
                label: it?.name ?? `#${String(it?._id).slice(-6)}`,
                price: it?.finalPrice ?? it?.price ?? 0,
                expiredInDays: it?.expiredInDays ?? null,
                durationUnit: it?.durationUnit ?? 'days', // 'days' | 'weeks' | 'months'
                full: it,
            }));
        } catch (e) {
            console.error('Error fetching items:', e);
            return [];
        }
    };

    const computeEndDate = (
        start: Dayjs,
        amount: number,
        unit: 'days' | 'weeks' | 'months'
    ): Dayjs => {
        // end date is inclusive => add X unit then subtract 1 day
        if (unit === 'months')
            return start.add(amount, 'month').subtract(1, 'day');
        if (unit === 'weeks')
            return start.add(amount, 'week').subtract(1, 'day');
        return start.add(amount, 'day').subtract(1, 'day');
    };

    const computeUnitPriceFromItem = (it: any) => {
        // Prefer finalPrice if present, else compute from price+tax when exclusive
        if (typeof it?.finalPrice === 'number') return Number(it.finalPrice);
        const price = Number(it?.price ?? 0);
        const tax = Number(it?.tax ?? 0);
        const inclusive = !!it?.isInclusiveofGst;
        return inclusive ? price : round2(price + (price * tax) / 100);
    };

    const updateProduct = (
        clientId: string,
        productId: string,
        field: keyof Product,
        value: any
    ) => {
        setClients((prev) =>
            prev.map((client) =>
                client.id !== clientId
                    ? client
                    : {
                          ...client,
                          products: client.products.map((product) =>
                              product.id === productId
                                  ? { ...product, [field]: value }
                                  : product
                          ),
                      }
            )
        );
    };

    const updateClientName = (rowClientId: string, patch: Partial<Client>) => {
        setClients((prev) =>
            prev.map((c) => (c.id === rowClientId ? { ...c, ...patch } : c))
        );
    };

    const toZStart = (ddmmyy: string) => {
        if (!ddmmyy) return null;
        const d = dayjs(ddmmyy, 'DD-MM-YY');
        return `${d.format('YYYY-MM-DD')}T00:00:00.000Z`;
    };

    const toZEnd = (ddmmyy: string) => {
        if (!ddmmyy) return null;
        const d = dayjs(ddmmyy, 'DD-MM-YY');
        // End-of-day inclusive
        return `${d.format('YYYY-MM-DD')}T23:59:59.999Z`;
    };

    // Validation helper function
    const validateAmountCollected = (
        amountCollected: number,
        packagePrice: number
    ): boolean => {
        return amountCollected <= packagePrice;
    };

    // Helper function to get validation style
    const getAmountCollectedStyle = (
        amountCollected: number,
        packagePrice: number
    ) => {
        const isValid = validateAmountCollected(amountCollected, packagePrice);
        return {
            border: `1px solid ${isValid ? '#d9d9d9' : '#ff4d4f'}`,
            borderRadius: 6,
            padding: '4px 11px',
            width: '100%',
            fontSize: 14,
            backgroundColor: isValid ? 'white' : '#fff2f0',
        };
    };

    // Ant Design Table columns configuration
    const getColumns = (clientId: string): ColumnsType<DataType> => [
        {
            title: 'Client Name',
            dataIndex: 'clientName',
            key: 'clientName',
            width: 160,
            render: (_: any, __: Product, index: number) => {
                if (index !== 0) return null; // only on first row
                return (
                    <div
                        className="ant-select ant-select-single ant-select-show-arrow"
                        style={{ width: 160 }}
                    >
                        <InfiniteScrollSelect
                            className="w-[100%] rounded-lg border border-[#d1d5db] bg-white"
                            fetchOptions={fetchClientList}
                            onChange={(value, option) => {
                                updateClientName(clientId, {
                                    name: option.label,
                                    clientUserId: value,
                                });
                            }}
                            placeholder={
                                selectedFacilityId
                                    ? 'Select Client'
                                    : 'Please select facility first'
                            }
                            disabled={!selectedFacilityId}
                            key={selectedFacilityId}
                        />
                    </div>
                );
            },
        },
        {
            title: 'Item',
            dataIndex: 'item',
            key: 'item',
            width: 200,
            render: (_: any, record: Product) => (
                <div
                    className="ant-select ant-select-single ant-select-show-search ant-select-show-arrow"
                    style={{ width: 200 }}
                >
                    <InfiniteScrollSelect
                        className="w-[100%] rounded-lg border border-[#d1d5db] bg-white"
                        fetchOptions={fetchItemsList}
                        value={record.item || undefined}
                        placeholder={
                            selectedFacilityId
                                ? 'Select an item'
                                : 'Select facility first'
                        }
                        disabled={!selectedFacilityId}
                        onChange={(value: string, option: any) => {
                            updateProduct(clientId, record.id, 'item', value);
                            updateProduct(
                                clientId,
                                record.id,
                                'itemName',
                                option?.label ?? ''
                            );
                            const unit = computeUnitPriceFromItem(option?.full);
                            const total = round2(unit * record.qty);
                            updateProduct(
                                clientId,
                                record.id,
                                'unitPrice',
                                unit
                            );
                            updateProduct(
                                clientId,
                                record.id,
                                'packagePrice',
                                total
                            );
                            updateProduct(
                                clientId,
                                record.id,
                                'manualPackagePrice',
                                false
                            );
                            if (!record.manualAmountCollected) {
                                // Cap amount collected at the new total
                                const cappedAmount = Math.min(
                                    total,
                                    record.amountCollected || total
                                );
                                updateProduct(
                                    clientId,
                                    record.id,
                                    'amountCollected',
                                    cappedAmount
                                );
                            }
                            const expiredInDays = option?.expiredInDays ?? null;
                            const durationUnit = option?.durationUnit ?? 'days';
                            updateProduct(
                                clientId,
                                record.id,
                                'expiredInDays',
                                expiredInDays
                            );
                            updateProduct(
                                clientId,
                                record.id,
                                'durationUnit',
                                durationUnit
                            );
                            if (record.startDate && expiredInDays) {
                                const start = dayjs(
                                    record.startDate,
                                    'DD-MM-YY'
                                );
                                const end = computeEndDate(
                                    start,
                                    Number(expiredInDays),
                                    durationUnit
                                );
                                updateProduct(
                                    clientId,
                                    record.id,
                                    'endDate',
                                    end.format('DD-MM-YY')
                                );
                            } else {
                                updateProduct(
                                    clientId,
                                    record.id,
                                    'endDate',
                                    ''
                                );
                            }
                        }}
                    />
                </div>
            ),
        },
        {
            title: 'Qty',
            dataIndex: 'qty',
            key: 'qty',
            width: 50,
            render: (_: any, record: Product) => (
                <div className="flex items-center justify-center ">
                    <Button
                        className="px-2"
                        onClick={() => updateQuantity(clientId, record.id, -1)}
                    >
                        <MinusOutlined style={{ fontSize: '10px' }} />
                    </Button>
                    <span className="px-2">{record.qty}</span>
                    <Button
                        className="px-2"
                        onClick={() => updateQuantity(clientId, record.id, 1)}
                    >
                        <PlusOutlined style={{ fontSize: '10px' }} />
                    </Button>
                </div>
            ),
        },
        {
            title: 'Start Date',
            dataIndex: 'startDate',
            key: 'startDate',
            width: 100,
            render: (_: any, record: Product, rowIndex: number) => {
                const value = record.startDate
                    ? dayjs(record.startDate, 'DD-MM-YY')
                    : undefined;
                return (
                    <div
                        className="ant-picker ant-picker-outlined"
                        style={{ width: 100 }}
                    >
                        <DatePicker
                            format="DD-MM-YY"
                            value={value}
                            disabled={!record.item || record.lockStartDate} // ⬅️ lock if needed
                            onChange={(date) => {
                                const start = date ?? null;
                                const startStr = start
                                    ? start.format('DD-MM-YY')
                                    : '';

                                updateProduct(
                                    clientId,
                                    record.id,
                                    'startDate',
                                    startStr
                                );

                                const amount = Number(
                                    record.expiredInDays ?? 0
                                );
                                const unit =
                                    (record.durationUnit as any) || 'days';

                                if (start && amount > 0) {
                                    const end = computeEndDate(
                                        start,
                                        amount,
                                        unit
                                    );
                                    updateProduct(
                                        clientId,
                                        record.id,
                                        'endDate',
                                        end.format('DD-MM-YY')
                                    );
                                } else {
                                    updateProduct(
                                        clientId,
                                        record.id,
                                        'endDate',
                                        ''
                                    );
                                }

                                // If this is the FIRST row, propagate to locked rows
                                if (rowIndex === 0 && startStr) {
                                    syncStartDateForClient(clientId, startStr);
                                }
                            }}
                        />
                    </div>
                );
            },
        },

        {
            title: 'End Date',
            dataIndex: 'endDate',
            key: 'endDate',
            width: 100,
            render: (_: any, record: Product) => {
                const value = record.endDate
                    ? dayjs(record.endDate, 'DD-MM-YY')
                    : undefined;
                return (
                    <div
                        className="ant-picker ant-picker-outlined"
                        style={{ width: 100 }}
                    >
                        <DatePicker
                            format="DD-MM-YY"
                            value={value}
                            disabled
                            allowClear={false}
                            placeholder="End date"
                        />
                    </div>
                );
            },
        },
        {
            title: 'Sub Total',
            dataIndex: 'packagePrice',
            key: 'packagePrice',
            width: 120,
            render: (_: any, record: Product) => {
                const computedTotal = round2(
                    (record.unitPrice ?? 0) * (record.qty ?? 1)
                );
                const value = record.manualPackagePrice
                    ? record.packagePrice
                    : computedTotal;
                return (
                    <div
                        className="ant-input-number ant-input-number-outlined"
                        style={{ width: '100%' }}
                    >
                        <Input
                            type="number"
                            value={Number.isFinite(value) ? value : 0}
                            disabled
                            onChange={(e) => {
                                const v = parseFloat(e.target.value) || 0;
                                updateProduct(
                                    clientId,
                                    record.id,
                                    'packagePrice',
                                    v
                                );
                                updateProduct(
                                    clientId,
                                    record.id,
                                    'manualPackagePrice',
                                    true
                                );
                                const qty = record.qty || 1;
                                updateProduct(
                                    clientId,
                                    record.id,
                                    'unitPrice',
                                    qty ? round2(v / qty) : v
                                );
                                // Cap amount collected when package price changes
                                if (record.amountCollected > v) {
                                    updateProduct(
                                        clientId,
                                        record.id,
                                        'amountCollected',
                                        v
                                    );
                                }
                            }}
                            onBlur={() => {
                                if (
                                    !record.manualPackagePrice &&
                                    value !== computedTotal
                                ) {
                                    updateProduct(
                                        clientId,
                                        record.id,
                                        'packagePrice',
                                        computedTotal
                                    );
                                }
                            }}
                            className="ant-input-number-input"
                            style={{
                                border: '1px solid #d9d9d9',
                                borderRadius: 6,
                                padding: '4px 11px',
                                width: '100%',
                                fontSize: 14,
                            }}
                        />
                    </div>
                );
            },
        },
        {
            title: 'Amount Collected',
            dataIndex: 'amountCollected',
            key: 'amountCollected',
            width: 120,
            render: (_: any, record: Product) => {
                const isValidAmount = validateAmountCollected(
                    record.amountCollected,
                    record.packagePrice
                );
                return (
                    <div
                        className="ant-input-affix-wrapper ant-input-outlined"
                        style={{ width: '100%' }}
                    >
                        <input
                            type="number"
                            value={record.amountCollected}
                            onChange={(e) => {
                                const v = parseFloat(e.target.value) || 0;
                                // Cap the amount at the package price
                                const cappedAmount = Math.min(
                                    v,
                                    record.packagePrice
                                );
                                updateProduct(
                                    clientId,
                                    record.id,
                                    'amountCollected',
                                    cappedAmount
                                );
                                updateProduct(
                                    clientId,
                                    record.id,
                                    'manualAmountCollected',
                                    true
                                );

                                // Show warning if user tries to enter more than package price
                                if (v > record.packagePrice) {
                                    Alertify.default(
                                        `Amount collected cannot exceed sub total (${record.packagePrice})`
                                    );
                                }
                            }}
                            placeholder="e.g. 2000.00"
                            className="ant-input"
                            style={getAmountCollectedStyle(
                                record.amountCollected,
                                record.packagePrice
                            )}
                            max={record.packagePrice}
                        />
                        {!isValidAmount && (
                            <div className="absolute text-base text-red-500">
                                Cannot exceed sub total
                            </div>
                        )}
                    </div>
                );
            },
        },
        {
            title: 'Payment Method',
            dataIndex: 'paymentMethod',
            key: 'paymentMethod',
            width: 140,
            render: (_: any, record: Product) => (
                <div
                    className="ant-select ant-select-single ant-select-show-arrow"
                    style={{ width: '100%' }}
                >
                    <Select
                        className="w-[100%]"
                        showSearch
                        key={selectedFacilityId}
                        value={record.paymentMethod || undefined}
                        options={paymentMethods}
                        loading={pmLoading}
                        disabled={
                            !selectedFacilityId ||
                            pmLoading ||
                            paymentMethods.length === 0
                        }
                        placeholder={
                            selectedFacilityId
                                ? pmLoading
                                    ? 'Loading methods…'
                                    : 'Select method'
                                : 'Select facility first'
                        }
                        filterOption={(input, option) =>
                            (option?.label as string)
                                ?.toLowerCase()
                                .includes(input.toLowerCase())
                        }
                        onChange={(val, option) => {
                            updateProduct(
                                clientId,
                                record.id,
                                'paymentMethodId',
                                String(val)
                            );
                            updateProduct(
                                clientId,
                                record.id,
                                'paymentMethodName',
                                String((option as any)?.label ?? '')
                            );
                            updateProduct(
                                clientId,
                                record.id,
                                'paymentMethod',
                                String((option as any)?.label ?? '')
                            );
                        }}
                    />
                </div>
            ),
        },
        {
            title: '',
            key: 'action',
            width: 30,
            render: (_: any, record: Product) => (
                <button
                    onClick={() => removeProduct(clientId, record.id)}
                    className="ant-btn ant-btn-text ant-btn-dangerous ant-btn-icon-only"
                    style={{
                        border: 'none',
                        background: 'none',
                        color: '#ff4d4f',
                        cursor: 'pointer',
                        padding: 4,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}
                >
                    <DeleteFilled size={16} />
                </button>
            ),
        },
    ];

    const buildBulkOrderPayload = () => {
        const orders = clients
            .map((c) => {
                // map valid product rows
                const items = c.products
                    .map((p) => ({
                        itemId: p.item, // selected item _id
                        quantity: p.qty || 1,
                        startDate: p.startDate ? toZStart(p.startDate) : null,
                        endDate: p.endDate ? toZEnd(p.endDate) : null,
                        packagePrice: Number(p.packagePrice || 0),
                        amountCollected: Number(p.amountCollected || 0),
                        paymentMethod:
                            p.paymentMethodName || p.paymentMethod || '', // label
                        paymentMethodId: p.paymentMethodId || '', // id
                    }))
                    // keep only fully-formed items
                    .filter(
                        (i) =>
                            i.itemId && i.quantity && i.startDate && i.endDate
                    );

                if (!c.clientUserId || items.length === 0) return null;
                return { clientId: c.clientUserId, items };
            })
            .filter(Boolean) as Array<{ clientId: string; items: any[] }>;

        return { orders };
    };

    const handleSubmitBulkOrder = async () => {
        const payload = buildBulkOrderPayload();

        if (!selectedFacilityId) {
            Alertify.error('Please select a facility.');
            return;
        }
        if (!payload.orders.length) {
            Alertify.error(
                'Please select at least one client with at least one valid item.'
            );
            return;
        }
        if (!paymentMethods.length) {
            Alertify.error('No payment methods available for this facility.');
            return;
        }
        const missingPayment = payload.orders.some((o) =>
            o.items.some((i) => !i.paymentMethodId && !i.paymentMethod)
        );
        if (missingPayment) {
            Alertify.error('Please select a payment method for each item.');
            return;
        }

        // Validate amount collected for all items
        const invalidAmounts = [];
        for (const client of clients) {
            for (const product of client.products) {
                if (
                    product.item &&
                    !validateAmountCollected(
                        product.amountCollected,
                        product.packagePrice
                    )
                ) {
                    invalidAmounts.push({
                        client: client.name,
                        item: product.itemName || product.item,
                        amountCollected: product.amountCollected,
                        packagePrice: product.packagePrice,
                    });
                }
            }
        }

        if (invalidAmounts.length > 0) {
            const errorMessage =
                invalidAmounts.length === 1
                    ? `Amount collected (${invalidAmounts[0].amountCollected}) cannot exceed sub total (${invalidAmounts[0].packagePrice}) for item "${invalidAmounts[0].item}"`
                    : `Amount collected cannot exceed sub total for ${invalidAmounts.length} items. Please check the highlighted fields.`;
            Alertify.error(errorMessage);
            return;
        }

        // Avoid duplicate submissions
        if (loader) return;

        try {
            startLoader(); // ⬅️ show spinner on Submit
            console.log('Bulk order payload:', payload);
            await dispatch(uploadBulkOrder(payload)).unwrap();
            Alertify.success('Bulk order submitted successfully!');
            setLocation('/order-listing');
        } catch (e) {
            console.error('Bulk order submission failed:', e);
            Alertify.error(
                'Failed to submit bulk order. Please check the console for details.'
            );
        } finally {
            endLoader(); // ⬅️ hide spinner
        }
    };

    return (
        <div className="min-h-screen">
            <CustomTable heading="Add Bulk Order" />

            {/* Facility Selection */}
            <div style={{ marginBottom: '24px' }}>
                <Select
                    showSearch
                    value={selectedFacilityId || undefined}
                    onChange={handleFacilityChange}
                    onSearch={(value) => setFacilitySearch(value)}
                    placeholder="Select facility"
                    style={{ width: '300px' }}
                    filterOption={(input, option) =>
                        String(option?.label ?? '')
                            .toLowerCase()
                            .includes(input.toLowerCase())
                    }
                    options={FacilityOptions}
                />
                {!selectedFacilityId && (
                    <div
                        style={{
                            color: '#ff4d4f',
                            fontSize: '12px',
                            marginTop: '4px',
                        }}
                    >
                        Please select a facility to load clients
                    </div>
                )}
            </div>

            <div className="space-y-6">
                {clients.map((client) => (
                    <>
                        <Table<DataType>
                            columns={getColumns(client.id)}
                            dataSource={client.products}
                            rowKey="id"
                            pagination={false}
                            size="middle"
                            scroll={{ x: 'max-content' }}
                        />
                        {client.products.length > 0 && (
                            <div style={{ padding: '16px' }}>
                                <Button
                                    onClick={() =>
                                        addProductToClient(client.id)
                                    }
                                    className="ant-btn ant-btn-link ant-btn-sm"
                                    style={{
                                        color: '#A77BDF',
                                        border: 'none',
                                        background: 'none',
                                        padding: '0',
                                        fontSize: '14px',
                                        fontWeight: 500,
                                    }}
                                >
                                    Add Item+
                                </Button>
                            </div>
                        )}
                    </>
                ))}

                <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button
                        onClick={addNewClient}
                        // loading={loader}
                        className="w-[130px] border-purpleLight bg-purpleLight text-xl text-white"
                    >
                        + Add New Order
                    </Button>
                </div>
            </div>

            <div className="flex justify-end py-10">
                <Button
                    htmlType="button"
                    className="me-6 w-[110px] border-[#1A3353] bg-[#fff] text-xl text-[#1A3353]"
                    onClick={() => setLocation('/order-listing')}
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleSubmitBulkOrder}
                    loading={loader}
                    className="w-[110px] border-purpleLight bg-purpleLight text-xl text-white"
                >
                    Submit
                </Button>
            </div>
        </div>
    );
};

export default BulkOrderUpload;
