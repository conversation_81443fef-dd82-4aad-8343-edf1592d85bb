import { CloudDownloadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Button, ConfigProvider, Modal, Radio, Table, Tooltip } from 'antd';
import Title from 'antd/es/typography/Title';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import {
    ConfimredButtonChip,
    PendingButtonChip,
    RejectedButtonChip,
} from '~/components/common/chip-component';
import {
    capitalizeFirstLetter,
    convertToWords,
    formatDate as formatCommonDate,
    goBack,
    toTitleCase,
} from '~/components/common/function';
import FullLoader from '~/components/library/loader/full-loader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import {
    DownloadInvoice,
    OrderInvoiceDetails,
} from '~/redux/actions/purchased-action';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { formatDate } from '~/utils/formatDate';
import { getQueryParams } from '~/utils/getQueryParams';

const columns: any = [
    {
        title: 'Item',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        render: (text: string, record: any) =>
            record.isReturn || record.isVoucherApply ? (
                <div className="flex items-center justify-between text-red-500">
                    <div>{text}</div>
                    <div className="rounded-md bg-red-100 px-4 py-2 text-red-500">
                        {record.isReturn ? 'Return' : 'Voucher'}
                    </div>
                </div>
            ) : (
                <div>{text}</div>
            ),
    },
    {
        title: 'Qty ',
        dataIndex: 'quantity',
        key: 'quantity',
        render: (text: string, record: any) => (
            <div
                className={
                    record.isReturn || record.isVoucherApply
                        ? 'text-red-500'
                        : ''
                }
            >
                {text}
            </div>
        ),
    },
    {
        title: 'Label',
        dataIndex: 'promotionLabel',
        key: 'label',
        render: (text: string, record: any) => (
            <div
                style={{
                    maxWidth: '150px', // You can adjust the width as needed
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                }}
                title={text} // shows full text on hover
            >
                {text}
            </div>
        ),
    },

    {
        title: 'Validity',
        dataIndex: '',
        key: 'expiredInDays',
        render: (text: any, record: any) => {
            return record?.expiredInDays ? (
                record.isReturn || record.isVoucherApply ? (
                    <div
                        className={
                            record.isReturn || record.isVoucherApply
                                ? 'text-red-500'
                                : ''
                        }
                    >
                        {record.expiredInDays} {record.durationUnit}
                    </div>
                ) : (
                    <div>
                        {record.expiredInDays} {record.durationUnit}
                    </div>
                )
            ) : (
                '--'
            );
        },
    },
    {
        title: 'Start Date',
        dataIndex: 'startDate',
        key: 'startDate',
        render: (text: any, record: any) =>
            text ? (
                record.isReturn || record.isVoucherApply ? (
                    <div
                        className={
                            record.isReturn || record.isVoucherApply
                                ? 'text-red-500'
                                : ''
                        }
                    >
                        --
                    </div>
                ) : (
                    <div>{formatDate(text)}</div>
                )
            ) : (
                '--'
            ),
    },
    {
        title: 'End Date',
        dataIndex: 'endDate',
        key: 'endDate',
        render: (text: any, record: any) =>
            text ? (
                record.isReturn || record.isVoucherApply ? (
                    <div
                        className={
                            record.isReturn || record.isVoucherApply
                                ? 'text-red-500'
                                : ''
                        }
                    >
                        --
                    </div>
                ) : (
                    <div>{formatDate(text)}</div>
                )
            ) : (
                '--'
            ),
    },

    {
        title: 'HSN / SAC',
        dataIndex: 'hsnOrSacCode',
        key: 'hsnOrSacCode',
        render: (text: string, record: any) => (
            <div
                className={
                    record.isReturn || record.isVoucherApply
                        ? 'text-red-500'
                        : ''
                }
            >
                {text}
            </div>
        ),
    },
    // {
    //     title: 'SKU',
    //     dataIndex: 'SKU',
    //     key: 'SKU',
    // },
    {
        title: 'Unit Price ',
        dataIndex: 'price',
        key: 'price',
        align: 'center',
        // render: (value: any) => value?.toFixed(2),
        render: (value: any, record: any) => {
            // const unitPrice = record?.isInclusiveofGst
            //     ? record?.finalPrice
            //     : record?.price;
            return (
                <div
                    className={
                        record.isReturn || record.isVoucherApply
                            ? 'text-red-500'
                            : ''
                    }
                >
                    {record.isReturn || record.isVoucherApply
                        ? -Math.trunc(value * 100) / 100
                        : Math.trunc(value * 100) / 100}
                </div>
            );
        },
    },
    {
        title: 'Discount ',
        dataIndex: 'discountValue',
        key: 'discountValue',
        align: 'center',
        render: (value: any, record: any) => (
            <div
                className={
                    record.isReturn || record.isVoucherApply
                        ? 'text-red-500'
                        : ''
                }
            >
                {Math.trunc((value || 0) * 100) / 100}
            </div>
        ),
    },
    {
        title: 'GST % ',
        dataIndex: 'tax',
        key: 'tax ',
        align: 'center',
        render: (value: any, record: any) => (
            <div
                className={
                    record.isReturn || record.isVoucherApply
                        ? 'text-red-500'
                        : ''
                }
            >
                {Math.trunc((value || 0) * 100) / 100}
            </div>
        ),
    },
    // {
    //     title: 'GST Value ',
    //     dataIndex: 'GSTValue',
    //     key: 'GSTValue',
    //     align: 'center',
    //     render: (text: string, record: any) => {
    //         const { price, discountValue, tax } = record;
    //         const taxableAmount = price - (discountValue || 0);
    //         const gstValue = (taxableAmount * (tax || 0)) / 100;
    //         return gstValue?.toFixed(2);
    //     },
    // },
    // {
    //     title: 'Final Price ',
    //     dataIndex: 'FinalPrice',
    //     key: 'FinalPrice',
    //     align: 'center',
    //     render: (text: string, record: any) => {
    //         const { price, discountValue, tax } = record;
    //         const taxableAmount = price - (discountValue || 0);
    //         const gstValue = (taxableAmount * (tax || 0)) / 100;
    //         const finalPrice = taxableAmount + gstValue;
    //         return finalPrice?.toFixed(2);
    //     },
    // },
];

const OrderDetail = () => {
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const orderId = params.orderId;
    const [loader, startLoader, endLoader] = useLoader(true);
    const [_, setLocation] = useLocation();
    const [invoiceModalOpen, setInvoiceModalOpen] = useState(false);
    const [selectedFormat, setSelectedFormat] = useState<'roll' | 'pdf'>('pdf');
    const [loading, setLoading] = useState(false);

    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        orderInvoiceDetail: state.purchased_store.orderInvoiceDetail,
    }));

    const dataSource = [
        ...(Array.isArray(store.orderInvoiceDetail?.returnPurchaseItems)
            ? store.orderInvoiceDetail.returnPurchaseItems.map((item: any) => ({
                  ...item,
                  isReturn: true,
                  name: item.packageName,
                  key:
                      item._id ||
                      `purchase_${Math.random().toString(36).substr(2, 9)}`,
              }))
            : []),
        ...(Array.isArray(store.orderInvoiceDetail?.appliedVoucherItems)
            ? store.orderInvoiceDetail.appliedVoucherItems.map((item: any) => ({
                  ...item,
                  isVoucherApply: true,
                  key: item._id,
              }))
            : []),
        ...(Array.isArray(store.orderInvoiceDetail?.purchaseItems)
            ? store.orderInvoiceDetail.purchaseItems.map((item: any) => ({
                  ...item,
                  key:
                      item._id ||
                      `purchase_${Math.random().toString(36).substr(2, 9)}`,
              }))
            : []),

        ...(Array.isArray(store.orderInvoiceDetail?.productItem)
            ? store.orderInvoiceDetail.productItem.map((item: any) => ({
                  ...item,
                  key:
                      item._id ||
                      `product_${Math.random().toString(36).substr(2, 9)}`,
              }))
            : []),
        ...(Array.isArray(store.orderInvoiceDetail?.customPackageItems)
            ? store.orderInvoiceDetail.customPackageItems.map((item: any) => ({
                  ...item,
                  key:
                      item._id ||
                      `package_${Math.random().toString(36).substr(2, 9)}`,
              }))
            : []),
    ];

    useEffect(() => {
        startLoader();
        if (orderId) {
            dispatch(OrderInvoiceDetails({ orderId: orderId }))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [orderId]);

    const handleDownloadInvoice = async () => {
        if (!orderId) return;

        try {
            const response = await dispatch(
                DownloadInvoice({ orderId })
            ).unwrap();

            if (!response) {
                throw new Error('No URL received from API.');
            }

            window.open(response, '_blank', 'noopener,noreferrer');

            // setTimeout(() => {
            //     const a = document.createElement("a");
            //     a.href = response;
            //     a.setAttribute("download", `Invoice-${orderId}.pdf`);
            //     a.setAttribute("target", "_blank"); // Ensures download doesn't affect current tab
            //     a.style.display = "none"; // Hide the element
            //     document.body.appendChild(a);
            //     a.click();
            //     document.body.removeChild(a);
            // }, 1000);
        } catch (error) {
            console.error('Error downloading invoice:', error);
        }
    };
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasOrdersActionPermission = useMemo(() => {
        if (store.role === RoleType.ORGANIZATION) {
            return true;
        } else {
            return all_permissions_for_role?.some((module: any) =>
                module.subjects?.some(
                    (subject: any) =>
                        subject.type === SUBJECT_TYPE.ORDER &&
                        subject.actions?.some((action: any) =>
                            action.permissions?.some(
                                (permission: any) =>
                                    permission.type ===
                                    PERMISSIONS_ENUM.PURCHASE_INVOICE_WRITE
                            )
                        )
                )
            );
        }
    }, [all_permissions_for_role]);
    console.log('hasOrdersActionPermission', hasOrdersActionPermission);

    const onCancel = () => {
        setInvoiceModalOpen(false);
    };

    // ~72mm is typical printable width on 80mm paper
    // 80mm POS-style receipt (
    const printInvoice80mm = (invoice: any) => {
        const INR = (n: number | null) =>
            n == null
                ? ''
                : `₹ ${(Math.round(Number(n) * 100) / 100).toFixed(2)}`;

        const dateIN = (d: string | number | Date) =>
            d
                ? new Date(d).toLocaleDateString('en-IN', {
                      year: 'numeric',
                      month: 'short',
                      day: '2-digit',
                  })
                : '--';

        // Payment method name
        const pmName =
            invoice?.updatedPaymentDetails?.[0]?.paymentMethodName ||
            invoice?.paymentDetails?.[0]?.paymentMethodName ||
            invoice?.paymentDetails?.[0]?.paymentMethod ||
            '-';

        // Check if same UT code for tax display
        const sameUT =
            invoice?.clientBillingDetails?.utCode ===
            invoice?.billingDetails?.utCode;

        // Combine all items from different arrays with proper classification
        const items = [
            // Purchase Items (positive values)
            ...(invoice?.purchaseItems || []).map(
                (item: {
                    name: any;
                    price: any;
                    finalPrice: any;
                    quantity: any;
                    discountValue: any;
                }) => ({
                    ...item,
                    itemType: 'purchase',
                    displayName: item?.name,
                    isNegative: false,
                    unitPrice: Number(item?.price || item?.finalPrice || 0),
                    quantity: item?.quantity || 1,
                    discountValue: Number(item?.discountValue || 0),
                })
            ),
            // Return Purchase Items (negative values)
            ...(invoice?.returnPurchaseItems || []).map(
                (item: { packageName: any; price: any; quantity: any }) => ({
                    ...item,
                    itemType: 'return',
                    displayName: `${item?.packageName} (Return)`,
                    isNegative: true,
                    unitPrice: Number(item?.price || 0),
                    quantity: item?.quantity || 1,
                    discountValue: 0, // Returns don't have additional item discounts
                })
            ),
            // Return Custom Package Items (negative values)
            ...(invoice?.returnCustomPackageItems || []).map(
                (item: {
                    name: any;
                    packageName: any;
                    price: any;
                    quantity: any;
                }) => ({
                    ...item,
                    itemType: 'returnCustom',
                    displayName: item?.name || item?.packageName,
                    isNegative: true,
                    unitPrice: Number(item?.price || 0),
                    quantity: item?.quantity || 1,
                    discountValue: 0,
                })
            ),
            // Applied Voucher Items (already accounted in voucherDiscount, but show for transparency)
            ...(invoice?.appliedVoucherItems || []).map(
                (item: { name: any; price: any; quantity: any }) => ({
                    ...item,
                    itemType: 'voucher',
                    displayName: `${item?.name} (Voucher Applied)`,
                    isNegative: true,
                    unitPrice: Number(item?.price || 0),
                    quantity: item?.quantity || 1,
                    discountValue: 0, // Vouchers don't have additional item discounts
                })
            ),
            // Custom Package Items (positive values)
            ...(invoice?.customPackageItems || []).map(
                (item: {
                    name: any;
                    price: any;
                    finalPrice: any;
                    quantity: any;
                    discountValue: any;
                }) => ({
                    ...item,
                    itemType: 'custom',
                    displayName: item?.name,
                    isNegative: false,
                    unitPrice: Number(item?.price || item?.finalPrice || 0),
                    quantity: item?.quantity || 1,
                    discountValue: Number(item?.discountValue || 0),
                })
            ),
            // Product Items (positive values)
            ...(invoice?.productItem || []).map(
                (item: {
                    name: any;
                    price: any;
                    quantity: any;
                    discountValue: any;
                }) => ({
                    ...item,
                    itemType: 'product',
                    displayName: item?.name,
                    isNegative: false,
                    unitPrice: Number(item?.price || 0),
                    quantity: item?.quantity || 1,
                    discountValue: Number(item?.discountValue || 0),
                })
            ),
        ];

        // Generate items HTML
        const generateItemsHTML = () => {
            return items
                .map((item) => {
                    const qty = item.quantity;
                    const rate = item.unitPrice;
                    const lineTotal = rate * qty;
                    const itemDiscount = item.discountValue;

                    // Apply negative sign for return items and vouchers
                    const displayRate = item.isNegative
                        ? -Math.abs(rate)
                        : rate;
                    const displayLineTotal = item.isNegative
                        ? -Math.abs(lineTotal)
                        : lineTotal;

                    return `
                <div class="row wrapline">
                    <div class="label" style="flex:1; text-align:left; ${
                        item.isNegative ? 'color: #dc2626;' : ''
                    }">${item.displayName || '-'}</div>
                </div>
                <div class="row tiny">
                    <div class="label" style="flex:1; text-align:left; ${
                        item.isNegative ? 'color: #dc2626;' : ''
                    }">
                        ${qty} @ ${INR(displayRate)}
                    </div>
                    <div class="val" style="text-align:right; ${
                        item.isNegative ? 'color: #dc2626;' : ''
                    }">
                        ${INR(displayLineTotal)}
                    </div>
                </div>
                ${
                    itemDiscount > 0 && !item.isNegative
                        ? `
                    <div class="row tiny">
                        <div class="label">Item Discount</div>
                        <div class="val">-${INR(itemDiscount)}</div>
                    </div>
                `
                        : ''
                }
                <div style="padding-top: 5px;"></div>
            `;
                })
                .join('');
        };

        // Company details
        const companyName =
            invoice?.billingDetails?.billingName || invoice?.facilityName || '';
        const address1 = invoice?.billingDetails?.addressLine1 || '';
        const address2 = invoice?.billingDetails?.addressLine2 || '';
        const city = invoice?.billingDetails?.cityName || '';
        const state = invoice?.billingDetails?.stateName || '';
        const pincode = invoice?.billingDetails?.postalCode || '';
        const email = invoice?.billingDetails?.email || '';
        const phone = invoice?.billingDetails?.phone || '';
        const gstin = (invoice?.billingDetails?.gstNumber || '').toUpperCase();

        const html = `
<!doctype html>
<html>
<head>
<meta charset="utf-8" />
<title>Receipt ${invoice?.invoiceNumber ?? ''}</title>
<style>
  @page { size: 80mm auto; margin: 0; }
  @media print { html, body { width: 80mm; } }
  html, body { margin: 0; width: 80mm; color: #000; }
  body { font: 12px/1.45 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif; }

  .rcpt   { width: 72mm; margin: 0 auto; padding: 8px 0; }
  .center { text-align: center; }
  .right  { text-align: right; }
  .bold   { font-weight: 700; }
  .muted  { color: #666; }
  .div    { border-top: 1px dashed #000; margin: 8px 0; }
  .row    { display: flex; justify-content: space-between; gap: 8px; }
  .row + .row { margin-top: 2px; }
  .h1     { font-size: 16px; font-weight: 800; }
  .tiny   { font-size: 11px; }
  .sp8    { margin-top: 8px; }
  .sp12   { margin-top: 12px; }
  .lh     { line-height: 1.3; }
  .addr a { color: #000; text-decoration: underline; }
  .label  { min-width: 50%; }
  .val    { flex: 1; text-align: right; }
  .pt-5   { padding-top: 5px; }

  /* Totals emphasis */
  .gtl    { font-size: 13px; font-weight: 800; }

  /* Make long item names wrap nicely */
  .wrapline { word-break: break-word; }

</style>
</head>
<body>
  <div class="rcpt">
    <!-- Header -->
    <div class="center h1">${companyName}</div>
    <div class="center tiny addr lh">
      ${address1 ? `${address1}<br/>` : ''}
      ${address2 ? `${address2}<br/>` : ''}
      ${
          city || state || pincode
              ? `${city ? city + ', ' : ''}${state || ''} ${pincode || ''}`
              : ''
      }
    </div>
    <div class="center tiny lh sp8">
      ${email ? `Email: ${email}<br/>` : ''}
      ${phone ? `Phone: ${phone}<br/>` : ''}
      ${gstin ? `GSTIN: ${gstin}` : ''}
    </div>

    <div class="div"></div>

    <!-- Invoice meta -->
    <div class="row tiny">
      <div class="label">Invoice No:</div>
      <div class="val">${invoice?.invoiceNumber ?? '-'}</div>
    </div>
    <div class="row tiny">
      <div class="label">Order Date:</div>
      <div class="val">${dateIN(invoice?.invoiceDate)}</div>
    </div>
    <div class="row tiny">
      <div class="label">Created By:</div>
      <div class="val">${invoice?.createdByName || '-'}</div>
    </div>

    <div class="div"></div>

    <!-- Customer -->
    <div class="row tiny">
      <div class="label">Customer Name:</div>
      <div class="val">${invoice?.clientDetails?.name || '-'}</div>
    </div>
    ${
        invoice?.clientDetails?.customerId
            ? `
    <div class="row tiny">
      <div class="label">Customer ID:</div>
      <div class="val">${invoice.clientDetails.customerId}</div>
    </div>
    `
            : ''
    }

    <div class="div"></div>

    <!-- Items -->
    ${generateItemsHTML()}

    <div class="div"></div>

    <!-- Cart Discount (if applied) -->
    ${
        invoice?.cartDiscountAmount > 0
            ? `
    <div class="row tiny">
      <div class="label">Cart Discount (${
          invoice?.cartDiscountType === 'Percentage'
              ? invoice?.cartDiscount + '%'
              : 'Fixed'
      })</div>
      <div class="val">-${INR(invoice?.cartDiscountAmount)}</div>
    </div>
    `
            : ''
    }

    <!-- Subtotal -->
    <div class="row">
      <div class="label">Sub Total:</div>
      <div class="val">${INR(invoice?.subTotal)}</div>
    </div>

    <!-- Tax Section -->
    ${
        sameUT
            ? `
      ${
          invoice?.cgst != null && invoice?.cgst > 0
              ? `<div class="row"><div class="label">CGST:</div><div class="val">${INR(
                    invoice?.cgst
                )}</div></div>`
              : ''
      }
      ${
          invoice?.sgst != null && invoice?.sgst > 0
              ? `<div class="row"><div class="label">SGST:</div><div class="val">${INR(
                    invoice?.sgst
                )}</div></div>`
              : ''
      }
    `
            : `
      ${
          invoice?.igst != null && invoice?.igst > 0
              ? `<div class="row"><div class="label">IGST:</div><div class="val">${INR(
                    invoice?.igst
                )}</div></div>`
              : ''
      }
    `
    }

    <!-- Voucher Discount (if applied) -->
    ${
        invoice?.voucherDiscount > 0
            ? `
    <div class="row tiny">
      <div class="label">Voucher Applied</div>
      <div class="val">-${INR(invoice?.voucherDiscount)}</div>
    </div>
    `
            : ''
    }

    <!-- Round Off -->
    ${
        invoice?.roundOff != null && invoice?.roundOff !== 0
            ? `
    <div class="row tiny">
      <div class="label">Round Off</div>
      <div class="val">${invoice?.roundOff > 0 ? '+' : ''}${INR(
                  invoice?.roundOff
              )}</div>
    </div>
    `
            : ''
    }

    <div class="div"></div>

    <!-- Grand Total -->
    <div class="row gtl">
      <div class="label">Grand Total</div>
      <div class="val">${INR(invoice?.grandTotal)}</div>
    </div>

    <div class="div"></div>

    <!-- Amount in words -->
    ${
        invoice?.amountInWords
            ? `
    <div class="tiny">${String(invoice.amountInWords)}</div>
    <div class="div"></div>
    `
            : ''
    }

    <!-- Payment Details -->
    <div class="row">
      <div class="label">Payment Method:</div>
      <div class="val">${pmName}</div>
    </div>
    <div class="row">
      <div class="label">Payment Status:</div>
      <div class="val">${
          invoice?.paymentStatus
              ? invoice.paymentStatus.charAt(0).toUpperCase() +
                invoice.paymentStatus.slice(1)
              : '-'
      }</div>
    </div>

    <!-- Cash payment details -->
    ${
        (pmName?.toLowerCase() === 'cash' || invoice?.isSplittedPayment) &&
        invoice?.tenderedAmount > 0
            ? `
      <div class="row">
        <div class="label">Tendered:</div>
        <div class="val">${INR(invoice?.tenderedAmount)}</div>
      </div>
      <div class="row">
        <div class="label">Change Due:</div>
        <div class="val">${INR(invoice?.changeDue)}</div>
      </div>
    `
            : ''
    }

    <!-- Footer notes -->
    <div class="div"></div>
    <div class="center tiny lh">
      ${invoice?.totalGstValue > 0 ? 'GST is included in the total.<br/>' : ''}
      This is a computer generated receipt.<br/>
      Disputes subject to ${
          invoice?.billingDetails?.cityName || 'local'
      } jurisdiction.
    </div>
    <div class="center bold sp8">Thank you for your visit!</div>
  </div>

  <script>
    window.onload = function () {
      window.print();
      setTimeout(function(){ window.close(); }, 400);
    };
  </script>
</body>
</html>
    `.trim();

        // Create and print via iframe
        const iframe = document.createElement('iframe');
        iframe.style.position = 'fixed';
        iframe.style.right = '0';
        iframe.style.bottom = '0';
        iframe.style.width = '0';
        iframe.style.height = '0';
        iframe.style.border = '0';
        iframe.setAttribute('aria-hidden', 'true');
        document.body.appendChild(iframe);

        const cleanup = () =>
            setTimeout(() => {
                try {
                    document.body.removeChild(iframe);
                } catch {
                    /* empty */
                }
            }, 800);

        iframe.srcdoc = html;
        iframe.addEventListener('load', cleanup);
    };

    const handleDownload = async () => {
        if (!orderId) return;

        try {
            setLoading(true);

            if (selectedFormat === 'roll') {
                printInvoice80mm(store.orderInvoiceDetail);
            } else {
                // your existing PDF flow
                const response = await dispatch(
                    DownloadInvoice({ orderId })
                ).unwrap();
                if (!response) throw new Error('No URL received from API.');
                window.open(response, '_blank', 'noopener,noreferrer');
            }
        } catch (err) {
            console.error(err);
        } finally {
            setLoading(false);
            setInvoiceModalOpen(false);
        }
    };

    return (
        <>
            {loader ? (
                <FullLoader state={true} />
            ) : (
                <>
                    <div className="flex flex-row justify-between">
                        <div className="flex items-center gap-5">
                            <img
                                src="/icons/back.svg"
                                alt="edit"
                                className="h-[10px] cursor-pointer"
                                onClick={goBack}
                            />
                            <Title className=" text-[#1a3353]" level={4}>
                                Order ID : {store.orderInvoiceDetail?.orderId}
                            </Title>
                        </div>
                        <div className="flex flex-row items-center gap-5">
                            {/* <Button
                                className=" fw-500 flex w-[100px] items-center rounded-2xl  border-[#1A3353] 
                                     py-3 text-xl text-[#1A3353]"
                            >
                                Schedule
                            </Button> */}

                            {store.orderInvoiceDetail?.paymentStatus ===
                            'pending' ? (
                                hasOrdersActionPermission && (
                                    <Button
                                        onClick={() =>
                                            setLocation(
                                                `/payment?orderId=${orderId}`
                                            )
                                        }
                                        className="fw-500 flex items-center rounded-2xl bg-purpleLight px-6 py-3 text-xl text-white"
                                    >
                                        Complete Payment
                                    </Button>
                                )
                            ) : store.orderInvoiceDetail?.paymentStatus ===
                                  'completed' ||
                              store.orderInvoiceDetail?.paymentStatus ===
                                  'refund' ? (
                                <Button
                                    // onClick={handleDownloadInvoice}
                                    onClick={() => setInvoiceModalOpen(true)}
                                    className="fw-500 flex items-center rounded-2xl bg-purpleLight px-6 py-3 text-xl text-white"
                                >
                                    <CloudDownloadOutlined className="text-2xl" />
                                    Download Invoice
                                </Button>
                            ) : null}
                        </div>
                    </div>

                    <Modal
                        title="Download Report as"
                        open={invoiceModalOpen}
                        onCancel={onCancel}
                        footer={null}
                        centered
                        maskClosable={false}
                    >
                        <div className="items-left flex flex-col gap-2">
                            <Radio.Group
                                value={selectedFormat}
                                onChange={(e) =>
                                    setSelectedFormat(e.target.value)
                                }
                                className="items-left flex flex-row gap-6"
                            >
                                <Radio value="roll">Thermal Print</Radio>
                                <Radio value="pdf">PDF</Radio>
                            </Radio.Group>

                            <div className="mt-8 flex gap-4">
                                <Button onClick={onCancel}>Cancel</Button>
                                <Button
                                    className="fw-500 w-[100px] rounded-2xl bg-purpleLight py-3 text-xl text-white"
                                    type="primary"
                                    onClick={handleDownload}
                                    loading={loading}
                                >
                                    Download
                                </Button>
                            </div>
                        </div>
                    </Modal>

                    {/* -----------------order detail on card-------------------- */}

                    <div className="mb-4 mt-10 flex flex-row gap-4 ">
                        <div className=" text-xl font-medium text-[#1A3353]">
                            Ordered on:{' '}
                        </div>
                        <div className=" text-[#1A3353]">
                            {formatCommonDate(
                                store.orderInvoiceDetail?.invoiceDate
                            )}
                        </div>
                    </div>

                    <div
                        className="mb-10 flex flex-row  gap-4 "
                        id="target-div"
                    >
                        <div className="flex w-[20%] flex-col gap-3 rounded-2xl border   px-4 py-4">
                            <div className="flex w-[100%] flex-row items-start gap-5 ">
                                <div className="flex w-[20%] flex-row items-center justify-between ">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Name
                                    </p>
                                    <p>:</p>
                                </div>
                                <p className="text-xl text-[#455560] ">
                                    {capitalizeFirstLetter(
                                        store.orderInvoiceDetail?.clientDetails
                                            ?.name
                                    )}
                                </p>
                            </div>
                            <div className="flex w-[100%] flex-row items-start gap-5 ">
                                <div className="flex w-[20%] flex-row items-center justify-between ">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Email
                                    </p>
                                    <p>:</p>
                                </div>
                                <p className=" overflow-hidden truncate text-xl text-[#455560]">
                                    {
                                        store.orderInvoiceDetail?.clientDetails
                                            ?.email
                                    }
                                </p>
                            </div>
                            <div className="flex w-[100%] flex-row items-center gap-5 ">
                                <div className="flex w-[20%] flex-row items-center justify-between ">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Mobile
                                    </p>
                                    <p>:</p>
                                </div>
                                <p className="text-xl text-[#455560] ">
                                    {
                                        store.orderInvoiceDetail?.clientDetails
                                            ?.phone
                                    }
                                </p>
                            </div>
                            <div className="flex w-[100%] flex-row items-center gap-5 ">
                                <div className="flex flex-row items-center justify-between ">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Paid On
                                    </p>
                                    <p>:</p>
                                </div>
                                <p className="text-xl text-[#455560] ">
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'pending'
                                        ? '--'
                                        : formatDate(
                                              store.orderInvoiceDetail
                                                  ?.paymentDetails?.[0]
                                                  ?.paymentDate
                                          )}
                                </p>
                            </div>

                            {/* <div className="flex w-[100%] flex-row items-center gap-5 ">
                                <div className="flex w-[20%] flex-row items-center justify-between ">
                                    <p className="  text-xl font-medium text-[#1A3353]">
                                        Status
                                    </p>
                                    <p>:</p>
                                </div>
                                {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'completed' && <ConfimredButtonChip />}
                                {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'pending' && <PendingButtonChip />}
                                {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'failed' && <RejectedButtonChip />}
                            </div> */}
                        </div>
                        <div className="flex w-[13%] flex-col gap-3">
                            <div className="flex flex-col items-center justify-center rounded-2xl border py-7">
                                <p className="text-2xl font-semibold text-[#1A3353]">
                                    {Math.floor(
                                        store.orderInvoiceDetail?.grandTotal
                                    )}{' '}
                                    /-
                                </p>
                                <p className="text-lg  text-[#455560]">
                                    Order Value
                                </p>
                            </div>
                            <div className="flex flex-col items-center justify-center rounded-2xl border py-7">
                                <p className="text-2xl font-semibold text-[#1A3353]">
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                        'completed' && <ConfimredButtonChip />}
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                        'pending' && <PendingButtonChip />}
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                        'failed' && <RejectedButtonChip />}
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                        'refund' && (
                                        <button
                                            className="h-[20px] rounded bg-red-100 px-4 text-[13px] font-[600] text-red-500"
                                            title={
                                                store.orderInvoiceDetail
                                                    ?.paymentReason
                                            }
                                        >
                                            {'Refunded'}
                                        </button>
                                    )}
                                    {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                        'cancel' && (
                                        <button
                                            className="h-[20px] rounded bg-red-100 px-4 text-[13px] font-[600] text-red-500"
                                            title={
                                                store.orderInvoiceDetail
                                                    ?.paymentReason
                                            }
                                        >
                                            {'Cancelled'}
                                        </button>
                                    )}
                                </p>
                                <p className="text-lg  text-[#455560]">
                                    Payment Status
                                </p>
                            </div>
                        </div>
                        <div className="flex w-[67%] flex-row items-start rounded-2xl border px-4 ">
                            <div className="flex w-[25%] flex-col justify-center gap-3  px-4 py-4">
                                <p className="  text-xl font-medium text-[#1A3353]">
                                    Client Details
                                </p>
                                <p className="  text-xl  text-[#455560]">
                                    {capitalizeFirstLetter(
                                        store.orderInvoiceDetail
                                            ?.clientBillingDetails?.name
                                    )}
                                </p>
                                <p className="  text-xl  text-[#455560]">
                                    {
                                        store.orderInvoiceDetail
                                            ?.clientBillingDetails?.addressLine1
                                    }{' '}
                                    {
                                        store.orderInvoiceDetail
                                            ?.clientBillingDetails?.addressLine2
                                    }
                                </p>

                                {(store.orderInvoiceDetail?.clientBillingDetails
                                    ?.city ||
                                    store.orderInvoiceDetail
                                        ?.clientBillingDetails?.state) && (
                                    <p className="text-xl text-[#455560]">
                                        {store.orderInvoiceDetail
                                            ?.clientBillingDetails?.city
                                            ? `${store.orderInvoiceDetail?.clientBillingDetails?.city}, ${store.orderInvoiceDetail?.clientBillingDetails?.state}`
                                            : store.orderInvoiceDetail
                                                  ?.clientBillingDetails?.state}
                                    </p>
                                )}

                                <p className="  text-xl  text-[#455560]">
                                    {
                                        store.orderInvoiceDetail
                                            ?.clientBillingDetails?.postalCode
                                    }
                                </p>
                                {store.orderInvoiceDetail?.clientBillingDetails
                                    ?.gstNumber && (
                                    <p className="  text-xl  text-[#455560]">
                                        GST No:{' '}
                                        {store.orderInvoiceDetail?.clientBillingDetails?.gstNumber?.toUpperCase()}
                                    </p>
                                )}
                            </div>
                            <div className="flex w-[35%]  flex-col justify-center gap-3  px-4 py-4">
                                <p className="  text-xl font-medium text-[#1A3353]">
                                    Billing Details
                                </p>
                                <p className="  text-xl  text-[#455560]">
                                    {
                                        store.orderInvoiceDetail?.billingDetails
                                            ?.billingName
                                    }
                                </p>
                                <p className="  text-xl  text-[#455560]">
                                    {
                                        store.orderInvoiceDetail?.billingDetails
                                            ?.cityName
                                    }{' '}
                                    {
                                        store.orderInvoiceDetail?.billingDetails
                                            ?.addressLine1
                                    }{' '}
                                    {
                                        store.orderInvoiceDetail?.billingDetails
                                            ?.addressLine2
                                    }
                                </p>

                                {(store.orderInvoiceDetail?.billingDetails
                                    ?.cityName ||
                                    store.orderInvoiceDetail?.billingDetails
                                        ?.state) && (
                                    <p className="text-xl text-[#455560]">
                                        {store.orderInvoiceDetail
                                            ?.billingDetails?.city
                                            ? `${store.orderInvoiceDetail?.billingDetails?.city}, ${store.orderInvoiceDetail?.billingDetails?.state}`
                                            : store.orderInvoiceDetail
                                                  ?.clientDetails?.state}
                                    </p>
                                )}
                                <p className="  text-xl  text-[#455560]">
                                    {
                                        store.orderInvoiceDetail?.billingDetails
                                            ?.postalCode
                                    }
                                </p>
                                {store.orderInvoiceDetail?.billingDetails
                                    ?.gstNumber && (
                                    <p className="  text-xl  text-[#455560]">
                                        GST No:{' '}
                                        {store.orderInvoiceDetail?.billingDetails?.gstNumber?.toUpperCase()}
                                    </p>
                                )}
                            </div>
                            <div className="flex w-[20%] flex-col justify-center gap-8  px-4 py-4 ">
                                <div className=" flex w-[100%] flex-col items-start justify-center  ">
                                    <p className="text-xl font-medium text-[#1A3353]">
                                        Payment Method
                                    </p>
                                    <p className="text-lg  text-[#455560]">
                                        {store.orderInvoiceDetail?.updatedPaymentDetails
                                            ?.map(
                                                (detail: any) =>
                                                    detail.paymentMethodName
                                            )
                                            .join(', ')}
                                    </p>
                                </div>
                                <div className="flex w-[100%] flex-col items-start justify-center ">
                                    <p className="text-xl font-medium text-[#1A3353]">
                                        Total Products
                                    </p>
                                    <p className="text-lg  text-[#455560]">
                                        {store.orderInvoiceDetail?.totalItems}
                                    </p>
                                </div>
                                {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'cancel' &&
                                    store?.orderInvoiceDetail?.cancelledBy && (
                                        <div className="flex w-[100%] flex-col items-start justify-center ">
                                            <p className="text-xl font-medium text-[#1A3353]">
                                                Cancelled By
                                            </p>
                                            <p className="text-lg  text-[#455560]">
                                                {
                                                    store.orderInvoiceDetail
                                                        ?.cancelledBy
                                                }
                                            </p>
                                        </div>
                                    )}
                                {store.orderInvoiceDetail?.paymentStatus?.toLowerCase() ===
                                    'refund' &&
                                    store?.orderInvoiceDetail?.cancelledBy && (
                                        <div className="flex w-[100%] flex-col items-start justify-center ">
                                            <p className="text-xl font-medium text-[#1A3353]">
                                                Refunded By
                                            </p>
                                            <p className="text-lg  text-[#455560]">
                                                {
                                                    store?.orderInvoiceDetail
                                                        ?.cancelledBy
                                                }
                                            </p>
                                        </div>
                                    )}
                                {/* {store?.orderInvoiceDetail?.paymentDetails?.[0]
                                    ?.transactionId && (
                                    <div className=" flex w-[100%] flex-col items-start justify-center ">
                                        <p className="text-xl font-medium text-[#1A3353]">
                                            Notes/TXN ID :-{' '}
                                            {
                                                store.orderInvoiceDetail
                                                    .paymentDetails[0]
                                                    .transactionId
                                            }
                                        </p>
                                    </div>
                                )} */}
                            </div>
                            <div className="flex w-[20%] flex-col justify-center gap-8  px-4 py-4">
                                <div className=" flex w-[100%] flex-col items-start justify-center ">
                                    <p className="text-xl font-medium text-[#1A3353]">
                                        Order Created By
                                    </p>
                                    <p className="text-lg  text-[#455560]">
                                        {store.orderInvoiceDetail
                                            ?.paymentByName ||
                                            store.orderInvoiceDetail
                                                ?.createdByName}
                                    </p>
                                </div>

                                <div className=" flex w-[100%] flex-col items-start justify-center ">
                                    <p className="text-xl font-medium text-[#1A3353]">
                                        Notes/TXN ID :-{' '}
                                    </p>
                                    <p className="text-lg  text-[#455560]">
                                        {
                                            store.orderInvoiceDetail
                                                ?.paymentDetails[0]
                                                ?.transactionId
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* ----------------------table----------------------- */}

                    <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        cellPaddingBlock: 12,
                                    },
                                },
                            }}
                        >
                            <Table
                                dataSource={dataSource}
                                columns={columns}
                                pagination={false}
                            />
                        </ConfigProvider>
                    </div>

                    {/* ====================bill reciept--------------------------- */}

                    <div className="flex flex-row justify-between py-8">
                        <div className="flex flex-col ">
                            <p className="text-2xl text-[#1a3353]">
                                Total amount in words :
                            </p>
                            <p className="text-2xl font-medium text-[#1a3353]">
                                {/* {convertToWords(
                                    Number(store.orderInvoiceDetail?.total)
                                )?.toUpperCase()} */}
                                {toTitleCase(
                                    store.orderInvoiceDetail?.amountInWords
                                )}
                            </p>
                            {store.orderInvoiceDetail?.paymentStatus ===
                            'refund' ? (
                                <div className="mt-16">
                                    <div className="font-bold text-red-400">
                                        Payment Cancellation Reason:
                                    </div>
                                    <p className="me-8 rounded-xl border p-4">
                                        {
                                            store.orderInvoiceDetail
                                                ?.paymentReason
                                        }
                                    </p>
                                </div>
                            ) : (
                                ''
                            )}
                        </div>
                        <div className="flex w-[25%] flex-col gap-3 rounded-xl border px-5 py-7  text-xl shadow-md">
                            {store.orderInvoiceDetail?.cartDiscountAmount >
                                0 && (
                                <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                    <p>Cart Discount </p>
                                    <p>
                                        ₹{' '}
                                        {Math.trunc(
                                            store.orderInvoiceDetail
                                                ?.cartDiscountAmount * 100
                                        ) / 100}
                                    </p>
                                </div>
                            )}
                            <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>Item Total </p>
                                <p>₹{store.orderInvoiceDetail?.subTotal}</p>
                            </div>
                            {/* <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>Total Item Discount </p>
                                <p>-₹{store.orderInvoiceDetail?.itemDiscount}</p>
                            </div> */}

                            {/* {store.orderInvoiceDetail?.returnDiscount > 0 && (
                                <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                    <p>Returns </p>
                                    <p>
                                        ₹{' '}
                                        {Math.trunc(
                                            store.orderInvoiceDetail
                                                ?.returnDiscount * 100
                                        ) / 100}
                                    </p>
                                </div>
                            )} */}

                            {store.orderInvoiceDetail?.clientBillingDetails
                                ?.utCode ===
                            store.orderInvoiceDetail?.billingDetails?.utCode ? (
                                <>
                                    <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                        <p>CGST</p>
                                        <p>₹{store.orderInvoiceDetail?.cgst}</p>
                                    </div>
                                    <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                        <p>SGST</p>
                                        <p>₹{store.orderInvoiceDetail?.sgst}</p>
                                    </div>
                                </>
                            ) : (
                                <>
                                    <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                        <p>IGST</p>
                                        <p>₹{store.orderInvoiceDetail?.igst}</p>
                                    </div>
                                </>
                            )}
                            {/* <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>Total GST</p>
                                <p>
                                    ₹{store.orderInvoiceDetail?.totalGstValue}
                                </p>
                            </div> */}
                            {store.orderInvoiceDetail?.voucherDiscount > 0 && (
                                <>
                                    <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                        <p>Voucher Applied </p>
                                        <p>
                                            ₹{' '}
                                            {Math.trunc(
                                                store.orderInvoiceDetail
                                                    ?.voucherDiscount * 100
                                            ) / 100}
                                        </p>
                                    </div>
                                </>
                            )}

                            <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>Total Amount </p>
                                <p>
                                    ₹
                                    {
                                        store.orderInvoiceDetail
                                            ?.totalAmountAfterGst
                                    }
                                </p>
                            </div>
                            <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                <p>Round Off </p>
                                <p>₹ {store.orderInvoiceDetail?.roundOff}</p>
                            </div>
                            <div className="flex flex-row items-center justify-between  px-4 pb-1 text-2xl font-semibold text-[#1a3353]">
                                <p>Grand Total </p>
                                <p>
                                    ₹
                                    {Math.floor(
                                        store.orderInvoiceDetail?.grandTotal
                                    )}
                                </p>
                            </div>
                            {store.orderInvoiceDetail?.tenderedAmount !== 0 && (
                                <>
                                    <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                        <p>
                                            Tendered Amount
                                            <Tooltip
                                                title="Amount paid"
                                                placement="right"
                                                trigger={['hover', 'focus']}
                                            >
                                                <InfoCircleOutlined
                                                    className="cursor-pointer align-middle text-gray-400 hover:text-gray-600"
                                                    aria-label="Info: Amount paid"
                                                    tabIndex={0}
                                                />
                                            </Tooltip>
                                        </p>
                                        <p>
                                            ₹{' '}
                                            {
                                                store.orderInvoiceDetail
                                                    ?.tenderedAmount
                                            }
                                        </p>
                                    </div>
                                </>
                            )}
                            {store.orderInvoiceDetail?.changeDue !== 0 && (
                                <>
                                    <div className="flex flex-row items-center justify-between border-b px-4 pb-1 font-medium text-[#1a3353]">
                                        <p>Change Due </p>
                                        <p className="text-red-600">
                                            -₹{' '}
                                            {
                                                store.orderInvoiceDetail
                                                    ?.changeDue
                                            }
                                        </p>
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </>
            )}
        </>
    );
};

export default OrderDetail;
