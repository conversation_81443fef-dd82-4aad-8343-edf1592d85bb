import { ConfigProvider, Form, Input, Select } from 'antd';
import BranchListing from './branchListing';
import { Link } from 'wouter';

const OrganizationDetails = ({
    form,
    store,
    CountryOptions,
    organizationId,
}: any) => {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Input: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                    },
                    Select: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                    },
                },
            }}
        >
            <div className="relative rounded-3xl border lg:p-16 @sm:p-5">
                <div className="absolute right-4 top-0 py-10 @sm:pb-5">
                    <Link to={`/create-organization/${organizationId}`}>
                        <img
                            src="/icons/common/edit.svg"
                            alt="edit"
                            className="ms-auto h-[20px] cursor-pointer"
                        />
                    </Link>
                </div>
                <Form
                    name="organizationCreate"
                    layout="vertical"
                    size="large"
                    form={form}
                    initialValues={{ remember: true }}
                    disabled
                    requiredMark={false}
                    autoComplete="off"
                >
                    <div className="flex flex-wrap">
                        <div className="w-full px-4 md:w-1/3 lg:mb-4">
                            <Form.Item
                                label="Organization Name"
                                name="organizationName"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please enter organization name!',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter Organization Name" />
                            </Form.Item>
                        </div>
                        <div className="w-full px-4 md:w-1/3 lg:mb-4">
                            <Form.Item
                                label="Email"
                                name="email"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter email!',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter email" />
                            </Form.Item>
                        </div>
                        <div className="w-full px-4 md:w-1/3 lg:mb-4">
                            <Form.Item
                                label="Phone Number"
                                name="phoneNumber"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter phone number!',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter Phone Number" />
                            </Form.Item>
                        </div>
                    </div>
                    <div className="flex flex-wrap">
                        <div className="w-full px-4 md:w-1/3 lg:mb-4">
                            <Form.Item
                                label="Contact Person Name"
                                name="contactPersonName"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please enter contact person name!',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter Contact Person Name" />
                            </Form.Item>
                        </div>
                        <div className="w-full px-4 md:w-1/3 lg:mb-4">
                            <Form.Item
                                label="State"
                                name="state"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select state!',
                                    },
                                ]}
                            >
                                <Select
                                    showSearch
                                    placeholder="Select state"
                                    filterOption={(input, option) =>
                                        String(option?.label ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={CountryOptions}
                                    onChange={() => console.log('value')}
                                />
                            </Form.Item>
                        </div>
                        <div className="w-full px-4 md:w-1/3 lg:mb-4">
                            <Form.Item
                                label="City"
                                name="city"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select city!',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter Address" />
                            </Form.Item>
                        </div>
                    </div>
                    <div className="flex flex-wrap">
                        <div className="w-full px-4 md:w-1/3 lg:mb-4">
                            <Form.Item
                                label="Pin Code"
                                name="pinCode"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter pin code!',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter pin code" />
                            </Form.Item>
                        </div>
                        <div className="w-full px-4 md:w-1/3 lg:mb-4">
                            <Form.Item
                                label="Address"
                                name="address"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter address!',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter Address" />
                            </Form.Item>
                        </div>
                    </div>
                </Form>
            </div>
            <BranchListing organizationId={organizationId} />
        </ConfigProvider>
    );
};

export default OrganizationDetails;
