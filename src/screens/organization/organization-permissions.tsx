import React, { useEffect, useState } from 'react';
import { Checkbox, Button, Form, Typography, message } from 'antd';
import {
    PermissionsList,
    UpdatePermissions,
} from '~/redux/actions/organization-action';
import { useDispatch } from 'react-redux';
import Alertify from '~/services/alertify';
import { LoaderIcon } from 'react-hot-toast';

const { Text } = Typography;
interface PermissionItem {
    key: string;
    name: string;
    isEnabled: boolean;
    subSettings: PermissionItem[];
}
interface FormattedPermissionItem {
    settingKey: {
        status: boolean;
        settingKey: string;
    };
    subSettingsKey: {
        status: boolean;
        settingKey: string;
    }[];
}

interface OrganizationPermissionsProps {
    organizationId?: string;
}

const OrganizationPermissions: React.FC<OrganizationPermissionsProps> = ({
    organizationId,
}) => {
    const [permissionsData, setPermissionsData] = useState<PermissionItem[]>(
        []
    );
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(PermissionsList({ organizationId })).then((response: any) => {
            // console.log(
            //     'Permissions Data:--------------',
            //     response.payload?.data?.data?.data
            // );
            setPermissionsData(response.payload?.data?.data?.data || []);
        });
    }, [organizationId]);

    const handleCheckboxChange = (key: string) => {
        setPermissionsData((prevPermissions: PermissionItem[]) => {
            // toggle children
            const toggleChildren = (
                items: PermissionItem[],
                isChecked: boolean
            ) => {
                return items.map((item): PermissionItem => {
                    if (
                        item.key !== 'subsettings_class_setup_booking' &&
                        item.key !==
                            'subsettings_class_setup_personal_appontment' &&
                        item.key !==
                            'subsettings_class_setup_personal_appointment'
                    ) {
                        return {
                            ...item,
                            isEnabled: isChecked,
                            subSettings: item.subSettings
                                ? toggleChildren(item.subSettings, isChecked)
                                : [],
                        };
                    } else {
                        return item; // Keep the item unchanged if key matches
                    }
                });
            };

            // update parent state based on children's state
            const updateParentState = (items: PermissionItem[]) => {
                return items.map((item): PermissionItem => {
                    if (item.subSettings && item.subSettings.length > 0) {
                        const updatedSubSettings = updateParentState(
                            item.subSettings
                        );

                        const allChildrenChecked = updatedSubSettings.every(
                            (sub) => sub.isEnabled
                        );

                        return {
                            ...item,
                            subSettings: updatedSubSettings,
                            isEnabled: allChildrenChecked,
                        };
                    }
                    return item;
                });
            };

            // update the correct checkbox
            const toggleCheckbox = (items: PermissionItem[]) => {
                return items.map((item): PermissionItem => {
                    if (item.key === key) {
                        const newIsEnabled = !item.isEnabled;
                        return {
                            ...item,
                            isEnabled: newIsEnabled,
                            subSettings: item.subSettings
                                ? toggleChildren(item.subSettings, newIsEnabled)
                                : [],
                        };
                    }

                    if (item.subSettings) {
                        return {
                            ...item,
                            subSettings: toggleCheckbox(item.subSettings),
                        };
                    }

                    return item;
                });
            };

            let updatedPermissions = toggleCheckbox(prevPermissions);
            updatedPermissions = updateParentState(updatedPermissions);
            return updatedPermissions;
        });

        // setPermissionsData(organizationId, ...permissionsData);
    };

    const [loading, setLoading] = useState(false);

    const handleSubmit = () => {
        setLoading(true);
        console.log('Selected Permissions:', permissionsData);
        const transformData = (items: PermissionItem[]) => {
            return items.map((item) => {
                const formattedItem: FormattedPermissionItem = {
                    settingKey: {
                        status: item.isEnabled,
                        settingKey: item.key,
                    },
                    subSettingsKey: [],
                };
                if (item.subSettings && item.subSettings.length > 0) {
                    formattedItem.subSettingsKey = item.subSettings.map(
                        (subItem) => ({
                            status: subItem.isEnabled,
                            settingKey: subItem.key,
                        })
                    );
                }
                return formattedItem;
            });
        };
        const formattedData = transformData(permissionsData);
        console.log('Formatted Data:', JSON.stringify(formattedData, null, 2));
        dispatch(
            UpdatePermissions({
                organizationId,
                permissionsData: formattedData,
            })
        )
            .then((response: any) => {
                // console.log('Update API response: ', response);
                message.success('Permissions updated successfully.');
                setLoading(false);
            })
            .catch((error: any) => {
                console.log('Update API error: ', error);
            });
    };

    return (
        <div className="relative rounded-3xl border p-16">
            {/* <Text>Organization Id: {organizationId}</Text> */}
            {loading ? (
                <div className="absolute z-10 flex h-full w-full -translate-x-16 -translate-y-16 items-center justify-center bg-white opacity-50">
                    <LoaderIcon className="h-[30px] w-[30px] text-black" />
                </div>
            ) : (
                ''
            )}
            <Form layout="vertical">
                {permissionsData?.map((item) => (
                    <div key={item?.key} className="mt-8">
                        <Checkbox
                            checked={item?.isEnabled}
                            onChange={() => handleCheckboxChange(item?.key)}
                            style={{ fontWeight: 'bold', fontSize: '16px' }}
                        >
                            {item?.name}
                        </Checkbox>
                        {item?.subSettings?.length > 0 && (
                            <div className="mt-4 ps-8">
                                {item?.subSettings?.map((subItem) => (
                                    <div key={subItem?.key}>
                                        <Checkbox
                                            checked={subItem?.isEnabled}
                                            onChange={() =>
                                                handleCheckboxChange(
                                                    subItem?.key
                                                )
                                            }
                                            disabled={
                                                subItem?.key ===
                                                    'subsettings_class_setup_booking' ||
                                                subItem?.key ===
                                                    'subsettings_class_setup_personal_appontment' ||
                                                subItem?.key ===
                                                    'subsettings_class_setup_personal_appointment'
                                            }
                                        >
                                            {subItem?.name}
                                        </Checkbox>
                                        {subItem?.subSettings?.length > 0 && (
                                            <div className="ps-8">
                                                {subItem?.subSettings?.map(
                                                    (grandChild) => (
                                                        <div
                                                            key={
                                                                grandChild?.key
                                                            }
                                                        >
                                                            <Checkbox
                                                                checked={
                                                                    grandChild?.isEnabled
                                                                }
                                                                onChange={() =>
                                                                    handleCheckboxChange(
                                                                        grandChild?.key
                                                                    )
                                                                }
                                                            >
                                                                {
                                                                    grandChild?.name
                                                                }
                                                            </Checkbox>
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                ))}
                <Form.Item>
                    <Button
                        type="primary"
                        style={{
                            backgroundColor: '#B58EF5',
                            borderColor: '#B58EF5',
                            marginTop: '20px',
                        }}
                        onClick={handleSubmit}
                    >
                        Save
                    </Button>
                </Form.Item>
            </Form>
        </div>
    );
};

export default OrganizationPermissions;
