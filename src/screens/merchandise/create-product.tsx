import { Button, Form, Input, Select } from 'antd';
import Title from 'antd/es/typography/Title';
import { useEffect, useState, useRef } from 'react';
import ReactQuill from 'react-quill';
import { goBack } from '~/components/common/function';
import {
    GetBrandsData,
    AttributeListData,
    ActiveDynamicAttributeList,
} from '~/redux/actions/merchandise/attribute-action';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { useAppSelector } from '~/hooks/redux-hooks';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import {
    PrimaryCategoryListData,
    SubCategoryList,
} from '~/redux/actions/merchandise/category-action';
import {
    CreateNewProduct,
    GetProductDetails,
    UpdateProduct,
} from '~/redux/actions/merchandise/product-action';
import { useParams } from 'wouter';
import VariantCardWrapper from './variant-card-wrapper';
import { navigate } from 'wouter/use-location';
import { RevenueCategoryList } from '~/redux/actions/revenue-category-action';
import Alertify from '~/services/alertify';
import RevenueCategory from '../settings/revenue-category';

const AdditionalDetailsTab = [
    { id: 1, label: 'Description', key: 'description' },
    // { id: 2, label: 'Ingredients', key: 'ingredients' },
    // { id: 3, label: 'How to use', key: 'howToUse' },
    // { id: 4, label: 'Key Benefits', key: 'keyBenefits' },
];

const discountValue = [
    {
        value: 0,
        label: '0 %',
    },
    {
        value: 5,
        label: '5 %',
    },
    {
        value: 12,
        label: '12 %',
    },
    {
        value: 18,
        label: '18 %',
    },
    {
        value: 28,
        label: '28 %',
    },
];

const CreateProduct = () => {
    const dispatch = useDispatch<AppDispatch>();
    const [activeTab, setActiveTab] = useState(1);
    const [variants, setVariants] = useState([0]);
    const [revenueCategoryOptions, setRevenueCategoryOptions] = useState<any[]>(
        []
    );
    const [form] = Form.useForm();
    const [showVariants, setShowVariants] = useState(false);
    const [secondaryCategoryList, setSecondaryCategoryList] = useState<any>([]);
    const { id } = useParams<{ id: string }>();
    const searchParams = new URLSearchParams(window.location.search);
    const isView = searchParams.get('view') === 'true';
    const addVariant = () => {
        setVariants([...variants, variants.length]);
    };
    const [variantsState, setVariantsState] = useState([
        { title: '', sku: '', hsnCode: '', status: true, attributes: {} },
        { title: '', sku: '', hsnCode: '', status: true, attributes: {} },
    ]);

    const [selectedItems, setSelectedItems] = useState([]);
    const [validateVariants, setValidateVariants] = useState<() => boolean>(
        () => () => true
    );
    const handleProductTypeChange = (value: any) => {
        setShowVariants(true);
        if (value === 'simple') {
            setShowVariants(false);
            setVariants([0]);
            setVariantsState([]);
        } // Reset variants when switching back to "simple"
    };
    useEffect(() => {
        dispatch(GetBrandsData());
        dispatch(ActiveDynamicAttributeList()).unwrap();
        dispatch(RevenueCategoryList({}))
            .unwrap()
            .then((response: any) => {
                setRevenueCategoryOptions(response?.data?.data);
            })
            .catch((error: any) => {
                Alertify.error('Could not fetch Revenue Categories');
            });
    }, []);

    const { brandList } = useAppSelector((state) => ({
        brandList: state.attributes_store.brandList,
    }));
    const brandOption = brandList.map((item: any) => ({
        value: item._id,
        label: item.value,
    }));
    const generateRandomSuffix = () => {
        return Math.random().toString(36).substring(2, 6).toUpperCase(); // 4-char code
    };

    const generateVariantCode = (base: string, index: number) => {
        return `VITEM-${base}-${index + 1}-${generateRandomSuffix()}`;
    };
    const onFinish = (values: any) => {
        if (showVariants && !validateVariants()) {
            return;
        }
        const baseCode =
            values.name?.replace(/\s+/g, '').toUpperCase().slice(0, 6) ||
            'PROD';

        const enrichedVariants = variantsState.map(
            (variant: any, index: number) => ({
                ...variant,
                itemCode: generateVariantCode(baseCode, index),
            })
        );
        const payLoad = {
            ...values,
            slug: values.sku,
            variants:
                enrichedVariants.length > 0 ? enrichedVariants : undefined,
            variantAttributesList:
                selectedItems.length > 0 ? selectedItems : undefined,
        };

        if (id === '0') {
            dispatch(CreateNewProduct(payLoad))
                .unwrap()
                .then(() => navigate('/product-listing'));
        } else if (id !== '0') {
            dispatch(UpdateProduct({ reqData: payLoad, id: id }))
                .unwrap()
                .then(() => navigate('/product-listing'))
                .catch(() => {})
                .finally();
        }
    };

    const fetchParentCategoryList = async (searchText = '', page: number) => {
        try {
            const response = await dispatch(
                PrimaryCategoryListData({
                    page,
                    pageSize: 10,
                    level: 'first',
                    search: searchText,
                })
            ).unwrap();
            return response?.data?.data?.list?.map((item: any) => ({
                value: item._id,
                label: item?.name,
            }));
        } catch (error: any) {
            console.error(error);
        }
    };
    const fetchSecondaryCategory = async (value: any) => {
        try {
            const response = await dispatch(
                SubCategoryList({
                    level: 'second',
                    parentId: value,
                })
            ).unwrap();
            setSecondaryCategoryList(
                response?.data?.data?.map((item: any) => ({
                    value: item._id,
                    label: item?.name,
                }))
            );
        } catch (error: any) {
            console.error(error);
        }
    };
    useEffect(() => {
        if (id !== '0') {
            dispatch(GetProductDetails({ id })).then((res: any) => {
                const productDetail = res.payload.res.data.data[0];
                if (productDetail) {
                    fetchSecondaryCategory(productDetail.firstCategoryId);
                    form.setFieldsValue({
                        name: productDetail.name,
                        slug: productDetail.slug,
                        sku: productDetail.sku,
                        firstCategoryId: productDetail.firstCategoryId,
                        secondCategoryId: productDetail.secondCategoryId,
                        revenueCategory: productDetail.revenueCategory,
                        hsn: productDetail.hsn,
                        mfgCode: productDetail.mfgCode,
                        gst: productDetail.gst,
                        description: productDetail.description,
                        type: productDetail.type,
                        brand: productDetail.parentAttributes.find(
                            (attr: any) => attr.key === 'brand'
                        )?.value[0]?._id,
                    });

                    // Set variants and attributes
                    setVariantsState(
                        productDetail.productVariants.map((variant: any) => ({
                            title: variant.title,
                            sku: variant.sku,
                            hsnCode: variant.hsnCode,
                            status: variant.status ? 'active' : 'inactive',
                            attributes: variant.attributes.reduce(
                                (acc: any, attr: any) => {
                                    acc[attr.key] = attr.value;
                                    return acc;
                                },
                                {}
                            ),
                        }))
                    );

                    setSelectedItems(
                        productDetail.variantAttributesList?.flat() || []
                    );

                    if (productDetail.type === 'variable') {
                        setShowVariants(true);
                    }
                }
            });
        }
    }, [id, dispatch, form]);
    return (
        <>
            <div className="flex flex-row justify-between">
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="Back"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353]" level={4}>
                        {isView
                            ? 'View Product'
                            : id === '0'
                            ? 'Create Product'
                            : 'Edit Product'}
                    </Title>
                </div>
            </div>
            <div className="mt-11 rounded-[6px] border-1 p-8">
                <Form
                    layout="vertical"
                    autoComplete="off"
                    className=""
                    form={form}
                    onValuesChange={(changedValues) => {
                        if (changedValues.type)
                            handleProductTypeChange(changedValues.type);
                    }}
                    onFinish={onFinish}
                    disabled={isView}
                    initialValues={{
                        type: 'simple', // Set default value here
                    }}
                >
                    {/* <Form.Item className="flex justify-end gap-8 py-10 @sm:hidden ">
                        <Button
                            htmlType="submit"
                            className=" h-16 w-28 bg-purpleLight text-xl text-white lg:ms-7"
                        >
                            <p>Save</p>
                        </Button>
                    </Form.Item> */}
                    <div className="flex items-start  justify-between">
                        <div className=" lg:w-3/5 lg:pe-8 @sm:w-[100%]">
                            <div className="flex items-center  lg:mb-8 ">
                                <Form.Item
                                    name="type"
                                    className=" lg:w-[35%] @sm:w-[100%]"
                                    label="Product Type"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Product Type is Required',
                                        },
                                    ]}
                                    hidden={id !== '0'}
                                >
                                    <Select
                                        placeholder="Select Product Type"
                                        onChange={handleProductTypeChange}
                                        options={[
                                            {
                                                value: 'simple',
                                                label: 'Simple Product',
                                            },
                                            {
                                                value: 'variable',
                                                label: 'Variable',
                                            },
                                        ]}
                                    />
                                </Form.Item>
                            </div>
                            <Form.Item
                                className="mb-4"
                                name="name"
                                label="Product Name"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Product Name is Required',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="Product Name"
                                    className="h-[40px] w-full"
                                />
                            </Form.Item>

                            {/* <Form.Item
                                className="mb-4"
                                name="slug"
                                label="Slug"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Slug is Required',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="slug"
                                    className="h-[40px]"
                                />
                            </Form.Item> */}
                            <Form.Item
                                className="mb-4"
                                name="sku"
                                label="sku"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Sku is Required',
                                    },
                                ]}
                            >
                                <Input placeholder="SKU" className="h-[40px]" />
                            </Form.Item>

                            <div className="mb-4">
                                <Form.Item
                                    label="Brand "
                                    name="brand"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Brand is Required',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="mt-2 h-[40px] w-full"
                                        placeholder="Select Brand"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        showSearch
                                        options={brandOption}
                                    />
                                </Form.Item>
                            </div>
                            <div className="mb-4">
                                <Form.Item
                                    label="Category "
                                    name="firstCategoryId"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Category is Required',
                                        },
                                    ]}
                                >
                                    <InfiniteScrollSelect
                                        fetchOptions={fetchParentCategoryList}
                                        onChange={(value) => {
                                            form.setFieldsValue({
                                                secondCategoryId: null,
                                                firstCategoryId: value,
                                            });
                                            fetchSecondaryCategory(value);
                                        }}
                                        placeholder="Select Parent Category"
                                        className="border-b border-[#d1d5db]"
                                    />
                                </Form.Item>
                            </div>
                            <div className="mb-4">
                                <Form.Item
                                    label="Sub-Category "
                                    name="secondCategoryId"
                                >
                                    <Select
                                        className="mt-2 h-[40px] w-full"
                                        placeholder="Select Sub Category"
                                        options={secondaryCategoryList}
                                    />
                                </Form.Item>
                            </div>

                            <Form.Item
                                className="mb-4"
                                name="gst"
                                label="GST"
                                rules={[
                                    {
                                        required: true,
                                        message: 'GST is Required',
                                    },
                                ]}
                            >
                                <Select
                                    className="mt-2 h-[40px] w-full"
                                    placeholder="Select GST"
                                    options={discountValue}
                                ></Select>
                            </Form.Item>
                            <Form.Item
                                className="mb-4"
                                name="hsn"
                                label="HSN"
                                rules={[
                                    {
                                        required: true,
                                        message: 'HSN is Required',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="HSN Code"
                                    className="h-[40px] w-full"
                                />
                            </Form.Item>
                            {/* <Form.Item
                                className="mb-4"
                                name="mfgCode"
                                label=" Manufacture Code"
                            >
                                <Input
                                    placeholder="Enter manufacturer code"
                                    className="h-[40px] w-full"
                                />
                            </Form.Item> */}
                            <Form.Item
                                label="Revenue Category"
                                name="revenueCategory"
                                rules={[
                                    {
                                        required: false,
                                        message:
                                            'Please select Revenue Category!',
                                    },
                                ]}
                            >
                                <Select
                                    showSearch
                                    placeholder="Select Revenue Category"
                                    filterOption={(input, option) =>
                                        (option?.label ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={revenueCategoryOptions?.map(
                                        (item: any) => ({
                                            label: item.name,
                                            value: item._id,
                                        })
                                    )}
                                    // onChange={() => {}}
                                />
                            </Form.Item>
                        </div>
                        {!isView && (
                            <Form.Item>
                                <Button
                                    htmlType="submit"
                                    className=" h-16 w-28 bg-purpleLight text-xl text-white lg:ms-7"
                                >
                                    <p>Save</p>
                                </Button>
                            </Form.Item>
                        )}
                    </div>

                    {/*   ----------------additional details------------------ */}

                    <div className="flex flex-row items-center gap-4 pb-5 pt-10">
                        {AdditionalDetailsTab.map((item) => (
                            <p
                                key={item.id}
                                onClick={() => setActiveTab(item.id)}
                                className={`cursor-pointer text-[#1A3353] ${
                                    activeTab === item.id ? 'font-semibold' : ''
                                }`}
                            >
                                {item.label}
                            </p>
                        ))}
                    </div>

                    {/* Ensure all tabs are part of the form */}
                    {AdditionalDetailsTab.map((item) => (
                        <Form.Item
                            key={item.id}
                            name={item.key}
                            hidden={activeTab !== item.id}
                        >
                            <ReactQuill theme="snow" readOnly={isView} />
                        </Form.Item>
                    ))}

                    {/* -----------------------------------------add variant section---------------------------------------- */}
                    {showVariants && (
                        <VariantCardWrapper
                            variantsState={variantsState}
                            setVariantsState={setVariantsState}
                            selectedItems={selectedItems}
                            setSelectedItems={setSelectedItems}
                            isView={isView}
                            setValidateVariants={setValidateVariants}
                        />
                    )}
                    {!isView && (
                        <Form.Item className="flex justify-end gap-8 py-10">
                            <Button
                                htmlType="submit"
                                className="h-16 w-28 bg-purpleLight text-xl text-white"
                            >
                                Save
                            </Button>
                        </Form.Item>
                    )}
                </Form>
            </div>
        </>
    );
};

export default CreateProduct;
