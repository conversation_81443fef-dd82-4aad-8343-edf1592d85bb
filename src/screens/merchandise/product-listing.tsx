import { MoreOutlined } from '@ant-design/icons';
import React, { useState, useEffect, useRef } from 'react';

import {
    ConfigProvider,
    Dropdown,
    Switch,
    Table,
    Menu,
    Pagination,
} from 'antd';
import { Link } from 'wouter';
import CustomTable from '~/components/common/customTable';
import { useLoader } from '~/hooks/useLoader';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';
import {
    ProductListData,
    updateProductStatus,
    DeleteProduct,
    ExportProduct,
    BulkUploadProduct,
    bulkupdateProduuct,
    getBulkUploadStatus,
    getBulkUploadErrorsPreview
} from '~/redux/actions/merchandise/product-action';
import { useAppSelector } from '~/hooks/redux-hooks';
import { navigate } from 'wouter/use-location';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import DeleteModal from '~/components/common/deleteModal';
import BulkUploadModal from './bulk-importModal';
import Alertify from '~/services/alertify';
import ErrorTableModal from './productimportErrorModal';
import { capitalizeFirstLetter, goBack } from '../../components/common/function';


function dispatchProgressEvent(payload: any) {
    window.dispatchEvent(new CustomEvent('bulk-upload-progress', { detail: payload }));
}
const ProductListing = () => {
    const pollTimerRef = useRef<number | null>(null);
    const activeBatchIdRef = useRef<string | null>(null);
    const dispatch = useDispatch<AppDispatch>();
    const params = getQueryParams();
    const [loader] = useLoader(true);
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;
    const [search, setSearch] = useState(params.search);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [currentProduct, setCurrentProduct] = useState(null);
    const [isDeleteModalVisible, setDeleteIsModalVisible] = useState(false);
    const [productId, setProductId] = useState(null);
    const [addBulkModal, setAddBulkModal] = useState(false);
    const [errorModalVisible, setErrorModalVisible] = useState(false);
    const [error_data, setErrorData] = useState([]);

    const [bulk_upload_loader, setBulkUploadLoader] = useState(false);
    const [csvFile, setCsvFile] = useState<File | null>(null);
    const [resetFile, setResetFile] = useState(false);

    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    function handleSearch(value: string) {
        console.log(value, 'value');
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    }
    const closeBulkModal = () => {
        setAddBulkModal(false);
        if (pollTimerRef.current) {
            window.clearInterval(pollTimerRef.current);
            pollTimerRef.current = null;
        }
        activeBatchIdRef.current = null;
    };

    useEffect(() => {
        console.log(search);
        const payload = {
            search,
            page: Number(currentPage),
            pageSize: Number(pageSizes),
        };
        dispatch(ProductListData(payload)).unwrap();
    }, [
        currentPage,
        pageSizes,
        search,
        confirmationModalVisible,
        isDeleteModalVisible,
        errorModalVisible,
        addBulkModal,
    ]);
    const store = useAppSelector((state: any) => ({
        productList: state.product_store.productList,
        productListCount: state.product_store.productListCount,
    }));
    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };
    const handleStatusChange = (record: any) => {
        setCurrentProduct(record);
        setConfirmationModalVisible(true);
    };
    const columns = [
        {
            title: 'Product Name ',
            dataIndex: '',
            key: '',
            render: (record: any) => (
                <Link to={`/create-product/${record._id}?view=true`}>
                    {record.name}
                </Link>
            ),
        },
        {
            title: 'SKU',
            dataIndex: 'sku',
        },
        {
            title: 'Brand',
            dataIndex: 'brandName'
        },
        {
            title: 'Type',
            dataIndex: 'type',
            render: (t: string) => <span>{capitalizeFirstLetter(t)}</span>,
        },
        {
            title: 'Category',
            dataIndex: 'category',
        },
        {
            title: 'HSN',
            dataIndex: 'hsn',
        },
        { title: 'GST', dataIndex: 'gst' },
        {
            title: 'Status',
            dataIndex: '',
            key: '',
            render: (record: any) => {
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    colorPrimaryBorder: '#8143D1',
                                    colorPrimary: '#8143D1',
                                    colorTextQuaternary: 'gray',
                                    colorFillQuaternary: 'gray',
                                },
                            },
                        }}
                    >
                        <Switch
                            id="swtich-off"
                            checkedChildren="ON"
                            unCheckedChildren="OFF"
                            checked={record?.status}
                            onChange={() => handleStatusChange(record)}
                        />
                    </ConfigProvider>
                );
            },
        },
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            render: (record: any) => {
                const menu = (
                    <Menu>
                        <Menu.Item
                            key="1"
                            onClick={() =>
                                navigate(`/create-product/${record._id}`)
                            }
                        >
                            Edit
                        </Menu.Item>
                        <Menu.Item
                            key="2"
                            onClick={() => {
                                setDeleteIsModalVisible(true);
                                setProductId(record._id);
                            }}
                        >
                            Delete
                        </Menu.Item>
                    </Menu>
                );

                return (
                    <Dropdown
                        overlay={menu}
                        trigger={['click']}
                        visible={openDropdownKey === record.key}
                        onOpenChange={(visible) => {
                            setOpenDropdownKey(visible ? record.key : null);
                        }}
                    >
                        <MoreOutlined
                            style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                            }}
                        />
                    </Dropdown>
                );
            },
        },
    ];

    const handleConfirmStatusChange = async () => {
        if (currentProduct) {
            try {
                await dispatch(
                    updateProductStatus({
                        productId: currentProduct._id,
                        status: !currentProduct.status,
                    })
                ).unwrap();

                // Fetch updated product list
                dispatch(
                    ProductListData({
                        search,
                        page: currentPage,
                        pageSize: pageSizes,
                    })
                ).unwrap();
            } catch (error) {
                console.error('Error updating product status', error);
            }
        }
        setConfirmationModalVisible(false);
    };

    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
    };

    const deleteProduct = async () => {
        if (!productId) return;
        try {
            await dispatch(DeleteProduct(productId)).unwrap();

            // setSubAttributeList((prevList: any) => prevList.filter((item: any) => item._id !== attributeId));
        } catch (error) {
            console.error('Error deleting attribute:', error);
        } finally {
            setDeleteIsModalVisible(false);
        }
    };
    const handleCSVFile = (file: File) => {
        const normalizeCsvFileType = (file: File): File => {
            const extension = file.name.split('.').pop()?.toLowerCase();
            const correctedType = extension === 'csv' ? 'text/csv' : file.type;

            return new File([file], file.name, {
                type: correctedType,
                lastModified: file.lastModified,
            });
        };
        const normalizedFile = normalizeCsvFileType(file);
        if (normalizedFile.type !== 'text/csv') {
            console.error('Invalid file type. Please upload a CSV file.');
            return;
        }

        setCsvFile(normalizedFile);
    };

    const bulkUpload = async (confirmUpdate: boolean) => {
        try {
            if (!csvFile) {
                Alertify.error('Please upload a CSV file before submitting.');
                return;
            }
            setBulkUploadLoader(true);

            const formData = new FormData();
            formData.append('productFile', csvFile);

            // enqueue (202)
            const res: any = await dispatch(
                confirmUpdate ? bulkupdateProduuct(formData) : BulkUploadProduct(formData)
            ).unwrap();

            console.log(res, "res")
            const batchId = res?.data?.batchId;
            if (!batchId) {
                Alertify.error('Failed to queue upload.');
                return;
            }

            activeBatchIdRef.current = batchId;

            // Immediately tell modal it's queued
            dispatchProgressEvent({
                status: 'queued',
                totalRows: 0,
                processedRows: 0,
                successCount: 0,
                failedCount: 0,
                batchId,
            });

            // Start polling status every 1.5s
            if (pollTimerRef.current) window.clearInterval(pollTimerRef.current);
            pollTimerRef.current = window.setInterval(async () => {
                try {
                    const doc = await dispatch(getBulkUploadStatus(batchId)).unwrap();
                    // doc: {_id, status, totalRows, processedRows, successCount, failedCount, errorFilePath}

                    const detail = {
                        status: doc?.status ?? 'processing',
                        totalRows: doc?.totalRows ?? 0,
                        processedRows: doc?.processedRows ?? 0,
                        successCount: doc?.successCount ?? 0,
                        failedCount: doc?.failedCount ?? 0,
                        errorUrl: doc?.errorFilePath ? `/product/bulkUpload/errors/${doc._id}/download` : undefined,
                        batchId: doc?._id,
                    };
                    dispatchProgressEvent(detail);

                    if (detail.status === 'completed' || detail.status === 'failed') {
                        if (pollTimerRef.current) {
                            window.clearInterval(pollTimerRef.current);
                            pollTimerRef.current = null;
                        }

                        // Refresh product list after completion (optional)
                        dispatch(
                            ProductListData({
                                search,
                                page: currentPage,
                                pageSize: pageSizes,
                            })
                        );

                        if (detail.status === 'completed') {
                            Alertify.success('Bulk upload completed');
                            setBulkUploadLoader(false)
                        } else {
                            Alertify.error('Bulk upload failed');
                            setBulkUploadLoader(false)

                        }
                        if ((detail.failedCount ?? 0) > 0 && detail.batchId) {
                            try {
                                const preview = await dispatch(getBulkUploadErrorsPreview(detail.batchId)).unwrap();
                                setErrorData(preview?.data || []);     
                                setErrorModalVisible(true);
                            } catch (e) {
                                console.error('Failed to fetch error preview', e);
                            }
                        }

                        closeBulkModal();
                    }
                } catch (e) {
                    console.error('Status poll failed', e);
                }
            }, 10000);
        } catch (err) {
            console.error(err);
            Alertify.error('Something went wrong while queuing upload.');
        } finally {
            setCsvFile(null);
            setResetFile(true);
        }
    };


    const handleExport = async () => {
        const payload = {
            fileType: 'csv',
            productType: 'both',
            responseType: 'stream',
        };
        await dispatch(ExportProduct(payload)).unwrap();
    };
    useEffect(() => {
        return () => {
            if (pollTimerRef.current) {
                window.clearInterval(pollTimerRef.current);
                pollTimerRef.current = null;
            }
        };
    }, []);
    return (
        <div>
            <CustomTable
                heading="Products"
                search={search}
                showSearch={true}
                onSearch={handleSearch}
                addNewTitle="Add Products"
                // showProductScreenButtons={true}
                addNewLink={`/create-product/0`}
                bulkImport={true}
                onBulkImportClick={() => setAddBulkModal(true)}
                showExport={true}
                handleExport={handleExport}
            />
            <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                <ConfigProvider
                    theme={{
                        components: {
                            Table: {
                                borderColor: '#0000001A',
                                cellFontSize: 13,
                                headerBg: '#fff',
                                headerColor: '#1A3353',
                                colorText: '#455560',
                            },
                        },
                    }}
                >
                    <Table
                        id=""
                        className="m-2 overflow-x-auto rounded-[6px] border-1 "
                        columns={columns}
                        dataSource={store.productList}
                        pagination={false}
                    />
                </ConfigProvider>
            </div>
            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={store.productListCount}
                    pageSize={pageSizes}
                    onChange={handlePageChange}
                    hideOnSinglePage
                />
            </div>
            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />
            {isDeleteModalVisible && (
                <DeleteModal
                    title="Confirm Delete"
                    message={`Do you want to delete this Product?`}
                    isVisible={isDeleteModalVisible}
                    onDelete={deleteProduct}
                    onCancel={() => {
                        setDeleteIsModalVisible(false);
                    }}
                />
            )}
            <BulkUploadModal
                open={addBulkModal}
                onClose={closeBulkModal}
                handleCSVFile={handleCSVFile}
                error_data={error_data}
                bulkUpload={bulkUpload}
                bulk_upload_loader={bulk_upload_loader}
                resetFile={resetFile}
                setResetFile={setResetFile}
            />
            {errorModalVisible && (
                <ErrorTableModal
                    visible={errorModalVisible}
                    data={error_data}
                    onClose={() => setErrorModalVisible(false)}
                />
            )}
        </div>
    );
};

export default ProductListing;
