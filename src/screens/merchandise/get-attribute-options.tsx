import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { GetAttributesValues } from '~/redux/actions/merchandise/product-action';
import { Form, Input, Select } from 'antd';
import type { AppDispatch } from '~/redux/store';

const GetAttributesOptions = ({
    data,
    selectAttribute,
    value = undefined,
    isView,
}) => {
    const dispatch = useDispatch<AppDispatch>(); // Use proper dispatch type
    const [text, setText] = useState(data.inputType === 'textBox' ? value : '');
    const [options, setOptions] = useState([]);

    function setAttributeData(select_value: any) {
        let newValue = '';
        const key = data.value;

        if (Array.isArray(select_value)) {
            newValue = select_value.map((item) => item.value);
        } else {
            newValue = select_value?.value || '';
        }

        selectAttribute(key, newValue);
    }

    useEffect(() => {
        let isMounted = true;
        if (data.inputType === 'dropdown') {
            console.log(data);
            dispatch(GetAttributesValues({ attribute: data.value }))
                .unwrap()
                .then((payload) => {
                    console.log(payload);
                    if (isMounted && payload?.data?.list) {
                        setOptions(payload.data.list);
                    }
                })
                .catch((error) => {
                    console.error('Error fetching attribute values:', error);
                });
        }

        return () => {
            isMounted = false;
        };
    }, [dispatch, data.value]);

    return (
        <Form.Item
            layout="vertical"
            // className="mb-4  "
            label={<label className="#1A3353">{data.label}</label>}
        >
            {data.inputType === 'textBox' ? (
                <Input
                    value={text}
                    onChange={(e) => {
                        setText(e.target.value);
                        selectAttribute(data.value, e.target.value);
                    }}
                    disabled={isView}
                    // className="w-[33.33%]"
                />
            ) : data.inputType === 'dropdown' ? (
                <Select
                    // className="w-[33.33%]"
                    placeholder="Select Value"
                    onChange={(_, option) => setAttributeData(option)}
                    mode={data.multiple ? 'multiple' : undefined}
                    value={value}
                    disabled={isView}
                >
                    {options.map((item) => (
                        <Select.Option key={item._id} value={item._id}>
                            {item.value}
                        </Select.Option>
                    ))}
                </Select>
            ) : null}
        </Form.Item>
    );
};

export default GetAttributesOptions;
