import { Button, Input, Select } from 'antd';
import { memo, useEffect, useState } from 'react';
import { useAppSelector } from '~/hooks/redux-hooks';
import GetAttributesOptions from './get-attribute-options';
import { CloseOutlined } from '@ant-design/icons';

interface Variant {
    title: string;
    sku: string;
    hsnCode: string;
    status: string;
    attributes: Record<string, string>;
}

interface VariantCardProps {
    variant: Variant;
    index: number;
    selectedItems: string[];
    variant_options: { value: string; label: string }[];
    handleVariantState: (key: keyof Variant, index: number, value: any) => void;
    isView: boolean;
    validationErrors: Record<number, Partial<Variant>>;
    removeVariant: (index: number) => void;
}

const VariantCard = memo(
    ({
        variant,
        index,
        selectedItems,
        variant_options,
        handleVariantState,
        isView,
        validationErrors,
        removeVariant,
    }: VariantCardProps) => {
        return (
            <div className="variant-section mt-6 rounded-lg border-1 p-6">
                <div className="flex items-center justify-between pb-7">
                    <h1 className="font-semibold text-[#1a3353] md:text-[1vw]">
                        Variant {index + 1}
                    </h1>
                    {!isView && index !== 0 && (
                        <div
                            className="cursor-pointer"
                            onClick={() => removeVariant(index)}
                        >
                            <img
                                src="/icons/common/delete.svg"
                                alt="Delete"
                                className="h-[20px] w-[20px] hover:opacity-75"
                            />
                        </div>
                    )}
                </div>

                <div className="grid gap-x-5 lg:grid-cols-4 @sm:grid-cols-2">
                    {selectedItems.map((selectedItem) => {
                        const selectedOption = variant_options?.find(
                            (option) => option.value === selectedItem
                        );
                        if (!selectedOption) return null;

                        return (
                            <div key={selectedOption.value}>
                                <GetAttributesOptions
                                    data={selectedOption}
                                    selectAttribute={(attr_key, value) =>
                                        handleVariantState(
                                            'attributes',
                                            index,
                                            { attr_key, value }
                                        )
                                    }
                                    value={
                                        variant.attributes?.[
                                            selectedOption.value
                                        ] || ''
                                    }
                                    isView={isView}
                                />
                            </div>
                        );
                    })}
                </div>

                <div className="flex lg:flex-row lg:gap-4 @sm:flex-col">
                    <div className="mb-4 lg:w-[33.33%]">
                        <label className="text-[13px] font-medium text-[#1A3353]">
                            SKU <span className="text-red-500"> * </span>
                        </label>
                        <Input
                            placeholder="SKU"
                            className="mt-2 w-full"
                            onChange={(e) =>
                                handleVariantState('sku', index, e.target.value)
                            }
                            value={variant.sku || ''}
                            disabled={isView}
                        />
                        {validationErrors[index]?.sku && (
                            <p className="text-[12px] text-red-500">
                                {validationErrors[index].sku}
                            </p>
                        )}
                    </div>
                    <div className="mb-4 lg:w-[33.33%]">
                        <label className="text-[13px] font-medium text-[#1A3353]">
                            Variant Title{' '}
                            <span className="text-red-500"> * </span>
                        </label>
                        <Input
                            placeholder="Variant Title"
                            className="mt-2 w-full"
                            onChange={(e) =>
                                handleVariantState(
                                    'title',
                                    index,
                                    e.target.value
                                )
                            }
                            value={variant.title || ''}
                            disabled={isView}
                        />
                        {validationErrors[index]?.title && (
                            <p className="text-[12px] text-red-500">
                                {validationErrors[index].title}
                            </p>
                        )}
                    </div>
                    {/* <div className="mb-4 lg:w-[33.33%]">
                        <label className="text-[13px] font-medium text-[#1A3353]">
                            HSN Code <span className="text-red-500"> * </span>
                        </label>
                        <Input
                            placeholder="HSN Code"
                            className="mt-2 w-full"
                            onChange={(e) =>
                                handleVariantState(
                                    'hsnCode',
                                    index,
                                    e.target.value
                                )
                            }
                            value={variant.hsnCode || ''}
                            disabled={isView}
                        />
                        {validationErrors[index]?.hsnCode && (
                            <p className="text-[12px] text-red-500">
                                {validationErrors[index].hsnCode}
                            </p>
                        )}
                    </div> */}
                </div>
            </div>
        );
    }
);

interface VariantCardWrapperProps {
    variantsState: Variant[];
    setVariantsState: (variants: Variant[]) => void;
    selectedItems: string[];
    setSelectedItems: (items: string[]) => void;
    isView: boolean;
    setValidateVariants: (validateFn: () => boolean) => void;
}

const VariantCardWrapper = ({
    variantsState,
    setVariantsState,
    selectedItems,
    setSelectedItems,
    isView,
    setValidateVariants,
}: VariantCardWrapperProps) => {
    const [validationErrors, setValidationErrors] = useState<
        Record<number, Partial<Variant>>
    >({});

    const store = useAppSelector((state) => ({
        attributes_mapping: state?.container_store?.attributes_mapping || {},
        active_attributes_mapping:state?.container_store?.active_attributes_mapping || {},
    }));

    const variantOptions = store.active_attributes_mapping?.variant || [];

    const addVariant = () => {
        setVariantsState((prev) => [
            ...prev,
            {
                title: '',
                sku: '',
                hsnCode: '',
                status: true,
                attributes: {},
            },
        ]);
    };
    const removeVariant = (index: number) => {
        setVariantsState((prev) => {
            if (prev.length === 1) return prev; // Ensure at least one variant remains
            return prev.filter((_, i) => i !== index);
        });
    };
    const handleSelectChange = (selected: string[]) => {
        setSelectedItems(selected);
        if (selected.length === 0 && variantsState.length === 0) {
            addVariant();
        }
    };

    const handleVariantState = (
        key: keyof Variant,
        index: number,
        value: any
    ) => {
        setVariantsState((prev) => {
            const updatedVariant = { ...prev[index] };
            if (key === 'attributes') {
                updatedVariant.attributes = {
                    ...updatedVariant.attributes,
                    [value.attr_key]: value.value,
                };
            } else {
                updatedVariant[key] = value;
            }

            const newVariants = [...prev];
            newVariants[index] = updatedVariant;
            return newVariants;
        });

        setValidationErrors((prevErrors) => {
            const newErrors = { ...prevErrors };
            if (newErrors[index]) {
                newErrors[index] = { ...newErrors[index], [key]: undefined };
            }
            return newErrors;
        });
    };

    const validateVariants = (): boolean => {
        const errors: Record<number, Partial<Variant>> = {};
        let isValid = true;

        variantsState.forEach((variant, index) => {
            const errorObj: Partial<Variant> = {};
            if (!variant.title) {
                errorObj.title = 'Variant Title is required';
                isValid = false;
            }
            if (!variant.sku) {
                errorObj.sku = 'SKU is required';
                isValid = false;
            }
           
            if (Object.keys(errorObj).length > 0) errors[index] = errorObj;
        });

        setValidationErrors(errors);
        return isValid;
    };

    useEffect(() => {
        setValidateVariants(() => validateVariants);
    }, [setValidateVariants, variantsState]);

    return (
        <div className="pt-8 lg:mt-8 lg:px-8">
            <div className="mb-6 flex items-center gap-5">
                <h1 className="font-semibold text-[#1a3353] md:text-[1.5vw]">
                    Variants
                </h1>
                <Select
                    mode="multiple"
                    className="w-full"
                    maxTagCount="responsive"
                    placeholder="Select Variant"
                    onChange={handleSelectChange}
                    value={selectedItems}
                    options={variantOptions}
                    disabled={isView}
                />
            </div>
            {variantsState.map((variant, index) => (
                <VariantCard
                    key={index}
                    variant={variant}
                    index={index}
                    selectedItems={selectedItems}
                    variant_options={variantOptions}
                    handleVariantState={handleVariantState}
                    isView={isView}
                    validationErrors={validationErrors}
                    removeVariant={removeVariant}
                />
            ))}
            {!isView && (
                <div className="flex justify-center">
                    <Button
                        onClick={addVariant}
                        className="fw-500  mt-6 h-16 w-40 bg-purpleLight text-xl text-white"
                    >
                        + Add Variant
                    </Button>
                </div>
            )}
        </div>
    );
};

export default memo(VariantCardWrapper);