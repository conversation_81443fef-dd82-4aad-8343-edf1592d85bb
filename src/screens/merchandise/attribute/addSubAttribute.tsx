import {
    ConfigProvider,
    Dropdown,
    Table,
    Pagination,
    Menu,
    Typography,
    Form,
    Input,
    ColorPicker,
    Button,
} from 'antd';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { navigate } from 'wouter/use-location';
import { useLoader } from '~/hooks/useLoader';
import { Link, useParams } from 'wouter';
import { capitalizeFirstLetter } from '../../../components/common/function';

import {
    CreateSubAttribute,
    AttributeValueListData,
    UpdateAttribute,
} from '~/redux/actions/merchandise/attribute-action';
const { Title } = Typography;

const AddsubAttribute = () => {
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const { id, attribute_id } = useParams<{
        id: string;
        attribute_id: string;
    }>();
    const [loader, startLoader, endLoader] = useLoader(false);
    const [color, setColor] = useState('#ffffff');

    function goBack() {
        window.history.back();
    }

    const cleanAttributeId = decodeURIComponent(id).replace(/^\/+/, '');
    const onFinish = (values: any) => {
        const { name, color } = values;
        const payload = {
            attribute: cleanAttributeId,
            value: name,
            hexCode: color,
        };
        if (attribute_id !== '0') {
            dispatch(UpdateAttribute({ payload, id: attribute_id })).then(
                (res: any) => {
                    console.log(res, 'ressssssss');
                    if (
                        res.payload.status === 200 ||
                        res.payload.status === 201
                    ) {
                        navigate(`/attributes/sub-attribute-list/${id}`);
                        form.resetFields();
                    }
                }
            );
        } else {
            dispatch(CreateSubAttribute(payload)).then((res: any) => {
                if (res.payload.status === 200 || res.payload.status === 201) {
                    navigate(`/attributes/sub-attribute-list/${id}`);
                    form.resetFields();
                }
            });
        }
    };

    const handleColorChange = (newColor: any) => {
        console.log(newColor);
        console.log(newColor?.toHexString(), 'lllll');
        setColor(newColor?.toHexString());
        form.setFieldsValue({ color: newColor?.toHexString() });
    };

    useEffect(() => {
        if (attribute_id !== '0') {
            dispatch(AttributeValueListData(attribute_id)).then((res: any) => {
                const subAttributeData = res.payload.data;
                if (res.payload.data) {
                    form.setFieldsValue({
                        name: subAttributeData.value,
                    });
                    if (id === 'shade') {
                        setColor(subAttributeData.hexCode);
                    }
                }
            });
        }
    }, []);
    return (
        <>
            <div className="flex justify-between">
                <div className="flex items-center gap-4 lg:mb-16 @sm:mb-10 ">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353]" level={4}>
                        {attribute_id === '0'
                            ? `Add Sub-Attribute of ${capitalizeFirstLetter(cleanAttributeId)}`
                            : `Edit Sub-Attribute of ${capitalizeFirstLetter(cleanAttributeId)}`}
                    </Title>
                </div>
            </div>
            <div className="rounded-lg border p-5 lg:w-[80%] lg:p-10 @sm:w-full">
                <Form
                    name="gymCreate"
                    layout="vertical"
                    size="large"
                    form={form}
                    onFinish={onFinish}
                    autoComplete="off"
                >
                    <div className="mt-6 flex gap-8">
                        <Form.Item
                            className="lg:w-1/2 @sm:w-[100%]"
                            name="name"
                            label="Name"
                            rules={[
                                {
                                    required: true,
                                    message: 'Attribute Name is Required',
                                },
                            ]}
                        >
                            <Input
                                placeholder="Attribute Name"
                                className="h-[40px]"
                            />
                        </Form.Item>

                        {/* {id === 'shade' ? (
                            <Form.Item
                                label="Color"
                                className="w-[100%] "
                                name="color"
                                initialValue={color}
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please pick a color!',
                                    },
                                ]}
                            >
                                <div
                                    className="flex h-[30px] w-[100%] items-center  border-[#ccc]"
                                    style={{
                                        borderRadius: '4px',
                                        padding: '5px',
                                        cursor: 'pointer',
                                    }}
                                >
                                    <ColorPicker
                                        format="hex"
                                        value={color}
                                        onChange={handleColorChange}
                                        style={{
                                            display: 'inline-block',
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: color,
                                        }}
                                    />
                                </div>
                            </Form.Item>
                        ) : null} */}
                    </div>
                    <Form.Item className="flex flex-row justify-end gap-8 py-10  ">
                        <Button
                            onClick={goBack}
                            htmlType="button"
                            className="w-36 border-[#1A3353] bg-[#fff] text-xl text-[#1A3353]"
                        >
                            <p>Cancel</p>
                        </Button>
                        <Button
                            htmlType="submit"
                            className="ms-7 w-36 bg-purpleLight text-xl text-white"
                        >
                            <p>Save</p>
                        </Button>
                    </Form.Item>
                </Form>
            </div>
        </>
    );
};
export default AddsubAttribute;
