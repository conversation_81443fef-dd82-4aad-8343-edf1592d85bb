import { ConfigProvider, Dropdown, Table, Pagination, <PERSON>u, Button, Typography, Switch } from 'antd';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';
import { navigate } from 'wouter/use-location';
import { useLoader } from '~/hooks/useLoader';
import { AttributeListData, deleteDynamicAttribute, updateDynamicAttributeStatus } from '~/redux/actions/merchandise/attribute-action';
import { useAppSelector } from '~/hooks/redux-hooks';
import AddAttributeModal from './addAttributeModal';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { MoreOutlined } from '@ant-design/icons';
import clsx from 'clsx';
import DeleteModal from '~/components/common/deleteModal';
import { Link } from 'wouter';

const { Title, Text } = Typography;
const AttributeListings = () => {
    const dispatch = useDispatch<AppDispatch>();
    const params = getQueryParams();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const [search, setSearch] = useState(params.search);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSize, setPageSize] = useState<number>(10);
    const [loader, startLoader, endLoader] = useLoader(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [deleteModalVisible, setDeleteModalVisible] = useState(false);
    const [confirmModalVisible, setConfirmModalVisible] = useState(false);
    const [recordId, setRecordId] = useState<string>('');
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [record, setRecord] = useState<any>(null);

    const attributeList = useAppSelector(
        (state: any) => state?.attributes_store?.attributeList
    );
    const attributeListCount = useAppSelector(
        (state: any) => state?.attributes_store?.attributeCount
    );
    const fetchAttributeList = async () => {
        try {
            await dispatch(AttributeListData()).unwrap();
        } catch (error: any) {
            console.error(error);
        }
    };
    const paginate = (pageNumber: number, pageSize?: number) => {
        setCurrentPage(pageNumber);
        if (pageSize && pageSize !== pageSizeParam) {
            setPageSize(pageSize);
        }
        navigate(`?page=${pageNumber}&pageSize=${pageSize || pageSizeParam}`);
    };

    useEffect(() => {
        fetchAttributeList();
    }, [currentPage, pageSize, search, modalVisible, deleteModalVisible, confirmModalVisible]);
    const indexOfLastItem = currentPage * pageSize;
    const indexOfFirstItem = indexOfLastItem - pageSize;
    const currentItems = attributeList?.slice(
        indexOfFirstItem,
        indexOfLastItem
    );
    function goBack() {
        window.history.back();
    }
    const handleStatusChange = (record: any) => {
        setRecordId(record.key);
        setRecord(record)
        setConfirmModalVisible(true);
    };
    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            render: (_: any, record: any) => (
                
                <Link
                    to={`/attributes/sub-attribute-list/${record.name}`}
                >
                    {record.name}
                </Link>
            ),
        },
        // {
        //     title: 'Slug',
        //     dataIndex: 'slug',
        // },
        {
            title: 'Status',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                if (record?.key === 'brand' || record?.key === 'shade') {
                    return null; // or return '-' if you want a placeholder
                }

                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    handleBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Switch
                            className={clsx(
                                'rounded-full transition-colors',
                                record.status ? 'bg-switch-on' : 'bg-switch-off'
                            )}
                            id="switch-off"
                            checkedChildren="ON"
                            unCheckedChildren="OFF"
                            checked={record.isActive || false}
                            onChange={() => handleStatusChange(record)}
                        />
                    </ConfigProvider>
                );
            },
        },

        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            render: (record: any) => {
                const menu = (
                    <Menu>
                        {
                            (record?.key !== 'brand' && record?.key !== 'shade') && (
                                <>
                                    <Menu.Item
                                        key="3"
                                        onClick={() => {
                                            setModalVisible(true);
                                            setRecordId(record.key);
                                        }}
                                    >
                                        Edit Attribute
                                    </Menu.Item>
                                </>

                            )}
                        <Menu.Item key="1" onClick={() => navigate(`/attributes/sub-attribute-list/${record.name}`)}>
                            Add Sub-Attribute
                        </Menu.Item>
                        {
                            (record?.key !== 'brand' && record?.key !== 'shade') && (
                                <>
                                    <Menu.Item
                                        key="2"
                                        onClick={() => {
                                            setRecordId(record.key);
                                            setDeleteModalVisible(true);
                                        }}
                                    >
                                        Delete
                                    </Menu.Item>
                                </>
                            )}

                    </Menu>
                );


                return (
                    <Dropdown
                        overlay={menu}
                        trigger={['click']}
                        visible={openDropdownKey === record.key}
                        onOpenChange={(visible) => {
                            setOpenDropdownKey(visible ? record.key : null);
                        }}
                    >
                        <MoreOutlined
                            style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                            }}
                        />
                    </Dropdown>
                );

            },
        },
    ];
    const handleDeleteConfirm = () => {
        if (!recordId) return;
        startLoader();
        dispatch(deleteDynamicAttribute(recordId))
            .unwrap()
            .then(() => {
                endLoader();
                setDeleteModalVisible(false);
                setRecordId('');
            })
            .catch((error: any) => {
                console.error(error);
                endLoader();
            })
            .finally(() => {
                setDeleteModalVisible(false);
                setRecordId('');
            })
    }
    const handleConfirmStatusChange = () => {

        if (recordId === '') return;
        startLoader();
        dispatch(updateDynamicAttributeStatus({ id: recordId, payload: { isActive: !record?.isActive } }))
            .unwrap()
            .then(() => {
                endLoader();
                setConfirmModalVisible(false);
                setRecordId('');
            })
            .catch((error: any) => {
                console.error(error);
                endLoader();
            })
            .finally(() => {
                setConfirmModalVisible(false);
                setRecordId('');
            });
    };
    return (
        <>
            <div>
                <div className="mb-4 flex items-center justify-between">
                    <div className="flex items-center gap-5">
                        <img
                            src="/icons/back.svg"
                            alt="edit"
                            className="h-[10px] cursor-pointer"
                            onClick={goBack}
                        />
                        <Title className="text-[#1A3353] " level={4}>
                            Attribute Listing
                        </Title>
                    </div>
                    <Button
                        className="fw-500 flex w-[100px] items-center justify-center rounded-2xl bg-purpleLight py-3 text-xl text-white"
                        onClick={() => setModalVisible(true)}
                    >
                        Add New +
                    </Button>
                </div>
                <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                    <ConfigProvider
                        theme={{
                            components: {
                                Table: {
                                    borderColor: '#0000001A',
                                    cellFontSize: 13,
                                    headerBg: '#fff',
                                    headerColor: '#1A3353',
                                    colorText: '#455560',
                                },
                            },
                        }}
                    >
                        <Table
                            className="m-2 overflow-x-auto rounded-[6px] border-1"
                            columns={columns}
                            dataSource={currentItems}
                            pagination={false}
                        />
                    </ConfigProvider>
                </div>
                <div className="flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={attributeListCount}
                        pageSize={pageSize}
                        onChange={paginate}
                        showSizeChanger
                    // hideOnSinglePage
                    />
                </div>
                {
                    modalVisible && (
                        <AddAttributeModal visible={modalVisible}
                            onClose={() => { setModalVisible(false); setRecordId('') }}
                            attributeId={recordId} />
                    )
                }
            </div>
            {
                confirmModalVisible && (
                    <CommonConfirmationModal
                        visible={confirmModalVisible}
                        message="  Are you sure you want to Change the Status of this attribute?"

                        onConfirm={handleConfirmStatusChange}
                        onCancel={() => {
                            setConfirmModalVisible(false);
                            setRecordId('');
                        }}
                    />
                )
            }

            {deleteModalVisible && (
                <DeleteModal
                    title="Confirm Delete"
                    message={`Do you want to delete this Room?`}
                    isVisible={deleteModalVisible}
                    onDelete={handleDeleteConfirm}
                    onCancel={() => {
                        setDeleteModalVisible(false);
                        setRecordId('');
                    }}
                />
            )}
        </>
    );
};

export default AttributeListings;
