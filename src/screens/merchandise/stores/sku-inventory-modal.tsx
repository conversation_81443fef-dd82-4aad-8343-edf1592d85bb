import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
    ConfigProvider,
    DatePicker,
    Form,
    Input,
    Modal,
    Select,
    Button,
    Table,
    Spin,
    Radio,
    Typography,
} from 'antd';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import {
    SearchProductBySkuDetails,
    CreateNewInventory,
    updateInventory,
} from '~/redux/actions/merchandise/inventory-action';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import dayjs, { Dayjs } from 'dayjs';
import {
    AddInventoryProductDiscount,
    DiscountListOnPricePage,
} from '~/redux/actions/createDiscountAction';
import Alertify from '~/services/alertify';
import AddDiscountModal from '~/components/modals/addDiscountOnPricing';
import TextArea from 'antd/es/input/TextArea';
import AddInventoryDiscountModal from '~/components/modals/addDiscountOnInventory';
import { useLoader } from '~/hooks/useLoader';

interface SkuInventoryModalProps {
    visible: boolean;
    onClose: () => void;
    storeId: string;
    inventoryId: string;
    inventroyDetail?: any;
    inventoryList?: any;
    isPricingEdit?: boolean;
}

const SkuInventoryModal: React.FC<SkuInventoryModalProps> = ({
    visible,
    onClose,
    storeId,
    inventoryId,
    inventroyDetail,
    inventoryList,
    isPricingEdit = false,
}) => {
    const INITIAL_STATE = {
        productType: 'simple',
        productName: '',
        sku: '',
        status: 'active',
        expiryDate: '',
        storeId,
        discount: '',
        mrp: '',
        salePrice: '',
        gst: '',
        discountPrice: '',
        productVariantId: undefined,
        id: inventoryId,
        productId: '',
    };
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const [selectedProduct, setSelectedProduct] = useState(INITIAL_STATE);
    const [selectedProductType, setSelectedProductType] = useState('simple');
    const [inventoryIds, setInventoryIds] = useState(inventoryId);
    const [discountData, setDiscountData] = useState<any[]>([]);
    const [selectedDiscount, setSelectedDiscount] = useState<string[]>([]);
    const [comingDiscounts, setComingDiscounts] = useState<string[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [loading, setLoading] = useState(false);
    const [totalPages, setTotalPages] = useState(0);
    const [addDiscountModal, setAddDiscountModal] = useState<boolean>(false);
    const [autoApplyId, setAutoApplyId] = useState<string>('');
    const [isEditable, setIsEditable] = useState<boolean>(false);
    const [view, setView] = useState<string>('false');
    const pageSize = 10;
    const [loader, startLoader, endLoader] = useLoader();

    const tableContainerRef = useRef<HTMLDivElement>(null);

    const [productId, setProductId] = useState(
        inventroyDetail?.productId || null
    );
    const [skuOptions, setSkuOptions] = useState<
        { value: string; label: string }[]
    >([]);
    const [actionType, setActionType] = useState<any>(null);
    const [currentQty, setCurrentQty] = useState<number>(0);
    const [computedTotal, setComputedTotal] = useState<number>(0);

    const handleInventoryDetails = (key: string, value: any) => {
        let data = { ...selectedProduct, [key]: value };
        if ((key === 'discount' || key === 'salePrice') && !!data.salePrice) {
            if (data.discount) {
                const discountPrice =
                    Number(data.salePrice) -
                    (Number(data.salePrice) * Number(data.discount)) / 100;
                const afterGst =
                    (discountPrice * Number(data.gst)) / 100 + discountPrice;

                data = {
                    ...data,
                    discountPrice: Number(
                        Math.trunc(discountPrice * 100) / 100
                    ),
                };

                form.setFieldsValue({
                    discountPrice: Number(
                        Math.trunc(discountPrice * 100) / 100
                    ),
                });
            } else {
                form.setFieldsValue({
                    discountPrice: Number(
                        Math.trunc(data.salePrice * 100) / 100
                    ),
                });
            }
        }

        setSelectedProduct(data);
    };

    console.log('InventoryId-------------', inventroyDetail);

    const fetchApplicableDiscounts = useCallback(
        async (productId: string, variantId?: string) => {
            try {
                setLoading(true);
                const response = await dispatch(
                    AddInventoryProductDiscount({
                        productId,
                        variantId: variantId || '',
                    })
                ).unwrap();

                const discounts = response?.data?.data || [];
                setDiscountData(discounts);

                const discountIds = discounts?.map((item: any) => item._id);
                setComingDiscounts(discountIds);
                setSelectedDiscount(discountIds);

                setCurrentPage(1);
                setHasMore(false);
                setTotalPages(1);
            } catch (error: any) {
                console.error('Error fetching applicable discounts:', error);
                setDiscountData([]);
                setComingDiscounts([]);
            } finally {
                setLoading(false);
            }
        },
        [dispatch]
    );
    // put this helper somewhere shared
    const EXPIRY_FORMATS = [
        'YYYY-MM-DDTHH:mm:ss.SSS[Z]',
        'YYYY-MM-DDTHH:mm:ss[Z]',
        'YYYY-MM-DD',
        'DD/MM/YYYY',
    ];

    function toDayjsOrNull(v: unknown) {
        if (v == null || v === '') return null;

        if (typeof v === 'number') {
            const ms = v > 1e12 ? v : v * 1000;
            const d = dayjs(ms);
            return d.isValid() ? d : null;
        }

        if (typeof v === 'string') {
            let d = dayjs(v, EXPIRY_FORMATS, true);
            if (d.isValid()) return d;

            d = dayjs(v);
            return d.isValid() ? d : null;
        }


        if (typeof v === 'object' && v?.isValid?.()) return v;

        return null;
    }

    useEffect(() => {
        if (inventroyDetail) {
            console.log(inventroyDetail, 'inventory Detail');
            setSelectedProductType(inventroyDetail.productType);
            const expiry =
                inventroyDetail.expiryDate &&
                dayjs(inventroyDetail.expiryDate, 'DD/MM/YYYY');
            form.setFieldsValue({
                productType: inventroyDetail.productType,
                sku: inventroyDetail.sku || '',
                productName: inventroyDetail.name || '',
                mrp: inventroyDetail.mrp || '',
                salePrice: inventroyDetail.salePrice || '',
                discountPercentage: inventroyDetail.discount || '',
                discountPrice: inventroyDetail.discountPrice || '',
                quantity: inventroyDetail.quantity,
                expiryDate: toDayjsOrNull(inventroyDetail.expiryDate)
            });
            setSelectedProduct({
                ...inventroyDetail,
                storeId: storeId,
            });
            setAutoApplyId(inventroyDetail.promotion);
            const qty = Number(inventroyDetail.quantity || 0);
            setCurrentQty(qty);
            // setActionType('restock');
            setComputedTotal(qty);
        }
    }, [inventroyDetail]);
    const recalcTotal = (qtyInput?: string | number) => {
        const q =
            typeof qtyInput === 'string' && qtyInput.trim() === '-'
                ? -0
                : Number(qtyInput ?? 0);

        const total = currentQty + q;

        setComputedTotal(total);

        // Live validation: restock cannot make total negative
        if (actionType === 'restock' && total < 0) {
            form.setFields([
                {
                    name: 'adjustQty',
                    errors: ['Adjustment would make inventory negative'],
                },
            ]);
        } else {
            // Clear prior error if any
            form.setFields([{ name: 'adjustQty', errors: [] }]);
        }
    };

    const searchProductBySku = async (searchText = '', page: number) => {
        try {
            const response = await dispatch(
                SearchProductBySkuDetails({
                    page,
                    pageSize: 10,
                    search: searchText,
                    productType: selectedProductType,
                })
            ).unwrap();

            return (
                response?.data?.data?.map((item: any, index: any) => ({
                    value: item._id,
                    label: item.sku + ' - ' + item.name,
                    productName: item.name,
                    sku: item.sku,
                    batch: item.batch,
                    gst: item.gst,
                    id: item._id,
                    productId: item.productId ?? item._id,
                    productVariantId: item._id,
                    index: index,
                    productType: item.type ?? 'simple',
                    discount: item.discount,
                    status: item.status,
                    mrp: item.mrp,
                    salePrice: item.salePrice,
                    expiryDate: item.expiryDate,
                })) || []
            );
        } catch (error) {
            console.error('Error fetching SKU:', error);
            return [];
        }
    };

    const handleProductTypeChange = async (value: string) => {
        form.setFieldsValue({
            sku: null,
            productName: null,
        });
        setSelectedProductType(value);
        handleInventoryDetails('productType', value);
        setSelectedProduct(INITIAL_STATE);
    };

    const disabledDate = (current: Dayjs) => {
        // Disable dates before today
        return current && current < dayjs().startOf('day');
    };
    const onFinish = (values: any) => {
        if (selectedProduct.productType === 'simple') {
            delete (selectedProduct as any).productVariantId;
        }

        const isCreate =
            inventoryId === '0' && inventoryIds === '0' && !inventroyDetail;

        const basePayload: any = {
            productType: values.productType || selectedProduct.productType,
            mrp: Number(values.mrp),
            // salePrice: Number(values.salePrice),
            expiryDate: values.expiryDate
                ? values.expiryDate.toISOString()
                : null,
            storeId: selectedProduct.storeId,
            ...(autoApplyId && { promotionId: autoApplyId }),
            applyPromotion: selectedDiscount,
            productId: selectedProduct.productId,
            productVariantId:
                selectedProduct?.productVariantId ||
                inventroyDetail?.productVariantId ||
                undefined,
        };
        if (basePayload.productType === 'simple') {
            delete basePayload.productVariantId;
        }

        startLoader();

        if (isCreate) {
            // ---------- CREATE: keep old behavior (send quantity) ----------
            const payload = {
                ...basePayload,
                quantity: Number(values.quantity),
            };

            dispatch(CreateNewInventory(payload))
                .unwrap()
                .then(() => {
                    onClose();
                    form.resetFields();
                    setSelectedProduct(INITIAL_STATE);
                    setSelectedProductType('simple');
                })
                .finally(endLoader);
            return;
        }

        // ---------- UPDATE: send inventoryAction + adjustQty (+ notes), not quantity ----------
        const actionTypeForApi = actionType; // 'restock' | 'adjustment'
        const rawAdj = Number(values.adjustQty ?? 0);
        let finalPreview: number;

        if (actionTypeForApi === 'restock') {
            // delta (+/-)
            finalPreview = Number(currentQty) + rawAdj;
        } else {
            finalPreview = rawAdj;
        }

        const updatePayload: any = {
            ...basePayload,
            inventoryAction: actionTypeForApi,
            adjustQty: rawAdj,
            notes: values.notes || undefined,
        };

        updatePayload.productId = inventroyDetail?.productId
            ? inventroyDetail?.productId
            : productId;

        dispatch(
            updateInventory({
                reqData: updatePayload,
                inventoryId: inventroyDetail?._id
                    ? inventroyDetail?._id
                    : inventoryIds,
            })
        )
            .unwrap()
            .then(() => {
                onClose();
                form.resetFields();
                setSelectedProduct(INITIAL_STATE);
                setSelectedProductType('simple');
            })
            .finally(endLoader);
    };

    const handleCancel = () => {
        form.resetFields();
        setSelectedProduct(INITIAL_STATE);
        setSelectedProductType('simple');
        onClose();
        setDiscountData([]);
    };

    const fetchDiscountData = useCallback(
        (pageNumber: number, isScroll: boolean = false) => {
            if (inventoryId === '0') return;
            if (isScroll && (loading || !hasMore)) return;

            setLoading(true);
            dispatch(
                DiscountListOnPricePage({
                    pricingId: inventoryId,
                    currentPage: pageNumber,
                    pageSize: pageSize,
                })
            )
                .then((response: any) => {
                    const newData = response?.payload?.data?.data || [];
                    const metadata =
                        response?.payload?.data?._metadata?.pagination;
                    const totalPagesFromAPI = metadata?.totalPages || 0;
                    // Update total pages from API response
                    setTotalPages(totalPagesFromAPI);

                    if (isScroll) {
                        setDiscountData((prev: any[]) => {
                            return [...prev, ...newData];
                        });
                    } else {
                        setDiscountData(newData);
                        // IMPORTANT: Reset comingDiscounts to only current discounts
                        const currentDiscountIds = newData.map(
                            (item: any) => item._id
                        );
                        setComingDiscounts(currentDiscountIds);
                    }

                    // Check if there's more data based on totalPages from API
                    if (totalPagesFromAPI > 0) {
                        // Use API metadata to determine if there are more pages
                        const hasMorePages = pageNumber < totalPagesFromAPI;
                        setHasMore(hasMorePages);
                    } else {
                        // Fallback to data length check if no metadata
                        if (newData.length < pageSize) {
                            setHasMore(false);
                        }
                    }

                    // Merge new discount IDs with existing comingDiscounts, avoiding duplicates
                    // const newDiscounts = newData.map((item: any) => item._id);
                    // setComingDiscounts((prev: string[]) =>
                    //     Array.from(new Set([...prev, ...newDiscounts]))
                    // );
                })
                .catch((error: any) => {
                    Alertify.error(
                        'Could not get the discounts list. Please try again later.'
                    );
                    console.log('Error in fetch discounts list:', error);
                })
                .finally(() => {
                    setLoading(false);
                });
        },
        [inventoryId, loading, hasMore, pageSize, dispatch]
    );

    // Scroll handler for infinite scroll
    const handleScroll = useCallback(() => {
        const container = tableContainerRef.current;
        if (container) {
            const { scrollTop, scrollHeight, clientHeight } = container;
            if (
                scrollTop + clientHeight >= scrollHeight - 10 &&
                hasMore &&
                !loading
            ) {
                const nextPage = currentPage + 1;
                setCurrentPage(nextPage);
                fetchDiscountData(nextPage, true);
            }
        }
    }, [hasMore, loading, currentPage, fetchDiscountData]);

    // Add scroll event listener
    useEffect(() => {
        const container = tableContainerRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);
        }
        return () => {
            if (container) {
                container.removeEventListener('scroll', handleScroll);
            }
        };
    }, [handleScroll]);

    useEffect(() => {
        if (inventoryId !== '0') {
            setCurrentPage(1);
            setHasMore(true);
            setTotalPages(0);
            setDiscountData([]);
            setComingDiscounts([]);
            fetchDiscountData(1, false);
        }
    }, [inventoryId, selectedDiscount, addDiscountModal]);

    // Debug effect to check scroll container
    useEffect(() => {
        const container = tableContainerRef.current;
    }, [discountData]);

    const discountColumns = [
        {
            key: 'name',
            title: 'Name',
            dataIndex: 'name',
        },
        {
            key: 'type',
            title: 'Type',
            dataIndex: 'type',
        },
        {
            key: 'value',
            title: 'Value',
            dataIndex: 'value',
        },
        {
            key: 'action',
            title: 'Action',
            render: (record: any) => {
                return (
                    <Button
                        key={record._id}
                        className={`h-[30px] rounded-md p-2 py-0 text-xl ${(view === 'true' && !isEditable) ||
                                autoApplyId === record._id
                                ? 'text-grey bg-[#ccc]'
                                : 'bg-purpleLight text-white'
                            }`}
                        disabled={
                            (view === 'true' && !isEditable ? true : false) ||
                            autoApplyId === record._id
                        }
                        onClick={() => {
                            setAutoApplyId(record._id);
                            // if (id != '0') {
                            //     form.submit();
                            // }
                        }}
                    >
                        Auto Apply
                    </Button>
                );
            },
        },
    ];

    console.log(
        'discountData------------',
        inventoryId,
        isPricingEdit,
        autoApplyId
    );

    return (
        <Modal
            title={
                <p className="w-fit border-b-2 border-primary text-2xl text-[#1A3353]">
                    {isPricingEdit === true
                        ? 'Edit Pricing'
                        : inventroyDetail
                            ? 'Edit Inventory'
                            : 'Add Inventory'}
                </p>
            }
            className="lg:w-[55%]"
            footer={false}
            centered
            visible={visible}
            onCancel={handleCancel}
            onOk={onClose}
        >
            <ConfigProvider
                theme={{
                    components: {
                        Input: {},
                        Form: {
                            verticalLabelMargin: -5,
                        },
                    },
                }}
            >
                <Form
                    name="gymCreate"
                    layout="vertical"
                    size="large"
                    form={form}
                    onFinish={onFinish}
                    autoComplete="off"
                >
                    <div className="flex flex-row  justify-between pt-8">
                        <Form.Item
                            className="w-[48%]"
                            label="Product Type"
                            name="productType"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select Product',
                                },
                            ]}
                        >
                            <Select
                                className="h-16"
                                placeholder="Select Product"
                                options={[
                                    {
                                        value: 'simple',
                                        label: 'Simple Product',
                                    },
                                    { value: 'variable', label: 'Variable' },
                                ]}
                                value={selectedProductType}
                                onChange={handleProductTypeChange}
                                disabled={inventroyDetail ? true : false}
                            />
                        </Form.Item>

                        <Form.Item
                            className="w-[48%]"
                            label="SKU"
                            name="sku"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select SKU',
                                },
                            ]}
                        >
                            <InfiniteScrollSelect
                                className="border-b border-[#d1d5db]"
                                fetchOptions={searchProductBySku}
                                onChange={(value, option) => {
                                    const extractedProductName =
                                        option?.productName ||
                                        option?.label?.split(' - ')[1] ||
                                        '';

                                    let filteredInventory = [];

                                    if (option.productType === 'variable') {
                                        filteredInventory =
                                            inventoryList?.filter(
                                                (item: any) =>
                                                    item.productVariantId ===
                                                    option.productVariantId
                                            );
                                    } else if (
                                        option.productType === 'simple'
                                    ) {
                                        filteredInventory =
                                            inventoryList?.filter(
                                                (item: any) =>
                                                    item.productId ===
                                                    option.productId
                                            );
                                    }

                                    if (filteredInventory?.length) {
                                        // If SKU exists in inventoryList, populate the form with existing inventory details
                                        const data = filteredInventory[0];
                                        form.setFieldsValue({
                                            productType: data.productType,
                                            sku: data.sku || '',
                                            productName:
                                                data.name ||
                                                extractedProductName,
                                            mrp: data.mrp || '',
                                            salePrice: data.salePrice || '',
                                            discountPercentage:
                                                data.discount || '',
                                            discountPrice:
                                                data.discountPrice || '',
                                            quantity: data.quantity || '',
                                            expiryDate: data.expiryDate
                                                ? dayjs(data.expiryDate)
                                                : null,
                                        });
                                        setInventoryIds(data.inventoryId);
                                        setProductId(data.productId);
                                        setSelectedProduct({
                                            ...data,
                                            storeId: storeId,
                                        });
                                    } else {
                                        // If SKU does not exist in inventoryList, set only the selected SKU details
                                        setSelectedProduct({
                                            ...option,
                                            storeId: storeId,
                                        });

                                        form.setFieldsValue({
                                            sku: value,
                                            productName: extractedProductName,
                                        });
                                        form.resetFields([
                                            'mrp',
                                            'salePrice',
                                            'discountPercentage',
                                            'discountPrice',
                                            'quantity',
                                            'expiryDate',
                                        ]);
                                        setInventoryIds('0');

                                        console.log(
                                            'Option----jiccjsncjsn------',
                                            option
                                        );
                                        if (option.productId) {
                                            fetchApplicableDiscounts(
                                                option.productId,
                                                option.productType ===
                                                    'variable'
                                                    ? option.productVariantId
                                                    : undefined
                                            );
                                        }
                                    }
                                }}
                                placeholder="Select SKU"
                                disabled={inventroyDetail ? true : false}
                            />
                        </Form.Item>
                    </div>

                    <div className="flex flex-row items-center justify-between ">
                        {/* <Form.Item
                            className="w-[48%]"
                            label="SKU"
                            name="sku"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select SKU',
                                },
                            ]}
                        >
                            <InfiniteScrollSelect
                                fetchOptions={searchProductBySku}
                                onChange={(value, option) => {
                                    setSelectedProduct({
                                        ...option,
                                        storeId: storeId,
                                    });
                                    const extractedProductName =
                                        option?.productName ||
                                        option?.label?.split(' - ')[1] ||
                                        '';
                                    form.setFieldsValue({
                                        sku: value,
                                        productName: extractedProductName,
                                    });
                                }}
                                placeholder="Select SKU"
                                disabled={inventroyDetail ? true : false}
                            />
                        </Form.Item> */}
                        {/* -----------------------product name input----------------- */}
                        {/* <Form.Item
                            className="w-[48%]"
                            name="productName"
                            label="Product Name"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter product name',
                                },
                            ]}
                        >
                            <Input
                                placeholder="Product name"
                                className="h-16"
                                disabled
                            />
                        </Form.Item> */}
                        <Form.Item
                            className="w-[48%]"
                            name="mrp"
                            label={
                                <label className="text-[#1A3353]">MRP</label>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter MRP',
                                },
                            ]}
                        >
                            <Input
                                placeholder="Product MRP"
                                // className="h-16"
                                onInput={(e: any) => {
                                    e.target.value = e.target.value.replace(
                                        /[^0-9]/g,
                                        ''
                                    );
                                }}
                                disabled={
                                    !(inventoryId === '0' || isPricingEdit)
                                }
                            />
                        </Form.Item>
                        {isPricingEdit ? (
                            <Form.Item
                                label="Expiry Date"
                                name="expiryDate"
                                rules={[
                                    {
                                        required: false,
                                        message: 'Please enter Expiry Date',
                                    },
                                ]}
                                className="w-[48%]"
                            >
                                <DatePicker
                                    placeholder="DD/MM/YYYY"
                                    format="DD/MM/YYYY"
                                    style={{ width: '100%' }}
                                    popupClassName="custom-datepicker"
                                    // className="h-12"
                                    disabledDate={disabledDate}
                                />
                            </Form.Item>
                        ) : (
                            <Form.Item
                                className="w-[48%]"
                                name="productName"
                                label="Product Name"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter product name',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="Product name"
                                    // className="h-16"
                                    disabled
                                />
                            </Form.Item>
                        )}
                    </div>

                    {(inventoryId === '0' || isPricingEdit) && (
                        <div className="">
                            <div className="flex items-end gap-4 lg:w-[100%]  lg:flex-col @sm:flex-col">
                                <div
                                    className="w-[100%] rounded-lg border"
                                    ref={tableContainerRef}
                                // className="max-h-[300px] overflow-y-auto"
                                // style={{
                                //     maxHeight:
                                //         '300px',
                                //     overflowY:
                                //         'auto',
                                // }}
                                >
                                    <Table
                                        columns={discountColumns}
                                        dataSource={discountData}
                                        pagination={false}
                                        rowKey="_id"
                                        // scroll={{
                                        //     // x: 'calc(300px + 50%)',
                                        //     y: 47 * 5,
                                        // }}
                                        size="small"
                                    />
                                    {loading && (
                                        <div className="flex justify-center py-4">
                                            <Spin size="small" />
                                        </div>
                                    )}
                                    {!hasMore && discountData.length > 0 && (
                                        <div className="flex hidden justify-center py-2 text-sm text-gray-500">
                                            No more data to load
                                        </div>
                                    )}
                                    <div className="flex hidden justify-center py-2">
                                        <Button
                                            size="small"
                                            onClick={() => {
                                                const nextPage =
                                                    currentPage + 1;
                                                setCurrentPage(nextPage);
                                                fetchDiscountData(
                                                    nextPage,
                                                    true
                                                );
                                            }}
                                            disabled={loading || !hasMore}
                                        >
                                            Load More
                                        </Button>
                                    </div>
                                </div>

                                <Button
                                    className={`fw-500 w-[15%] rounded-lg border px-8 py-2 text-xl ${view === 'true' && !isEditable
                                            ? 'text-grey bg-[#ccc]'
                                            : 'bg-purpleLight text-white'
                                        } `}
                                    onClick={() => setAddDiscountModal(true)}
                                >
                                    Add Discount
                                </Button>
                            </div>

                            {/* <Form.Item
                            className="w-[48%]"
                            name="salePrice"
                            label="Sale Price"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter Sale price',
                                },
                                ({ getFieldValue }) => ({
                                    validator(_, value) {
                                        if (
                                            !value ||
                                            Number(value) <=
                                            Number(getFieldValue('mrp'))
                                        ) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(
                                            new Error(
                                                'Sale Price cannot be greater than MRP'
                                            )
                                        );
                                    },
                                }),
                            ]}
                        >
                            <Input
                                placeholder="Add product sale price"
                                className="h-16"
                                onInput={(e: any) => {
                                    e.target.value = e.target.value.replace(
                                        /[^0-9]/g,
                                        ''
                                    );
                                }}
                                onChange={(e) =>
                                    handleInventoryDetails(
                                        'salePrice',
                                        e.target.value
                                    )
                                }
                            />
                        </Form.Item> */}
                        </div>
                    )}

                    {/* <div className="flex flex-row items-center justify-between gap-16">
                        <Form.Item
                            className="w-[48%]"
                            name="discountPercentage"
                            label="Percentage Discount"
                            rules={[
                                {
                                    required: false,
                                    message: 'Please enter Discount',
                                },
                                ({ getFieldValue }) => ({
                                    validator(_, value) {
                                        if (!value || Number(value) <= 100) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(
                                            new Error(
                                                'Discount Percentage can not be greater than 100'
                                            )
                                        );
                                    },
                                }),
                            ]}
                        >
                            <Input
                                placeholder="Add discount"
                                className="h-16"
                                onInput={(e: any) => {
                                    e.target.value = e.target.value.replace(
                                        /[^0-9]/g,
                                        ''
                                    );
                                }}
                                onChange={(e) =>
                                    handleInventoryDetails(
                                        'discount',
                                        e.target.value
                                    )
                                }
                            />
                        </Form.Item>

                        <Form.Item
                            className="w-[48%]"
                            name="discountPrice"
                            label="Discount price"
                        >
                            <Input
                                placeholder="Discount Price"
                                className="h-16"
                            />
                        </Form.Item>
                    </div> */}

                    {isPricingEdit ? null : (
                        <div className="flex flex-row items-center justify-between ">
                            <Form.Item
                                className="w-[48%]"
                                name="quantity"
                                label={
                                    <label className="text-[#1A3353]">
                                        Inventory
                                    </label>
                                }
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter inventory',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="Add inventory"
                                    // className="h-16"
                                    disabled={inventroyDetail ? true : false}
                                />
                            </Form.Item>

                            <Form.Item
                                label="Expiry Date"
                                name="expiryDate"
                                rules={[
                                    {
                                        required: false,
                                        message: 'Please enter Expiry Date',
                                    },
                                ]}
                                className="w-[48%]"
                            >
                                <DatePicker
                                    placeholder="DD/MM/YYYY"
                                    format="DD/MM/YYYY"
                                    style={{ width: '100%' }}
                                    popupClassName="custom-datepicker"
                                    // className="h-16"
                                    disabledDate={disabledDate}
                                />
                            </Form.Item>
                        </div>
                    )}

                    {inventroyDetail && !isPricingEdit && (
                        <>
                            <div className="flex flex-row items-center justify-between gap-16">
                                <Form.Item
                                    label="Type"
                                    style={{ marginBottom: 8 }}
                                >
                                    <Radio.Group
                                        value={actionType}
                                        onChange={(e) => {
                                            setActionType(e.target.value);
                                            recalcTotal(
                                                form.getFieldValue('adjustQty')
                                            );
                                            // force field to re-run the custom validator below
                                            form.validateFields([
                                                'adjustQty',
                                            ]).catch(() => { });
                                        }}
                                    >
                                        <Radio value="restock">Restock</Radio>
                                        <Radio value="adjustment">
                                            Qty Adjustment
                                        </Radio>
                                    </Radio.Group>
                                </Form.Item>
                            </div>
                            <div className="flex flex-row items-center justify-between">
                                <Form.Item
                                    className="w-[48%]"
                                    name="adjustQty"
                                    label="Quantity Adjustment"
                                    rules={[
                                        {
                                            validator: async (_, value) => {
                                                const q = Number(value ?? 0);

                                                // Allow blank: treat as 0


                                                if (actionType === 'restock') {
                                                    // restock is a delta; total must not go below 0
                                                    const total =
                                                        currentQty + q;
                                                    if (total < 0) {
                                                        return Promise.reject(
                                                            new Error(
                                                                'Adjustment would make inventory negative'
                                                            )
                                                        );
                                                    }
                                                }

                                                return Promise.resolve();
                                            },
                                        },
                                    ]}
                                >
                                    <Input
                                        placeholder="0"
                                        onInput={(e: any) => {
                                            // allow optional leading minus only for restock deltas
                                            const raw = e.target
                                                .value as string;
                                            const cleaned =
                                                actionType === 'restock'
                                                    ? raw.replace(/[^0-9]/g, '')
                                                    : raw.replace(
                                                        /[^0-9-]/g,
                                                        ''
                                                    );
                                            e.target.value = cleaned;
                                        }}
                                        onChange={(e) =>
                                            recalcTotal(e.target.value)
                                        }
                                    />
                                </Form.Item>

                                <Typography.Text strong>
                                    Total: {computedTotal}
                                </Typography.Text>
                            </div>
                            <div className="flex flex-row items-center justify-between gap-16">
                                <Form.Item
                                    label="Notes"
                                    name="notes"
                                    className="w-full"
                                >
                                    <TextArea
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        autoSize={{ minRows: 3, maxRows: 5 }}
                                        placeholder="Notes..."
                                        className="rounded-md"
                                    />
                                </Form.Item>
                            </div>
                        </>
                    )}

                    <Form.Item className="flex justify-end py-10">
                        <Button
                            htmlType="button"
                            className="me-6 w-[110px] border-[#1A3353] bg-[#fff] text-xl text-[#1A3353]"
                            onClick={handleCancel}
                        >
                            Cancel
                        </Button>
                        <Button
                            htmlType="submit"
                            loading={loader}
                            className="w-[110px] border-purpleLight bg-purpleLight text-xl text-white"
                        >
                            Save
                        </Button>
                    </Form.Item>
                </Form>

                {addDiscountModal && (
                    <AddInventoryDiscountModal
                        visible={addDiscountModal}
                        pricingId={inventoryId}
                        priceToCompare={form.getFieldValue('mrp')}
                        setDiscountData={setDiscountData}
                        selectedDiscount={selectedDiscount}
                        setSelectedDiscount={setSelectedDiscount}
                        setComingDiscounts={setComingDiscounts}
                        autoApplyId={autoApplyId}
                        setAutoApplyId={setAutoApplyId}
                        comingDiscounts={comingDiscounts}
                        preloadedDiscounts={
                            inventoryId === '0' ? discountData : undefined
                        }
                        onCancel={() => {
                            setAddDiscountModal(false);
                            // setDiscountData([]);
                            // setComingDiscounts([]);
                            // setSelectedDiscount([]);
                        }}
                    />
                )}
            </ConfigProvider>
        </Modal>
    );
};

export default SkuInventoryModal;
