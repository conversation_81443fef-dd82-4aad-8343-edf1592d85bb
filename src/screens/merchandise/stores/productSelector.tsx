import { Checkbox, CheckboxProps, Tooltip, Config<PERSON><PERSON>ider, Spin } from 'antd';
import { capitalizeFirstLetter } from '~/components/common/function';
import FullLoader from '~/components/library/loader/full-loader';

type Product = {
    productId?: string;
    _id?: string;
    name: string;
    status?: boolean;
    price?: number;
    variantId?: string;
    type?: string;
};

type Props = {
    form: any;
    productList: Product[];
    selectedProducts: string[];
    setSelectedProducts: (ids: string[]) => void;
    discountValueState?: number;
    loader: boolean;
};

const getId = (p: Product) => {
    if (p.type === 'variable' && p.variantId) {
        return `${p.productId || p._id}_${p.variantId}`;
    }
    return p.productId ?? p._id!;
};
const getPrice = (p: Product) =>
    typeof p.price === 'number' ? p.price : undefined;

const ProductSelector = ({
    form,
    productList,
    selectedProducts,
    setSelectedProducts,
    discountValueState,
    loader,
}: Props) => {
    const options = productList?.map((item) => {
        const id = getId(item);
        const price = getPrice(item);
        const disabledByPrice =
            typeof discountValueState === 'number' &&
            typeof price === 'number' &&
            discountValueState > 0 &&
            price < discountValueState;

        const disabledByStatus = item.status === false;

        return {
            label: (
                <Tooltip
                    title={
                        disabledByPrice
                            ? `Price is less than the discounted amount ₹${discountValueState}`
                            : disabledByStatus
                            ? 'Product is inactive'
                            : ''
                    }
                >
                    <div className="flex w-full items-center gap-4">
                        <p>{capitalizeFirstLetter(item.name)}</p>
                        {typeof price === 'number' && (
                            <p>(₹{price.toFixed(2)})</p>
                        )}
                    </div>
                </Tooltip>
            ),
            value: id,
            disabled: disabledByPrice || disabledByStatus,
        };
    });

    const selectableIds = productList
        .filter((p) => {
            const price = getPrice(p);
            const disabledByPrice =
                typeof discountValueState === 'number' &&
                typeof price === 'number' &&
                discountValueState > 0 &&
                price < discountValueState;
            const disabledByStatus = p.status === false;
            return !disabledByPrice && !disabledByStatus;
        })
        .map(getId);

    const checkAll =
        selectableIds.length > 0 &&
        selectedProducts.length === selectableIds.length;
    const indeterminate =
        selectedProducts.length > 0 &&
        selectedProducts.length < selectableIds.length;

    const onChange = (list: string[]) => {
        setSelectedProducts(list);
        form.setFieldsValue({ products: list });
    };

    const onCheckAllChange: CheckboxProps['onChange'] = (e) => {
        const newList = e.target.checked ? selectableIds : [];
        setSelectedProducts(newList);
        form.setFieldsValue({ products: newList });
    };

    return (
        <ConfigProvider
            theme={{
                token: {
                    colorPrimary: '#8143D1',
                    colorPrimaryHover: '#8143D1',
                },
            }}
        >
            {loader ? (
                // <FullLoader state={true} />
                <div className="flex items-center justify-center py-10">
                    <Spin />
                </div>
            ) : (
                <>
                    <Checkbox
                        indeterminate={indeterminate}
                        onChange={onCheckAllChange}
                        checked={checkAll}
                        className="mb-4"
                    >
                        Select All
                    </Checkbox>

                    <div className="h-64 overflow-y-scroll">
                        <Checkbox.Group
                            className="flex w-full flex-col gap-4"
                            options={options}
                            value={selectedProducts}
                            onChange={(vals) => onChange(vals as string[])}
                        />
                    </div>
                </>
            )}
        </ConfigProvider>
    );
};

export default ProductSelector;
