import { MoreOutlined } from '@ant-design/icons';
import {
    Checkbox,
    ConfigProvider,
    Dropdown,
    Table,
    Tag,
    Pagination,
    Menu,
} from 'antd';
import { useState, useEffect } from 'react';
import { useParams, useLocation } from 'wouter';
import CustomTable from '~/components/common/customTable';
import { useLoader } from '~/hooks/useLoader';
import SkuInventoryModal from '~/screens/merchandise/stores/sku-inventory-modal';
import { getQueryParams } from '~/utils/getQueryParams';
import {
    BulkUploadInventory,
    ExportInventory,
    ExportProductsTemplate,
    InventoryList,
    _ManipulateInventoryList,
} from '~/redux/actions/merchandise/inventory-action';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { FacilityDetails } from '~/redux/actions/facility-action';
import dayjs from 'dayjs';
import { formatDateString } from '~/components/common/function';
import Alertify from '~/services/alertify';
import BulkInventoryUploadModal from './inventory-upload-modal';
import ImportIssuesModal, { ImportIssue } from './ImportIssuesModal';
import InventoryHistoryModal from './InventoryHistoryModal.modal';
import { navigate } from 'wouter/use-location';

const StoreDetail = () => {
    const dispatch = useDispatch<AppDispatch>();
    const params = getQueryParams();
    const [, setLocation] = useLocation();
    const [loader] = useLoader(true);

    const pageParam = parseInt(params.page, 10) || 1;
    const pageSizeParam = parseInt(params.pageSize, 10) || 10;
    const searchParam = params.search || '';

    const [search, setSearch] = useState(searchParam);
    const [currentPage, setCurrentPage] = useState(pageParam);
    const [pageSize, setPageSize] = useState(pageSizeParam);
    const [modalVisible, setModalVisible] = useState(false);

    const { storeId } = useParams<{ storeId: string }>();
    const [inventoryList, setInventoryList] = useState([]);
    const [inventoryCount, setInventoryCount] = useState<number>(0);
    const [storeName, setStoreName] = useState<string>('');
    const [productType, setProductType] = useState<string>('');
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [inventoryDetail, setInventoryDetail] = useState<any>(null);

    const [importOpen, setImportOpen] = useState(false);
    const [selectedCsvFile, setSelectedCsvFile] = useState<File | null>(null);
    const [importLoading, setImportLoading] = useState(false);
    const [resetImportFile, setResetImportFile] = useState(false);
    const [isPricingEdit, setIsPricingEdit] = useState(false);
    const [importErrors, setImportErrors] = useState<any>(null);
    const showModal = () => setModalVisible(true);
    const [errModalOpen, setErrModalOpen] = useState(false);
    const [loaderList, startLoaderList, endLoaderList] = useLoader(false);
    const [historyOpen, setHistoryOpen] = useState(false);
    const [historyCtx, setHistoryCtx] = useState<{
        inventoryId: string;
        productType: 'simple' | 'variable';
        sku: string;
        productName: string;
        inventoryQty: number;
    }>({
        inventoryId: '',
        productType: 'simple',
        sku: '',
        productName: '',
        inventoryQty: 0,
    });

    const handleImportClick = () => {
        setImportOpen(true);
        setImportErrors(null);
    };

    const handleImportClose = () => {
        setImportOpen(false);
        setSelectedCsvFile(null);
        setResetImportFile(true); // clears file name in modal
    };

    const handleCSVFile = (file: File) => {
        setSelectedCsvFile(file);
    };
    const handleClose = () => {
        setModalVisible(false);
        setIsPricingEdit(false);
        setInventoryDetail(null);
    };

    const handleSearch = (value: string) => {
        setSearch(value);
        setCurrentPage(1);
        setPageSize(10);
    };
    useEffect(() => {
        dispatch(FacilityDetails({ facilityId: storeId }))
            .unwrap()
            .then((res: any) => {
                setStoreName(res.data.data.facilityName);
            });
    }, []);
    useEffect(() => {
        startLoaderList();
        dispatch(
            InventoryList({
                page: currentPage,
                pageSize,
                storeId,
                search,
                productType,
            })
        )
            .unwrap()
            .then((res: any) => {
                const inventoryDetails = _ManipulateInventoryList(
                    res.data.list
                );
                setInventoryCount(res.data.count);
                setInventoryList(inventoryDetails);
            })
            .finally(endLoaderList);
    }, [
        dispatch,
        currentPage,
        pageSize,
        search,
        storeId,
        productType,
        modalVisible,
        importLoading,
        importOpen,
    ]);

    const handleInventoryEdit = (record: any) => {
        console.log(record);
        setInventoryDetail(record);
        showModal();
    };
    const handleInventoryHistory = (record: any) => {
        setHistoryCtx({
            inventoryId: record?._id,
            productType:
                record?.productType === 'variable' ? 'variable' : 'simple',
            sku: record?.sku || '',
            productName: record?.name || '',
            inventoryQty: Number(record?.quantity ?? 0),
        });
        setHistoryOpen(true);
    };
    const EXPIRY_FORMATS = [
        'YYYY-MM-DDTHH:mm:ss.SSS[Z]',
        'YYYY-MM-DDTHH:mm:ss[Z]',
        'YYYY-MM-DD',
        'DD/MM/YYYY',
    ];

    function toDayjsOrNull(v: any) {
        if (v == null || v === '') return null;

        if (typeof v === 'number') {
            const ms = v > 1e12 ? v : v * 1000;
            const d = dayjs(ms);
            return d.isValid() ? d : null;
        }

        if (typeof v === 'string') {
            let d = dayjs(v, EXPIRY_FORMATS, true);
            if (d.isValid()) return d;

            d = dayjs(v);
            return d.isValid() ? d : null;
        }

        if (typeof v === 'object' && v?.isValid?.()) return v;

        return null;
    }
    const columns: any = [
        {
            title: 'SKU',
            dataIndex: 'sku',
            key: 'sku',
        },
        {
            title: 'Product Name',
            dataIndex: 'name',
        },
        {
            title: 'Type',
            dataIndex: 'productType',
        },

        {
            title: 'Inventory',
            dataIndex: 'quantity',
        },
        {
            title: 'HSN',
            dataIndex: 'hsn',
        },
        {
            title: 'GST %',
            dataIndex: 'tax',
        },
        {
            title: 'MRP',
            dataIndex: 'mrp',
        },
        // {
        //     title: 'Sale Price',
        //     dataIndex: 'salePrice',
        // },
        // {
        //     title: 'Discount Price',
        //     dataIndex: 'price',
        // },
        {
            title: 'Status',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                let color = 'yellow';
                let statusText = 'Low';

                if (record.quantity === 0) {
                    color = 'red';
                    statusText = 'Out of Stock';
                } else if (record.quantity > 100) {
                    color = 'green';
                    statusText = 'Available';
                } else if (record.quantity < 50) {
                    color = 'yellow';
                    statusText = 'Low';
                }

                return (
                    <div style={{ display: 'flex', justifyContent: 'center' }}>
                        <Tag color={color}>{statusText}</Tag>
                    </div>
                );
            },
        },
        {
            title: 'Expiry Date',
            dataIndex: 'expiryDate',
            key: 'expiry',
            render: (_: any, record: any) => {
                const d = toDayjsOrNull(record?.expiryDate);

                return (
                    <div style={{ display: 'flex', justifyContent: 'center' }}>
                        {d ? d.format('DD/MM/YYYY') : '-'}
                    </div>
                );
            },
        },

        {
            title: 'Action',
            dataIndex: '',
            width: '120px',
            align: 'center',
            key: 'action',
            render: (record: any) => {
                const menu = (
                    <Menu>
                        <Menu.Item key="edit">
                            <div
                                onClick={() => {
                                    handleInventoryEdit(record);
                                    setIsPricingEdit(false);
                                }}
                            >
                                Update Inventory
                            </div>
                        </Menu.Item>
                        <Menu.Item key="edit-pricing">
                            <div
                                onClick={() => {
                                    handleInventoryEdit(record);
                                    setIsPricingEdit(true);
                                }}
                            >
                                Update Pricing
                            </div>
                        </Menu.Item>
                        <Menu.Item key="add-branch">
                            <div
                                onClick={() => {
                                    handleInventoryHistory(record);
                                }}
                            >
                                History
                            </div>
                        </Menu.Item>
                    </Menu>
                );
                return (
                    <>
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                            }}
                        >
                            <Dropdown overlay={menu} trigger={['click']}>
                                <MoreOutlined
                                    style={{ fontSize: 20, cursor: 'pointer' }}
                                />
                            </Dropdown>
                        </div>
                    </>
                );
            },
        },
    ];
    const handleTypeChange = (value: any) => {
        setProductType(value);
        console.log('Selected Type:', value);
    };
    const handlePageChange = (page: number, pageSize: number) => {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    };
    const handleExportInventory = async () => {
        try {
            const payload = {
                fileType: 'csv',
                facilityId: storeId,
                responseType: 'stream',
            };
            await dispatch(ExportInventory(payload)).unwrap();
        } catch (err: any) {
            console.error('Export inventory failed:', err);
            Alertify.error(err?.message || 'Failed to export the inventory');
        }
    };
    const bulkUpload = async (_confirmUpdate: boolean) => {
        if (!selectedCsvFile) {
            Alertify.error('Please select a CSV file.');
            return;
        }

        try {
            setImportLoading(true);

            const form = new FormData();
            form.append('inventoryFile', selectedCsvFile);
            form.append('storeId', storeId);
            form.append('isUpdated', String(_confirmUpdate));

            const res: any = await dispatch(BulkUploadInventory(form)).unwrap();

            console.log(res, 'res bulk upload response');
            Alertify.success('Inventory import completed');

            // collect both errors & warnings
            const uploadErrors = res?.data?.uploadReport?.errors || [];
            const uploadWarnings = res?.data?.uploadReport?.warnings || [];

            if (uploadErrors.length || uploadWarnings.length) {
                setImportErrors([
                    ...uploadErrors.map((e: any) => ({ ...e, level: 'error' })),
                    // ...uploadWarnings.map((w: any) => ({ ...w, level: 'warning' })),
                ]);
                setErrModalOpen(true);
            }

            // Close modal only if no errors/warnings
            if (!uploadErrors.length) {
                handleImportClose();
            }
        } catch (err: any) {
            console.error('Import inventory failed:', err);
            const msg =
                err?.message ||
                err?.response?.data?.message ||
                'Failed to import inventory';
            Alertify.error(msg);

            if (err?.uploadReport?.errors) {
                setImportErrors([
                    ...(err.uploadReport.errors || []).map((e: any) => ({
                        ...e,
                        level: 'error',
                    })),
                    // ...(err.uploadReport.warnings || []).map((w: any) => ({ ...w, level: 'warning' })),
                ]);
                setErrModalOpen(true);
            }
        } finally {
            setImportLoading(false);
        }
    };
    const handleExportProductsTemplate = async () => {
        try {
            const payload = {
                fileType: 'csv',
                responseType: 'stream',
            };
            await dispatch(ExportProductsTemplate(payload)).unwrap();
        } catch (err: any) {
            console.error('Export products template failed:', err);
            Alertify.error(
                err?.message || 'Failed to export the products template'
            );
        }
    };
    return (
        <div>
            <CustomTable
                heading={storeName}
                search={search}
                showSearch={true}
                onSearch={handleSearch}
                showStoreDetailScreenButtons={true}
                storeId={storeId}
                discountManagementButtonForInventory={true}
                addNewModal={true}
                openModal={showModal}
                addNewTitle="Add New"
                onTypeChange={handleTypeChange}
                onExportClick={handleExportInventory}
                onImportClick={handleImportClick}
                ExportProductsTemplate={handleExportProductsTemplate}
            />
            <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                <ConfigProvider
                    theme={{
                        components: {
                            Table: {
                                borderColor: '#0000001A',
                                cellFontSize: 13,
                                headerBg: '#fff',
                                headerColor: '#1A3353',
                                colorText: '#455560',
                            },
                        },
                    }}
                >
                    <Table
                        className="m-2 rounded-[6px] border-1"
                        columns={columns}
                        dataSource={inventoryList}
                        pagination={false}
                        loading={loaderList}
                    />
                </ConfigProvider>
            </div>
            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={inventoryCount}
                    pageSize={pageSize}
                    onChange={handlePageChange}
                    pageSizeOptions={['10', '20', '50']}
                    hideOnSinglePage
                />
            </div>
            <SkuInventoryModal
                onClose={handleClose}
                visible={modalVisible}
                storeId={storeId}
                inventoryId={inventoryDetail ? inventoryDetail._id : '0'}
                inventroyDetail={inventoryDetail}
                inventoryList={inventoryList}
                isPricingEdit={isPricingEdit}
            />
            <BulkInventoryUploadModal
                open={importOpen}
                onClose={handleImportClose}
                handleCSVFile={handleCSVFile}
                error_data={importErrors}
                bulkUpload={bulkUpload}
                bulk_upload_loader={importLoading}
                resetFile={resetImportFile}
                setResetFile={setResetImportFile}
            />
            <ImportIssuesModal
                open={errModalOpen}
                onClose={() => setErrModalOpen(false)}
                issues={importErrors}
                title="Inventory Import Errors"
            />
            <InventoryHistoryModal
                visible={historyOpen}
                onClose={() => setHistoryOpen(false)}
                inventoryId={historyCtx.inventoryId}
                productType={historyCtx.productType}
                sku={historyCtx.sku}
                productName={historyCtx.productName}
                inventoryQty={historyCtx.inventoryQty}
            />
        </div>
    );
};

export default StoreDetail;
