import React, { useEffect, useMemo, useState, useCallback } from 'react';
import { Modal, Table, Input, Tag, Result, Pagination } from 'antd';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { GetInventoryHistory } from '~/redux/actions/merchandise/inventory-action';

type HistoryType = 'restock' | 'adjustment' | 'bulk_restock' | 'MRP_UPDATE';

export interface InventoryHistoryItem {
    _id: string;
    at: string | Date;
    changeQty: number;
    unitCost?: number;
    actionType: HistoryType;
    notes?: string;
    performedByName?: string;
}

interface Props {
    visible: boolean;
    onClose: () => void;

    // header fields
    inventoryId: string;
    productType: 'simple' | 'variable';
    sku: string;
    productName: string;
    inventoryQty: number;
}

const currency = new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
});

const TypeTag = ({ t }: { t: HistoryType }) => {
    if (t === 'restock') return <Tag color="green">Restock</Tag>;
    if (t === 'adjustment') return <Tag color="blue">Qty Adjust</Tag>;
    if (t === 'MRP_UPDATE') return <Tag color="green">MRP Updated</Tag>;
    return <Tag color="purple">Bulk Restock</Tag>;
};

const InventoryHistoryModal: React.FC<Props> = ({
    visible,
    onClose,
    inventoryId,
    productType,
    sku,
    productName,
    inventoryQty,
}) => {
    const dispatch = useDispatch<AppDispatch>();

    const [rows, setRows] = useState<InventoryHistoryItem[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [loading, setLoading] = useState<boolean>(false);
    const [errorText, setErrorText] = useState<string>('');
    const [page, setPage] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(5);

    const fetchHistory = useCallback(
        async (p = page, ps = pageSize) => {
            if (!inventoryId) return;
            setLoading(true);
            setErrorText('');
            try {
                const res = await dispatch(
                    GetInventoryHistory({ inventoryId, page: p, pageSize: ps })
                ).unwrap();
                setRows(res?.data?.items || []);
                setTotal(res?.data?.total ?? res?.data?.items?.length ?? 0);
            } catch (err: any) {
                setErrorText(err?.message || 'Failed to load history');
                setRows([]);
                setTotal(0);
            } finally {
                setLoading(false);
            }
        },
        [dispatch, inventoryId]
    );

    const handlePageChange = (nextPage: number, nextPageSize?: number) => {
        setPage(nextPage);
        setPageSize(nextPageSize || pageSize);
        fetchHistory(nextPage, nextPageSize || pageSize);
    };

    // Fetch when opened / id changes
    useEffect(() => {
        if (visible) {
            setPage(1);
            fetchHistory(1, pageSize);
        }
    }, [visible, inventoryId]);

    const onChangeTable = (pagination: TablePaginationConfig) => {
        const nextPage = pagination.current || 1;
        const nextSize = pagination.pageSize || pageSize;
        setPage(nextPage);
        setPageSize(nextSize);
        fetchHistory(nextPage, nextSize);
    };

    const columns: ColumnsType<InventoryHistoryItem> = useMemo(
        () => [
            {
                title: 'Date',
                dataIndex: 'at',
                key: 'at',
                width: 100,
                align: 'center',
                render: (v: string | Date) => dayjs(v).format('DD-MM-YYYY'),
            },
            {
                title: 'Qty',
                dataIndex: 'changeQty',
                key: 'changeQty',
                width: 100,
                align: 'center',
                render: (q: number) => {
                    if (q === undefined || q === null) {
                        return <span className="text-[#1A3353]">-</span>;
                    }
                    return (
                        <span
                            className={
                                q >= 0 ? 'text-green-600' : 'text-red-600'
                            }
                        >
                            {q || '-'}
                        </span>
                    );
                },
            },
            {
                title: 'Unit Cost',
                dataIndex: 'unitCost',
                key: 'unitCost',
                width: 100,
                align: 'center',
                render: (v?: number, record?: any) => {
                    if (
                        record?.actionType === 'restock' ||
                        record?.actionType === 'adjustment'
                    ) {
                        return <span className="text-[#1A3353]">-</span>;
                    }
                    return currency.format(v ?? 0);
                },
            },
            {
                title: 'Type',
                dataIndex: 'actionType',
                key: 'actionType',
                width: 100,
                align: 'center',
                render: (t: HistoryType) => <TypeTag t={t} />,
            },
            {
                title: 'Performed By',
                dataIndex: 'performedByName',
                key: 'performedByName',
                width: 100,
                align: 'center',
                render: (name: string, record) => (
                    <span>
                        {name || '—'} <br />
                    </span>
                ),
            },
            {
                title: 'Notes',
                dataIndex: 'notes',
                key: 'notes',
                width: 100,
                align: 'center',
                ellipsis: true,
                render: (v?: string) => (v ? <span title={v}>{v}</span> : '—'),
            },
        ],
        []
    );

    return (
        <Modal
            open={visible}
            onCancel={onClose}
            title={
                <p className="w-fit border-b-2 border-primary text-2xl text-[#1A3353]">
                    History
                </p>
            }
            className="lg:w-[55%]"
            centered
            footer={null}
            destroyOnClose
        >
            {/* Header */}
            <div className="mt-2 grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="flex flex-col gap-1">
                    <label className="text-lg text-slate-600">
                        Product Type
                    </label>
                    <Input
                        value={productType === 'simple' ? 'Simple' : 'Variable'}
                        disabled
                    />
                </div>

                <div className="flex flex-col gap-1">
                    <label className="text-lg text-slate-600">SKU*</label>
                    <Input value={sku} disabled />
                </div>

                <div className="flex flex-col gap-1">
                    <label className="text-lg text-slate-600">
                        Product Name*
                    </label>
                    <Input value={productName} disabled />
                </div>

                <div className="flex flex-col gap-1">
                    <label className="text-lg text-slate-600">Inventory*</label>
                    <Input value={inventoryQty} disabled />
                </div>
            </div>

            {/* Section title */}
            <div className="mb-2 mt-6 font-semibold text-slate-700">Type</div>

            {/* Table / States */}
            {errorText ? (
                <Result
                    status="error"
                    title="Failed to load history"
                    subTitle={errorText}
                />
            ) : (
                <Table<InventoryHistoryItem>
                    rowKey={(r) => r._id}
                    columns={columns}
                    dataSource={rows}
                    loading={loading}
                    pagination={false}
                    locale={{
                        emptyText: loading ? ' ' : 'No history found',
                    }}
                    onChange={onChangeTable}
                    size="middle"
                    className="rounded-lg"
                    scroll={{ y: 320 }}
                />
            )}

            <div className="flex justify-center py-10">
                <Pagination
                    current={page}
                    total={total}
                    pageSize={pageSize}
                    onChange={handlePageChange}
                    showSizeChanger
                    pageSizeOptions={['5', '10', '20', '50']}
                />
            </div>

            {/* Footer */}
            <div className="mt-6 flex justify-end">
                <button
                    onClick={onClose}
                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                    // className="rounded-xl border border-slate-300 px-5 py-2 text-slate-700 hover:bg-slate-50"
                >
                    Close
                </button>
            </div>
        </Modal>
    );
};

export default InventoryHistoryModal;
