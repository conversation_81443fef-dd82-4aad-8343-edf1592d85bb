import { MoreOutlined } from '@ant-design/icons';
import {
    Checkbox,
    ConfigProvider,
    Dropdown,
    Switch,
    Table,
    Pagination,
    Spin,
} from 'antd';
import { useState, useEffect } from 'react';
import { Link } from 'wouter';
import CustomTable from '~/components/common/customTable';
import { useLoader } from '~/hooks/useLoader';
import {
    FacilitiesList,
    UpdateFacilityStoreStatus,
} from '~/redux/actions/facility-action';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { getQueryParams } from '~/utils/getQueryParams';
import { useSelector } from 'react-redux';
import { navigate } from 'wouter/use-location';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';

const StoreListing = () => {
    const params = getQueryParams();
    const dispatch = useAppDispatch();

    const [loader, startLoader, endLoader] = useLoader(true);
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;
    const [branchData, setBranchData] = useState<any>(null);
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    const [search, setSearch] = useState(searchParam || '');
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    const { facilityListCount, facilityList } = useSelector(
        (state: any) => state.facility_store
    );
    const debouncedRequest = useDebounce((callback) => callback(), 300);

    const fetchFacilities = () => {
        startLoader();
        dispatch(
            FacilitiesList({
                currentPage,
                pageSize: pageSizes,
                search: search || undefined,
            })
        )
            .unwrap()
            .finally(endLoader);
    };

    useEffect(() => {
        debouncedRequest(fetchFacilities);
    }, [currentPage, search, pageSizes]);

    const handleSearch = (value: string) => {
        setSearch(value);
        setCurrentPage(1);
        navigate(`?page=1&pageSize=${pageSizes}&search=${value}`, {
            replace: true,
        });
    };

    const paginate = (page: number, pageSize: number) => {
        setCurrentPage(page);
        setPageSize(pageSize);
        navigate(
            `?page=${page}&pageSize=${pageSize}${
                search ? `&search=${search}` : ''
            }`,
            { replace: true }
        );
    };
    const openConfirmationModal = (record: any) => {
        setBranchData(record);
        setConfirmationModalVisible(true);
    };
    const handleCancelStatusChange = () => {
        setBranchData(null);
        setConfirmationModalVisible(false);
    };

    const handleConfirmStatusChange = () => {
        dispatch(
            UpdateFacilityStoreStatus({
                facilityId: branchData._id,
                reqData: {
                    status: !branchData.storeStatus,
                },
            })
        );
        setConfirmationModalVisible(false);
        setBranchData(null);
    };
    const columns = [
        {
            title: 'Store Name',
            dataIndex: '',
            render: (record: any) => {
                return (
                    <>
                        {record.storeStatus ? (
                            <>
                                {' '}
                                <Link to={`/store-detail/${record.key}`}>
                                    {record.name}
                                </Link>
                            </>
                        ) : (
                            <>{record.name}</>
                        )}
                    </>
                );
            },
        },
        {
            title: 'Phone Number',
            dataIndex: 'number',
        },
        {
            title: 'Pincode',
            dataIndex: 'pincode',
        },
        {
            title: 'Address',
            dataIndex: 'address',
        },
        {
            title: 'STATUS',
            dataIndex: '',
            key: '',
            render: (record: any) => {
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    colorPrimaryBorder: '#8143D1',
                                    colorPrimary: '#8143D1',
                                    colorTextQuaternary: 'gray',
                                    colorFillQuaternary: 'gray',
                                },
                            },
                        }}
                    >
                        <Switch
                            id="swtich-off"
                            checkedChildren="ON"
                            // className={`${
                            //     record?.isActive
                            //         ? 'bg-[#8143D1]'
                            //         : 'bg-[#D0D4D7]'
                            // }`}
                            unCheckedChildren="OFF"
                            checked={record?.storeStatus}
                            onChange={() => openConfirmationModal(record)}
                        />
                    </ConfigProvider>
                );
            },
        },
    ];

    const dataSource = facilityList.map((facility: any) => ({
        key: facility._id,
        _id: facility._id,
        name: facility.facilityName,
        number: facility.mobile || 'N/A',
        pincode: facility.address?.postalCode || 'N/A',
        address: facility.address?.addressLine1 || 'N/A',
        storeStatus: facility.isStoreActive,
    }));

    return (
        <div>
            <CustomTable
                heading="Inventory"
                showSearch={true}
                search={search}
                onSearch={handleSearch}
                showDateRange={false}
                showExport={false}
                showStaffLocation={false}
                showServiceCategory={false}
                showPaymentStatus={false}
                // showStoreScreenButtons={true}
            />

            <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                <ConfigProvider
                    theme={{
                        components: {
                            Table: {
                                borderColor: '#0000001A',
                                cellFontSize: 13,
                                headerBg: '#fff',
                                headerColor: '#1A3353',
                                colorText: '#455560',
                            },
                        },
                    }}
                >
                    <Table
                        className="m-2 rounded-[6px] border-1"
                        columns={columns}
                        dataSource={dataSource}
                        pagination={false}
                    />
                </ConfigProvider>
            </div>

            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={facilityListCount}
                    pageSize={pageSizes}
                    onChange={paginate}
                    hideOnSinglePage
                />
            </div>
            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />
        </div>
    );
};

export default StoreListing;
