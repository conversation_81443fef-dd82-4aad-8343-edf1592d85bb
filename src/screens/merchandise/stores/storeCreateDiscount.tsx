import {
    Button,
    Checkbox,
    ConfigProvider,
    Form,
    Input,
    Modal,
    Radio,
    RadioChangeEvent,
    Select,
    Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import {
    BrandListForDiscounts,
    CreateDiscount,
    DiscountList,
    PricingListForDiscountId,
    ProductListbyBrand,
    ProductListbyBrandForEdit,
    UpdateDiscount,
} from '~/redux/actions/createDiscountAction';
import { useDispatch } from 'react-redux';
import Alertify from '~/services/alertify';
import BranchSelector from '~/screens/services/branchSelector';
import ProductSelector from './productSelector';
import { useLoader } from '~/hooks/useLoader';
const { Text } = Typography;

const parseProductId = (combinedId: string) => {
    if (combinedId.includes('_')) {
        const [productId, variantId] = combinedId.split('_');
        return { productId, variantId };
    }
    return { productId: combinedId, variantId: null };
};

const findProductByCombinedId = (productList: any[], combinedId: string) => {
    const { productId, variantId } = parseProductId(combinedId);
    return productList.find((p) => {
        if (variantId) {
            return (
                (p.productId === productId || p._id === productId) &&
                p.variantId === variantId
            );
        }
        return p.productId === productId || p._id === productId;
    });
};

const InventoryCreateDiscountModal = (props: any) => {
    const [form] = Form.useForm();
    const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
    const dispatch = useDispatch();
    const [productList, setProductList] = useState<any[]>([]);
    const [discountTypeState, setDiscountTypeState] = useState('percentage');
    const [discountValueState, setDiscountValueState] = useState(0);
    const [selectedOption, setSelectedOption] = useState('');
    const [selected, setSelected] = useState<string[]>([]);
    const [allSelectedItems, setAllSelectedItems] = useState<
        Array<{ itemType: string; itemId: string; serviceType: string }>
    >([]);
    const [brandList, setBrandList] = useState<any[]>([]);
    const [addLabel, setAddLabel] = useState<boolean>();

    const [productLoader, startProductLoader, endProductLoader] = useLoader();
    const [loader, startLoader, endLoader] = useLoader();
    const [autoApplyAll, setAutoApplyAll] = useState(false);

    const resetAll = () => {
        form.resetFields();
        setSelectedProducts([]);
        setDiscountTypeState('percentage');
        setDiscountValueState(0);
        setSelectedOption('');
        setSelected([]);
        setAllSelectedItems([]);
        setProductList([]);
        if (props.setSelectedBranches) {
            props.setSelectedBranches([]);
        }
        if (props.setDiscountDetails) {
            props.setDiscountDetails(null);
        }
    };

    console.log('props.discountDetails-----------', props.discountDetails);
    // 1) Add this helper inside your component (top-level of InventoryCreateDiscountModal)
    const expandAllBranches = (ids: string[] | undefined) => {
        if (!ids || ids.length === 0) return [];
        if (ids.includes('allBranches')) {
            return Array.isArray(props.allBranchIds) &&
                props.allBranchIds.length
                ? props.allBranchIds
                : ids.filter((id) => id !== 'allBranches');
        }
        return ids.filter((id) => id !== 'allBranches');
    };

    useEffect(() => {
        if (props.newDiscountModal && props.discountDetails) {
            if (props.discountDetails.promotionLabel) {
                setAddLabel(true);
            }

            form.setFieldsValue({
                ...props.discountDetails,
                // products: props.discountDetails.items?.map(
                //     (item: any) => item.itemId
                // ),
            });
            if (props.discountDetails.promotionLabel) {
                setAddLabel(true);
            }
            if (props.discountDetails?.autoApplyAll) {
                setAutoApplyAll(true);
            }
            setDiscountTypeState(props.discountDetails.type || 'percentage');
            setDiscountValueState(props.discountValueState || 0);
        } else {
            resetAll();
            // Set default values
            form.setFieldsValue({
                target: 'all_users',
                type: 'percentage',
                facility: [],
            });
        }
        console.log('props.editId is', props.editId);

        if (props.editId) {
            // Call the first API with the promotionId
            const reqData = {
                promotionId: props.editId,
                itemType: 'product',
            };

            dispatch(ProductListbyBrandForEdit(reqData))
                .unwrap()
                .then((res: any) => {
                    console.log('First API Response:', res);

                    const transformedItems = res?.data?.data
                        .map((item: any) => {
                            if (item.selected) {
                                const { productId, variantId } = item;

                                if (variantId) {
                                    return {
                                        itemType: 'product',
                                        itemId: `${productId}_${variantId}`,
                                        productId,
                                        variantId,
                                    };
                                }
                                return {
                                    itemType: 'product',
                                    itemId: productId,
                                    productId,
                                };
                            }
                            return null;
                        })
                        .filter((item: any) => item !== null);

                    setSelectedProducts(
                        transformedItems.map((item: any) => item.itemId)
                    );
                    setAllSelectedItems(transformedItems);

                    form.setFieldsValue({
                        products: transformedItems.map(
                            (item: any) => item.itemId
                        ),
                    });

                    console.log('Transformed Items:', transformedItems);
                })
                .catch((error: any) => {
                    console.error('Error in first API call:', error);
                });
        }
    }, [
        props.newDiscountModal,
        props.discountDetails,
        props.editId,
        setAllSelectedItems,
    ]);

    // Get Pricing List
    const handlebrandChange = (e: RadioChangeEvent) => {
        const newServiceType = e.target.value;
        setSelectedOption(newServiceType);

        const reqData = {
            brandId: [newServiceType],
            // page: 1,
            // pageSize: 20,
        };

        startProductLoader();

        dispatch(ProductListbyBrand(reqData))
            .then((response: any) => {
                console.log('Data response-----------', response);
                const productData = response?.payload?.data?.data?.data || [];
                setProductList(productData);

                const currentProductIds = new Set(
                    productData?.map((pkg: any) => {
                        if (pkg.type === 'variable' && pkg.variantId) {
                            return `${pkg.productId || pkg._id}_${
                                pkg.variantId
                            }`;
                        }
                        return pkg.productId || pkg._id;
                    })
                );

                const previouslySelectedIds = allSelectedItems
                    .filter((item) => currentProductIds.has(item.itemId))
                    .map((item) => item.itemId);

                console.log(
                    'previouslySelectedIds----------',
                    allSelectedItems
                );

                setSelectedProducts(previouslySelectedIds);
                form.setFieldsValue({ products: previouslySelectedIds });
            })
            .catch((error: any) => {
                Alertify.error(error);
            })
            .finally(() => {
                endProductLoader();
            });
    };
    useEffect(() => {
        if (props.newDiscountModal && props.discountDetails) {
            // Only set fields if the modal is open and form is mounted
            form.setFieldsValue({
                ...props.discountDetails,
                products: props.discountDetails.items?.map(
                    (item: any) => item.itemId
                ),
            });

            // Set the discount type and value states
            setDiscountTypeState(props.discountDetails.type || 'percentage');
            setDiscountValueState(props.discountValueState || 0);
            setAutoApplyAll(props.discountDetails?.autoApplyAll || false);

            // // Set the selected packages
            // setSelectedProducts(
            //     props.discountDetails.items?.map((item: any) => item.itemId) ||
            //         []
            // );

            // Pre-select the facilities in BranchSelector
            const facilityIds = props.discountDetails.facilityIds?.map(
                (facility: any) => facility._id
            );
            // if (facilityIds) {
            //     props.setSelectedBranches(facilityIds);
            // }
        } else {
            // Reset all form fields and states when modal is closed
            form.resetFields();
            setSelectedProducts([]);
            setDiscountTypeState('percentage');
            setDiscountValueState(0);
            setSelectedOption('');
            setAutoApplyAll(false);
            setSelected([]);
            setAllSelectedItems([]);
            setProductList([]);
            if (props.setSelectedBranches) {
                props.setSelectedBranches([]);
            }
            if (props.setDiscountDetails) {
                props.setDiscountDetails(null);
            }
            form.setFieldsValue({
                target: 'all_users',
                type: 'percentage',
                facility: [],
            });
        }
    }, [props.newDiscountModal, props.discountDetails, form]);

    useEffect(() => {
        dispatch(BrandListForDiscounts())
            .unwrap()
            .then((response: any) => {
                console.log('Resposne-------------', response);
                setBrandList(response?.data?.data);
            })
            .catch(() => {})
            .finally();
    }, []);

    const handleProductChange = (selectedIds: string[]) => {
        setSelectedProducts(selectedIds);

        const currentPricingIds = productList?.map((item) => {
            if (item.type === 'variable' && item.variantId) {
                return `${item.productId || item._id}_${item.variantId}`;
            }
            return item.productId || item._id;
        });

        const currentServiceItems = allSelectedItems.filter((item) =>
            currentPricingIds.includes(item.itemId)
        );
        const otherServiceItems = allSelectedItems.filter(
            (item) => !currentPricingIds.includes(item.itemId)
        );

        const uncheckedIds = currentServiceItems
            .map((item) => item.itemId)
            .filter((id) => !selectedIds.includes(id));

        const remainingCurrentItems = currentServiceItems.filter(
            (item) => !uncheckedIds.includes(item.itemId)
        );

        const existingIds = allSelectedItems.map((item) => item.itemId);
        const newItemIds = selectedIds.filter(
            (id) => !existingIds.includes(id)
        );

        const newItems = newItemIds.map((combinedId) => {
            const { productId, variantId } = parseProductId(combinedId);
            const baseItem = {
                itemType: 'product',
                itemId: combinedId,
                serviceType: selectedOption || '',
            };

            if (variantId) {
                return {
                    ...baseItem,
                    variantId: variantId,
                    productId: productId,
                };
            }

            return baseItem;
        });

        setAllSelectedItems([
            ...otherServiceItems,
            ...remainingCurrentItems,
            ...newItems,
        ]);

        form.setFieldsValue({ products: selectedIds });
    };

    // inside your component
    const handleSave = (values: any) => {
        const currentValues = form.getFieldsValue();
        console.log(currentValues, 'current Values');

        const defaultValues = {
            target: 'all_users',
            type: 'percentage',
        };
        const mergedValues = {
            ...defaultValues,
            ...currentValues,
        };
        const normalizedFacilityIds = expandAllBranches(mergedValues.facility);
        if (!mergedValues.target) mergedValues.target = defaultValues.target;
        if (!mergedValues.type) mergedValues.type = defaultValues.type;
        if (!mergedValues.products) mergedValues.products = [];

        form.setFieldsValue(mergedValues);

        // form.validateFields()
        //     .then((values: any) => {
        //         const {
        //             facility,
        //             products,
        //             value,
        //             serviceType,
        //             ...valuesWithoutFacility
        //         } = values;

        console.log('values-----------', values);

        const formData = {
            // ...valuesWithoutFacility,
            facilityIds: props.selectBranches,
            items: allSelectedItems.map(({ itemId }) => ({
                itemType: 'product',
                itemId,
            })),
            // value: Number(value),
            itemType: 'product',
        };

        const payload = {
            name: values.name,
            type: values.type,
            value: Number(values.value),
            target: values.target,
            itemType: 'product',
            // startDate: '2023-01-01T00:00:00.000Z',
            // endDate: '2023-12-31T23:59:59.999Z',
            isActive: true,
            facilityIds: normalizedFacilityIds,
            items: allSelectedItems.map((item) => {
                const { productId, variantId } = parseProductId(item.itemId);
                const baseItem = {
                    itemType: 'product',
                    itemId: productId,
                };

                if (variantId) {
                    return {
                        ...baseItem,
                        variantId: variantId,
                    };
                }

                return baseItem;
            }),
            promotionLabel: values.promotionLabel,
            autoApplyAll: autoApplyAll,
            overrideExistingAutoApply: autoApplyAll ? true : false,
        };

        startLoader();

        if (!props.edit) {
            dispatch(CreateDiscount(payload))
                .unwrap()
                .then(() => {
                    Alertify.success('Discount created successfully');
                    props.setNewDiscountModal(false);
                    setAddLabel(false);

                    dispatch(
                        DiscountList({
                            page: 1,
                            perPage: 10,
                            itemType: 'product',
                        })
                    )
                        .then((response: any) => {
                            const data =
                                response?.payload?.data?.data.map(
                                    (item: any) => ({
                                        ...item,
                                        key: item._id,
                                        facilityIds: (
                                            (item?.facilityIds as []) || []
                                        )
                                            .map((cc: any) => cc.facilityName)
                                            .join(', '),
                                    })
                                ) || [];
                            props.setDataSource(data);
                            props.setTotalItems(
                                response?.payload?.data?._metadata?.pagination
                                    ?.total || 0
                            );
                        })
                        .catch((error: any) =>
                            console.log('Error in fetch:', error)
                        )
                        .finally(() => {
                            endLoader();
                        });
                })
                .catch((error: any) => {
                    console.log('Error in creating::::', error);
                })
                .finally(() => {
                    endLoader();
                });
        } else {
            dispatch(UpdateDiscount({ id: props.editId, formData: payload }))
                .unwrap()
                .then(() => {
                    Alertify.success('Discount updated successfully');
                    props.setNewDiscountModal(false);
                    props.setEditId('');
                    props.setEdit(false);
                    setAddLabel(false);

                    dispatch(
                        DiscountList({
                            page: 1,
                            perPage: 10,
                            itemType: 'product',
                        })
                    )
                        .then((response: any) => {
                            const data =
                                response?.payload?.data?.data.map(
                                    (item: any) => ({
                                        ...item,
                                        facilityIds: (
                                            (item?.facilityIds as []) || []
                                        )
                                            .map((cc: any) => cc.facilityName)
                                            .join(', '),
                                    })
                                ) || [];
                            props.setDataSource(data);
                        })
                        .catch((error: any) =>
                            console.log('Error in fetch:', error)
                        )
                        .finally(() => {
                            endLoader();
                        });
                })
                .catch((error: any) => {
                    console.log('Error in updating::::', error);
                })
                .finally(() => {
                    endLoader();
                });
        }
        // })
    };

    return (
        <Modal
            open={props.newDiscountModal}
            closable={true}
            centered
            onCancel={() => {
                props.setNewDiscountModal(false);
                props.setEditId('');
                props.setEdit(false);
                setAddLabel(false);

                resetAll();
            }}
            title={props.edit ? 'Edit Discount' : 'Create Discount'}
            footer={false}
            width={800}
        >
            <ConfigProvider
                theme={{
                    components: {
                        Form: {
                            itemMarginBottom: 0,
                        },
                    },
                }}
            >
                <Form
                    form={form}
                    layout="horizontal"
                    size="large"
                    preserve={false}
                    initialValues={{}}
                    autoComplete="off"
                    className="create-discount-form"
                    onFinish={handleSave}
                >
                    <BranchSelector
                        form={form}
                        setSelected={setSelected}
                        selected={selected}
                        preSelectBranches={
                            props.discountDetails?.facilityIds || []
                        } // Add null check
                        setSelectedBranches={props.setSelectedBranches}
                    />
                    <div className="mb-8 flex w-full items-center">
                        <Text className="w-[20%] font-medium text-[#1a3353]">
                            Discount Name
                        </Text>

                        <div className="w-[80%]">
                            <Form.Item
                                // label="Discount Name"
                                name="name"
                                style={{ width: '100%', marginBottom: 0 }}
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter discount name',
                                    },
                                ]}
                            >
                                <Input placeholder="Discount Name" />
                            </Form.Item>
                        </div>
                    </div>

                    <div className="flex w-full items-center">
                        <Text className="w-[20%] font-medium text-[#1a3353]">
                            Discount
                        </Text>

                        <div className="flex w-[80%] gap-8">
                            <Form.Item
                                name="type"
                                className="w-1/3"
                                initialValue="percentage"
                            >
                                <Select
                                    onChange={() => {
                                        setDiscountTypeState(
                                            form.getFieldValue('type')
                                        );
                                    }}
                                    options={[
                                        {
                                            label: 'Flat',
                                            value: 'flat',
                                        },
                                        {
                                            label: 'Percentage',
                                            value: 'percentage',
                                        },
                                    ]}
                                />
                            </Form.Item>

                            <Form.Item
                                name="value"
                                className="mb-4 w-2/3"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter discount value',
                                    },
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                            if (!value || value < 0) {
                                                return Promise.reject(
                                                    'Value cannot be negative'
                                                );
                                            }
                                            if (
                                                getFieldValue('type') ===
                                                    'percentage' &&
                                                value > 100
                                            ) {
                                                return Promise.reject(
                                                    'Percentage cannot exceed 100'
                                                );
                                            }
                                            if (discountTypeState === 'flat') {
                                                setDiscountValueState(value);
                                            } else {
                                                setDiscountValueState(0);
                                            }
                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <Input
                                    type="number"
                                    min={0}
                                    max={
                                        form.getFieldValue('type') ===
                                        'percentage'
                                            ? 100
                                            : undefined
                                    }
                                    placeholder="Enter discount value"
                                />
                            </Form.Item>
                        </div>
                    </div>

                    <div>
                        {!addLabel && (
                            <Button
                                onClick={() => setAddLabel(true)}
                                className="fw-500 my-4 flex items-center rounded-lg border bg-purpleLight px-4 py-0 text-xl text-white"
                            >
                                Add Label
                            </Button>
                        )}
                    </div>

                    {addLabel && (
                        <div className="my-8 flex w-full items-center">
                            <Text className="w-[20%] font-medium text-[#1a3353]">
                                Label
                            </Text>
                            <div style={{ width: '80%' }}>
                                <Form.Item
                                    name="promotionLabel"
                                    style={{ width: '100%', marginBottom: 0 }}
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please enter Label name',
                                        },
                                    ]}
                                >
                                    <Input placeholder="Label Name" />
                                </Form.Item>
                            </div>
                        </div>
                    )}

                    <Text className="mb-0 mt-4 font-medium text-[#1a3353]">
                        Can Apply this discount to
                    </Text>
                    <Form.Item name="target" initialValue="all_users">
                        <Radio.Group>
                            <Radio value="all_users">All Users</Radio>
                            <Radio value="members_only">Members</Radio>
                        </Radio.Group>
                    </Form.Item>

                    <Form.Item name="autoApplyAll">
                        <Checkbox
                            checked={autoApplyAll}
                            onChange={() => setAutoApplyAll(!autoApplyAll)}
                        >
                            Auto Apply
                        </Checkbox>
                    </Form.Item>

                    <div className="mt-4">
                        <p className="mb-4 text-gray-500">
                            Please link your discount to pricing packages by
                            selecting the category and selecting the pricing you
                            want to associate this with
                        </p>

                        <div className="flex gap-4">
                            <div className="w-1/2 pe-4 shadow-sm">
                                <p className="mb-2 font-medium text-[#1A3353]">
                                    Brands
                                </p>
                                <div className="h-64 overflow-y-scroll">
                                    <Form.Item name="brands">
                                        <Radio.Group
                                            className="custom-radio flex flex-col gap-2"
                                            onChange={handlebrandChange}
                                        >
                                            {brandList?.map((category) => (
                                                <Radio
                                                    key={category._id}
                                                    value={category._id}
                                                    className="[&>span:last-child]:!ml-0"
                                                >
                                                    <div
                                                        className={`rounded-md px-4 py-2 transition-colors ${
                                                            selectedOption ===
                                                            category._id
                                                                ? 'bg-[rgba(129,67,209,0.1)] text-primary'
                                                                : 'bg-gray-100'
                                                        }`}
                                                    >
                                                        {category.value}
                                                    </div>
                                                </Radio>
                                            ))}
                                        </Radio.Group>
                                    </Form.Item>
                                </div>
                            </div>
                            <div className="w-1/2 ps-4 shadow-sm">
                                <p className="mb-2 font-medium text-[#1a3353]">
                                    Select Product
                                </p>
                                <Form.Item name="products">
                                    <ProductSelector
                                        form={form}
                                        productList={productList}
                                        selectedProducts={selectedProducts}
                                        setSelectedProducts={
                                            handleProductChange
                                        }
                                        discountValueState={discountValueState}
                                        loader={productLoader}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                    </div>
                    <div className="flex justify-end gap-6 py-10 ">
                        <Button
                            key="cancel"
                            className="w-[110px] border-[#1A3353]  bg-[#fff]  text-[#1A3353] "
                            onClick={() => {
                                props.setNewDiscountModal(false);
                                props.setEditId('');
                                props.setEdit(false);

                                resetAll();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            key="save"
                            htmlType="submit"
                            loading={loader}
                            className="w-[110px] bg-purpleLight  text-white"
                        >
                            Save
                        </Button>
                    </div>
                </Form>
            </ConfigProvider>
        </Modal>
    );
};

export default InventoryCreateDiscountModal;

// InventoryCreateDiscountModal
