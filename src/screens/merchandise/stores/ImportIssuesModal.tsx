import React, { useMemo, useState } from 'react';
import { Modal, Button, Tag, Input, Space, Typography } from 'antd';

const { Text } = Typography;

export type ImportIssue = {
  row: number;              // CSV line number (header = 1)
  field?: string | null;
  code: string;             // e.g. REQUIRED | INVALID | FORMAT | UNKNOWN_SKU | VARIANT_MISMATCH
  message: string;
  sku?: string | null;
};

interface ImportIssuesModalProps {
  open: boolean;
  onClose: () => void;
  issues: ImportIssue[] | null | undefined; // pass errors (array) from your uploadReport
  title?: string;
}

const codeColor = (code: string) => {
  const key = (code || '').toUpperCase();
  if (key.includes('REQUIRED')) return 'red';
  if (key.includes('UNKNOWN') || key.includes('MISMATCH')) return 'volcano';
  if (key.includes('INVALID') || key.includes('FORMAT') || key.includes('PRICE')) return 'orange';
  if (key.includes('DUP')) return 'gold';
  if (key.includes('IGNORED') || key.includes('DEFAULTED')) return 'blue';
  return 'geekblue';
};

const ImportIssuesModal: React.FC<ImportIssuesModalProps> = ({
  open,
  onClose,
  issues,
  title = 'Import Issues',
}) => {
  const [search, setSearch] = useState('');

  const filtered = useMemo(() => {
    const list = issues || [];
    const s = search.trim().toLowerCase();
    if (!s) return list;
    return list.filter((e) => {
      const hay = `${e.row} ${e.field ?? ''} ${e.code} ${e.message}`.toLowerCase();
      return hay.includes(s);
    });
  }, [issues, search]);

  return (
    <Modal
      title={`${title}${issues?.length ? ` (${issues.length})` : ''}`}
      open={open}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="close" onClick={onClose}
          className="fw-500 rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
        >
          Close
        </Button>,
      ]}
      bodyStyle={{ maxHeight: 440, overflowY: 'auto', paddingTop: 8 }}
      destroyOnClose
    >
      <Space direction="vertical" style={{ width: '100%' }}>


        {!issues?.length ? (
          <div className="text-gray-500 py-6">No errors to display.</div>
        ) : (
          <div>
            {filtered.map((e, idx) => (
              <div
                key={`${e.row}-${idx}`}
                className="border-b border-[#00000014] py-2 flex items-start gap-3"
              >
                <div style={{ width: 64 }}>
                  <span className="text-xl text-gray-500">Row</span>
                  <div className="font-medium">{e.row}</div>
                </div>
                <div style={{ width: 140 }}>
                  <span className="text-xl text-gray-500">Product Sku</span>
                  <div className="font-mono">{e.sku || '—'}</div>
                </div>
                <div style={{ width: 140 }}>
                  <span className="text-xl text-gray-500">Field</span>
                  <div className="font-mono">{e.field || '—'}</div>
                </div>

                {/* <div style={{ width: 160 }}>
                  <span className="text-xs text-gray-500">Code</span>
                  <div>
                    <Tag color={codeColor(e.code)}>{e.code}</Tag>
                  </div>
                </div> */}

                <div className="flex-1">
                  <span className="text-xl text-gray-500">Message</span>
                  <div><Text>{e.message}</Text></div>
                </div>
              </div>
            ))}
          </div>
        )}
      </Space>
    </Modal>
  );
};

export default ImportIssuesModal;
