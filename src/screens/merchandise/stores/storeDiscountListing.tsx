import { MoreOutlined } from '@ant-design/icons';
import { Dropdown, Pagination, Switch } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'wouter';
import CommonTable from '~/components/common/commonTable';
import {
    DeleteDiscount,
    DiscountDetails,
    DiscountList,
    UpdateDiscountStatus,
} from '~/redux/actions/createDiscountAction';
import { getQueryParams } from '~/utils/getQueryParams';
import InventoryCreateDiscountModal from './storeCreateDiscount';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import Alertify from '~/services/alertify';
import { useLoader } from '~/hooks/useLoader';

const StoreDiscountListing = () => {
    const [newDiscountModal, setNewDiscountModal] = useState(false);
    const dispatch = useDispatch();
    const [selectBranches, setSelectedBranches] = useState<string[]>([]);
    const [dataSource, setDataSource] = useState<string[]>([]);
    const [discountDetails, setDiscountDetails] = useState<string[]>([]);
    const [totalItems, setTotalItems] = useState(0);
    const params = getQueryParams();
    const [perPage, setPerPage] = useState(10);
    const pageParam = Number(params.page);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [edit, setEdit] = useState(false);
    const [editId, setEditId] = useState('');
    const [loader, startLoader, endLoader] = useLoader();

    const { storeId } = useParams<{ storeId: string }>();

    const fetchDiscounts = (page: number, perPage: number) => {
        const queryParams = {
            page,
            perPage,
            itemType: 'product',
            includedPricingIds: storeId,
        };
        startLoader();
        dispatch(DiscountList(queryParams))
            .then((response: any) => {
                // console.log('Respons is::', response);
                const data = response?.payload?.data?.data.map((item: any) => {
                    return {
                        ...item,
                    };
                });
                setDataSource(data);
                setTotalItems(
                    response?.payload?.data?._metadata?.pagination?.total || 0
                );
                setDataSource(response?.payload?.data?.data);
            })
            .catch((error: any) => {
                console.log('Error in fetch:', error);
            })
            .finally(endLoader);
    };
    useEffect(() => {
        fetchDiscounts(currentPage, perPage);
        if (edit) {
            dispatch(
                DiscountDetails({
                    discountId: editId,
                })
            )
                .then((response: any) => {
                    setDiscountDetails(response?.payload?.data?.data);
                })
                .catch((error: any) => {
                    console.log('Error in fetch:', error);
                });
        }
    }, [currentPage, perPage, edit, editId]);

    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState<boolean>(false);
    const [deleteConfirmationModalVisible, setDeleteConfirmationModalVisible] =
        useState<boolean>(false);
    const [currentRecord, setCurrentRecord] = useState<any>(null);
    const [deleteCurrentRecord, setDeleteCurrentRecord] = useState<any>(null)
    const openConfirmationModal = (record: any) => {
        console.log('record', record);
        setCurrentRecord(record);
        setConfirmationModalVisible(true);
    };
    const openDeleteConfirmationModal = (record: any) => {
        console.log('record', record);
        setDeleteCurrentRecord(record);
        setDeleteConfirmationModalVisible(true);
    };

    // console.log('currentRecord', currentRecord);
    const handleConfirmStatusChange = () => {
        setConfirmationModalVisible(false);
        dispatch(
            UpdateDiscountStatus({
                discountId: currentRecord._id,
                isActive: !currentRecord.isActive,
            })
        )
            .then(() => {
                Alertify.success('Discount status updated successfully');
                fetchDiscounts(currentPage, perPage);
            })
            .catch((error: any) => {
                console.log('Error in fetch:', error);
                Alertify.error('Could not update status!');
            });

        fetchDiscounts(currentPage, perPage);
        setCurrentRecord(null);
    };
    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
        setCurrentRecord(null);
    };
    const handleCancelDelete = () => {
        setDeleteConfirmationModalVisible(false);
        setDeleteCurrentRecord(null);
    };
    const handleConfirmDelete = () => {
        setDeleteConfirmationModalVisible(false);
        dispatch(
            DeleteDiscount({
                discountId: deleteCurrentRecord._id,
            })
        ).then(() => {
            Alertify.success('Discount deleted successfully');
            fetchDiscounts(currentPage, perPage);
        }).catch((error: any) => {
            console.log('Error in Discount Delete:', error);
            Alertify.error('Could not Delete the Discount!');
        });
        setDeleteCurrentRecord(null);

    }
    const columns = [
        {
            key: 'discountName',
            title: 'Discount Name',
            dataIndex: 'name',
        },
        {
            title: 'Discount Type',
            key: 'type',
            dataIndex: 'type',
        },
        {
            title: 'Value',
            dataIndex: 'value',
            key: 'value',
            align: 'center',
        },
        {
            title: 'Status',
            dataIndex: '',
            key: 'isActive',
            align: 'center',
            render: (record: any) => {
                return (
                    <Switch
                        checked={record.isActive}
                        className="rounded-full transition-colors"
                        checkedChildren="ON"
                        unCheckedChildren="OFF"
                        onChange={() => openConfirmationModal(record)}
                    />
                );
            },
        },
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            width: '120px',
            align: 'center',
            render: (record: any) => {
                const menuItems = [
                    {
                        key: 'edit',
                        label: (
                            <div
                                className="text-xl text-[#1A3353]"
                                onClick={() => {
                                    setNewDiscountModal(true);
                                    setEdit(true);
                                    setEditId(record._id);
                                }}
                            >
                                Edit Discount
                            </div>
                        ),
                    },
                    {
                        key: 'delete',
                        label: (
                            <div
                                className="text-xl text-[#1A3353]"
                                onClick={() => {
                                    openDeleteConfirmationModal(
                                        record

                                    );
                                }}
                            // onClick={() => {
                            //     // console.log('discountId', record._id);
                            //     dispatch(
                            //         DeleteDiscount({
                            //             discountId: record._id,
                            //         })
                            //     ).then(() => {
                            //         Alertify.success('Discount deleted!');
                            //         fetchDiscounts(currentPage, perPage);
                            //     });
                            // }}
                            >
                                Delete Discount
                            </div>
                        ),
                    },
                ];

                return (
                    <>
                        <span className="flex justify-center gap-5 ">
                            <div>
                                <Dropdown
                                    menu={{ items: menuItems }}
                                    trigger={['click']}
                                >
                                    <MoreOutlined
                                        style={{
                                            fontSize: '20px',
                                            cursor: 'pointer',
                                        }}
                                    />
                                </Dropdown>
                            </div>
                        </span>
                    </>
                );
            },
        },
    ];

    const handlePageChange = (page: number, perPage?: number) => {
        setCurrentPage(page);
        if (perPage) setPerPage(perPage);
    };
    // console.log('kasjdfljkasdhfkhsd', discountDetails);
    return (
        <>
            <CommonTable
                className="min-w-min"
                columns={columns}
                backButton={true}
                dataSource={dataSource}
                loading={loader}
                heading="Inventory Discount(s)"
                addNewTitle="Create Discount"
                addNewModal={true}
                openModal={() => {
                    setNewDiscountModal(true);
                }}
            />

            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={totalItems}
                    pageSize={perPage}
                    onChange={handlePageChange}
                    showSizeChanger
                    pageSizeOptions={['10', '20', '50']}
                    hideOnSinglePage
                />
            </div>

            {newDiscountModal && (
                <InventoryCreateDiscountModal
                    edit={edit}
                    setEdit={setEdit}
                    editId={editId}
                    setEditId={setEditId}
                    storeId={storeId}
                    newDiscountModal={newDiscountModal}
                    setNewDiscountModal={setNewDiscountModal}
                    discountDetails={discountDetails}
                    setDiscountDetails={setDiscountDetails}
                    setDataSource={setDataSource}
                    setSelectedBranches={setSelectedBranches}
                />
            )}

            {confirmationModalVisible && (
                <CommonConfirmationModal
                    visible={confirmationModalVisible}
                    onConfirm={handleConfirmStatusChange}
                    onCancel={handleCancelStatusChange}
                    message={'Are you sure you want to change the status'}
                />
            )}
            {deleteConfirmationModalVisible && (
                <CommonConfirmationModal
                    visible={deleteConfirmationModalVisible}
                    onConfirm={handleConfirmDelete}
                    onCancel={handleCancelDelete}
                    message={'Are you sure you want to Delete the Discount'}
                />
            )}
        </>
    );
};

export default StoreDiscountListing;
