import React, { useEffect, useState } from 'react';
import { Modal, Button, Form, Upload, Tag, Space } from 'antd';
import type { UploadProps } from 'antd/es/upload';

interface BulkInventoryUploadModalProps {
  open: boolean;
  onClose: () => void;
  handleCSVFile: (file: File) => void;        // pass the selected CSV file up
  error_data: any;                             // optional: to render an error table if you add one
  bulkUpload: (confirmUpdate: boolean) => void;// call to trigger upload (you can ignore the flag if you always update)
  bulk_upload_loader: boolean;                 // loading state
  resetFile?: boolean;
  setResetFile?: (val: boolean) => void;
}

const BulkInventoryUploadModal: React.FC<BulkInventoryUploadModalProps> = ({
  open,
  onClose,
  handleCSVFile,
  error_data,
  bulkUpload,
  bulk_upload_loader,
  resetFile,
  setResetFile,
}) => {
  const [selectedFileName, setSelectedFileName] = useState<string | null>(null);
  const [confirmUpdate, setConfirmUpdate] = useState(false);
  const [errModalOpen, setErrModalOpen] = useState(false);
  useEffect(() => {
    if (resetFile) {
      setSelectedFileName(null);
      setConfirmUpdate(false);
      setResetFile?.(false);
    }
  }, [resetFile, setResetFile]);

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.csv',
    beforeUpload: () => false, // prevent auto-upload; we handle it manually
    onChange(info) {
      const fileObj = info.fileList?.[0]?.originFileObj as File | undefined;
      if (fileObj && fileObj.name.toLowerCase().endsWith('.csv')) {
        setSelectedFileName(fileObj.name);
        handleCSVFile(fileObj);
      }
    },
    fileList: [], // keep Upload stateless (we manage selection)
  };

  const handleModalClose = () => {
    setSelectedFileName(null);
    setConfirmUpdate(false);
    onClose();
  };

  return (
    <>
      <Modal centered open={open} footer={false} onCancel={handleModalClose} width={600}>
        <div className="w-full h-full p-10">
          <h1 className="text-[1.4vw] font-bold mb-6">Bulk Inventory Upload</h1>

          {/* <div className="mb-3 text-left">
            <Button
              className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
              type="primary"
              ghost
              onClick={() => {
                const link = document.createElement('a');
                // point this to wherever you host your sample file
                // e.g. public/ folder: "/sample-inventory-upload.csv"
                link.href = '/sample-inventory-upload.csv';
                link.download = 'sample-inventory-upload.csv';
                document.body.appendChild(link);
                link.click();
                link.remove();
              }}
            >
              Download Sample CSV
            </Button>
          </div> */}

          <Form layout="vertical">
            <Form.Item
              name="csvFile"
              label={<label className="text-[#1A3353]">Upload CSV File *</label>}
            >
              <Upload.Dragger {...uploadProps} style={{ borderRadius: 8 }}>
                <p className="text-[#3B3B3B] font-medium">
                  Click or drag CSV file to this area
                </p>
                <p className="text-gray-400 text-sm">Only .csv files are accepted</p>
              </Upload.Dragger>

              {selectedFileName && (
                <div className="mt-2 text-xl font-medium">
                  Selected File: <span>{selectedFileName}</span>
                </div>
              )}
            </Form.Item>

            {/* Optional: render your error table here */}
            {/* {error_data && <ErrorTable data={error_data} />} */}

            {/* <Form.Item>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={confirmUpdate}
                  onChange={(e) => setConfirmUpdate(e.target.checked)}
                  className="accent-purple-600 w-5 h-5"
                />
                <span className="text-[#1A3353] font-medium">
                  Do you want to update existing inventory?
                </span>
              </label>
            </Form.Item> */}

            <Button
              className="fw-500   rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
              onClick={() => bulkUpload(confirmUpdate)}
              loading={bulk_upload_loader}
            >
              Upload
            </Button>
          </Form>
        </div>
      </Modal>
      <Modal
        title={`Import Issues${error_data?.length ? ` (${error_data.length})` : ''}`}
        open={errModalOpen}
        onCancel={() => setErrModalOpen(false)}
        footer={[
          <Button key="close" type="primary" onClick={() => setErrModalOpen(false)}>
            Close
          </Button>,
        ]}
        width={800}
        bodyStyle={{ maxHeight: 420, overflowY: 'auto', paddingTop: 8 }}
        destroyOnClose
      >


        {!error_data?.length ? (
          <div className="text-gray-500 py-6">No errors to display.</div>
        ) : (
          <div>
            {error_data.map((e, idx) => (
              <div
                key={`${e.row}-${idx}`}
                className="border-b border-[#00000014] py-2 flex items-start gap-3"
              >
                <div style={{ width: 64 }}>
                  <span className="text-xs text-gray-500">Row</span>
                  <div className="font-medium">{e.row}</div>
                </div>

                <div style={{ width: 140 }}>
                  <span className="text-xs text-gray-500">Field</span>
                  <div className="font-mono">{e.field || '—'}</div>
                </div>

                <div style={{ width: 160 }}>
                  <span className="text-xs text-gray-500">Code</span>
                  <div>
                    <Tag >{e.code}</Tag>
                  </div>
                </div>

                <div className="flex-1">
                  <span className="text-xs text-gray-500">Message</span>
                  <div>{e.message}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </Modal>
    </>
  );
};

export default BulkInventoryUploadModal;
