import {
    Typo<PERSON>,
    Row,
    Col,
    Form,
    FormProps,
    Input,
    Button,
    Select,
    Checkbox,
} from 'antd';
import { useParams } from 'wouter';
import { useEffect, useState } from 'react';
import {
    PrimaryCategoryListData,
    createCategory,
    CategoriesDetailsById,
    EditCategoriesDetails,
} from '~/redux/actions/merchandise/category-action';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { useAppSelector } from '~/hooks/redux-hooks';
import { navigate } from 'wouter/use-location';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';

const { Title } = Typography;

const AddNewCategory = () => {
    const dispatch = useDispatch<AppDispatch>();

    const [form] = Form.useForm();
    const { id } = useParams<{ id: string }>(); // Explicitly typing id
    const [isSubCategory, setIsSubCategory] = useState<boolean>(false);
    function goBack() {
        window.history.back();
    }

    const fetchParentCategoryList = async (searchText = '', page: number) => {
        try {
            const response = await dispatch(
                PrimaryCategoryListData({
                    page,
                    pageSize: 10,
                    level: 'first',
                    search: searchText,
                })
            ).unwrap();
            return response?.data?.data?.list?.map((item: any) => ({
                value: item._id,
                label: item?.name,
            }));
        } catch (error: any) {
            console.error(error);
        }
    };

    const onFinish = (values: any) => {
        const { slug, parentCategory, categoryName } = values;
        let newLevel = '';
        let categoryId = '';
        if (parentCategory) {
            newLevel = 'second';
            categoryId = parentCategory;
        } else {
            newLevel = 'first';
        }

        const newCategoryData = {
            name: categoryName,
            slug,
            level: newLevel,
            parentId: categoryId ? categoryId : undefined,
        };

        if (id === '0') {
            dispatch(createCategory(newCategoryData))
                .unwrap()
                .then(() => {
                    navigate('/categories');
                });
        } else if (id !== '0') {
            console.log('iddd', newCategoryData, id);
            dispatch(EditCategoriesDetails({ id, reqData: newCategoryData }))
                .unwrap()
                .then(() => {
                    navigate('/categories');
                });
        }
    };
    function slugify(string: any) {
        const uniqueTimestamp = new Date().getTime();
        const slugifyValue = `${string
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/&\s*/g, 'and')
            .replace(/&/g, '')
            .replace(/-+/g, '-')
            .replace(/[^a-z0-9-_]/g, '')}-${uniqueTimestamp}`;
        return slugifyValue;
    }
    const handleNameChange = (e: any) => {
        const { value } = e.target;
        const slugValue = slugify(value);
        form.setFieldsValue({ slug: slugValue });
    };

    useEffect(() => {
        if (id !== '0' && id) {
            dispatch(CategoriesDetailsById(id))
                .unwrap()
                .then((res: any) => {
                    const categoryDetail = res.res.data.data;
                    console.log(categoryDetail);
                    if (categoryDetail.parentId) {
                        setIsSubCategory(true);
                        form.setFieldsValue({
                            parentCategory: categoryDetail?.parentId?._id,
                        });
                    }
                    form.setFieldsValue({
                        categoryName: categoryDetail.name,
                        slug: categoryDetail.slug,
                    });
                });
        }
    }, []);
    return (
        <>
            <div className="flex justify-between">
                <div className="flex items-center gap-4 lg:mb-16 @sm:mb-10 ">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353]" level={4}>
                        {id === '0' ? 'Add Category' : 'Edit Category'}
                    </Title>
                </div>
            </div>

            <div className="rounded-lg border p-5 lg:w-[80%] lg:p-10 @sm:w-full">
                <Form
                    name="gymCreate"
                    layout="vertical"
                    size="large"
                    form={form}
                    onFinish={onFinish}
                    autoComplete="off"
                >
                    {/* Category Name */}

                    <div className="flex flex-col ">
                        <div className="flex flex-row items-center justify-between @sm:flex-col">
                            <Form.Item
                                label="Category Name"
                                name="categoryName"
                                className="lg:w-[48%] @sm:w-full"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter a category name',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="Enter category name"
                                    onChange={handleNameChange}
                                />
                            </Form.Item>

                            {isSubCategory && (
                                <Form.Item
                                    label="Parent Category"
                                    className="lg:w-[48%] @sm:w-full"
                                    name="parentCategory"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please Select the Parent Category',
                                        },
                                    ]}
                                >
                                    <InfiniteScrollSelect
                                        fetchOptions={fetchParentCategoryList}
                                        onChange={(value) => {
                                            form.setFieldsValue({
                                                parentCategory: value,
                                            });
                                        }}
                                        placeholder="Select Parent Category"
                                        className="border-b border-[#d1d5db]"
                                    />
                                </Form.Item>
                            )}
                        </div>

                        {/* <Form.Item
                            className="lg:w-1/2"
                            name="slug"
                            label={
                                <label style={{ color: '#1A3353' }}>
                                    Slug *
                                </label>
                            }
                        >
                            <Input
                                disabled={true}
                                value={form.getFieldValue('slug')}
                            />
                        </Form.Item> */}
                        {id === '0' && (
                            <Checkbox
                                className="me-4"
                                checked={isSubCategory}
                                onChange={() =>
                                    setIsSubCategory((prev) => !prev)
                                }
                            >
                                <span className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                                    It is a sub-category?
                                </span>
                            </Checkbox>
                        )}
                    </div>
                    {/* Parent Category */}

                    <Form.Item className="flex justify-end gap-8 py-10  ">
                        <Button
                            onClick={() => navigate('/categories')}
                            htmlType="button"
                            className="w-[110px] border-[#1A3353] bg-[#fff] text-xl text-[#1A3353]"
                        >
                            <p>Cancel</p>
                        </Button>
                        <Button
                            htmlType="submit"
                            className="ms-7 w-[110px] bg-purpleLight text-xl text-white"
                        >
                            <p>Save</p>
                        </Button>
                    </Form.Item>
                </Form>
            </div>
        </>
    );
};

export default AddNewCategory;
