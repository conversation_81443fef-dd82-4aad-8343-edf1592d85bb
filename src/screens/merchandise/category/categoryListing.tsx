import { MoreOutlined } from '@ant-design/icons';
import { ConfigProvider, Dropdown, Table, Pagination, Menu } from 'antd';
import { useState, useEffect } from 'react';
import CustomTable from '~/components/common/customTable';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';
import { navigate } from 'wouter/use-location';
import { useLoader } from '~/hooks/useLoader';
import {
    PrimaryCategoryListData,
    DeleteCategory,
} from '~/redux/actions/merchandise/category-action';
import { useAppSelector } from '~/hooks/redux-hooks';
import FullLoader from '~/components/library/loader/full-loader';
import DeleteModal from '~/components/common/deleteModal';

const CategoryListing = () => {
    const dispatch = useDispatch<AppDispatch>();
    const params = getQueryParams();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const [search, setSearch] = useState(params.search);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [selectedCategoryLevel, setSelectedCategoryLevel] = useState<any>();
    const [pageSize, setPageSize] = useState<number>(10);
    const [loader, startLoader, endLoader] = useLoader(false);
    const [isDeleteModalVisible, setDeleteIsModalVisible] = useState(false);
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [categoryId, setCategoryId] = useState<string>('');

    function handleSearch(value: string) {
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    }
    const fetchCategoryList = async () => {
        try {
            const payload = {
                page: Number(currentPage),
                pageSize: Number(pageSize),
                search: search,
            };
            if (selectedCategoryLevel) {
                payload['level'] = selectedCategoryLevel;
            }
            await dispatch(PrimaryCategoryListData(payload)).unwrap();
        } catch (error: any) {
            console.error(error);
        }
    };
    useEffect(() => {
        fetchCategoryList();
    }, [currentPage, search, selectedCategoryLevel, isDeleteModalVisible]);

    const store = useAppSelector((state: any) => ({
        parentCategoryList: state.category_store.parentCategoryList,
        parentListCount: state.category_store.parentCategoryCount,
    }));

    const dataSource = store.parentCategoryList.map((item: any, i: number) => ({
        key: i + 1,
        _id: item?._id,
        name: item?.name,
        slug: item?.slug,
        parentCategory: item.parentId ? item.parentId.name : ' ',
        parentId: item.parentId ? item.parentId._id : undefined,
        level: item.level.charAt(0).toUpperCase() + item.level.slice(1).toLowerCase(),
    }));

    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
        },
        {
            title: 'Category Level',
            dataIndex: 'level',
        },
        {
            title: 'Parent Category',
            dataIndex: 'parentCategory',
        },
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            render: (record: any) => {
                const menu = (
                    <Menu>
                        <Menu.Item
                            key="1"
                            onClick={() =>
                                navigate(`/add-categories/${record._id}`)
                            }
                        >
                            Edit
                        </Menu.Item>
                        <Menu.Item
                            key="2"
                            onClick={() => {
                                setDeleteIsModalVisible(true);
                                setCategoryId(record._id);
                            }}
                        >
                            Delete
                        </Menu.Item>
                    </Menu>
                );

                return (
                    <Dropdown
                        overlay={menu}
                        trigger={['click']}
                        visible={openDropdownKey === record.key}
                        onOpenChange={(visible) => {
                            setOpenDropdownKey(visible ? record.key : null);
                        }}
                    >
                        <MoreOutlined
                            style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                            }}
                        />
                    </Dropdown>
                );
            },
        },
    ];

    const handleAddCategory = () => {
        navigate(`/add-categories/0`);
    };

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };
    const deleteCategory = () => {
        if (categoryId) {
            dispatch(DeleteCategory(categoryId));
            fetchCategoryList();
        }
        setDeleteIsModalVisible(false);
    };
    return (
        <>
            {loader ? (
                <FullLoader state={true} />
            ) : (
                <div>
                    <CustomTable
                        heading="Category Listing"
                        search={search}
                        showSearch={true}
                        onSearch={handleSearch}
                        showCategoryAddButton={true}
                        onAddCategory={handleAddCategory}
                        onCategoryFilter={true}
                        {...{
                            setSelectedCategoryLevel,
                        }}
                    />
                    <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        borderColor: '#0000001A',
                                        cellFontSize: 13,
                                        headerBg: '#fff',
                                        headerColor: '#1A3353',
                                        colorText: '#455560',
                                    },
                                },
                            }}
                        >
                            <Table
                                id=""
                                className="m-2 overflow-x-auto rounded-[6px] border-1"
                                columns={columns}
                                dataSource={dataSource}
                                pagination={false}
                            />
                        </ConfigProvider>
                    </div>
                    {/* <div className="flex justify-center py-10">
                        <Pagination
                            current={currentPage}
                            total={store.parentListCount}
                            pageSize={pageSize}
                            onChange={handlePageChange}
                            hideOnSinglePage
                        />
                    </div> */}
                </div>
            )}
            {isDeleteModalVisible && (
                <DeleteModal
                    title="Confirm Delete"
                    message={`Do you want to delete this Category?`}
                    isVisible={isDeleteModalVisible}
                    onDelete={deleteCategory}
                    onCancel={() => {
                        setDeleteIsModalVisible(false);
                    }}
                />
            )}
        </>
    );
};

export default CategoryListing;
