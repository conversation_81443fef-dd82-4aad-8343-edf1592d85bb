import React, { useEffect, useState } from 'react';
import { Modal, Button, Form, Upload, Progress } from 'antd';
import type { UploadProps } from 'antd/es/upload';

interface BulkUploadModalProps {
  open: boolean;
  onClose: () => void;
  handleCSVFile: (file: File) => void;
  error_data: any;
  bulkUpload: (confirmUpdate: boolean) => void;
  bulk_upload_loader: boolean;
  resetFile?: boolean;
  setResetFile?: (val: boolean) => void;
}

type UploadProgress = {
  status: 'idle' | 'queued' | 'processing' | 'completed' | 'failed';
  totalRows: number;
  processedRows: number;
  successCount?: number;
  failedCount?: number;
};

const BulkUploadModal: React.FC<BulkUploadModalProps> = ({
  open,
  onClose,
  handleCSVFile,
  error_data,
  bulkUpload,
  bulk_upload_loader,
  resetFile,
  setResetFile,
}) => {
  const [selectedFileName, setSelectedFileName] = useState<string | null>(null);
  const [confirmUpdate, setConfirmUpdate] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({
    status: 'idle',
    totalRows: 0,
    processedRows: 0,
    successCount: 0,
    failedCount: 0,
  });

  // Reset when parent asks
  useEffect(() => {
    if (resetFile) {
      setSelectedFileName(null);
      setConfirmUpdate(false);
      setUploadProgress({
        status: 'idle',
        totalRows: 0,
        processedRows: 0,
        successCount: 0,
        failedCount: 0,
      });
      setResetFile?.(false);
    }
  }, [resetFile, setResetFile]);

  // Listen for progress events dispatched from ProductListing bulkUpload() poller
  useEffect(() => {
    const onProgress = (e: any) => {
      const { status, totalRows, processedRows, successCount, failedCount } = e.detail || {};
      setUploadProgress({
        status: (status as UploadProgress['status']) ?? 'processing',
        totalRows: totalRows ?? 0,
        processedRows: processedRows ?? 0,
        successCount: successCount ?? 0,
        failedCount: failedCount ?? 0,
      });
    };
    window.addEventListener('bulk-upload-progress', onProgress as any);
    return () => window.removeEventListener('bulk-upload-progress', onProgress as any);
  }, []);

  const uploading = bulk_upload_loader || uploadProgress.status === 'processing' || uploadProgress.status === 'queued';

  const percent =
    uploadProgress.totalRows > 0
      ? Math.floor((uploadProgress.processedRows / uploadProgress.totalRows) * 100)
      : uploadProgress.status === 'queued'
      ? 0
      : uploadProgress.status === 'completed'
      ? 100
      : 0;

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.csv',
    beforeUpload: () => false, // prevent auto-upload by Ant
    onChange(info) {
      const fileObj = info.fileList?.[0]?.originFileObj as File | undefined;
      if (fileObj && fileObj.name.toLowerCase().endsWith('.csv')) {
        setSelectedFileName(fileObj.name);
        handleCSVFile(fileObj);
      }
    },
    fileList: [], // control file list externally
    disabled: uploading, // lock while processing
  };

  const handleModalClose = () => {
    if (uploading) return; // prevent closing during processing
    setSelectedFileName(null);
    setConfirmUpdate(false);
    setUploadProgress({
      status: 'idle',
      totalRows: 0,
      processedRows: 0,
      successCount: 0,
      failedCount: 0,
    });
    onClose();
  };

  return (
    <Modal
      centered
      open={open}
      footer={false}
      onCancel={handleModalClose}
      width={600}
      maskClosable={!uploading}
      closable={!uploading}
    >
      <div className="w-full h-full p-10">
        <h1 className="text-[1.4vw] font-bold mb-6">Bulk Product Upload</h1>

        <div className="mb-3 text-left">
          <Button
            className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
            type="primary"
            ghost
            onClick={() => {
              const link = document.createElement('a');
              link.href = '/sample-product-upload-formatted.csv';
              link.download = 'sample-product-upload-formatted.csv';
              document.body.appendChild(link);
              link.click();
              link.remove();
            }}
            disabled={uploading}
          >
            Download Sample CSV
          </Button>
        </div>

        <Form layout="vertical">
          <Form.Item
            name="csvFile"
            label={<label className="text-[#1A3353]">Upload CSV File *</label>}
          >
            <Upload.Dragger {...uploadProps} style={{ borderRadius: 8 }}>
              <p className="text-[#3B3B3B] font-medium">
                Click or drag CSV file to this area
              </p>
              <p className="text-gray-400 text-sm">Only .csv files are accepted</p>
            </Upload.Dragger>

            {selectedFileName && (
              <div className="mt-2 text-sm font-medium">
                Selected File: <span>{selectedFileName}</span>
              </div>
            )}
          </Form.Item>

          {/* Live progress */}
          {/* {uploading && (
            <div className="mt-4">
              <div className="flex items-center justify-between mb-1">
                <span className="text-[#1A3353] font-medium">
                  {uploadProgress.status === 'queued' ? 'Queued…' : 'Processing…'}
                </span>
                <span className="text-sm text-gray-600">
                  {uploadProgress.processedRows}/{uploadProgress.totalRows}
                </span>
              </div>
              <Progress percent={percent} />
              {(uploadProgress.successCount || uploadProgress.failedCount) ? (
                <div className="mt-1 text-xs text-gray-600">
                  Success: {uploadProgress.successCount ?? 0} · Failed: {uploadProgress.failedCount ?? 0}
                </div>
              ) : null}
            </div>
          )} */}

          <Form.Item className="mt-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={confirmUpdate}
                onChange={(e) => setConfirmUpdate(e.target.checked)}
                className="accent-purple-600 w-5 h-5"
                disabled={uploading}
              />
              <span className="text-[#1A3353] font-medium">
                Do you want to update the product?
              </span>
            </label>
          </Form.Item>

          <Button
            className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
            onClick={() => bulkUpload(confirmUpdate)}
            loading={bulk_upload_loader || uploading}
            disabled={uploading} // once processing starts, button disabled; loader shows during the initial enqueue call
          >
            {uploading ? 'Uploading…' : 'Upload'}
          </Button>
        </Form>
      </div>
    </Modal>
  );
};

export default BulkUploadModal;
