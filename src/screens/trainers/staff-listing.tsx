import { MoreOutlined } from '@ant-design/icons';
import { Menu, Switch, Dropdown, Pagination, ConfigProvider } from 'antd';
import clsx from 'clsx';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { Link, useLocation } from 'wouter';
import { navigate } from 'wouter/use-location';
import CommonTable from '~/components/common/commonTable';
import { capitalizeFirstLetter } from '~/components/common/function';
import FullLoader from '~/components/library/loader/full-loader';
import AddStaffModal from '~/components/modals/add-staff';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import ScheduleModal from '~/components/staff/scheduleModal';
import AddPayRate from '~/components/table/addPayRate';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { GetRolesList } from '~/redux/actions/permission-action';
import {
    CreateStaff,
    GetStaffList,
    UpdateStaffStatus,
} from '~/redux/actions/staff-action';
import { clearPayRateDetails } from '~/redux/slices/pay-rate-slice';
import Alertify from '~/services/alertify';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { getQueryParams } from '~/utils/getQueryParams';

const columns = [
    {
        title: 'Full Name',
        dataIndex: '',
        render: (record: any) => {
            return (
                <Link to={`/staff-details/${record.userId}`}>
                    {capitalizeFirstLetter(record.fullName)}
                </Link>
            );
        },
    },
    {
        title: 'Branch Name',
        dataIndex: 'facilityName',
        ellipsis: true,
        width: 250,
    },
    {
        title: 'Email',
        dataIndex: 'email',
        width: 200,
    },
    {
        title: 'Phone No',
        dataIndex: 'phoneNumber',
    },
    {
        title: 'Role',
        dataIndex: 'role',
        render: (text: string | undefined | null) => {
            if (!text) return '-';

            const formatted =
                text.toLowerCase() === 'trainer' ? 'instructor' : text;

            return formatted
                .toLowerCase()
                .split(' ')
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');
        },
    },
];

const TrainerListing: React.FC = () => {
    const [addStaffModal, setAddStaffModal] = useState(false);
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [selectedRoles, setSelectedRoles] = useState([]);
    const [selectedCity, setSelectedCity] = useState([]);
    const [loader, startLoader, endLoader] = useLoader(true);
    const [formLoader, startFormLoader, endFormLoader] = useLoader(false);
    const [saveAnotherStaffLoader, startOtherStaffLoader, endOtherStaffLoader] =
        useLoader(false);
    const [filterLoader, startFilterLoader, endFilterLoader] = useLoader(false);
    const [showAvailabilityModal, setShowvailabilityModal] =
        useState<boolean>(false);
    const [staffName, setStaffName] = useState<string>('');
    const [staffId, setStaffId] = useState<string>('');
    const [_, setLocation] = useLocation();
    const params = getQueryParams();
    const [search, setSearch] = useState(params.search);
    const [staffData, setStaffData] = useState<any>(null);
    const [addModal, setAddModal] = useState(false);
    const [staffIdForSpecialization, setStaffIdForSpecialization] =
        useState<string>('');
    const store = useAppSelector((state) => ({
        staffList: state.staff_store.staffList,
        staffListCount: state.staff_store.staffListCount,
        role: state.auth_store.role,
    }));
    // for add staff permissions
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasStaffWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_STAFF_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type === PERMISSIONS_ENUM.STAFF_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasStaffDeletePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_STAFF_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.STAFF_DELETE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const hasStaffUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_STAFF_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.STAFF_UPDATE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasAvailabilityPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_AVAILABILITY &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_AVAILABILITY_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);

    const dispatch = useAppDispatch();

    const handleCancelStatusChange = () => {
        setStaffData(null);
        setConfirmationModalVisible(false);
    };

    const handleConfirmStatusChange = () => {
        dispatch(
            UpdateStaffStatus({
                id: staffData.userId,
                reqData: {
                    status: !staffData.status,
                },
            })
        );
        setConfirmationModalVisible(false);
        setStaffData(null);
    };

    const openConfirmationModal = (record: any) => {
        setStaffData(record);
        setConfirmationModalVisible(true);
    };

    const openAvailabilityModal = (record: any) => {
        setStaffName(record.fullName);
        setStaffId(record.userId);
        setShowvailabilityModal(true);
    };

    const closeAvailabilityModal = () => {
        setShowvailabilityModal(false);
        setStaffName('');
        setStaffId('');
    };

    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    const debouncedRequest = useDebounce((callback) => callback(), 300);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (search) {
            navigate(`?page=${page}&pageSize=${pageSize}&search=${search}`, {
                replace: true,
            });
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    const selectColumn = [
        {
            title: 'Status',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                // console.log("Record--------------", record)
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    handleBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Switch
                            className={clsx(
                                'rounded-full transition-colors',
                                record.status ? 'bg-switch-on' : 'bg-switch-off'
                            )}
                            id="swtich-off"
                            checkedChildren="ON"
                            disabled={
                                !hasStaffUpdatePermission &&
                                store.role != RoleType.ORGANIZATION
                            }
                            // className="bg-[#D0D4D7]"
                            unCheckedChildren="OFF"
                            onChange={() => openConfirmationModal(record)}
                            checked={record.status || false}
                        />
                    </ConfigProvider>
                );
            },
        },
    ];

    const showAddStaffModal = () => {
        setAddStaffModal(true);
    };
    const hideAddStaffModal = () => {
        setAddStaffModal(false);
    };

    function editProfile(record: any) {
        setLocation(`/staff-details/${record.userId}?edit=true`);
    }
    function StaffSpecialization(record: any) {
        setLocation(`/class-pay-rates/${record.userId}`);
    }

    function saveStaffModal(fields: any, formRef: any) {
        console.log('Fields', fields);
        startFormLoader();
        dispatch(CreateStaff({ fields }))
            .unwrap()
            .then((response: any) => {
                endFormLoader();
                // hideAddStaffModal();
                console.log('Response is:', response);
                const roleId = response?.data?.data?.role;
                let roleType = '';

                dispatch(GetRolesList()).then((res: any) => {
                    const roles = res?.payload?.data?.data || [];
                    const matchedRole = roles.find(
                        (r: any) => r._id === roleId
                    );

                    if (matchedRole) {
                        //    const roleName = matchedRole.name;
                        if (matchedRole.name === 'Instructor') {
                            roleType = 'Instructor';
                        } else if (matchedRole.name === 'Web Master') {
                            roleType = 'webMaster';
                        } else if (matchedRole.name === 'Front Desk Admin') {
                            roleType = 'frontDeskAdmin';
                        }
                        if (roleType === 'Instructor') {
                            // setStaffIdForSpecialization(
                            //     response?.data?.data?._id
                            // );
                            setLocation(
                                `/class-pay-rates/${response?.data?.data?._id}?addSpecialization=true`
                            );
                            // setStaffIdForSpecialization(
                            //     response?.data?.data?._id
                            // );
                            setAddModal(true);
                            hideAddStaffModal();
                        } else {
                            hideAddStaffModal();
                        }
                    }
                });

                formRef.resetFields();
                dispatch(GetStaffList({})).unwrap();
            })
            .catch((error: any) => {
                console.error('Error creating staff:', error);
            })
            .finally(() => {
                endFormLoader();
            });
    }

    function saveAnotherStaff(fields: any, formRef: any) {
        console.log('Fields', fields);
        startOtherStaffLoader();

        dispatch(CreateStaff({ fields }))
            .unwrap()
            .then(() => {
                formRef.resetFields();
            })
            .catch((error: any) => {
                console.error('Error creating staff:', error);
            })
            .finally(() => {
                endOtherStaffLoader();
            });
    }

    function handleSearch(value: string) {
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    }

    useEffect(() => {
        dispatch(FacilitiesList({})).unwrap();
    }, []);

    useEffect(() => {
        const requestParams = {
            page: currentPage,
            pageSize: pageSizes,
        } as any;

        startLoader();

        dispatch(GetStaffList({ ...requestParams }))
            .unwrap()
            .then(() => {})
            .finally(endLoader);
    }, []);

    useEffect(() => {
        const requestParams = {
            page: currentPage,
            pageSize: pageSizes,
        } as any;

        if (selectedCity && selectedCity?.length > 0) {
            requestParams.locationId = selectedCity;
        }

        if (selectedRoles && selectedRoles?.length > 0) {
            requestParams.role = selectedRoles;
        }
        if (
            search ||
            currentPage > 1 ||
            selectedCity.length ||
            selectedRoles.length
        )
            startFilterLoader();
        if (search) {
            debouncedRequest(() => {
                dispatch(
                    GetStaffList({
                        search: search,
                        ...requestParams,
                    })
                )
                    .unwrap()
                    .then(() => {})
                    .finally(endFilterLoader);
            });
        } else {
            debouncedRequest(() => {
                dispatch(GetStaffList({ ...requestParams }))
                    .unwrap()
                    .then(() => {})
                    .finally(endFilterLoader);
            });
        }
    }, [search, selectedRoles, selectedCity, pageParam, pageSizeParam]);

    const actionColumn = [
        ...(hasStaffUpdatePermission ||
        hasAvailabilityPermission ||
        store.role === RoleType.ORGANIZATION
            ? [
                  {
                      title: 'Action',
                      dataIndex: '',
                      key: 'action',
                      render: (record: any) => {
                          // console.log("record----------", record)
                          const menu = (
                              <Menu>
                                  {(hasStaffUpdatePermission ||
                                      store.role === RoleType.ORGANIZATION) && (
                                      <Menu.Item
                                          key="1"
                                          onClick={() => editProfile(record)}
                                      >
                                          Edit Staff Profile
                                      </Menu.Item>
                                  )}
                                  {(hasStaffUpdatePermission ||
                                      store.role === RoleType.ORGANIZATION) &&
                                      record?.staffRole ===
                                          RoleType.TRAINER && (
                                          <Menu.Item
                                              key="2"
                                              onClick={() =>
                                                  StaffSpecialization(record)
                                              }
                                          >
                                              Manage Staff specialization
                                          </Menu.Item>
                                      )}
                                  {(hasAvailabilityPermission ||
                                      store.role === RoleType.ORGANIZATION) && (
                                      <Menu.Item
                                          onClick={() =>
                                              openAvailabilityModal(record)
                                          }
                                          key="3"
                                      >
                                          Manage Schedules
                                      </Menu.Item>
                                  )}
                                  {/* <Menu.Item key="4">Manage Appointments Type</Menu.Item> */}
                                  {(hasStaffUpdatePermission ||
                                      store.role === RoleType.ORGANIZATION) && (
                                      <Menu.Item key="5">
                                          Re-set Password
                                      </Menu.Item>
                                  )}
                              </Menu>
                          );

                          return (
                              <Dropdown
                                  overlay={menu}
                                  trigger={['click']}
                                  visible={openDropdownKey === record.key}
                                  onOpenChange={(visible) => {
                                      setOpenDropdownKey(
                                          visible ? record.key : null
                                      );
                                  }}
                              >
                                  <MoreOutlined
                                      style={{
                                          fontSize: '20px',
                                          cursor: 'pointer',
                                      }}
                                  />
                              </Dropdown>
                          );
                      },
                  },
              ]
            : []),
    ];

    const combinedColumns = [...columns, ...selectColumn, ...actionColumn];

    return loader ? (
        <FullLoader state={true} />
    ) : (
        <>
            <div className="mt-6">
                <CommonTable
                    className="min-w-min"
                    columns={combinedColumns}
                    dataSource={store.staffList}
                    addNewModal={
                        hasStaffWritePermission ||
                        store.role === RoleType.ORGANIZATION
                    }
                    openModal={showAddStaffModal}
                    addNewTitle="Create Staff"
                    heading="Staff"
                    onSearch={handleSearch}
                    loading={filterLoader}
                    search={search}
                    showStaffRole={true}
                    showStaffLocation={true}
                    showSearch={true}
                    {...{
                        selectedRoles,
                        setSelectedRoles,
                        selectedCity,
                        setSelectedCity,
                    }}
                />
                <div className="b flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={store.staffListCount}
                        pageSize={pageSizes}
                        pageSizeOptions={['10', '20', '50']}
                        onChange={paginate}
                        hideOnSinglePage
                    />
                </div>
            </div>
            <AddStaffModal
                visible={addStaffModal}
                onClose={hideAddStaffModal}
                onSave={saveStaffModal}
                onSaveAnother={saveAnotherStaff}
                loader={formLoader}
                addOtherStaffLoader={saveAnotherStaffLoader}
            />
            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />

            {showAvailabilityModal && (
                <ScheduleModal
                    key={`${staffName}-${staffId}`}
                    visible={showAvailabilityModal}
                    onClose={closeAvailabilityModal}
                    isStaffDetails={true}
                    staffName={staffName}
                    staffId={staffId}
                />
            )}
            <AddPayRate
                visible={addModal}
                onClose={() => {
                    setAddModal(false);
                    // setPayRateId(null);
                    dispatch(clearPayRateDetails());
                }}
                // id={payRateId}
                staffId={staffIdForSpecialization}
            />
        </>
    );
};

export default TrainerListing;
