import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
    <PERSON><PERSON>,
    Col,
    ConfigProvider,
    Form,
    Input,
    Row,
    FormProps,
    Typography,
    Select,
    Upload,
    GetProp,
    UploadProps,
    message,
    SelectProps,
} from 'antd';
import PhoneInput from 'antd-phone-input';
import React, { useState } from 'react';
import ReactQuill from 'react-quill';

const { Title } = Typography;
function goBack() {
    window.history.back();
}

// for banner upload
type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

const getBase64 = (img: FileType, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(img);
};

const beforeUpload = (file: FileType) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
        message.error('You can only upload JPG/PNG file!');
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
        message.error('Image must smaller than 2MB!');
    }
    return isJpgOrPng && isLt2M;
};
const aminityOptions: SelectProps['options'] = [];

for (let i = 10; i < 36; i++) {
    aminityOptions.push({
        value: i.toString(36) + i,
        label: i.toString(36) + i,
    });
}

const CreateTrainer: React.FC = () => {
    // For banner upload
    const [loading, setLoading] = useState(false);
    const [imageUrl, setImageUrl] = useState<string>();

    const handleChange: UploadProps['onChange'] = (info) => {
        if (info.file.status === 'uploading') {
            setLoading(true);
            return;
        }
        if (info.file.status === 'done') {
            // Get this url from response in real world.
            getBase64(info.file.originFileObj as FileType, (url) => {
                setLoading(false);
                setImageUrl(url);
            });
        }
    };

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </button>
    );
    // Banner upload end

    const onFinish: FormProps['onFinish'] = (values) => {
        console.log('Success:', values);
        const payload = {
            gymName: values.gymName,
            mobile: values.mobile,
            email: values.email,
            address: {
                state: values.state,
                city: values.city,
                location: values.location,
            },
            profilePicture:
                'https://my-bucket.s3.us-west-2.amazonaws.com/uploads/images/example.jpg', // Dummy URL for now
            description: values.description,
            aminities: values.aminities,
        };
        console.log('Data---------', payload);
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                },
            }}
        >
            <div className="flex items-center gap-4">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title level={4}>Create Trainer</Title>
            </div>
            <Row className="mt-16 justify-around">
                <Col className="rounded-3xl border p-16" span={15}>
                    <Form
                        name="gymCreate"
                        layout="vertical"
                        size="large"
                        initialValues={{ remember: true }}
                        onFinish={onFinish}
                        autoComplete="off"
                    >
                        <Form.Item
                            label="Full Name"
                            name="fullName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please input gym name!',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Gym Name" />
                        </Form.Item>
                        <Form.Item
                            label="Gym Name"
                            name="gymName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please input gym name!',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Gym Name" />
                        </Form.Item>
                        <Form.Item
                            label="Tier"
                            name="tier"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select tier!',
                                },
                            ]}
                        >
                            <Select
                                showSearch
                                placeholder="Select Tier"
                                filterOption={(input, option) =>
                                    (option?.label ?? '')
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                }
                                options={[
                                    { value: '1', label: 'Haryana' },
                                    { value: '2', label: 'Delhi' },
                                    { value: '3', label: 'UP' },
                                    { value: '4', label: 'Rajasthan' },
                                ]}
                            />
                        </Form.Item>
                        <Form.Item
                            label="Category"
                            name="category"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select category!',
                                },
                            ]}
                        >
                            <Select
                                showSearch
                                placeholder="Select Category"
                                filterOption={(input, option) =>
                                    (option?.label ?? '')
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                }
                                options={[
                                    { value: '1', label: 'Haryana' },
                                    { value: '2', label: 'Delhi' },
                                    { value: '3', label: 'UP' },
                                    { value: '4', label: 'Rajasthan' },
                                ]}
                            />
                        </Form.Item>
                        <Form.Item
                            label="Description"
                            name="description"
                            rules={[
                                {
                                    required: true,
                                    // message: 'Please input your address!',
                                },
                            ]}
                        >
                            <ReactQuill
                                theme="snow"
                                // className="h-[20vh] rounded-lg"
                            />
                        </Form.Item>
                        <Form.Item className="mt-8">
                            <Button
                                htmlType="submit"
                                className="border-[#8143d1] bg-[#8143d1] text-white"
                            >
                                Submit
                            </Button>
                        </Form.Item>
                    </Form>
                </Col>
                <Col className="rounded-3xl border p-16" span={8}>
                    <Typography.Title level={4}>
                        <span className="text-[#8143d1]">
                            UPLOAD GYM BANNER
                        </span>
                    </Typography.Title>
                    <Upload
                        name="avatar"
                        listType="picture-card"
                        className="avatar-uploader mt-8"
                        showUploadList={false}
                        action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                        beforeUpload={beforeUpload}
                        onChange={handleChange}
                    >
                        {imageUrl ? (
                            <img
                                src={imageUrl}
                                alt="avatar"
                                style={{ width: '100%' }}
                            />
                        ) : (
                            uploadButton
                        )}
                    </Upload>
                </Col>
            </Row>
        </ConfigProvider>
    );
};

export default CreateTrainer;
