import { ConfigProvider, Dropdown, Switch } from 'antd';
import { useEffect, useState } from 'react';
import CommonTable from '~/components/common/commonTable';
import clsx from 'clsx';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    DeleteAdminPaymentMethod,
    SuperAdminPaymentMethodList,
    updatePaymentStatus,
} from '~/redux/actions/payment-method.action';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { LoaderIcon } from 'react-hot-toast';
import CreatePaymentModal from './create-payment-method';
import { DeleteOutlined, EditOutlined, MoreOutlined } from '@ant-design/icons';

const SuperAdminPaymentMethodListing = () => {
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const dispatch = useAppDispatch();
    const [isAddPaymentModalVisible, setIsAddPaymentModalVisible] =
        useState<boolean>(false);
    const [currentRecord, setCurrentRecord] = useState<any>(null);
    const [currentPayment, setCurrentPayement] = useState<any>(null);

    useEffect(() => {
        setLoading(true);
        dispatch(SuperAdminPaymentMethodList({}))
            .unwrap()
            .then(() => {
                setLoading(false);
            });
    }, [dispatch]);
    const store = useAppSelector((state) => ({
        superAdminPaymentMethodsList:
            state.paymentmethod_store.superAdminPaymentMethodsList,
    }));
    const [changeStatus, setChangeStatus] = useState<boolean>(false);
    const handleStatusChange = (record: any) => {
        setCurrentPayement(record);
        setConfirmationModalVisible(true);
    };

    const handleConfirmStatusChange = async () => {
        const apiPayload = {
            // facilityId: selectedFacilityId,
            paymentMethodId: currentPayment?._id,
            isActive: !currentPayment?.isActive,
        };
        await dispatch(updatePaymentStatus(apiPayload));
        dispatch(SuperAdminPaymentMethodList({}));
        setConfirmationModalVisible(false);
    };

    const Columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: 'Status',
            dataIndex: '',
            key: 'isActive',
            render: (record: any) => {
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    handleBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Switch
                            id="switch-off"
                            className={clsx(
                                'rounded-full transition-colors',
                                record.isActive
                                    ? 'bg-switch-on'
                                    : 'bg-switch-off'
                            )}
                            checkedChildren="ON"
                            unCheckedChildren="OFF"
                            checked={record.isActive || false}
                            onChange={() => handleStatusChange(record)}
                        />
                    </ConfigProvider>
                );
            },
        },
        {
            title: 'Action',
            dataIndex: '',
            width: '120px',
            key: 'action',
            render: (record: any) => {
                const menuItems = [
                    {
                        key: 1,
                        label: (
                            <p
                                onClick={() => {
                                    setCurrentRecord(record._id);
                                    setIsAddPaymentModalVisible(true);
                                }}
                            >
                                <span className="pe-3">
                                    <EditOutlined />
                                </span>
                                Edit Method
                            </p>
                        ),
                    },
                    {
                        key: 2,
                        label: (
                            <p
                                className="text-red-500"
                                onClick={() => {
                                    setLoading(true);
                                    dispatch(
                                        DeleteAdminPaymentMethod({
                                            paymentMethodId: record._id,
                                        })
                                    )
                                        .unwrap()
                                        .then((res: any) => {
                                            dispatch(
                                                SuperAdminPaymentMethodList({})
                                            )
                                                .unwrap()
                                                .then(() => {
                                                    setLoading(false);
                                                });
                                        });
                                }}
                            >
                                <span className="pe-3">
                                    <DeleteOutlined />
                                </span>
                                Delete Method
                            </p>
                        ),
                    },
                ];

                return (
                    <>
                        <span className="flex gap-5 ">
                            <div>
                                <Dropdown
                                    menu={{ items: menuItems }}
                                    trigger={['click']}
                                >
                                    <MoreOutlined
                                        style={{
                                            fontSize: '20px',
                                            cursor: 'pointer',
                                        }}
                                    />
                                </Dropdown>
                            </div>
                        </span>
                    </>
                );
            },
        },
    ];


    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
    };

    // for Add New Modal

    const onClose = () => {
        setIsAddPaymentModalVisible(false);
    };

    return (
        <>
            {loading ? (
                <div className="flex h-full items-center justify-center">
                    <LoaderIcon className="h-[30px] w-[30px]" />
                </div>
            ) : (
                <div>
                    <div className="mt-6">
                        {/* <Select
                    placeholder="Select the branch name"
                    onChange={(value: any) => setSelectedFacilityId(value)}
                    value={selectedFacilityId}
                >
                    {store.facilityList?.map((facility: any) => (
                        <Option key={facility._id} value={facility._id}>
                            {facility.facilityName}
                        </Option>
                    ))}
                </Select> */}
                        <CommonTable
                            className="min-w-min"
                            columns={Columns}
                            openModal={setIsAddPaymentModalVisible}
                            dataSource={store.superAdminPaymentMethodsList}
                            bulkAction={false}
                            backButton={true}
                            heading="Payment Methods"
                            // addNewLink="/setting/create-announcementsmm"
                            addNewTitle="Add New"
                            addNewModal={true}
                        />
                    </div>
                    {/* <AddPaymentModal
                visible={isAddPaymentModalVisible}
                onClose={hidePaymentModal}
                facilityList={store.facilityList}
            /> */}
                    <CreatePaymentModal
                        currentRecord={currentRecord}
                        setCurrentRecord={setCurrentRecord}
                        isAddPaymentModalVisible={isAddPaymentModalVisible}
                        onClose={onClose}
                    />
                    <CommonConfirmationModal
                        visible={confirmationModalVisible}
                        onConfirm={handleConfirmStatusChange}
                        onCancel={handleCancelStatusChange}
                        message="Are you sure you want to change the status?"
                    />
                </div>
            )}
        </>
    );
};

export default SuperAdminPaymentMethodListing;
