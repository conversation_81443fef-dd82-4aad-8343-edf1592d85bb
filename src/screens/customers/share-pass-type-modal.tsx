import React, { useState } from 'react';
import { Modal, Button, Typography, Space } from 'antd';
import { SwapOutlined, ExportOutlined } from '@ant-design/icons';
import SharePassModal from './share-pass-modal';
import SharePassToModal from './share-pass-to-Modal';

const { Title, Text } = Typography;


interface SharePassTypeModalProps {
    visible: boolean;
    onClose: (val?: boolean) => void;
    clientId?: string;
    purchaseId?: string;
    clientName?: string;
    bundledPricingId?: string;
    packageId?: string;
    invoiceId?: string;
    // onSelect: (type: 'from' | 'to') => void;

}
const SharePassTypeModal: React.FC<SharePassTypeModalProps> = ({
    visible,
    onClose,
   clientId,
    purchaseId,
    clientName,
    bundledPricingId,
    packageId,
    invoiceId,

}) => {
    const [shareFromModal, setShareFromModal] = useState<boolean>(false);
    const [shareToModal, setShareToModal] = useState<boolean>(false);

    const handleSharePassClose = (refresh = false) => {
        setShareFromModal(false);
        setShareToModal(false)
        onClose(true)

    };
    return (
        <>
            <Modal
                open={visible}
                 title="Share User's Pass"
                footer={null}
                onCancel={() => onClose()}
                centered
                closable={true}
                width={400}
            >
                <div style={{ textAlign: 'center' }}>
                    <Text type="secondary">
                        Choose whether you want to share from or share to
                    </Text>

                    <Space
                        style={{ marginTop: 24 }}
                        direction="horizontal"
                        size="large"
                    >
                        <Button
                            type="default"
                            icon={<SwapOutlined />}
                            style={{
                                backgroundColor: '#f5f3ff',
                                color: '#5f27cd',
                                fontWeight: 500,
                                padding: '16px 24px',
                                borderRadius: 10,
                                border: 'none',
                            }}
                            onClick={() => setShareFromModal(true)}
                        >
                            Share To
                        </Button>

                        <Button
                            type="default"
                            icon={<ExportOutlined />}
                            style={{
                                backgroundColor: '#e6fffb',
                                color: '#10ac84',
                                fontWeight: 500,
                                padding: '16px 24px',
                                borderRadius: 10,
                                border: 'none',
                            }}
                            onClick={() => setShareToModal(true)}
                        >
                            Share From
                        </Button>
                    </Space>
                </div>
            </Modal>
            {shareFromModal && (
                <SharePassModal
                    visible={shareFromModal}
                    onClose={handleSharePassClose}
                    clientId={clientId}
                    clientName={clientName}
                    purchaseId={purchaseId}
                />
            )}
             {shareToModal && (
                <SharePassToModal
                    visible={shareToModal}
                    onClose={handleSharePassClose}
                    clientId={clientId}
                    clientName={clientName}
                    purchaseId={purchaseId}
                />
            )}
        </>
    );
};

export default SharePassTypeModal;
