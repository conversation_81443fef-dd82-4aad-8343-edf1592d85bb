import React, { useEffect, useState } from 'react';
import {
    Button,
    ConfigProvider,
    DatePicker,
    Form,
    Input,
    Modal,
    Select,
} from 'antd';
import { Dayjs } from 'dayjs';
import Title from 'antd/es/typography/Title';

const { Option } = Select;

interface IStaffFormFields {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
    gender: 'male' | 'female' | 'other';
    role: 'webmaster' | 'trainer' | 'other';
    setUpDate: Dayjs;
    facilityId: string[];
}

interface AddNewLeadModalProps {
    visible: boolean;
    onClose: () => void;
    onSave: (fields: IStaffFormFields, formRef: any) => void;
    onSaveAnother: (fields: IStaffFormFields, formRef: any) => void;
    loader: boolean;
    addOtherStaffLoader: boolean;
}

const AddNewLeadModal: React.FC<AddNewLeadModalProps> = ({
    visible,
    onClose,
}) => {
    const handleClose = () => {
        onClose();
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Form: {
                        itemMarginBottom: 22,
                        verticalLabelMargin: -5,
                    },
                },
                token: {
                    borderRadius: 5,
                },
            }}
        >
            <Modal
                open={visible}
                onOk={handleClose}
                onCancel={handleClose}
                centered
                title={
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                        }}
                    >
                        <Title level={4} className=" text-[#1a3353]">
                            Add New
                        </Title>
                    </div>
                }
                footer={null}
                closeIcon={true}
                style={{ top: 10 }}
                className="lg:w-[50%]"
            >
                <Form
                    className=" flex flex-col gap-3 pt-10"
                    name="add-trainer"
                    layout="vertical"
                    size="large"
                    autoComplete="off"
                >
                    <div className="flex flex-row items-center gap-10">
                        <Form.Item
                            label="First Name"
                            className="w-full"
                            name="firstName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter full name',
                                },
                            ]}
                        >
                            <Input placeholder="Enter First Name" />
                        </Form.Item>

                        <Form.Item
                            label="Email"
                            name="email"
                            className="w-full"
                            rules={[
                                {
                                    required: true,
                                    message: 'Email is required',
                                },
                                {
                                    type: 'email',
                                    message: 'Please enter a valid email',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Email" />
                        </Form.Item>
                    </div>
                    <div className="flex flex-row items-center gap-10">
                        <Form.Item
                            label="Mobile"
                            name="mobile"
                            className="w-full"
                            rules={[
                                {
                                    required: true,
                                    message: 'Mobile number is required',
                                },
                                {
                                    pattern: /^\d{10}$/,
                                    message:
                                        'Mobile number must be exactly 10 digits',
                                },
                            ]}
                        >
                            <Input
                                type="text"
                                maxLength={10}
                                onInput={(e: any) => {
                                    e.target.value = e.target.value.replace(
                                        /[^0-9]/g,
                                        ''
                                    );
                                }}
                                placeholder="Enter Mobile"
                            />
                        </Form.Item>
                        <Form.Item
                            label="Created at"
                            name="setUpDate"
                            className="w-full"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select the date',
                                },
                            ]}
                        >
                            <DatePicker
                                popupClassName="custom-datepicker"
                                placeholder="DD/MM/YYYY"
                                format="DD/MM/YYYY"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </div>
                    <div className="flex flex-row items-center gap-10">
                        <Form.Item
                            label="Lead Source"
                            name="leadSource"
                            className="w-full"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select lead source',
                                },
                            ]}
                        >
                            <Select placeholder="Select Lead Source">
                                <Option value="male">Instagram</Option>
                                <Option value="female">Twitter</Option>
                                <Option value="other">LinkedIn</Option>
                            </Select>
                        </Form.Item>

                        <Form.Item
                            label="Staff Name"
                            className="w-full"
                            name="staffName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter staff name',
                                },
                            ]}
                        >
                            <Input placeholder="Enter staff Name" />
                        </Form.Item>
                    </div>
                    <div className="flex flex-row items-center gap-10">
                        <Form.Item
                            label="Follow up Date"
                            name="followUpDate"
                            className="w-full"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select the follow up date',
                                },
                            ]}
                        >
                            <DatePicker
                                popupClassName="custom-datepicker"
                                placeholder="DD/MM/YYYY"
                                format="DD/MM/YYYY"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>

                        <Form.Item
                            label="Notes"
                            name="notes"
                            className="w-full"
                        >
                            <Input.TextArea
                                placeholder="Enter Notes"
                                autoSize={{ minRows: 1, maxRows: 1 }}
                            />
                        </Form.Item>
                    </div>

                    <div className="flex flex-row justify-end">
                        <Form.Item>
                            <div style={{ display: 'flex', gap: '10px' }}>
                                <Button
                                    onClick={handleClose}
                                    className=" rounded-lg border  border-[#1a3353] px-10  text-xl text-[#1a3353]"
                                    type="default"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    className="rounded-lg bg-purpleLight px-10   py-7 text-xl text-white  "
                                    htmlType="submit"
                                >
                                    Save
                                </Button>
                            </div>
                        </Form.Item>
                    </div>
                </Form>
            </Modal>
        </ConfigProvider>
    );
};

export default AddNewLeadModal;
