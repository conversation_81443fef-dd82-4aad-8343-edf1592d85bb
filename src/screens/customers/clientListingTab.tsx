import React, { useState } from 'react';
import { Tabs } from 'antd';
import clsx from 'clsx';
import CustomerListing from './customerListing';
import WavierLeadListingModal from './wavierLead';
import LeadsListing from './leads-listing';
import InactiveClientTab from './InactiveClientTab';
import { useDispatch, useSelector } from 'react-redux';
import { setCollapsed } from '~/redux/slices/topBar-slice';
import ClientPreview from './clientPreview';

const { TabPane } = Tabs;

const ClientTabNavigation: React.FC = () => {
    const [activeKey, setActiveKey] = useState('clients');
    const [viewClientId, setViewClientId] = useState<string>('');
    const [clientDetails, setClientDetails] = useState<boolean>(false);
    const [clientUpdatePermission, setClientUpdatePermission] =
        useState<boolean>(false);
    const handleTabChange = (key: string) => {
        setActiveKey(key);
    };
    const collapsed = useSelector((state: any) => state.sidebar.collapsed);
    const dispatch = useDispatch();
    const handleSetCollapsed = (val: boolean) => {
        dispatch(setCollapsed(val));
    };
    const viewClient = (clientId: string) => {
        handleSetCollapsed(true);
        setViewClientId(clientId);
        setClientDetails(true);
    };
    const cancelViewClient = () => {
        handleSetCollapsed(false);
        setViewClientId('');
        setClientDetails(false);
    };
    // console.log('the active client is:', viewClientId);
    const tabs = [
        { key: 'clients', label: 'Clients' },
        // { key: 'Inactive', label: 'Inactive Clients' },
        { key: 'wavier', label: 'Waiver' },
        { key: 'leads', label: 'Leads' },
    ];

    const renderTabBar = () => (
        <div className="flex gap-12 rounded-md px-4 py-3 shadow-sm">
            {tabs.map((tab) => {
                const isActive = activeKey === tab.key;
                return (
                    <button
                        key={tab.key}
                        onClick={() => handleTabChange(tab.key)}
                        className={clsx(
                            ' flex w-[50%] items-center justify-center rounded-md px-8  py-3  text-2xl ',
                            isActive
                                ? 'bg-purpleLight text-white shadow'
                                : '  text-[#112D55]'
                        )}
                    >
                        <p className="text-center font-semibold">{tab.label}</p>
                    </button>
                );
            })}
        </div>
    );

    const renderContent = () => {
        switch (activeKey) {
            case 'clients':
                return (
                    <CustomerListing
                        viewClientId={viewClientId}
                        clientDetails={clientDetails}
                        setViewClientId={setViewClientId}
                        viewClient={viewClient}
                        cancelViewClient={cancelViewClient}
                        setClientUpdatePermission={setClientUpdatePermission}
                    />
                );
            case 'wavier':
                return <WavierLeadListingModal />;
            case 'leads':
                return <LeadsListing />;
            case 'Inactive':
                return <InactiveClientTab />;
            default:
                return null;
        }
    };

    return (
        <div className="sidebar-scrollbar flex max-h-full gap-8 overflow-y-scroll">
            <div
                className={`${
                    clientDetails
                        ? '3xl:w-[80%] md:w-[73%] 2xl:w-[75%]'
                        : 'w-full'
                } sidebar-scrollbar max-h-full overflow-y-scroll transition-all duration-300 ease-linear`}
            >
                {renderTabBar()}
                <div className="mt-6">{renderContent()}</div>
            </div>
            <div
                className={`3xl:w-[20%] max-h-full overflow-y-scroll rounded-xl border p-8 transition-all duration-300 ease-linear md:w-[27%] 2xl:w-[25%] ${
                    clientDetails ? '' : 'hidden'
                }`}
            >
                <ClientPreview
                    cancelViewClient={cancelViewClient}
                    viewClientId={viewClientId}
                    clientUpdatePermission={clientUpdatePermission}
                />
            </div>
        </div>
    );
};

export default ClientTabNavigation;
