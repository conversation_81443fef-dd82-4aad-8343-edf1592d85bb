import {
    Button,
    ConfigProvider,
    Dropdown,
    Form,
    FormProps,
    Input,
    Menu,
    Modal,
    Pagination,
    Select,
} from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { capitalizeFirstLetter } from '~/components/common/function';
import CommonTable from '~/components/common/commonTable';
import { CustomerLeadList } from '~/redux/actions/customerLead-action';
import { useLoader } from '~/hooks/useLoader';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { SetFacilityId } from '~/redux/slices/appointment-slice';
import { MoreOutlined } from '@ant-design/icons';
import { navigate } from 'wouter/use-location';
import AddSubprofileModal from './wavier/add-child-modal';
import AddClientLeadModal from './wavier/add-individual-client-modal';
import dayjs from 'dayjs';
import AddClientModal from './add-client-modal';
import { useSelector } from 'react-redux';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
const { Search } = Input;

const WavierLeadListingModal = (props: any) => {
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSizes, setPageSize] = useState(10);
    const [selectedLocation, setSelectedLocation] = useState<string>();
    const [ClientLeadList, setClientLeadList] = useState<any>([]);
    const [addSubProfileLeadModel, setaddSubProfileLeadModel] = useState(false);
    const [addClientLeadModel, setAddClientLeadModel] = useState(false);
    const [isClientModal, setIsClientModal] = useState<boolean>(false);

    const [leadId, setLeadId] = useState<string>();
    const [searchValue, setSearchValue] = useState<string>();
    const [total, setTotal] = useState(0);
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        facilityList: state.facility_store.facilityList,
    }));
    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));
    useEffect(() => {
        dispatch(FacilitiesList({ page: 1, pageSize: 30 })).then((res: any) => {
            setSelectedLocation(res?.payload?.data?.data?.list?.[0]?._id);
            dispatch(SetFacilityId(res?.payload?.data?.data?.[0]?._id));
        });
    }, []);
    useEffect(() => {
        if (selectedLocation) {
            startLoader();
            dispatch(
                CustomerLeadList({
                    page: currentPage,
                    pageSize: pageSizes,
                    search: searchValue ? searchValue : '',
                    facilityId: selectedLocation,
                })
            )
                .then((res: any) => {
                    setClientLeadList(res?.payload?.data?.clientLead);
                    setTotal(res?.payload?.data?.totalClientLead);
                    endLoader();
                })
                .catch((err: any) => {
                    endLoader();
                });
        }
    }, [selectedLocation, searchValue, currentPage, pageSizes]);

    const columns = [
        {
            title: 'Name',
            dataIndex: '',
            render: (record: any) => {
                const firstName = capitalizeFirstLetter(
                    record?.firstName || ''
                );
                const lastName = record?.lastName
                    ? ' ' + capitalizeFirstLetter(record.lastName)
                    : '';
                return (
                    <div
                        className="cursor-pointer"
                        onClick={() => {
                            const id =
                                record.flag === 'minor'
                                    ? record?.parentClientId
                                    : record._id;
                            navigate(`/wavier-lead/${id}`);
                        }}
                    >
                        {firstName + lastName}
                    </div>
                );
            },
        },

        {
            title: 'Phone No',
            dataIndex: 'phone',
            render: (text: string | undefined | null) => {
                return text?.trim() ? text : <p className="">—</p>;
            },
        },
        {
            title: 'Email',
            dataIndex: 'email',
            render: (text: string | undefined | null) => {
                return text?.trim() ? text : <p className="">—</p>;
            },
        },
        {
            title: 'Location',
            dataIndex: '',
            render: (record: any) => {
                return (
                    <>
                        <div>
                            {capitalizeFirstLetter(
                                record?.facilityId?.facilityName
                            )}
                        </div>
                    </>
                );
            },
        },
        {
            title: 'Date Requested',
            dataIndex: '',
            render: (record: any) => {
                return (
                    <>
                        <div>
                            {dayjs(record?.createdAt).format('MMM DD, YYYY')}
                        </div>
                    </>
                );
            },
        },
        {
            title: 'Action',
            dataIndex: '',
            width: '120px',
            align: 'center',
            key: 'action',
            render: (record: any) => {
                const showAddAsIndividual =
                    record.flag !== 'minor' && !record.isConvertedToClient;

                const menu = (
                    <Menu>
                        {showAddAsIndividual && (
                            <Menu.Item
                                key="assign-package"
                                onClick={() => {
                                    setLeadId(record._id);
                                    setAddClientLeadModel(true);
                                }}
                            >
                                <span className="text-xl text-[#1A3353]">
                                    Add As an Individual
                                </span>
                            </Menu.Item>
                        )}
                    </Menu>
                );

                if (!showAddAsIndividual) return null;

                return (
                    <span className="flex justify-center gap-5">
                        <Dropdown overlay={menu} trigger={['click']}>
                            <MoreOutlined
                                style={{
                                    fontSize: '20px',
                                    cursor: 'pointer',
                                }}
                            />
                        </Dropdown>
                    </span>
                );
            },
        },
    ];
    const handleSearchChange = (values: any) => {
        setSearchValue(values);
        setCurrentPage(1);
    };
    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
    }
    const handleFacilityChange = (value: string) => {
        setSelectedLocation(value);
        setCurrentPage(1); // Reset page
    };
    // for add client permissions
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasClientWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.CLIENTS_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    return (
        <>
            <div className="flex w-full items-center justify-end gap-10 pt-5">
                {/* Left: Search */}

                {/* Right: Location Select + Buttons */}

                <Search
                    allowClear
                    placeholder="Search"
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="w-[20%]"
                />

                <div className="w-[20%]">
                    <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                        Location
                    </p>
                    <Select
                        className="calendar-filters w-[100%]"
                        showSearch
                        allowClear
                        value={selectedLocation}
                        onChange={handleFacilityChange}
                        placeholder="All"
                        filterOption={(input, option) =>
                            String(option?.label ?? '')
                                .toLowerCase()
                                .includes(input.toLowerCase())
                        }
                        options={FacilityOptions}
                    />
                </div>
                {(hasClientWritePermission ||
                    store.role === RoleType.ORGANIZATION) && (
                    <>
                        <Button
                            className="rounded-lg  bg-purpleLight   px-8 py-2 text-white "
                            onClick={() => setaddSubProfileLeadModel(true)}
                        >
                            Add Sub-Client
                        </Button>

                        <Button
                            className="rounded-lg bg-purpleLight   px-8 py-2 text-white"
                            onClick={() => setIsClientModal(true)}
                        >
                            Add Individual +
                        </Button>
                    </>
                )}
            </div>

            <div className="mt-3">
                <CommonTable
                    className="min-w-min"
                    columns={columns}
                    dataSource={ClientLeadList || []}
                />
            </div>
            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={total}
                    pageSize={pageSizes}
                    showSizeChanger
                    pageSizeOptions={['10', '20', '50']}
                    onChange={paginate}
                />
            </div>

            {addSubProfileLeadModel && (
                <>
                    <AddSubprofileModal
                        pinRequirement={true}
                        open={addSubProfileLeadModel}
                        onCancel={() => setaddSubProfileLeadModel(false)}
                    />
                </>
            )}
            {addClientLeadModel && (
                <>
                    <AddClientLeadModal
                        open={addClientLeadModel}
                        onClose={() => {
                            setLeadId('');
                            setAddClientLeadModel(false);
                        }}
                        leadId={leadId}
                    />
                </>
            )}
            {isClientModal && (
                <>
                    <AddClientModal
                        open={isClientModal}
                        onClose={() => {
                            setIsClientModal(false);
                        }}
                    />
                </>
            )}
        </>
    );
};
export default WavierLeadListingModal;
