import {
    CloseCircleOutlined,
    EditOutlined,
    InfoCircleOutlined,
} from '@ant-design/icons';
import { Avatar, Button, Tooltip, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Link } from 'wouter';
import { formatDateString } from '~/components/common/function';
import ActivePackages from '~/components/gym/active-packages';
import { useAppSelector } from '~/hooks/redux-hooks';
import { ClientsDetails } from '~/redux/actions/customer-action';
import { ActivePurchasePricingPackagesByClient } from '~/redux/actions/pricing-actions';
const { Text, Title } = Typography;

interface ClientDetails {
    viewClientId: string;
    cancelViewClient: any;
    clientUpdatePermission: boolean;
}
const ClientPreview: React.FC<ClientDetails> = ({
    viewClientId,
    cancelViewClient,
    clientUpdatePermission,
}) => {
    const [clientDetails, setClientDetails] = useState<any>('');
    const [activePackages, setActivePackages] = useState<any>([]);
    const dispatch = useDispatch();
    const store = useAppSelector((state) => ({
        clientOnboarding: state.settings_store.clientOnboarding,
    }));
    useEffect(() => {
        if (viewClientId) {
            dispatch(ClientsDetails({ clientId: viewClientId })).then(
                (res: any) => {
                    // console.log('The view client response is::::::::::::', res);
                    setClientDetails(res?.payload?.data?.data);
                    dispatch(
                        ActivePurchasePricingPackagesByClient({
                            userId: res?.payload?.data?.data?.userId,
                            page: 1,
                            pageSize: 5,
                        })
                    ).then((res: any) => {
                        // console.log(
                        //     'aksjdhfkjasdhkfjhsdkj',
                        //     res?.payload?.data?.data
                        // );
                        setActivePackages(res?.payload?.data?.data);
                    });
                }
            );
        }
    }, [viewClientId]);

    return (
        <>
            <CloseCircleOutlined
                className="absolute right-16 ms-auto text-4xl"
                onClick={() => cancelViewClient()}
            />
            {/* <Image preview={false}/> */}
            <div className="text-center">
                <Avatar
                    className="mx-auto"
                    size={{
                        xs: 24,
                        sm: 32,
                        md: 40,
                        lg: 64,
                        xl: 80,
                        xxl: 100,
                    }}
                    // src="https://fitness-saas-images.s3.ap-south-1.amazonaws.com/gymBanners/af173c28-7a61-48b2-b29a-611dc4751a3b-mishru%202%202.png"
                    src={clientDetails?.photo}
                />
            </div>
            <div className="mt-8 flex items-center justify-center gap-4">
                <Title level={3} className="m-0 capitalize text-[#8143D1]">
                    {clientDetails?.firstName} {clientDetails?.lastName}
                </Title>
                {clientUpdatePermission && (
                    <Link
                        to={`/user-profile/${viewClientId}?userId=${clientDetails?.userId}&isEdit=true`}
                    >
                        <EditOutlined className="text-3xl font-medium" />
                    </Link>
                )}
            </div>
            <div className="mt-8 flex flex-wrap items-center justify-between">
                <div className="w-1/2 font-bold">DOB</div>
                <div className="w-1/2 text-[#72849A]">
                    {formatDateString(clientDetails?.dob) || '-'}
                </div>

                <div className="w-1/2 font-bold">Gender</div>
                <div className="w-1/2 capitalize text-[#72849A]">
                    {clientDetails?.gender || '-'}
                </div>

                <div className="w-1/2 font-bold">Membership Id</div>
                <div className="w-1/2 text-[#72849A]">
                    {clientDetails?.membershipId || '-'}
                </div>

                {/* <div className="w-1/2 font-bold">Waiver Status</div>
                <div className="w-1/2 text-[#72849A]">Signed</div> */}
                {store.clientOnboarding?.showProficiencyLevel && (
                    <>
                        <div className="w-1/2 font-bold">Proficiency Level</div>
                        <div className="w-1/2 capitalize text-[#72849A]">
                            {clientDetails?.activityLevel || '-'}
                        </div>
                    </>
                )}
                <div className="w-1/2 font-bold">Last Visit</div>
                <div className="w-1/2 capitalize text-[#72849A]">
                    {clientDetails?.lastVisited
                        ? dayjs(clientDetails.lastVisited).format('DD/MM/YYYY')
                        : '-'}
                </div>
                <div className="w-1/2 font-bold">
                    <Tooltip title="Total number of visits in the last 3 months">
                        No. of last visits <InfoCircleOutlined />
                    </Tooltip>
                </div>
                <div className="w-1/2 capitalize text-[#72849A]">
                    {clientDetails?.visitsLast3Months || '-'}
                </div>
            </div>
            {/* <div className="mt-8 flex gap-4 rounded-xl border border-[#FFF2BD] bg-[#FFFDF0] p-4">
                <div className="flex min-w-fit items-center gap-2">
                    <InfoCircleOutlined className="text-3xl text-[#FFC542]" />
                    <Title level={5} className="m-0">
                        Alert !
                    </Title>
                </div>

                <Text className="text-[#455560]">
                    Safety Briefing expired on 20-07-2025
                </Text>
                <Button className="fw-500 flex-initial rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white">
                    Info
                </Button>
            </div> */}
            {clientDetails?.missingRequiredPolicies?.map((item: any) => {
                return (
                    <div
                        key={item?._id}
                        className="mt-8 flex gap-4 rounded-xl border border-[#FFF2BD] bg-[#FFFDF0] p-4"
                    >
                        <div className="flex min-w-fit items-center gap-2">
                            <InfoCircleOutlined className="text-3xl text-red-500" />
                            {/* <Title level={5} className="m-0">
                                Alert !
                            </Title> */}
                        </div>

                        <Text className="text-[#455560]">{item?.message}</Text>
                    </div>
                );
            })}
            <hr className="my-10 border border-[#0000001A]" />
            {activePackages?.length > 0 && (
                <Title
                    level={4}
                    className="mx-auto w-fit border-b-2 border-[#8143D1] pb-1"
                >
                    Active Package
                </Title>
            )}

            {/* <div className="max-h-[30vh] overflow-y-scroll border border-black"> */}
            {activePackages.map((item: any) => {
                return (
                    <div
                        key={item._id}
                        className="mt-4 flex flex-wrap items-center justify-between rounded-xl bg-[#F2F2F2] p-4"
                    >
                        <div className="w-1/2 font-bold">Package Name :</div>
                        <div className="w-1/2 text-[#72849A]">
                            {item.packageName}
                        </div>
                        <div className="w-1/2 font-bold">Expiry Date :</div>
                        <div className="w-1/2 text-[#72849A]">
                            {formatDateString(item?.expiryDate) || '-'}
                        </div>
                        <div className="w-1/2 font-bold">Service Type :</div>
                        <div className="w-1/2 text-[#72849A]">
                            {item?.type === 'personalAppointment'
                                ? 'Appointment'
                                : item?.type === 'classes'
                                ? 'Classes'
                                : item?.type === 'bookings'
                                ? 'Booking'
                                : item?.type === 'courses'
                                ? 'Courses'
                                : '-'}
                        </div>
                        <div className="w-1/2 font-bold">
                            Remaining Sessions :
                        </div>
                        <div className="w-1/2 text-[#72849A]">
                            {item?.remainingSession}
                        </div>
                    </div>
                );
            })}
            {/* </div> */}
        </>
    );
};

export default ClientPreview;
