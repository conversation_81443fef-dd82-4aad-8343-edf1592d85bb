import {
    Button,
    ConfigProvider,
    Form,
    FormProps,
    Input,
    Modal,
    Select,
} from 'antd';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { PricingListingForSharePass } from '~/redux/actions/appointment-action';
import {
    CustomerList,
    sharePassToOther,
} from '~/redux/actions/customer-action';
import { useDebounce } from '~/hooks/useDebounce';
import { capitalizeFirstLetter } from '~/components/common/function';
import { useLoader } from '~/hooks/useLoader';
import { error } from 'console';

interface SharePassModalProps {
    visible: boolean;
    onClose: (val?: boolean) => void;
    clientId?: string;
    purchaseId?: string;
    clientName?: string;
    bundledPricingId?: string;
    packageId?: string;
    invoiceId?: string;
}

const SharePassToModal = (props: SharePassModalProps) => {
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const [searchText, setSearchText] = useState('');
    const [availableSessions, setAvailableSessions] = useState(0);
    const [pricingOption, setPricingOption] = useState([]);
    const debouncedRequest = useDebounce((callback) => callback(), 300);
    const [loader, startLoader, endLoader] = useLoader();

    const store = useAppSelector((state) => ({
        customerListForForms: state.customer_store.customerListForForms,
    }));

    const ClientOptions = store.customerListForForms
        ?.filter((item: any) => item.userId !== props?.clientId)
        ?.map((item: any) => ({
            value: item.userId,
            label: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
            id: item._id,
            ...item,
        }));
    const [filteredOptions, setFilteredOptions] = useState(ClientOptions);

    console.log(ClientOptions, "client options")
    useEffect(() => {
        dispatch(
            CustomerList({
                page: 1,
                pageSize: 30,
                isActive: true,
                notIncludedClientId: props?.clientId,
            })
        );
    }, [props?.clientId]);

    const handleShareToSelect = (userId: string) => {
        const selectedUser: any = store.customerListForForms?.find(
            (user: any) => user.userId === userId
        );
        if (selectedUser) {
            form.setFieldsValue({
                phone: selectedUser.mobile,
            });
        }

        dispatch(
            PricingListingForSharePass({
                userId,
                bundledPricingId: props.bundledPricingId,
                invoiceId: props.invoiceId,
            })
        )
            .unwrap()
            .then((res: any) => {
                console.log(res, "resssss")
                const pricingList = res.data?.data?.map((item: any) => ({
                    value: item._id,
                    label: `${item.packageName}`,
                    id: item._id,
                    packageId: item.packageId,
                    remainingSessions: item.remainingSessions,
                }));

                // if (props.packageId) {
                //     pricingList = pricingList.filter(
                //         (opt: any) => opt.packageId === props.packageId
                //     );
                // } else if (props.purchaseId) {
                //     pricingList = pricingList.filter(
                //         (opt: any) => opt._id === props.purchaseId
                //     );
                // }
                console.log(pricingList, "price list")
                setPricingOption(pricingList);

                if (pricingList.length > 0) {
                    const pkg = pricingList[0];
                    setAvailableSessions(pkg.remainingSessions || 0);
                    form.setFieldsValue({
                        package: pkg.value,
                        availableSessions: pkg.remainingSessions,
                    });
                }
            });
    };

    const handlePackageChange = (_: string, option: any) => {
        form.setFieldsValue({
            availableSessions: option?.remainingSessions,
            transferSessions: undefined,
        });
        setAvailableSessions(option?.remainingSessions || 0);
    };

    const validateTransferSessions = (_: any, value: any) => {
        if (value > availableSessions) {
            return Promise.reject(
                `You can't transfer more than ${availableSessions} sessions!`
            );
        }
        return Promise.resolve();
    };

    const onFinish: FormProps['onFinish'] = (values) => {
        startLoader();
        const payload = {
            shareTo: props?.clientId,
            shareFrom: values.shareTo,
            purchaseId: values.package,
            noOfSessions: Number(values.transferSessions),
        };
        dispatch(sharePassToOther({ payload }))
            .unwrap()
            .then((res: any) => {
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    form.resetFields();
                    props.onClose(true);
                }
            }).catch(() => { })
            .finally(endLoader);

    };

    const handleSearch = (input: string) => {
        if (!input) {
            // Reset to original ClientOptions
            setFilteredOptions(ClientOptions);
        } else {
            const lowerInput = input.toLowerCase();
            const filtered = ClientOptions.filter(option =>
                option.label.toLowerCase().includes(lowerInput) ||
                (option.mobile ?? '').toLowerCase().includes(lowerInput)
            );
            setFilteredOptions(filtered);
        }
    };

    return (
        <Modal
            title="Share User's Pass"
            open={props.visible}
            onCancel={() => props.onClose()}
            footer={null}
            centered
            className="custom-share-pass-modal"
        >
            <ConfigProvider
                theme={{
                    components: {
                        Form: {
                            verticalLabelMargin: -3,
                        },
                    },
                }}
            >
                <Form
                    layout="vertical"
                    form={form}
                    onFinish={onFinish}
                    initialValues={{
                        name: props.clientName,
                    }}
                >
                    {/* Name field */}


                    {/* Share From & Phone */}
                    <div className="grid grid-cols-2 gap-4">
                        <Form.Item
                            label="Share From"
                            name="shareTo"
                            rules={[{ required: true, message: 'Please select name' }]}
                        >
                            <Select
                                showSearch
                                allowClear
                                placeholder="Select name"
                                onSearch={handleSearch}
                                onChange={handleShareToSelect}
                                options={ClientOptions}
                                filterOption={(input, option) => {
                                    const label = (option?.label ?? '').toLowerCase();
                                    const mobile = (option?.mobile ?? '').toLowerCase(); // Assuming mobile is added in option
                                    return (
                                        label.includes(input.toLowerCase()) ||
                                        mobile.includes(input.toLowerCase())
                                    );
                                }}
                                className="h-12"
                            />

                        </Form.Item>

                        <Form.Item
                            label="Phone Number"
                            name="phone"
                            rules={[{ required: false }]}
                        >
                            <Input disabled placeholder="Phone number" className="h-12" />
                        </Form.Item>
                    </div>

                    {/* Package & Available Sessions */}
                    <div className="grid grid-cols-2 gap-4">
                        <Form.Item
                            label="Select Package"
                            name="package"
                            rules={[{ required: true, message: 'Please select package' }]}
                        >
                            <Select
                                placeholder="Select Package"
                                showSearch
                                options={pricingOption}
                                onChange={handlePackageChange}
                                filterOption={(input, option) =>
                                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                className="h-12"
                            />
                        </Form.Item>

                        <Form.Item
                            label="Available No. of Session(s)"
                            name="availableSessions"
                            rules={[{ required: true }]}
                        >
                            <Input disabled className="h-12" />
                        </Form.Item>
                    </div>

                    {/* Sessions to Transfer */}
                    <Form.Item
                        label="No. of Sessions to Transfer"
                        name="transferSessions"
                        rules={[
                            { required: true, message: 'Please enter number of sessions' },
                            { validator: validateTransferSessions },
                        ]}
                    >
                        <Select
                            placeholder="Select No. of Sessions"
                            options={Array.from(
                                { length: availableSessions },
                                (_, i) => ({
                                    value: i + 1,
                                    label: (i + 1).toString(),
                                })
                            )}
                        />
                    </Form.Item>
                    <Form.Item
                        label="Share To"
                        name="name"
                        rules={[{ required: false, message: 'Please enter name!' }]}
                    >
                        <Input disabled placeholder="Enter Name" className="h-12" />
                    </Form.Item>
                    {/* <label className='text-[#6B7280]'>
                        Share To: {props.clientName || ''}
                    </label> */}
                    {/* Submit Button */}
                    <div className="flex justify-center pt-4">
                        <Form.Item>
                            <Button
                                htmlType="submit"
                                className="bg-[#B296F5] text-white px-10 h-12 rounded-md"
                                loading={loader}
                            >
                                Submit
                            </Button>
                        </Form.Item>
                    </div>
                </Form>
            </ConfigProvider>
        </Modal>
    );
};

export default SharePassToModal;
