import { Modal, Table, Button, ConfigProvider, Checkbox } from 'antd';
import { useEffect, useState } from 'react';
import {
    customerLeadDetail,
    customerMinorList,
} from '~/redux/actions/customerLead-action';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';

const ImportChildFromWavierModal = ({
    open,
    onCancel,
    clientSourceId,
    onProceed,
    selectedMinors = [],
}: any) => {
    const dispatch = useAppDispatch();
    const [selectedId, setSelectedId] = useState<string | null>(null);
    const [loader, startLoader, endLoader] = useLoader();
    const [minorData, setMinorData] = useState<any[]>([]);
console.log(selectedMinors,"sakhfkashkashkfhaskfhksahf")
   useEffect(() => {
  if (clientSourceId) {
    startLoader();

    // Build a fast lookup set of IDs to exclude
    const excludeIds = new Set(
      (selectedMinors || []).map((m: any) => String(m.minorId ?? m._id))
    );

    dispatch(customerMinorList(clientSourceId))
      .then((res: any) => {
        const apiMinors: any[] = res?.payload?.data?.minors || [];
        const remaining = apiMinors.filter((item: any) => !excludeIds.has(String(item._id)));
        const subClientList = remaining.map((item: any) => ({
          key: item._id,
          _id: item._id,
          firstName: item.firstName,
          lastName: item.lastName,
          dob: item.dob ? new Date(item.dob).toLocaleDateString() : 'NA',
          gender: item.gender,
          dateRequested: res?.payload?.data?.createdAt
            ? new Date(res.payload.data.createdAt).toLocaleDateString()
            : 'NA',
          raw: item,
        }));
        setMinorData(subClientList);
        setSelectedId(subClientList[0]?.key ?? null);
      })
      .catch(console.error)
      .finally(() => endLoader());
  }
}, [clientSourceId, selectedMinors]);


    useEffect(() => {
        if (!open) {
            setSelectedId(null);
        }
    }, [open]);

    const columns = [
        {
            title: <span className="font-semibold">Name</span>,
            dataIndex: '',
            render: (_: any, record: any) => (
                <ConfigProvider
                    theme={{
                        token: {
                            colorPrimary: '#8143D1',
                        },
                    }}
                >
                    <Checkbox
                        checked={selectedId === record.key}
                        onChange={() => setSelectedId(record.key)}
                    >
                        <span className="text-gray-800">
                            {record.firstName} {record.lastName}
                        </span>
                    </Checkbox>
                </ConfigProvider>
            ),
        },
        {
            title: 'GENDER',
            dataIndex: 'gender',
        },
        {
            title: 'D.O.B.',
            dataIndex: 'dob',
        },
        {
            title: 'DATE REQUESTED',
            dataIndex: 'dateRequested',
        },
    ];

    const handleProceed = () => {
        const selectedData = minorData.find((row) => row.key === selectedId);
        if (selectedData) {
            onProceed?.([selectedData.raw]);
        }
    };

    return (
        <Modal
            open={open}
            onCancel={onCancel}
            footer={null}
            centered
            closable={false}
            className="rounded-xl"
            width={700}
        >
            <h2 className="mb-4 text-2xl font-semibold text-[#1a3353]">
                Import Client From Waiver
            </h2>
            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            colorPrimary: '#8143D1',
                        },
                    },
                }}
            >
                <Table
                    columns={columns}
                    dataSource={minorData}
                    pagination={false}
                    className="rounded-xl border"
                    rowClassName={(record) =>
                        selectedId === record.key
                            ? '!bg-white !hover:bg-white'
                            : 'hover:bg-gray-50'
                    }
                />
            </ConfigProvider>
            <div className="mt-6 flex justify-end gap-4">
                <Button
                    onClick={onCancel}
                    className="border-[#1A3353] !text-[#1A3353] hover:!text-[#1A3353]"
                >
                    Cancel
                </Button>
                <Button
                    type="primary"
                    className="bg-purpleLight text-white"
                    loading={loader}
                    onClick={handleProceed}
                    disabled={!selectedId}
                >
                    Proceed
                </Button>
            </div>
        </Modal>
    );
};

export default ImportChildFromWavierModal;
