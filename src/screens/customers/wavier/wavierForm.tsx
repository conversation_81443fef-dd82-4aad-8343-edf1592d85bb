import React, { useEffect, useState } from 'react';
import { customerLeadDetail } from '~/redux/actions/customerLead-action';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useParams } from 'wouter';
import Title from 'antd/es/typography/Title';

const formatCamelCase = (str: string) => {
    return str
        .replace(/_/g, ' ')
        .replace(/([a-z])([A-Z])/g, '$1 $2')
        .replace(/\b\w/g, (char) => char.toUpperCase());
};
const Detail = ({ label, value }: { label: string; value: any }) => (
    <div className="flex flex-col">
        <span className=" text-xl font-semibold text-[#1A3353]">{label}</span>
        <span className=" text-[#455560] ">{value || '—'}</span>
    </div>
);
function goBack() {
    window.history.back();
}
const WavierForm = ({ sourceId = '' }: any) => {
    const params = useParams();
    const dispatch = useAppDispatch();
    const [clientDetails, setClientDetails] = useState<any>(null);

    const id = sourceId ? sourceId : params.id;
    useEffect(() => {
        if (id) {
            dispatch(customerLeadDetail(id)).then((res: any) => {
                setClientDetails(res?.payload?.data);
            });
        }
    }, [id]);
    if (!clientDetails) {
        return (
            <div className="flex h-screen items-center justify-center text-xl text-gray-500">
                No data available
            </div>
        );
    }

    const {
        firstName,
        lastName,
        email,
        phone,
        mobileDetails,
        address,
        minors = [],
        rawZohoData = {},
        signature,
    } = clientDetails;

    const displayedFields = new Set([
        'client_firstName',
        'client_LastName',
        'client_email',
        'client_phone',
        'client_address_street',
        'client_address_addressLine1',
        'client_address_city',
        'client_address_state',
        'client_address_country',
    ]);

    const remainingZohoFields = Object.entries(rawZohoData).filter(
        ([key]) => !displayedFields.has(key)
    );

    return (
        <>
            <div className="flex items-center gap-4 ">
                {!sourceId && (
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                )}{' '}
                <Title className="text-[#1a3353]" level={4}>
                    Waiver Form
                </Title>
            </div>
            <div className="space-y-8  rounded-lg bg-white p-6 shadow-md">
                <div>
                    <h2 className="mb-3 text-2xl font-bold text-[#1A3353]">
                        Client
                    </h2>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <Detail label="First Name" value={firstName} />
                        <Detail label="Last Name" value={lastName} />
                        <Detail label="Email" value={email} />
                        <Detail
                            label="Phone"
                            value={phone || mobileDetails?.number}
                        />
                        <Detail label="Street" value={address?.street} />
                        <Detail
                            label="Address Line 1"
                            value={address?.addressLine1}
                        />
                        <Detail label="City" value={address?.city} />
                        <Detail label="State" value={address?.state} />
                        <Detail label="Country" value={address?.country} />
                    </div>
                </div>

                {minors.length > 0 && (
                    <div>
                        <h2 className="mb-3 text-xl font-bold text-[#1A3353]">
                            Sub-Clients (Minors)
                        </h2>
                        <div className="space-y-4">
                            {minors.map((minor: any, index: number) => (
                                <div
                                    key={index}
                                    className="grid grid-cols-1 gap-6 rounded-lg border bg-gray-50 p-4 md:grid-cols-2"
                                >
                                    {Object.entries(minor).map(
                                        ([key, value]) => (
                                            <Detail
                                                key={key}
                                                label={formatCamelCase(key)}
                                                value={value}
                                            />
                                        )
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {remainingZohoFields.length > 0 && (
                    <div>
                        <h2 className="mb-3 text-xl font-bold text-[#1A3353]">
                            Additional Form Fields
                        </h2>
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            {remainingZohoFields.map(([key, value]) => (
                                <Detail
                                    key={key}
                                    label={formatCamelCase(key)}
                                    value={value}
                                />
                            ))}
                        </div>
                    </div>
                )}

                {signature && (
                    <div className="pt-6">
                        <h2 className="mb-3 text-xl font-bold text-[#1A3353] ">
                            Signature
                        </h2>
                        <img
                            src={signature}
                            alt="Client Signature"
                            className="max-w-xs rounded border shadow-md "
                        />
                    </div>
                )}
            </div>
        </>
    );
};

export default WavierForm;
