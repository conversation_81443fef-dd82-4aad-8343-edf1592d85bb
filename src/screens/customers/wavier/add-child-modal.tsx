import React, { useEffect, useRef, useState } from 'react';
import {
    Modal,
    Input,
    Select,
    DatePicker,
    Form,
    Button,
    ConfigProvider,
    Checkbox,
    Upload,
    Row,
    Col,
} from 'antd';
import { CustomerList } from '~/redux/actions/customer-action';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import ImportChildFromWavierModal from './import-child.modal';
import dayjs from 'dayjs';
import { addClientMinor } from '~/redux/actions/customerLead-action';
import UploadPhotoModal from './upload-photo-modal';
import {
    InfoCircleOutlined,
    LoadingOutlined,
    PlusOutlined,
} from '@ant-design/icons';
import {
    formatDateString,
    formatStringWithSpaces,
} from '~/components/common/function';
import { UploadImage } from '~/redux/actions/common-action';
import Webcam from 'react-webcam';
import { useSelector } from 'react-redux';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import ModulePinConfirmationModal from '~/components/modals/module-pin-confirmation-modal';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
const { TextArea } = Input;

interface AddSubprofileModalProps {
    open: boolean;
    onCancel: () => void;
    onSaveSuccess?: () => void;
    clientOnboarding?: any;
    pinRequirement: boolean;
    // setPosSelectClient?: any;
    selectedParentClient?: {
        value: string;
        label: string;
        userId: string;
        data: any;
        formData: any;
    };
}

const AddSubprofileModal: React.FC<AddSubprofileModalProps> = ({
    open,
    onCancel,
    onSaveSuccess,
    selectedParentClient,
    // setPosSelectClient,
    clientOnboarding,
    pinRequirement,
}) => {
    const dispatch = useAppDispatch();
    const [form] = Form.useForm();
    console.log('check values selectedParentClient', selectedParentClient);

    const [parentImageUrl, setParentImageUrl] = useState<any>(
        '/assets/Profile_icon.png'
    );
    const [minorImageUrl, setMinorImageUrl] = useState<any>(
        '/assets/Profile_icon.png'
    );

    const [selectedClient, setSelectClient] = useState<any>('');
    const [selectedClientUserId, setSelectClientUserId] = useState<any>('');
    const [selectedClientData, setSelectedClientData] = useState<any>(null);
    const [importFromWavierButton, setImportFromWavierButton] =
        useState<boolean>(false);
    const [importWavierModal, setImportWavierModal] = useState<boolean>(false);
    const [clientSourceId, setClientSourceId] = useState<string>('');
    const [selectedMinor, setSelectedMinor] = useState<any>([]);
    const [uploadedPhotos, setUploadedPhotos] = useState<boolean>(false);
    const [selectedFormMinorFromWaiver, setSelectedFormMinorFromWaiver] =
        useState<any>(null);
    const webcamRef = useRef(null);
    const [useWebcam, setUseWebcam] = useState(false);
    const [loading, setLoading] = useState(false);
    const [photoError, setPhotoError] = useState<string | null>(null);

    const [selectedMinorIndexForPhoto, setSelectedMinorIndexForPhoto] =
        useState<number | null>(null);

    const [policyDates, setPolicyDates] = useState<Record<string, any>>({});
    const [manuallyAddedMinors, setManuallyAddedMinors] = useState<any[]>([]);

    const store = useAppSelector((state) => ({
        clientDetails: state.customer_store.customerDetails,
        clientOnboarding: state.settings_store.clientOnboarding,
    }));

    const { policies, showPolicies, photoPolicy } = clientOnboarding
        ? clientOnboarding
        : store.clientOnboarding;
    useEffect(() => {
        if (
            minorImageUrl &&
            minorImageUrl !== '/assets/Profile_icon.png' &&
            photoError
        ) {
            setPhotoError(null);
        }
    }, [minorImageUrl, photoError]);
    const isPhotoRequired = Boolean(photoPolicy);
    const isMissingPhoto = (url?: string) =>
        !url || url === '/assets/Profile_icon.png';

    const [selectedPolicies, setSelectedPolicies] = useState<string[]>([]);
    const [requirePinModal, setRequirePinModal] = useState<boolean>(false);
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(true);
    const [openAfterPin, setOpenAfterPin] = useState<boolean>(false);
    const isDefaultImage = (url: string) =>
        !url || url === '/assets/Profile_icon.png';
    const getMinorId = (m: any) => m?.minorId ?? m?._id ?? undefined;

    useEffect(() => {
        if (selectedParentClient) {
            selectedParentClient.data['email'] = selectedParentClient?.formData
                ?.email
                ? selectedParentClient?.formData?.email
                : null;
            selectedParentClient.data['countryCode'] =
                selectedParentClient?.formData?.countryCode &&
                selectedParentClient?.formData?.mobile
                    ? `${selectedParentClient?.formData.countryCode} `
                    : undefined;
            selectedParentClient.data['mobile'] =
                selectedParentClient?.formData?.countryCode &&
                selectedParentClient?.formData?.mobile
                    ? `${selectedParentClient?.formData?.mobile}`
                    : undefined;
            setSelectClient(selectedParentClient?.value);
            setSelectedClientData(selectedParentClient?.data);
        }
    }, [selectedParentClient]);

    useEffect(() => {
        if (selectedClientData?.photo) {
            setParentImageUrl(selectedClientData.photo);
        } else {
            setParentImageUrl('/assets/Profile_icon.png');
        }

        if (selectedClientData?.sourceId) {
            setImportFromWavierButton(true);
            setClientSourceId(selectedClientData?.sourceId);
        } else {
            setImportFromWavierButton(false);
            setClientSourceId('');
        }
    }, [selectedClientData]);

    useEffect(() => {
        if (!open) return;
        // No defaults selected
        form.setFieldsValue({
            subProfiles: [{}],
            policies: [],
        });

        setSelectedPolicies([]);
        setPolicyDates({});
    }, [open, form]);

    const fetchCustomerOptions = async (searchText = '', page = 1) => {
        const response = await dispatch(
            CustomerList({
                page,
                pageSize: 10,
                search: searchText,
                isActive: true,
                isParent: true,
            })
        ).unwrap();

        return response?.data?.data?.list?.map((customer: any) => ({
            value: customer._id,
            label: `${customer.firstName} ${customer.lastName}`,
            userId: customer.userId,
            data: customer,
        }));
    };

    const handleSave = async () => {
        try {
            console.log(manuallyAddedMinors, 'ashdkashdkashkdhsakdha');
            const minorsToSubmit: any[] = [];

            // ✅ 1. Manually added minors — already have full `policies` array
            if (manuallyAddedMinors.length > 0) {
                manuallyAddedMinors.forEach((minor: any) => {
                    const cleanedMinor = {
                        firstName: minor.firstName,
                        lastName: minor.lastName || '',
                        dob: dayjs(minor.dob, [
                            'DD-MMM-YYYY',
                            'YYYY-MM-DD',
                            'DD/MM/YYYY',
                        ]).toISOString(),
                        gender: minor.gender?.toLowerCase(),
                        relation: minor.relation || 'child',
                        minorId: getMinorId(minor),
                        photo: minor.photo || '',
                        policies: minor.policies || [], // ✅ Use policy array as is
                    };
                    minorsToSubmit.push(cleanedMinor);
                });
            }

            // ✅ 2. Handle form input if user didn’t click "Add Another"
            const formValues = form.getFieldsValue();
            const hasFormData = Object.values(formValues).some(
                (val) => val !== undefined && val !== null && val !== ''
            );

            if (hasFormData) {
                if (isPhotoRequired && isMissingPhoto(minorImageUrl)) {
                    setPhotoError('Photo is required ');
                    return;
                }
                const validatedValues = await form.validateFields();

                // 🔄 Convert selected policies from selectedPolicies + policyDates ➜ into full array
                const policiesFormatted = (selectedPolicies || []).map(
                    (policyName: string) => {
                        const policyDef = policies?.items?.find(
                            (item: any) => item.name === policyName
                        );
                        return {
                            policyType: formatStringWithSpaces(policyName), // or just policyName if already clean
                            isEnabled: true,
                            required: policyDef?.required || false,
                            policyId: policyDef?._id || null,
                            date: policyDates[policyName] || null,
                        };
                    }
                );

                const formMinor = {
                    firstName: validatedValues.firstName,
                    lastName: validatedValues.lastName || '',
                    dob: validatedValues.dob?.toISOString(),
                    gender: validatedValues.gender?.toLowerCase(),
                    relation: validatedValues.relation,
                    policies: policiesFormatted, // ✅ Correctly formatted policy array
                    minorId: selectedFormMinorFromWaiver?._id || undefined,
                    photo: minorImageUrl || '',
                };

                minorsToSubmit.push(formMinor);
            }

            // ❌ Nothing to submit? Exit
            if (minorsToSubmit.length === 0) {
                console.warn('No minors to submit.');
                return;
            }
            if (isPhotoRequired) {
                const missingPhotoIndex = minorsToSubmit.findIndex((m) =>
                    isMissingPhoto(m.photo)
                );
                if (missingPhotoIndex !== -1) {
                    setPhotoError('Photo is required.');

                    return;
                }
            }

            // ✅ 3. Submit all minors
            await dispatch(
                addClientMinor({
                    clientId: selectedClientData?._id,
                    minor: minorsToSubmit,
                    wavierSourceId: clientSourceId || undefined,
                })
            ).then((res: any) => {
                console.log('Minor details are:', res);
            });

            setSelectedFormMinorFromWaiver(null);
            setPhotoError(null);
            setMinorImageUrl('/assets/Profile_icon.png');
            form.resetFields();
            onCancel();
            if (onSaveSuccess) {
                onSaveSuccess(); // ✅ Callback
            }
        } catch (error: any) {
            if (error?.errorFields && error.errorFields.length > 0) {
                form.scrollToField(error.errorFields[0].name[0]);
            }
            console.warn('Form validation failed or nothing to submit', error);
        }
    };

    const handlePolicyChange = (checkedValues: string[]) => {
        setSelectedPolicies(checkedValues);
        form.setFieldsValue({ policies: checkedValues });
        // Add or remove dates based on selection
        const updatedDates: Record<string, any> = {};
        checkedValues.forEach((policy) => {
            updatedDates[policy] = policyDates[policy] || null;
        });

        setPolicyDates(updatedDates);
    };

    const handlePolicyDateChange = (policy: string, date: any) => {
        setPolicyDates((prev) => ({
            ...prev,
            [policy]: date,
        }));
    };

    // useEffect(() => {
    //     if (open) {
    //         const defaultCheckedPolicies = policies?.items?.map((p: any) => p.name) || [];
    //         form.setFieldsValue({
    //             policies: defaultCheckedPolicies,
    //         });
    //     }
    // }, [open]);

    const handleImageUpload = (file: any) => {
        setLoading(true);
        dispatch(UploadImage({ file: file.file }))
            .then((res: any) => {
                setMinorImageUrl(res?.payload?.res?.data?.data);
            })
            .finally(() => setLoading(false));
    };
    const requiredPolicyAlerts = (policies?.items || []).filter(
        (item: any) => item.isShown && !selectedPolicies.includes(item.name)
    );
    const { role } = useSelector((state: any) => state.auth_store);
    useEffect(() => {
        if (!open || role === RoleType.ORGANIZATION || !pinRequirement) return;

        dispatch(
            GetSettingActiveStatus({
                settingKey: 'settings_pin',
            })
        ).then((response: any) => {
            const settingData = response?.payload?.data?.data;
            const isEnabled = settingData?.isEnabled;
            const isActive = settingData?.isActive;

            if (isEnabled && isActive) {
                setRequirePinModal(true);
                setPinModalVisible(true);
            } else {
                setRequirePinModal(false);
                setPinModalVisible(false);
            }
        });
    }, [open, role, dispatch, pinRequirement]);
    if (requirePinModal && pinModalVisible && role !== RoleType.ORGANIZATION) {
        return (
            <ModulePinConfirmationModal
                visible={true}
                onConfirm={() => {
                    setPinModalVisible(false);
                    setOpenAfterPin(true);
                }}
                onCancel={onCancel}
                module={SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING}
                subModule={PERMISSIONS_ENUM.CLIENTS_WRITE}
            />
        );
    }
    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Form: {
                            verticalLabelMargin: -5,
                        },
                    },
                }}
            >
                <Modal
                    className=" w-[70%] "
                    title={null}
                    // open={open}
                    open={!requirePinModal ? true : openAfterPin}
                    onCancel={onCancel}
                    footer={null}
                    centered
                    width={1000}
                    bodyStyle={{ padding: 0, borderRadius: 10 }}
                >
                    <div className="w-full    text-center  text-3xl font-semibold text-[#1A3353]">
                        Add Subprofile
                    </div>

                    <div className="flex flex-col gap-4 bg-white px-6 pb-10 pt-6 md:flex-row">
                        <div className="w-[40%] border-r border-gray-200  pr-7">
                            <div className="mb-4 flex justify-center">
                                <img
                                    src={parentImageUrl}
                                    className="w-h-32 h-32 rounded-full border object-cover"
                                    alt="profile"
                                />
                            </div>

                            <Form
                                variant="borderless"
                                className="space-y-8"
                                layout="horizontal"
                            >
                                <Form.Item
                                    label={
                                        <p className="text-left">
                                            Parent <br /> Client
                                        </p>
                                    }
                                >
                                    <InfiniteScrollSelect
                                        fetchOptions={fetchCustomerOptions}
                                        value={selectedClient}
                                        onChange={(value, option) => {
                                            setSelectClient(value);
                                            setSelectClientUserId(
                                                option.userId
                                            );
                                            setSelectedClientData(option.data);
                                            setSelectedMinor([]);
                                        }}
                                        placeholder="Select Parent Client"
                                        disabled={false}
                                        className="w-full border-b-1 border-[#d1d5db]"
                                    />
                                </Form.Item>
                                {/* <Form.Item label="Client ID">
                                    <Input
                                        // className="border-b-1"
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        disabled
                                        value={
                                            selectedClientData?.clientId || ''
                                        }
                                    />
                                </Form.Item> */}
                                <Form.Item label="Phone">
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                        readOnly
                                        value={
                                            selectedClientData?.countryCode &&
                                            selectedClientData?.mobile
                                                ? `(${selectedClientData?.countryCode}) ${selectedClientData?.mobile}`
                                                : ''
                                        }
                                    />
                                </Form.Item>
                                <Form.Item label="Email">
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                        readOnly
                                        value={selectedClientData?.email || ''}
                                    />
                                </Form.Item>
                                <Form.Item label="Date of Birth">
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                        readOnly
                                        value={
                                            formatDateString(
                                                selectedClientData?.dob
                                            ) || ''
                                        }
                                    />
                                </Form.Item>
                                <Form.Item label="Location">
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                        readOnly
                                        value={
                                            selectedClientData?.facilityName ||
                                            ''
                                        }
                                    />
                                </Form.Item>
                            </Form>
                            {showPolicies && (
                                <div className="mt-12 w-full rounded-md border border-gray-200 px-6 py-6">
                                    <p className="text-xl font-semibold text-[#1A3353]">
                                        Policies
                                    </p>

                                    <div className="flex flex-col gap-4">
                                        {policies?.items?.map(
                                            (policyItem: any) =>
                                                policyItem?.isShown ? (
                                                    <div
                                                        key={policyItem._id}
                                                        className="flex items-center justify-between gap-4"
                                                    >
                                                        {/* Checkbox and label */}
                                                        <div className="flex items-center gap-2">
                                                            <Checkbox
                                                                value={
                                                                    policyItem.name
                                                                }
                                                                checked={selectedPolicies.includes(
                                                                    policyItem.name
                                                                )}
                                                                onChange={(
                                                                    e
                                                                ) => {
                                                                    const {
                                                                        checked,
                                                                        value,
                                                                    } =
                                                                        e.target;
                                                                    const updated =
                                                                        checked
                                                                            ? [
                                                                                  ...selectedPolicies,
                                                                                  value,
                                                                              ]
                                                                            : selectedPolicies.filter(
                                                                                  (
                                                                                      v
                                                                                  ) =>
                                                                                      v !==
                                                                                      value
                                                                              );
                                                                    handlePolicyChange(
                                                                        updated
                                                                    );
                                                                }}
                                                            />
                                                            <span className="text-2xl text-[#455560]">
                                                                {formatStringWithSpaces(
                                                                    policyItem.name
                                                                )}
                                                                {policyItem.required && (
                                                                    <span className="ml-1 text-red-500">
                                                                        {' '}
                                                                        *
                                                                    </span>
                                                                )}
                                                            </span>
                                                        </div>

                                                        {/* Date Picker */}
                                                        <DatePicker
                                                            className="w-[120px]"
                                                            placeholder="Select Date"
                                                            value={dayjs()}
                                                            onChange={(date) =>
                                                                handlePolicyDateChange(
                                                                    policyItem.name,
                                                                    date
                                                                )
                                                            }
                                                        />
                                                    </div>
                                                ) : null
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                        <div className="w-[60%] pl-6">
                            {manuallyAddedMinors.length > 0 && (
                                <div className="mt-4">
                                    <p className="mb-2 font-semibold  text-[#1A3353]">
                                        Added Client
                                    </p>
                                    <table className="w-full rounded-lg border border-gray-200 text-left ">
                                        <thead className="bg-gray-100 font-semibold text-[#1A3353]">
                                            <tr>
                                                <th className="p-2">NAME</th>
                                                <th className="p-2">GENDER</th>
                                                <th className="p-2">D.O.B.</th>
                                                <th className="p-2">
                                                    RELATION
                                                </th>
                                                <th className="p-2">Photo</th>
                                                <th className="p-2">ACTION</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {manuallyAddedMinors.map(
                                                (minor: any, index: number) => (
                                                    <tr
                                                        key={minor._id}
                                                        className="border-t"
                                                    >
                                                        <td className="p-2 capitalize">
                                                            {minor.firstName}{' '}
                                                            {minor.lastName}
                                                        </td>
                                                        <td className="p-2 capitalize">
                                                            {minor.gender}
                                                        </td>
                                                        <td className="p-2">
                                                            {new Date(
                                                                minor.dob
                                                            ).toLocaleDateString(
                                                                'en-GB',
                                                                {
                                                                    day: '2-digit',
                                                                    month: 'short',
                                                                    year: 'numeric',
                                                                }
                                                            )}
                                                        </td>
                                                        <td className="p-2 capitalize">
                                                            {minor.relation ||
                                                                'Child'}
                                                        </td>
                                                        <td className="p-2">
                                                            <img
                                                                src={
                                                                    minor.photo ||
                                                                    '/assets/Profile_icon.png'
                                                                }
                                                                className="mx-auto h-12 w-12 rounded-full border object-cover"
                                                                alt="profile"
                                                            />
                                                        </td>

                                                        <td className="p-2">
                                                            {/* <button
                                                        type="button"
                                                        onClick={() => {
                                                            form.setFieldsValue({
                                                                ...minor,
                                                                dob: minor.dob ? dayjs(minor.dob) : null,
                                                            });
                                                            setSelectedMinor((prev: any) => prev.filter((_: any, i: any) => i !== index));
                                                        }}
                                                    >
                                                        <img
                                                            src="/icons/common/edit.svg"
                                                            alt="Edit"
                                                            className="w-6 h-6 hover:opacity-80"
                                                        />
                                                    </button> */}

                                                            <button
                                                                type="button"
                                                                onClick={() =>
                                                                    setManuallyAddedMinors(
                                                                        (
                                                                            prev: any[]
                                                                        ) =>
                                                                            prev.filter(
                                                                                (
                                                                                    _,
                                                                                    i
                                                                                ) =>
                                                                                    i !==
                                                                                    index
                                                                            )
                                                                    )
                                                                }
                                                            >
                                                                <img
                                                                    src="/icons/common/delete.svg"
                                                                    alt="Delete"
                                                                    className="h-5 w-5 text-red-500 hover:text-red-700"
                                                                />
                                                            </button>
                                                        </td>
                                                    </tr>
                                                )
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            )}

                            {importFromWavierButton && (
                                <div className="mb-7 mt-4">
                                    {selectedMinor.length > 0 && (
                                        <p className=" mb-1 font-medium text-gray-700">
                                            Add Another
                                        </p>
                                    )}
                                    <button
                                        className="rounded-md border border-purpleLight  px-4 py-1 font-normal  text-purpleLight"
                                        onClick={() =>
                                            setImportWavierModal(true)
                                        }
                                    >
                                        Import from Waiver
                                    </button>
                                </div>
                            )}
                            {/* Form Section */}
                            <Form
                                layout="horizontal"
                                name="subProfiles"
                                form={form}
                                className="space-y-8"
                                variant="borderless"
                            >
                                <Form.Item
                                    label="First Name"
                                    name="firstName"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'First name is required',
                                        },
                                    ]}
                                >
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        placeholder="Enter First Name"
                                        className="rounded-md"
                                        disabled={!selectedClient}
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Last Name"
                                    name="lastName"
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Last name is required',
                                        },
                                    ]}
                                >
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        placeholder="Enter Last Name"
                                        className="rounded-md"
                                        disabled={!selectedClient}
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Relation to Parent"
                                    name="relation"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Relation is required',
                                        },
                                    ]}
                                >
                                    <Select
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        placeholder="Child / Spouse / Partner"
                                        options={[
                                            { label: 'Child', value: 'child' },
                                            {
                                                label: 'Spouse',
                                                value: 'spouse',
                                            },
                                            {
                                                label: 'Partner',
                                                value: 'partner',
                                            },
                                        ]}
                                        disabled={!selectedClient}
                                        className="rounded-md"
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Gender"
                                    name="gender"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Gender is required',
                                        },
                                    ]}
                                >
                                    <Select
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        placeholder="Select Gender"
                                        options={[
                                            { label: 'Male', value: 'male' },
                                            {
                                                label: 'Female',
                                                value: 'female',
                                            },
                                            { label: 'Other', value: 'other' },
                                        ]}
                                        disabled={!selectedClient}
                                        className="rounded-md"
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Date of Birth"
                                    name="dob"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Date of Birth is required',
                                        },
                                    ]}
                                >
                                    <DatePicker
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        placeholder="Select DOB"
                                        disabled={!selectedClient}
                                        className="w-full rounded-md"
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Notes"
                                    name="notes"
                                    className="md:col-span-2"
                                >
                                    <TextArea
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        rows={2}
                                        placeholder="Notes..."
                                        disabled={!selectedClient}
                                        className="rounded-md"
                                    />
                                </Form.Item>

                                <div
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'stretch',
                                        gap: '16px',
                                        width: '100%',
                                        flexWrap: 'nowrap', // keep in one row
                                    }}
                                >
                                    {/* ====== PHOTO (40%) ====== */}
                                    <div style={{ flex: '0 0 40%' }}>
                                        <div
                                            style={{
                                                border: '1px solid #E7EAF3',
                                                borderRadius: '12px',
                                                background: '#fff',
                                                display: 'flex',
                                                flexDirection: 'column',
                                                boxShadow:
                                                    '0 1px 2px rgba(16,24,40,.06)',
                                                minHeight: '220px', // ⬅️ reduced
                                                width: '100%',
                                            }}
                                        >
                                            {/* Header */}
                                            <div
                                                style={{
                                                    borderBottom:
                                                        '1px solid #E7EAF3',
                                                    padding: '8px 12px', // ⬅️ reduced padding
                                                    display: 'flex',
                                                    justifyContent:
                                                        'space-between',
                                                    alignItems: 'center',
                                                }}
                                            >
                                                <p
                                                    style={{
                                                        margin: 0,
                                                        fontSize: '15px',
                                                        fontWeight: 600,
                                                        color: '#6D28D9',
                                                    }}
                                                >
                                                    Photo
                                                    {isPhotoRequired ? (
                                                        <span
                                                            style={{
                                                                color: '#DC2626',
                                                            }}
                                                        >
                                                            {' '}
                                                            *
                                                        </span>
                                                    ) : null}
                                                </p>
                                            </div>

                                            {/* Body */}
                                            <div
                                                style={{
                                                    padding: '12px',
                                                    flex: 1,
                                                }}
                                            >
                                                {useWebcam ? (
                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            flexDirection:
                                                                'column',
                                                            gap: '8px',
                                                            alignItems:
                                                                'center',
                                                        }}
                                                    >
                                                        <Webcam
                                                            audio={false}
                                                            ref={webcamRef}
                                                            screenshotFormat="image/jpeg"
                                                            width={260} // ⬅️ smaller
                                                            videoConstraints={{
                                                                width: 260,
                                                                height: 180,
                                                                facingMode:
                                                                    'user',
                                                            }}
                                                            style={{
                                                                borderRadius:
                                                                    '6px',
                                                                border: '1px solid #E5E7EB',
                                                            }}
                                                        />
                                                        <div
                                                            style={{
                                                                display: 'flex',
                                                                gap: '12px',
                                                            }}
                                                        >
                                                            <Button
                                                                onClick={async () => {
                                                                    const imageSrc =
                                                                        webcamRef.current.getScreenshot();
                                                                    setMinorImageUrl(
                                                                        imageSrc
                                                                    );
                                                                    setUseWebcam(
                                                                        false
                                                                    );
                                                                    if (
                                                                        imageSrc
                                                                    ) {
                                                                        const blob =
                                                                            await (
                                                                                await fetch(
                                                                                    imageSrc
                                                                                )
                                                                            ).blob();
                                                                        const file =
                                                                            new File(
                                                                                [
                                                                                    blob,
                                                                                ],
                                                                                'webcam-image.jpg',
                                                                                {
                                                                                    type: 'image/jpeg',
                                                                                }
                                                                            );
                                                                        handleImageUpload(
                                                                            {
                                                                                file,
                                                                            }
                                                                        );
                                                                        setPhotoError(
                                                                            null
                                                                        );
                                                                    }
                                                                }}
                                                                style={{
                                                                    background:
                                                                        '#8143D1',
                                                                    color: '#fff',
                                                                    borderRadius:
                                                                        '6px',
                                                                    border: 'none',
                                                                }}
                                                            >
                                                                Capture
                                                            </Button>
                                                            <Button
                                                                type="text"
                                                                onClick={() =>
                                                                    setUseWebcam(
                                                                        false
                                                                    )
                                                                }
                                                            >
                                                                Cancel
                                                            </Button>
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <>
                                                        <Upload
                                                            name="avatar"
                                                            listType="picture-card"
                                                            showUploadList={
                                                                false
                                                            }
                                                            customRequest={
                                                                handleImageUpload
                                                            }
                                                            // style={{
                                                            //     width: '100%',
                                                            //     height: '180px',
                                                            // }} // ⬅️ reduced
                                                            className="avatar-uploader overflow-hidden"
                                                        >
                                                            {minorImageUrl &&
                                                            minorImageUrl !==
                                                                '/assets/Profile_icon.png' ? (
                                                                <div className="relative h-full w-full">
                                                                    <img
                                                                        src={
                                                                            minorImageUrl
                                                                        }
                                                                        className="object-contain"
                                                                        alt="avatar"
                                                                        style={{
                                                                            width: '100%',
                                                                            height: '100%',
                                                                        }}
                                                                    />
                                                                    {loading && (
                                                                        <div
                                                                            style={{
                                                                                position:
                                                                                    'absolute',
                                                                                inset: 0,
                                                                                display:
                                                                                    'flex',
                                                                                justifyContent:
                                                                                    'center',
                                                                                alignItems:
                                                                                    'center',
                                                                                background:
                                                                                    'rgba(255,255,255,0.6)',
                                                                            }}
                                                                        >
                                                                            <LoadingOutlined
                                                                                style={{
                                                                                    fontSize: 20,
                                                                                    color: '#8143D1',
                                                                                }}
                                                                                spin
                                                                            />
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            ) : (
                                                                <div
                                                                    style={{
                                                                        width: '100%',
                                                                        height: '180px', // ⬅️ reduced
                                                                        display:
                                                                            'flex',
                                                                        flexDirection:
                                                                            'column',
                                                                        justifyContent:
                                                                            'center',
                                                                        alignItems:
                                                                            'center',
                                                                        color: '#6B7280',
                                                                    }}
                                                                >
                                                                    <PlusOutlined />
                                                                    <div
                                                                        style={{
                                                                            marginTop: 6,
                                                                        }}
                                                                    >
                                                                        Upload
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </Upload>

                                                        {photoError && (
                                                            <div className="pt-2 text-center text-2xl text-red-600">
                                                                {photoError}
                                                            </div>
                                                        )}
                                                    </>
                                                )}
                                            </div>

                                            {/* Footer */}
                                            {!useWebcam && (
                                                <div
                                                    style={{
                                                        borderTop:
                                                            '1px solid #E7EAF3',
                                                        padding: '8px 12px',
                                                    }}
                                                >
                                                    <Button
                                                        block
                                                        onClick={() =>
                                                            setUseWebcam(true)
                                                        }
                                                    >
                                                        Capture from Webcam
                                                    </Button>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    {/* ====== POLICY ALERT (50%) ====== */}
                                    <div
                                        style={{
                                            flex: '0 0 50%',
                                            display: 'flex',
                                        }}
                                    >
                                        {requiredPolicyAlerts.length ===
                                        0 ? null : (
                                            <div
                                                style={{
                                                    width: '100%',
                                                    border: '1px solid #E7EAF3',
                                                    borderRadius: '12px',
                                                    background: '#fff',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    boxShadow:
                                                        '0 1px 2px rgba(16,24,40,.06)',
                                                    minHeight: '220px', // ⬅️ reduced
                                                }}
                                            >
                                                {/* Header */}
                                                <div
                                                    style={{
                                                        borderBottom:
                                                            '1px solid #E7EAF3',
                                                        padding: '8px 12px',
                                                    }}
                                                >
                                                    <p
                                                        style={{
                                                            margin: 0,
                                                            fontSize: '15px',
                                                            fontWeight: 600,
                                                            color: '#6D28D9',
                                                        }}
                                                    >
                                                        Policy Alert
                                                    </p>
                                                </div>

                                                {/* Body */}
                                                <div
                                                    style={{
                                                        padding: '12px',
                                                        flex: 1,
                                                        width: '100%',
                                                    }}
                                                >
                                                    {requiredPolicyAlerts.length ===
                                                    0 ? (
                                                        <p
                                                            style={{
                                                                margin: 0,
                                                                fontSize:
                                                                    '14px',
                                                                fontWeight: 500,
                                                                color: '#16A34A',
                                                            }}
                                                        >
                                                            All required
                                                            policies selected.
                                                        </p>
                                                    ) : (
                                                        <div
                                                            style={{
                                                                maxHeight:
                                                                    '180px',
                                                                overflowY:
                                                                    'auto',
                                                                width: '100%',
                                                            }}
                                                        >
                                                            {requiredPolicyAlerts.map(
                                                                (
                                                                    policy: any
                                                                ) => (
                                                                    <div
                                                                        key={
                                                                            policy.name
                                                                        }
                                                                        style={{
                                                                            width: '100%',
                                                                            display:
                                                                                'flex',
                                                                            gap: '6px',
                                                                            alignItems:
                                                                                'flex-start',
                                                                            border: '1px solid #FACC15',
                                                                            background:
                                                                                '#FEFCE8',
                                                                            borderRadius:
                                                                                '6px',
                                                                            padding:
                                                                                '6px 10px',
                                                                            marginBottom:
                                                                                '6px',
                                                                            color: '#1A3353',
                                                                        }}
                                                                    >
                                                                        <InfoCircleOutlined className="text-3xl text-red-500" />

                                                                        <div>
                                                                            <p
                                                                                style={{
                                                                                    margin: 0,
                                                                                    fontSize:
                                                                                        '13px',
                                                                                }}
                                                                            >
                                                                                {
                                                                                    policy.name
                                                                                }{' '}
                                                                                yet
                                                                                to
                                                                                be
                                                                                selected
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                )
                                                            )}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <Form.Item className="flex justify-end">
                                    <div className="mt-6 flex w-[100%] justify-end gap-4 ">
                                        <Button
                                            type="default"
                                            onClick={() => {
                                                form.validateFields().then(
                                                    (values) => {
                                                        if (
                                                            isPhotoRequired &&
                                                            isMissingPhoto(
                                                                minorImageUrl
                                                            )
                                                        ) {
                                                            setPhotoError(
                                                                'Photo is required.'
                                                            );
                                                            return;
                                                        }

                                                        const policiesFormatted =
                                                            (
                                                                selectedPolicies ||
                                                                []
                                                            ).map(
                                                                (
                                                                    policyName: string
                                                                ) => {
                                                                    const policyDef =
                                                                        policies?.items?.find(
                                                                            (
                                                                                item: any
                                                                            ) =>
                                                                                item.name ===
                                                                                policyName
                                                                        );
                                                                    return {
                                                                        policyType:
                                                                            formatStringWithSpaces(
                                                                                policyName
                                                                            ),
                                                                        isEnabled:
                                                                            true,
                                                                        required:
                                                                            policyDef?.required ||
                                                                            false,
                                                                        policyId:
                                                                            policyDef?._id ||
                                                                            null,
                                                                        date:
                                                                            policyDates[
                                                                                policyName
                                                                            ] ||
                                                                            null,
                                                                    };
                                                                }
                                                            );

                                                        const minorWithPolicy =
                                                            {
                                                                ...values,
                                                                policies:
                                                                    policiesFormatted,
                                                                photo:
                                                                    minorImageUrl ||
                                                                    '', // or however you're handling photo here
                                                            };
                                                        if (
                                                            selectedFormMinorFromWaiver?._id
                                                        ) {
                                                            minorWithPolicy.minorId =
                                                                selectedFormMinorFromWaiver._id;
                                                        }
                                                        // ✅ Add minorId if imported from waiver
                                                        if (
                                                            !minorWithPolicy.minorId &&
                                                            selectedMinor?.[0]
                                                                ?._id
                                                        ) {
                                                            minorWithPolicy.minorId =
                                                                selectedMinor[0]._id;
                                                        }

                                                        // ✅ Save to manuallyAddedMinors
                                                        setManuallyAddedMinors(
                                                            (prev: any) => [
                                                                ...prev,
                                                                minorWithPolicy,
                                                            ]
                                                        );
                                                        setSelectedPolicies([]);
                                                        setPolicyDates({});
                                                        // ✅ Reset form + temporary waiver minor
                                                        form.resetFields([
                                                            'firstName',
                                                            'lastName',
                                                            'dob',
                                                            'gender',
                                                            'relation',
                                                            'notes',
                                                        ]);
                                                        setMinorImageUrl(
                                                            '/assets/Profile_icon.png'
                                                        );
                                                        setSelectedMinor([]); // ✅ Clear imported minor after adding
                                                    }
                                                );
                                            }}
                                            className="rounded-md border border-[#1a3353] py-1.5 text-[#1a3353]"
                                        >
                                            Add Another
                                        </Button>

                                        <Button
                                            onClick={handleSave}
                                            type="primary"
                                            className="  rounded-md bg-purpleLight py-1.5 text-white"
                                        >
                                            Save
                                        </Button>
                                    </div>
                                </Form.Item>
                            </Form>
                        </div>
                    </div>
                </Modal>
            </ConfigProvider>
            {importWavierModal && (
                <ImportChildFromWavierModal
                    open={importWavierModal}
                    onCancel={() => setImportWavierModal(false)}
                    clientSourceId={clientSourceId}
                    selectedMinors={manuallyAddedMinors}
                    onProceed={(selectedMinorsFromModal: any) => {
                        const minor = selectedMinorsFromModal[0]; // Only one will be allowed
                        form.setFieldsValue({
                            firstName: minor.firstName || '',
                            lastName: minor.lastName || '',
                            gender: minor.gender?.toLowerCase() || '',
                            dob: minor.dob ? dayjs(minor.dob) : null,
                            relation: minor.relation || 'child',
                            notes: minor.notes || '',
                        });
                        setSelectedMinor([minor]); // Keep it in state to show in table and handle photo
                        setSelectedFormMinorFromWaiver(minor);
                        setImportWavierModal(false);
                    }}
                />
            )}
            {uploadedPhotos && (
                <>
                    <UploadPhotoModal
                        open={uploadedPhotos}
                        onCancel={() => setUploadedPhotos(false)}
                        onSave={(photoUrl: string) => {
                            setManuallyAddedMinors((prevMinors: any[]) => {
                                if (
                                    selectedMinorIndexForPhoto !== null &&
                                    selectedMinorIndexForPhoto >= 0 &&
                                    selectedMinorIndexForPhoto <
                                        prevMinors.length
                                ) {
                                    const updatedMinors = [...prevMinors];
                                    updatedMinors[selectedMinorIndexForPhoto] =
                                        {
                                            ...updatedMinors[
                                                selectedMinorIndexForPhoto
                                            ],
                                            photo: photoUrl,
                                        };
                                    return updatedMinors;
                                }
                                return prevMinors;
                            });
                            setUploadedPhotos(false);
                        }}

                        // selectedClientUserId={selectedClientUserId}
                        // selectedMinor={selectedMinor}
                        // setSelectedMinor={setSelectedMinor}
                    />
                </>
            )}
        </>
    );
};

export default AddSubprofileModal;
