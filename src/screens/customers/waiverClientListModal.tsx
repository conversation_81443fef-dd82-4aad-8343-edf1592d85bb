import {
    Button,
    ConfigProvider,
    Dropdown,
    Form,
    FormProps,
    Input,
    Menu,
    Modal,
    Pagination,
    Select,
    Spin,
    Table,
} from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { capitalizeFirstLetter } from '~/components/common/function';
import CommonTable from '~/components/common/commonTable';
import { CustomerLeadList } from '~/redux/actions/customerLead-action';
import { useLoader } from '~/hooks/useLoader';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { SetFacilityId } from '~/redux/slices/appointment-slice';
import { MoreOutlined } from '@ant-design/icons';
import { navigate } from 'wouter/use-location';
import AddSubprofileModal from './wavier/add-child-modal';
import AddClientLeadModal from './wavier/add-individual-client-modal';
import dayjs from 'dayjs';
import AddClientModal from './add-client-modal';
import { useSelector } from 'react-redux';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import Title from 'antd/es/typography/Title';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import ModulePinConfirmationModal from '~/components/modals/module-pin-confirmation-modal';
const { Search } = Input;

interface WavierClientListingModalProps {
    open: boolean;
    onClose: () => void;
    onAddCustomer: (leadId: string) => void;
}

const WavierClientListingModal: React.FC<WavierClientListingModalProps> = ({
    open,
    onClose,
    onAddCustomer,
}) => {
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const [isInitialLoading, setIsInitialLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSizes, setPageSize] = useState(10);
    const [selectedLocation, setSelectedLocation] = useState<string>();
    const [ClientLeadList, setClientLeadList] = useState<any>([]);
    const [addSubProfileLeadModel, setaddSubProfileLeadModel] = useState(false);
    const [addClientLeadModel, setAddClientLeadModel] = useState(false);
    const [isClientModal, setIsClientModal] = useState<boolean>(false);
    const [selectedRow, setSelectedRow] = useState<any>(null);

    const [leadId, setLeadId] = useState<string>();
    const [searchValue, setSearchValue] = useState<string>();
    const [total, setTotal] = useState(0);
    const [requirePinModal, setRequirePinModal] = useState<boolean>(false);
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(true);
    const [openAfterPin, setOpenAfterPin] = useState<boolean>(false);

    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        facilityList: state.facility_store.facilityList,
    }));
    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));
    useEffect(() => {
        startLoader();
        dispatch(FacilitiesList({ page: 1, pageSize: 30 })).then((res: any) => {
            const firstLocationId = res?.payload?.data?.data?.list?.[0]?._id;
            if (firstLocationId) {
                setSelectedLocation(firstLocationId);
                dispatch(SetFacilityId(firstLocationId));
            }
        });
    }, []);
    const { role } = useSelector((state: any) => state.auth_store);

    useEffect(() => {
        if (!open || role === RoleType.ORGANIZATION) return;

        dispatch(
            GetSettingActiveStatus({
                settingKey: 'settings_pin',
            })
        ).then((response: any) => {
            const settingData = response?.payload?.data?.data;
            const isEnabled = settingData?.isEnabled;
            const isActive = settingData?.isActive;

            if (isEnabled && isActive) {
                setRequirePinModal(true);
                setPinModalVisible(true);
            } else {
                setRequirePinModal(false);
                setPinModalVisible(false);
            }
        });
    }, [open, role, dispatch]);
    useEffect(() => {
        if (selectedLocation) {
            startLoader();
            dispatch(
                CustomerLeadList({
                    page: currentPage,
                    pageSize: pageSizes,
                    search: searchValue || '',
                    facilityId: selectedLocation,
                })
            )
                .then((res: any) => {
                    setClientLeadList(res?.payload?.data?.clientLead || []);
                    setTotal(res?.payload?.data?.totalClientLead || 0);
                })
                .finally(() => {
                    endLoader();
                    setIsInitialLoading(false); // ✅ Marks initial data as loaded
                });
        }
    }, [selectedLocation, searchValue, currentPage, pageSizes]);

    const refreshTableData = () => {
        if (selectedLocation) {
            setIsInitialLoading(true); // ✅ Show the loader UI
            startLoader(); // Optional: for internal logic/debug

            dispatch(
                CustomerLeadList({
                    page: currentPage,
                    pageSize: pageSizes,
                    search: searchValue || '',
                    facilityId: selectedLocation,
                })
            )
                .then((res: any) => {
                    setClientLeadList(res?.payload?.data?.clientLead || []);
                    setTotal(res?.payload?.data?.totalClientLead || 0);
                })
                .finally(() => {
                    endLoader(); // End loader logic
                    setIsInitialLoading(false); // ✅ Hide loader UI
                });
        }
    };

    const columns = [
        {
            title: 'Name',
            dataIndex: '',
            render: (record: any) => {
                const firstName = capitalizeFirstLetter(
                    record?.firstName || ''
                );
                const lastName = record?.lastName
                    ? ' ' + capitalizeFirstLetter(record.lastName)
                    : '';
                return (
                    <div
                        className="cursor-pointer"
                        onClick={() => {
                            const id =
                                record.flag === 'minor'
                                    ? record?.parentClientId
                                    : record._id;

                            // ✅ Select valid rows only
                            if (
                                record.flag !== 'minor' &&
                                !record.isConvertedToClient
                            ) {
                                setSelectedRow(record);
                            } else {
                                setSelectedRow(null);
                            }
                        }}
                    >
                        {firstName + lastName}
                    </div>
                );
            },
        },

        {
            title: 'Phone No',
            dataIndex: 'phone',
            render: (text: string | undefined | null) => {
                return text?.trim() ? text : <p className="">—</p>;
            },
        },
        {
            title: 'Email',
            dataIndex: 'email',
            render: (text: string | undefined | null) => {
                return text?.trim() ? text : <p className="">—</p>;
            },
        },
        {
            title: 'Location',
            dataIndex: '',
            render: (record: any) => {
                return (
                    <>
                        <div>
                            {capitalizeFirstLetter(
                                record?.facilityId?.facilityName
                            )}
                        </div>
                    </>
                );
            },
        },
        {
            title: 'Date Requested',
            dataIndex: '',
            render: (record: any) => {
                return (
                    <>
                        <div>
                            {dayjs(record?.createdAt).format('MMM DD, YYYY')}
                        </div>
                    </>
                );
            },
        },
        {
            title: 'Age Group',
            dataIndex: '',
            key: 'ageGroup',
            align: 'center' as const,
            render: (record: any) => {
                const showAddAsIndividual =
                    record.flag !== 'minor' && !record.isConvertedToClient;

                if (record.flag === 'minor') {
                    return (
                        <span className="rounded-md bg-orange-100 px-3 py-2 text-12 font-medium text-orange-600">
                            Under 18
                        </span>
                    );
                }

                if (showAddAsIndividual) {
                    return (
                        <span className="rounded-md bg-green-100 px-3 py-2 text-12 font-medium text-green-600">
                            Over 18 Adult
                        </span>
                    );
                }

                return '';
            },
        },

        // {
        //     title: 'Action',
        //     dataIndex: '',
        //     width: '120px',
        //     align: 'center' as const,
        //     key: 'action',
        //     render: (record: any) => {
        //         const showAddAsIndividual =
        //             record.flag !== 'minor' && !record.isConvertedToClient;

        //         const menu = (
        //             <Menu>
        //                 {showAddAsIndividual && (
        //                     <Menu.Item
        //                         key="assign-package"
        //                         onClick={() => {
        //                             setLeadId(record._id);
        //                             setAddClientLeadModel(true);
        //                         }}
        //                     >
        //                         <span className="text-xl text-[#1A3353]">
        //                             Add As an Individual
        //                         </span>
        //                     </Menu.Item>
        //                 )}
        //             </Menu>
        //         );

        //         if (!showAddAsIndividual) return null;

        //         return (
        //             <span className="flex justify-center gap-5">
        //                 <Dropdown overlay={menu} trigger={['click']}>
        //                     <MoreOutlined
        //                         style={{
        //                             fontSize: '20px',
        //                             cursor: 'pointer',
        //                         }}
        //                     />
        //                 </Dropdown>
        //             </span>
        //         );
        //     },
        // },
    ];
    const handleSearchChange = (values: any) => {
        setSearchValue(values);
        setCurrentPage(1);
    };
    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
    }
    const handleFacilityChange = (value: string) => {
        setSelectedLocation(value);
        setCurrentPage(1); // Reset page
    };
    // for add client permissions
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasClientWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.CLIENTS_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const handleAddClientModalClose = () => {
        onClose();
    };
    if (requirePinModal && pinModalVisible && role !== RoleType.ORGANIZATION) {
        return (
            <ModulePinConfirmationModal
                visible={true}
                onConfirm={() => {
                    setPinModalVisible(false);
                    setOpenAfterPin(true);
                }}
                onCancel={onClose}
                module={SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING}
                subModule={PERMISSIONS_ENUM.CLIENTS_WRITE}
            />
        );
    }
    return (
        <>
            <Modal
                className=" w-[90%] "
                title={
                    <Title className="text-[#1A3353]" level={4}>
                        Add Client
                    </Title>
                }
                // open={open}
                open={!requirePinModal ? true : openAfterPin}
                onOk={handleAddClientModalClose}
                onCancel={handleAddClientModalClose}
                footer={false}
                centered // ✅
                width={1000}
                maskClosable={false}
                bodyStyle={{
                    maxHeight: '85vh',
                    minHeight: '85vh',
                    overflowY: 'auto',
                }}
            >
                <div className="flex w-full items-end justify-between gap-10 pt-5">
                    {/* Left: Search */}
                    <div className="w-full ">
                        <Button
                            className="rounded-lg  bg-purpleLight  px-8 py-2 text-white "
                            onClick={refreshTableData}
                        >
                            Refresh
                        </Button>
                    </div>

                    {/* Right: Location Select + Buttons */}
                    <div className="flex w-full items-end justify-end gap-10">
                        <Search
                            allowClear
                            placeholder="Search"
                            onChange={(e) => handleSearchChange(e.target.value)}
                            className="w-[40%] "
                        />

                        <div className="w-[40%]">
                            <p className="text-lg font-medium text-[#1A3353] ">
                                Location
                            </p>
                            <Select
                                className="w-[100%]"
                                showSearch
                                allowClear
                                value={selectedLocation}
                                onChange={handleFacilityChange}
                                placeholder="All"
                                filterOption={(input, option) =>
                                    String(option?.label ?? '')
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                }
                                options={FacilityOptions}
                            />
                        </div>
                    </div>
                    {(hasClientWritePermission ||
                        store.role === RoleType.ORGANIZATION) && (
                        <>
                            {/* <Button
                                className="py-  rounded-lg   bg-purpleLight px-8 text-white"
                                onClick={() => setaddSubProfileLeadModel(true)}
                            >
                                Add Sub-Client
                            </Button>

                            <Button
                                className="rounded-lg bg-purpleLight   px-8 py-2 text-white"
                                onClick={() => setIsClientModal(true)}
                            >
                                Add Client +
                            </Button> */}
                        </>
                    )}
                </div>

                {isInitialLoading ? (
                    <div className="flex h-[60vh] items-center justify-center">
                        <Spin size="large" />
                    </div>
                ) : (
                    <div className="overflow-y-scroll">
                        <div className="mt-10">
                            <Table
                                className="max-w-full"
                                columns={columns}
                                dataSource={ClientLeadList || []}
                                rowKey={(record: { _id: any }) => record._id}
                                pagination={false}
                                rowHoverable={false}
                                scroll={{ y: 450 }}
                                onRow={(record: { _id: any }) => ({
                                    onClick: () => {
                                        const selectedRecord =
                                            ClientLeadList.find(
                                                (item: any) =>
                                                    item._id === record._id
                                            );
                                        if (
                                            selectedRecord?.flag !== 'minor' &&
                                            !selectedRecord?.isConvertedToClient
                                        ) {
                                            setSelectedRow(selectedRecord);
                                        } else {
                                            setSelectedRow(null);
                                        }
                                    },
                                })}
                                rowClassName={(record: { _id: any }) =>
                                    selectedRow &&
                                    selectedRow._id === record._id
                                        ? 'bg-purple-200'
                                        : ''
                                }
                            />
                        </div>
                        <div className="flex justify-center py-10">
                            <Pagination
                                current={currentPage}
                                total={total}
                                pageSize={pageSizes}
                                showSizeChanger
                                pageSizeOptions={['10', '20', '50']}
                                onChange={paginate}
                            />
                        </div>
                        {selectedRow && (
                            <div className="flex justify-end pb-4 pr-5">
                                <Button
                                    className="rounded-lg bg-purpleLight px-6 py-2 text-white"
                                    onClick={() => {
                                        onAddCustomer(selectedRow._id); // ✅ Calls the parent handler
                                    }}
                                >
                                    Add Customer
                                </Button>
                            </div>
                        )}
                    </div>
                )}

                {/* <div className="flex justify-center  py-10">
                    <Pagination
                        current={currentPage}
                        total={total}
                        pageSize={pageSizes}
                        showSizeChanger
                        pageSizeOptions={['10', '20', '50']}
                        onChange={paginate}
                    />
                </div> */}

                {addSubProfileLeadModel && (
                    <>
                        <AddSubprofileModal
                            pinRequirement={true}
                            open={addSubProfileLeadModel}
                            onCancel={() => setaddSubProfileLeadModel(false)}
                        />
                    </>
                )}
                {/* {addClientLeadModel && (
                    <>
                        <AddClientLeadModal
                            open={addClientLeadModel}
                            leadId={leadId}
                            onClose={() => {
                                setAddClientLeadModel(false);
                                setLeadId('');
                            }}
                        />
                    </>
                )} */}
                {isClientModal && (
                    <>
                        <AddClientModal
                            open={isClientModal}
                            onClose={() => {
                                setIsClientModal(false);
                            }}
                        />
                    </>
                )}
            </Modal>
        </>
    );
};
export default WavierClientListingModal;
