import {
    Button,
    Config<PERSON><PERSON>ider,
    Dropdown,
    Form,
    FormProps,
    Input,
    Menu,
    Modal,
    Pagination,
    Select,
} from 'antd';
import { useEffect, useMemo, useState } from 'react';

import CommonTable from '~/components/common/commonTable';
import { useLoader } from '~/hooks/useLoader';
import { MoreOutlined } from '@ant-design/icons';
const { Search } = Input;

const InactiveClientTab = (props: any) => {

    const columns = [
        {
            title: 'Name',
            dataIndex: '',
            render: (record: any) => {

                return (
                    <div
                        className="cursor-pointer"

                    >
                        {record.name}
                    </div>
                );
            },
        },

        {
            title: 'Phone No',
            dataIndex: 'phone',
            render: (text: string | undefined | null) => {
                return text?.trim() ? text : <p className="">—</p>;
            },
        },
        {
            title: 'Email',
            dataIndex: 'email',
            render: (text: string | undefined | null) => {
                return text?.trim() ? text : <p className="">—</p>;
            },
        },
        {
            title: 'Branch',
            dataIndex: '',
            render: (record: any) => {
                return (
                    <>
                        <div>
                            {
                                record?.facilityId?.facilityName
                            }
                        </div>
                    </>
                );
            },
        },
        {
            title: 'Last Visit',
            dataIndex: '',
            render: (record: any) => {
                return (
                    <>
                        <div>
                            {record?.createdAt}
                        </div>
                    </>
                );
            },
        },

    ];

    return (
        <>
            <div className="flex w-full items-center justify-end gap-10 pt-5">
                {/* Left: Search */}

                {/* Right: Location Select + Buttons */}





            </div>

            <div className="mt-3">
                <CommonTable
                    className="min-w-min"
                    columns={columns}
                    dataSource={[]}
                />
            </div>
        </>
    );
};
export default InactiveClientTab;
