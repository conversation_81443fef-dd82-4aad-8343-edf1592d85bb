import { DeleteOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import CommonTable from '~/components/common/commonTable';

import AddNewLeadModal from './add-new-lead-modal';
const Columns = [
    // { title: 'Client Id', dataIndex: 'clientId' },
    {
        title: 'Name',
        dataIndex: 'name',
        render: (text: string | undefined | null, record: any) => (
            <a
                className=" "
                href="/lead-details"
                onClick={(e) => {
                    e.preventDefault();
                    window.location.href = `/lead-details`;
                }}
            >
                {text?.trim() || '—'}
            </a>
        ),
    },
    {
        title: 'Phone No',
        dataIndex: 'phone',
    },
    {
        title: 'Email',
        dataIndex: 'email',
        render: (text: string | undefined | null) => {
            return text?.trim() ? text : <p className="">—</p>;
        },
        // align: "center",
        // width: "10%",
    },
    {
        title: 'Created at',
        dataIndex: 'createdAt',
        align: 'center',
        // width: "10%",
    },

    {
        title: 'Lead Source',
        dataIndex: 'leadSource',
        align: 'center',
        // width: "10%",
    },
    {
        title: 'Action',
        dataIndex: 'action',
        align: 'center',
        // width: "10%",
    },
];

const Data = [
    {
        key: '1',
        name: 'John Doe',
        phone: '0987654321',
        email: '<EMAIL>',
        createdAt: '2024-03-10',
        leadSource: 'Website',
        action: (
            <div className="flex flex-row justify-center">
                <DeleteOutlined style={{ color: 'red', cursor: 'pointer' }} />
            </div>
        ),
    },
    {
        key: '2',
        name: 'Jane Smith',
        phone: '0987654321',
        email: '<EMAIL>',
        createdAt: '2024-03-12',
        leadSource: 'Phone Call',
        action: (
            <div className="flex flex-row justify-center">
                <DeleteOutlined style={{ color: 'red', cursor: 'pointer' }} />
            </div>
        ),
    },
];

const LeadsListing = () => {
    const [modalVisible, setModalVisible] = useState<boolean>();
    const showModal = () => {
        setModalVisible(true);
    };
    const handleClose = () => {
        setModalVisible(false);
    };

    return (
        <div>
            <CommonTable
                heading={<p>&nbsp;</p>}
                className="min-w-min"
                columns={Columns}
                dataSource={Data}
                DivWidth="w-[100%]"
                showSearch={true}
                addNewTitle="Add New"
                addNewModal={true}
                openModal={showModal}
                smsButton={true}
                leadsDatePicker={true}
            />

            {modalVisible && (
                <AddNewLeadModal visible={modalVisible} onClose={handleClose} />
            )}
        </div>
    );
};

export default LeadsListing;
