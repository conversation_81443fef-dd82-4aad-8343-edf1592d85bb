import {
    EditOutlined,
    MailOutlined,
    MessageOutlined,
    PhoneOutlined,
    SmallDashOutlined,
    UserOutlined,
} from '@ant-design/icons';
import {
    Button,
    Checkbox,
    ConfigProvider,
    DatePicker,
    Drawer,
    Form,
    Input,
    Select,
    Steps,
} from 'antd';
import Title from 'antd/es/typography/Title';
import React, { useState } from 'react';
import { goBack } from '~/components/common/function';
import ImageUpload from '~/components/common/image-upload-comp';
import AddClientModal from './add-client-modal';
const { Option } = Select;

const data = [
    {
        label: 'Name :',
        value: `<PERSON><PERSON>`,
    },
    {
        label: 'Created At :',
        value: `11-7-2025 - 10:00 AM`,
    },
    {
        label: 'Follow Up Date :',
        value: `11-7-2025 - 10:00 AM`,
    },

    {
        label: 'Email :',
        value: `<EMAIL>`,
    },
    {
        label: 'Lead Source :',
        value: `Twitter`,
    },
    {
        label: 'Notes :',
        value: `Want 3 months membership`,
    },
    {
        label: 'Phone :',
        value: `**********`,
    },
    {
        label: 'Staff Name :',
        value: `Lucky`,
    },
];
const options = [
    { label: 'Kanav Parwal', value: 'kanav' },
    { label: 'Avinav Pathak', value: 'avinav' },
    { label: 'Faraz', value: 'faraz' },
];
const LeadDetails = () => {
    const [form] = Form.useForm();
    const [current, setCurrent] = useState(0);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedValues, setSelectedValues] = useState({
        createdBy: undefined,
        createdTo: undefined,
    });

    const showModal = () => {
        setModalVisible(true);
    };

    const handleCancel = () => {
        setModalVisible(false);
    };

    const [open, setOpen] = useState(false);
    const showDrawer = () => {
        setOpen(true);
    };
    const onClose = () => {
        setOpen(false);
    };

    const onChange = (value: any) => {
        console.log('onChange:', value);
        setCurrent(value);
    };

    const handleChange = (key: 'createdBy' | 'createdTo', value: string) => {
        const updated = { ...selectedValues, [key]: value };
        setSelectedValues(updated);
        form.setFieldValue(key, value);
    };

    const createdByOptions = options.filter(
        (opt) => opt.value !== selectedValues.createdTo
    );
    const createdToOptions = options.filter(
        (opt) => opt.value !== selectedValues.createdBy
    );

    const description1 = (
        <div className="">
            <div className="flex items-center  pe-5">
                <div className="flex flex-col items-center lg:w-[15%] ">
                    <p className="text-[12px] text-[#72849A]">7 Jul 2025</p>
                    <p className="text-[12px] text-[#72849A]">10:50 AM</p>
                </div>

                <div className="flex  flex-col gap-2 rounded-lg  bg-[#F2F2F2] px-5 py-3  lg:w-[100%]">
                    <div className="flex items-center">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Assigned to -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Avinav Pathak |{' '}
                                <span className="font-medium"> Due : </span>10
                                Jul 09:00 AM
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Created By -
                            </p>
                        </div>
                        <div className="flex w-[85%] items-center ">
                            <p className="text-[13px] text-[#1A3353]">
                                Kanav Parwal |{' '}
                            </p>

                            <EditOutlined className="text-primary" />
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Note -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Client wants to purchase a 3-month membership.
                                Needs invoice issued in her company’s name.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    const description2 = (
        <div className="">
            <div className="flex items-center  pe-5">
                <div className="flex flex-col items-center lg:w-[15%] ">
                    <p className="text-[12px] text-[#72849A]">8 Jul 2025</p>
                    <p className="text-[12px] text-[#72849A]">2:45 PM</p>
                </div>

                <div className="flex  flex-col gap-2 rounded-lg  bg-[#F2F2F2] px-5 py-3  lg:w-[100%]">
                    <div className="flex items-center">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Assigned to -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Avinav Pathak |{' '}
                                <span className="font-medium"> Due : </span> 9
                                Jul 10:00 AM
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Created By -
                            </p>
                        </div>
                        <div className="flex w-[85%] items-center ">
                            <p className="text-[13px] text-[#1A3353]">
                                Kanav Parwal |{' '}
                            </p>

                            <EditOutlined className="text-primary" />
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Note -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Requested a trial Zumba class. Waiting for
                                confirmation of Thursday morning batch
                                availability.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    const description3 = (
        <div className="">
            <div className="flex items-center  pe-5">
                <div className="flex flex-col items-center lg:w-[15%] ">
                    <p className="text-[12px] text-[#72849A]">9 Jul 2025</p>
                    <p className="text-[12px] text-[#72849A]">10:20 AM</p>
                </div>

                <div className="flex  flex-col gap-2 rounded-lg  bg-[#F2F2F2] px-5 py-3  lg:w-[100%]">
                    <div className="flex items-center">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Assigned to -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Avinav Pathak |{' '}
                                <span className="font-medium"> Due : </span> 11
                                Jul 05:30 PM
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Created By -
                            </p>
                        </div>
                        <div className="flex w-[85%] items-center ">
                            <p className="text-[13px] text-[#1A3353]">
                                Kanav Parwal |{' '}
                            </p>

                            <EditOutlined className="text-primary" />
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Note -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Inquired about payment in installments. Asked
                                for EMI option details.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
    const description4 = (
        <div className="">
            <div className="flex items-center  pe-5">
                <div className="flex flex-col items-center lg:w-[15%] ">
                    <p className="text-[12px] text-[#72849A]">10 Jul 2025</p>
                    <p className="text-[12px] text-[#72849A]">04:05 PM</p>
                </div>

                <div className="flex  flex-col gap-2 rounded-lg  bg-[#F2F2F2] px-5 py-3  lg:w-[100%]">
                    <div className="flex items-center">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Assigned to -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Avinav Pathak |{' '}
                                <span className="font-medium"> Due : </span> 13
                                Jul 11:00 AM
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Created By -
                            </p>
                        </div>
                        <div className="flex w-[85%] items-center ">
                            <p className="text-[13px] text-[#1A3353]">
                                Kanav Parwal |{' '}
                            </p>

                            <EditOutlined className="text-primary" />
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Note -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Client is travelling next week. Wants the
                                package to start from 20th July.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    const description5 = (
        <div className="">
            <div className="flex items-center  pe-5">
                <div className="flex flex-col items-center lg:w-[15%] ">
                    <p className="text-[12px] text-[#72849A]">11 Jul 2025</p>
                    <p className="text-[12px] text-[#72849A]">01:20 PM</p>
                </div>

                <div className="flex  flex-col gap-2 rounded-lg  bg-[#F2F2F2] px-5 py-3  lg:w-[100%]">
                    <div className="flex items-center">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Assigned to -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Avinav Pathak |{' '}
                                <span className="font-medium"> Due : </span> 13
                                Jul 09:00 AM
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Created By -
                            </p>
                        </div>
                        <div className="flex w-[85%] items-center ">
                            <p className="text-[13px] text-[#1A3353]">
                                Kanav Parwal |{' '}
                            </p>

                            <EditOutlined className="text-primary" />
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Note -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Referred by existing client (Mr. Mehta) – asking
                                for referral discount code.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
    const description6 = (
        <div className="">
            <div className="flex items-center  pe-5">
                <div className="flex flex-col items-center lg:w-[15%] ">
                    <p className="text-[12px] text-[#72849A]">12 Jul 2025</p>
                    <p className="text-[12px] text-[#72849A]">09:10 PM</p>
                </div>

                <div className="flex  flex-col gap-2 rounded-lg  bg-[#F2F2F2] px-5 py-3  lg:w-[100%]">
                    <div className="flex items-center">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Assigned to -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Avinav Pathak |{' '}
                                <span className="font-medium"> Due : </span> 14
                                Jul 12:00 PM
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Created By -
                            </p>
                        </div>
                        <div className="flex w-[85%] items-center ">
                            <p className="text-[13px] text-[#1A3353]">
                                Kanav Parwal |{' '}
                            </p>

                            <EditOutlined className="text-primary" />
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Note -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Asked for female physiotherapist availability
                                for post-natal recovery program.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
    const description7 = (
        <div className="">
            <div className="flex items-center  pe-5">
                <div className="flex flex-col items-center lg:w-[15%] ">
                    <p className="text-[12px] text-[#72849A]">14 Jul 2025</p>
                    <p className="text-[12px] text-[#72849A]">03:45 PM</p>
                </div>

                <div className="flex  flex-col gap-2 rounded-lg  bg-[#F2F2F2] px-5 py-3  lg:w-[100%]">
                    <div className="flex items-center">
                        <div className="w-[15%]">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Assigned to -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Avinav Pathak |{' '}
                                <span className="font-medium"> Due : </span> 15
                                Jul 11:00 AM
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Created By -
                            </p>
                        </div>
                        <div className="flex w-[85%] items-center ">
                            <p className="text-[13px] text-[#1A3353]">
                                Kanav Parwal |{' '}
                            </p>

                            <EditOutlined className="text-primary" />
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="w-[15%] ">
                            <p className="text-[13px] font-medium text-[#1A3353]">
                                Note -
                            </p>
                        </div>
                        <div className="w-[85%]">
                            <p className="text-[13px] text-[#1A3353]">
                                Client wants a call back to finalize schedule
                                before making payment. Available after 5 PM.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
    return (
        <>
            {/* -------------------------lead detail section------------------------- */}

            <div className="flex items-center gap-4  @xl:w-[85%] @2xl:w-[80%]">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
                <Title className="text-[#1A3353]" level={4}>
                    Lead Detail
                </Title>
            </div>

            <div className="my-10 flex rounded-lg py-10    shadow-md @xl:w-[85%] @2xl:w-[80%]">
                <div className="w-[15%] @sm:hidden ">
                    <ImageUpload
                    // onUpload={handleImageUpload}
                    // imageUrl={store.imageUrl}
                    />
                </div>
                <div className=" lg:w-[85%] @sm:w-full ">
                    <div className="w-full justify-start gap-x-10 lg:flex lg:flex-wrap ">
                        {data.map((item, index) => (
                            <div
                                key={index}
                                className={`mt-5 flex items-center gap-3 lg:w-[31%] @sm:ps-5   `}
                            >
                                <div
                                    className={` w-auto text-lg font-bold text-[#1A3353]`}
                                >
                                    <p className="text-2xl font-semibold">
                                        {item.label}
                                    </p>
                                </div>
                                <div className="overflow-wrap  break-word w-auto break-words text-xl capitalize text-[#72849A]">
                                    <p>{item.value}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <div className="flex flex-row items-center justify-end gap-6 @xl:w-[85%] @2xl:w-[80%]">
                <Button
                    onClick={showDrawer}
                    className="w-[15%] border-1 border-[#1A3353] py-7 text-2xl"
                    htmlType="submit"
                >
                    Follow up <EditOutlined />
                </Button>
                <Button
                    className="w-[15%] bg-purpleLight py-7 text-2xl"
                    type="primary"
                    htmlType="submit"
                    onClick={showModal}
                >
                    Enroll
                </Button>
            </div>

            {/* -------------------------lead log section------------------------- */}

            <div className="py-14 @xl:w-[85%] @2xl:w-[80%]">
                <ConfigProvider
                    // theme={{
                    //     components: {
                    //         Steps: {
                    //             navArrowColor: '#8143D1',
                    //         },
                    //     },
                    // }}
                    theme={{
                        token: {
                            colorPrimary: '#8143D1',
                            colorBorderSecondary: '#8143D1',
                        },
                    }}
                >
                    <Steps
                        current={current}
                        onChange={onChange}
                        direction="vertical"
                        id="log-stepper"
                        items={[
                            {
                                title: '',
                                icon: (
                                    <MessageOutlined className="rounded-full border border-primary p-2 text-3xl text-primary" />
                                ),
                                description: description1,
                            },
                            {
                                title: '',
                                icon: (
                                    <PhoneOutlined className="rounded-full border border-primary p-2 text-3xl text-primary" />
                                ),
                                description: description2,
                            },
                            {
                                title: '',
                                icon: (
                                    <MailOutlined className="rounded-full border border-primary p-2 text-3xl text-primary" />
                                ),
                                description: description3,
                            },
                            {
                                title: '',
                                icon: (
                                    <PhoneOutlined className="rounded-full border border-primary p-2 text-3xl text-primary" />
                                ),
                                description: description4,
                            },
                            {
                                title: '',
                                icon: (
                                    <MailOutlined className="rounded-full border border-primary p-2 text-3xl text-primary" />
                                ),
                                description: description5,
                            },
                            {
                                title: '',
                                icon: (
                                    <MessageOutlined className="rounded-full border border-primary p-2 text-3xl text-primary" />
                                ),
                                description: description6,
                            },
                            {
                                title: '',
                                icon: (
                                    <MailOutlined className="rounded-full border border-primary p-2 text-3xl text-primary" />
                                ),
                                description: description7,
                            },
                        ]}
                    />
                </ConfigProvider>
            </div>

            <Drawer
                title="Add Followup"
                closable={{ 'aria-label': 'Close Button' }}
                onClose={onClose}
                open={open}
            >
                <ConfigProvider
                    theme={{
                        components: {
                            Form: {
                                itemMarginBottom: 22,
                                verticalLabelMargin: -5,
                            },
                        },
                        token: {
                            borderRadius: 5,
                        },
                    }}
                >
                    <Form
                        className=" flex flex-col gap-1"
                        name="add-trainer"
                        layout="vertical"
                        size="large"
                        autoComplete="off"
                    >
                        <Form.Item
                            label="Created By"
                            name="createdBy"
                            className="w-full"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select created by',
                                },
                            ]}
                        >
                            <Select
                                placeholder="Select created by"
                                options={createdByOptions}
                                value={selectedValues.createdBy}
                                onChange={(value) =>
                                    handleChange('createdBy', value)
                                }
                                allowClear
                            />
                        </Form.Item>
                        <Form.Item
                            label="Assigned to"
                            name="createdTo"
                            className="w-full"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select created to',
                                },
                            ]}
                        >
                            <Select
                                placeholder="Select assigned to"
                                options={createdToOptions}
                                value={selectedValues.createdTo}
                                onChange={(value) =>
                                    handleChange('createdTo', value)
                                }
                                allowClear
                            />
                        </Form.Item>
                        <Form.Item
                            label="Follow up Date"
                            name="followUpDate"
                            className="w-full"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select the follow up date',
                                },
                            ]}
                        >
                            <DatePicker
                                popupClassName="custom-datepicker"
                                placeholder="DD/MM/YYYY"
                                format="DD/MM/YYYY"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                        <Form.Item
                            label="Mode of communication"
                            name="communication"
                            className="w-full"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select created by',
                                },
                            ]}
                        >
                            <Select placeholder="Select mode of communication">
                                <Select.Option value="call">Call</Select.Option>
                                <Select.Option value="email">
                                    Email
                                </Select.Option>
                                <Select.Option value="sms">SMS</Select.Option>
                            </Select>
                        </Form.Item>
                        <Form.Item
                            label="Notes"
                            name="notes"
                            className="w-full"
                        >
                            <Input.TextArea
                                className="mt-1.5"
                                placeholder="Enter Notes"
                                autoSize={{ minRows: 3, maxRows: 3 }}
                            />
                        </Form.Item>

                        <div className="flex flex-row justify-end">
                            <Form.Item>
                                <Button
                                    className="rounded-lg bg-purpleLight px-10   py-7 text-xl text-white  "
                                    htmlType="submit"
                                >
                                    Confirm
                                </Button>
                            </Form.Item>
                        </div>
                    </Form>
                </ConfigProvider>
            </Drawer>

            {/* <AddClientModal
                open={modalVisible}
                onClose={handleCancel}
                isAdded={true}
            /> */}
        </>
    );
};

export default LeadDetails;
