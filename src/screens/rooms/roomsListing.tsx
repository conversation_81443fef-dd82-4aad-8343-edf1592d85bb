import React, { useState, useEffect } from 'react';
import CommonTable from '~/components/common/commonTable';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { Menu, Dropdown, Switch, ConfigProvider, Pagination } from 'antd';
import { MoreOutlined } from '@ant-design/icons';
import { getQueryParams } from '~/utils/getQueryParams';
import { useLocation } from 'wouter';
import { navigate } from 'wouter/use-location';
import { useLoader } from '~/hooks/useLoader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    roomListing,
    deleteRoom,
    updateRoomStatus,
} from '~/redux/actions/room-action';
import DeleteModal from '~/components/common/deleteModal';
import { useSelector } from 'react-redux';

import clsx from 'clsx';
interface DataSourceItem {
    key: number;
    roomName: string;
    capacity: number;
    status: boolean;
    location: string;
    classType: string[];
    description: string;
}
const columns = [
    {
        title: 'Names',
        dataIndex: 'roomName',
        // width: "25%",
    },
    {
        title: 'Service Category',
        dataIndex: 'serviceCategory',
        ellipses: true,
        width: 400,
    },
    {
        title: 'Capacity',
        dataIndex: 'capacity',
    },
    {
        title: 'Location',
        dataIndex: 'facilityId',
    },
];

const RoomListing: React.FC = () => {
    const [_, setLocation] = useLocation();
    const [loader, startLoader, endLoader] = useLoader();
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    const [isDeleteModalVisible, setDeleteIsModalVisible] = useState(false);
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const params = getQueryParams();
    const [search, setSearch] = useState(params.search);
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const [roomId, setRoomId] = useState(null);
    const [currentRecord, setCurrentRecord] = useState(null);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [classTypes, setClassTypes] = useState([]);
    const dispatch = useAppDispatch();

    function handleSearch(value: string) {
        console.log(value);
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    }
    const handleStatusChange = (record: any) => {
        console.log(record);
        setCurrentRecord(record);
        setConfirmationModalVisible(true);
    };
    const handleConfirmStatusChange = () => {
        if (currentRecord)
            dispatch(
                updateRoomStatus({
                    roomId: currentRecord?._id,
                    isActive: !currentRecord.status,
                })
            );
        setConfirmationModalVisible(false);
    };

    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
    };

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (search) {
            navigate(`?page=${page}&pageSize=${pageSize}&search=${search}`, {
                replace: true,
            });
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }
    useEffect(() => {
        startLoader();
        dispatch(
            roomListing({
                page: currentPage,
                pageSize: pageSizes,
                search: search,
                classType: classTypes,
            })
        )
            .unwrap()
            .then(() => {})
            .finally(endLoader);
    }, [currentPage, pageSizes, search, classTypes]);
    const store = useAppSelector((state) => ({
        roomList: state.room_store.roomList,
        roomListCount: state.room_store.roomListCount,
    }));
    console.log(store.roomList);
    const dataSource: DataSourceItem[] = store.roomList.map(
        (rate: any, i: number) => ({
            key: i,
            _id: rate._id,
            roomName: rate.roomName,
            capacity: rate.capacity,
            status: rate.status,
            facilityId: rate?.locationDetails?.facilityName || ' ',
            classType: rate.classType,
            description: rate.description,
            serviceCategory: rate?.serviceDetails?.map((rate: any) => {
                return rate.name;
            }),
        })
    );
    const selectColumn = [
        {
            title: 'Status',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                // console.log("Record--------------", record)
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    handleBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Switch
                            className={clsx(
                                'rounded-full transition-colors',
                                record.status ? 'bg-switch-on' : 'bg-switch-off'
                            )}
                            id="swtich-off"
                            checkedChildren="ON"
                            // className="bg-[#D0D4D7]"
                            unCheckedChildren="OFF"
                            onChange={() => handleStatusChange(record)}
                            checked={record.status || false}
                        />
                    </ConfigProvider>
                );
            },
        },
    ];
    /* handle the functionality of the recod */
    const editRoom = (record: any) => {
        setLocation(`/setting/room/create-room/${record._id}`);
    };
    const deleteRoomData = () => {
        if (roomId) {
            dispatch(deleteRoom(roomId));
        }
        setDeleteIsModalVisible(false);
    };
    const actionColumn = [
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            render: (record: any) => {
                // console.log("record----------", record)
                const menu = (
                    <Menu>
                        <Menu.Item key="1" onClick={() => editRoom(record)}>
                            Edit Room
                        </Menu.Item>
                        <Menu.Item
                            key="2"
                            onClick={() => {
                                setDeleteIsModalVisible(true);
                                setRoomId(record._id);
                            }}
                        >
                            Delete
                        </Menu.Item>
                    </Menu>
                );

                return (
                    <Dropdown
                        overlay={menu}
                        trigger={['click']}
                        visible={openDropdownKey === record.key}
                        onOpenChange={(visible) => {
                            setOpenDropdownKey(visible ? record.key : null);
                        }}
                    >
                        <MoreOutlined
                            style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                            }}
                        />
                    </Dropdown>
                );
            },
        },
    ];

    const combinedColumns = [...columns, ...selectColumn, ...actionColumn];
    return (
        <>
            <div className="pt-5">
                <CommonTable
                    className="min-w-min"
                    columns={combinedColumns}
                    dataSource={dataSource}
                    bulkAction={false}
                    heading="Room Listing"
                    onSearch={handleSearch}
                    showSearch={true}
                    addNewLink="/setting/room/create-room/0"
                    addNewTitle="Create Room"
                    classType={false}
                    {...{
                        classTypes,
                        setClassTypes,
                    }}
                    backButton={true}
                />
            </div>
            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={store.roomListCount}
                    pageSize={pageSizes}
                    pageSizeOptions={['10', '20', '50']}
                    onChange={paginate}
                    // hideOnSinglePage
                />
            </div>
            {/* </>
                )} */}

            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />
            {isDeleteModalVisible && (
                <DeleteModal
                    title="Confirm Delete"
                    message={`Do you want to delete this Room?`}
                    isVisible={isDeleteModalVisible}
                    onDelete={deleteRoomData}
                    onCancel={() => {
                        setDeleteIsModalVisible(false);
                    }}
                />
            )}
        </>
    );
};
export default RoomListing;
