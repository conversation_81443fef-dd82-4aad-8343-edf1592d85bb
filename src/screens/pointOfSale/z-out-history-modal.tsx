import React, { useEffect, useState, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, DatePicker, Select, Table, Pagination } from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import {
    ZoutReportDownload,
    ZoutReportExport,
    ZoutReportHistory,
} from '~/redux/actions/report.action';
import dayjs, { Dayjs } from 'dayjs';
import { ColumnsType } from 'antd/es/table';
import DownloadReportModal from './fileDownload.modal';
import { CreateReconsiliation } from '~/redux/actions/purchased-action';
import Alertify from '~/services/alertify';
import { format } from 'path';

const { RangePicker } = DatePicker;

interface ReconciliationModalProps {
    visible: boolean;
    onClose: () => void;
}

const ZOutHistoryModal: React.FC<ReconciliationModalProps> = ({
    visible,
    onClose,
}) => {
    const [selectedFacilityId, setSelectedFacilityId] = useState<string | null>(
        null
    );
    const [selectedDateRange, setSelectedDateRange] = useState<
        [Dayjs, Dayjs] | null
    >(null);
    const [salesData, setSalesData] = useState<any[]>([]);
    const [loader, startLoader, endLoader] = useLoader();
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

    // Pagination states
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSizes, setPageSizes] = useState<number>(10);
    const [totalCount, setTotalCount] = useState<number>(0);
    const [zOutLoader, startZOutLoader, endZOutLoader] = useLoader();
    const [downlaodReportModal, setReportDownloadModal] =
        useState<boolean>(false);
    const [reconciliationId, setReconciliationId] = useState<string>('');
    const [downloadLoader, startDownloadLoader, stopDownloadLoader] =
        useLoader();

    const dispatch = useAppDispatch();

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
    }));

    // Memoize FacilityOptions to avoid re-creation on every render
    const FacilityOptions = React.useMemo(() => {
        return store.facilityList?.map((item: any) => ({
            value: item._id,
            label: item.facilityName,
            id: item._id,
        }));
    }, [store.facilityList]);

    // Memoize fetchData function to prevent re-creation on every render
    const fetchData = useCallback(
        (
            facilityId: string | null,
            dateRange: [Dayjs, Dayjs] | null,
            page: number = 1,
            pageSize: number = 10
        ) => {
            if (!facilityId || loader) return; // Only check for facilityId, not dateRange

            startLoader();

            // Only set date parameters if dateRange is provided
            const startDateISO = dateRange?.[0]?.toISOString()
                ? dayjs(dateRange[0].toISOString())
                      .startOf('day')
                      .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
                : undefined;

            const endDateISO = dateRange?.[1]?.toISOString()
                ? dayjs(dateRange[1].toISOString())
                      .endOf('day')
                      .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
                : undefined;

            const payload = {
                pageSize: pageSize,
                page: page,
                facilityId,
                // Only include date fields if they exist
                ...(startDateISO && { startDate: startDateISO }),
                ...(endDateISO && { endDate: endDateISO }),
            };

            dispatch(ZoutReportHistory(payload))
                .unwrap()
                .then((res: any) => {
                    const data = Array.isArray(res.data.data.data)
                        ? res.data.data.data
                        : [];
                    setSalesData(data);
                    setTotalCount(res.data.data.totalCount || 0);
                })
                .catch((error: any) => {
                    setSalesData([]);
                    setTotalCount(0);
                })
                .finally(() => {
                    endLoader();
                });
        },
        [dispatch] // Remove loader from dependencies to prevent infinite loops
    );

    const paginate = (page: number, pageSize: number) => {
        setCurrentPage(page);
        setPageSizes(pageSize);

        if (selectedFacilityId) {
            fetchData(selectedFacilityId, selectedDateRange, page, pageSize);
        }
    };

    useEffect(() => {
        if (visible) {
            if (FacilityOptions && FacilityOptions.length > 0) {
                const firstFacilityId = FacilityOptions[0].value;
                setSelectedFacilityId(firstFacilityId);

                // Hit API immediately when modal opens with first facility (no date range)
                fetchData(firstFacilityId, null, 1, 10);
            }

            setSelectedDateRange(null);
            setCurrentPage(1);
            setPageSizes(10);
            setTotalCount(0);
        }
    }, [visible, FacilityOptions]); // Remove fetchData from dependencies

    // Modified useEffect to fetch data whenever facility or date range changes
    useEffect(() => {
        if (selectedFacilityId && visible) {
            fetchData(
                selectedFacilityId,
                selectedDateRange,
                currentPage,
                pageSizes
            );
        }
    }, [selectedFacilityId, selectedDateRange]); // Remove fetchData from dependencies

    const handleFacilityChange = (value: string) => {
        setSelectedFacilityId(value);
        setCurrentPage(1);
        setPageSizes(10);
    };

    const handleDateRangeChange = (
        dates: [Dayjs | null, Dayjs | null] | null
    ) => {
        if (dates && dates[0] && dates[1]) {
            setSelectedDateRange([dates[0], dates[1]]);
        } else {
            setSelectedDateRange(null);
        }
        setCurrentPage(1);
        setPageSizes(10);
    };

    const getColumns = (): ColumnsType<any> => [
        { title: 'Register', dataIndex: 'name', key: '1' },
        {
            title: 'Start',
            dataIndex: 'startDate',
            key: '2',
            render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
        },
        {
            title: 'End',
            dataIndex: 'endDate',
            key: '3',
            render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
        },
        { title: 'Over/Under', dataIndex: 'overUnder', key: '4' },
        { title: 'Petty Cash', dataIndex: 'pettyCash', key: '5' },
        { title: 'Role', dataIndex: 'role', key: '6' },
    ];

    const handleClose = () => {
        onClose();
        setSelectedFacilityId(null);
        setSelectedDateRange(null);
        setSalesData([]);
        setSelectedRowKeys([]);
        setCurrentPage(1);
        setPageSizes(10);
        setTotalCount(0);
    };

    const HandleZOut = () => {
        startZOutLoader();
        setReportDownloadModal(true);
    };

    const toIdPayload = (keys: React.Key[]) =>
        keys.length === 1 ? String(keys[0]) : keys.map(String);

    const downnLoadReport = () => {
        if (!selectedRowKeys?.length) {
            Alertify.error('Please select at least one Z-Out.');
            return;
        }

        startDownloadLoader();

        const format: 'pdf' | 'zip' =
            selectedRowKeys.length === 1 ? 'pdf' : 'zip';

        dispatch(
            ZoutReportDownload({
                facilityIds: [selectedFacilityId || ''],
                responseType: format, // server must honor 'zip' for multiples
                zOutIds: selectedRowKeys,
            })
        )
            .unwrap()
            .then((res: ArrayBuffer) => {
                const mime =
                    format === 'pdf' ? 'application/pdf' : 'application/zip';
                const blob = new Blob([res], { type: mime });
                const url = window.URL.createObjectURL(blob);

                const link = document.createElement('a');
                link.href = url;

                // Windows-safe timestamp (no ":" or ".")
                const stamp = new Date().toISOString().replace(/[:.]/g, '-');
                const ext = format === 'pdf' ? 'pdf' : 'zip';
                link.setAttribute('download', `z-out_${stamp}.${ext}`);

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                Alertify.success('Z-Out report downloaded successfully!');
                setReportDownloadModal(false);
                handleClose();

                // cleanup on next tick
                setTimeout(() => window.URL.revokeObjectURL(url), 0);
            })
            .catch((error: any) => {
                console.error('Export failed:', error);
                Alertify.error('Failed to download Z-Out report.');
            })
            .finally(() => {
                stopDownloadLoader();
            });
    };

    // Row selection configuration for full row selection
    const rowSelection = {
        selectedRowKeys, // Track selected rows
        onChange: (newSelectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(newSelectedRowKeys); // Update the selected rows
        },
        getCheckboxProps: (record: any) => ({
            // You can add additional logic here to disable certain rows if needed
            disabled: false, // All rows are selectable in this case
        }),
    };

    return (
        <>
            <Modal
                title="Z-Out History"
                open={visible}
                centered
                onCancel={handleClose}
                footer={false}
                className="min-h-fit w-[75%]"
                styles={{
                    body: {
                        maxHeight: '80vh',
                        minHeight: 'auto',
                    },
                }}
            >
                <div className="flex flex-col gap-5 py-8 lg:flex-row lg:items-end">
                    <div>
                        <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                            Select Facility
                        </p>
                        <Select
                            showSearch
                            value={selectedFacilityId || undefined}
                            onChange={handleFacilityChange}
                            placeholder="Select facility"
                            style={{ width: '280px' }}
                            filterOption={(input, option) =>
                                String(option?.label ?? '')
                                    .toLowerCase()
                                    .includes(input.toLowerCase())
                            }
                            options={FacilityOptions}
                        />
                    </div>

                    <div className="lg:w-[30%]">
                        <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                            Date Range
                        </p>
                        <RangePicker
                            value={selectedDateRange}
                            onChange={handleDateRangeChange}
                            placeholder={['Start Date', 'End Date']}
                            format="DD/MM/YYYY"
                            style={{ width: '85%' }}
                            allowClear
                        />
                    </div>
                </div>

                <Table<any>
                    columns={getColumns()}
                    dataSource={salesData}
                    rowKey="_id"
                    pagination={false} // Disable built-in pagination
                    size="middle"
                    scroll={{ y: 300 }} // Set table height for vertical scrolling
                    rowSelection={rowSelection} // Row selection for full row selection
                    loading={loader} // Set loader state for the table
                    onRow={(record) => ({
                        onClick: () => {
                            const selectedKeys = [...selectedRowKeys];
                            const index = selectedKeys.indexOf(record._id);
                            if (index >= 0) {
                                selectedKeys.splice(index, 1);
                            } else {
                                selectedKeys.push(record._id);
                            }
                            setSelectedRowKeys(selectedKeys); // Update selected row keys on row click
                        },
                    })}
                />

                <div className="flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={totalCount}
                        pageSizeOptions={['10', '20', '50']}
                        pageSize={pageSizes}
                        onChange={paginate}
                        onShowSizeChange={paginate}
                        hideOnSinglePage={true}
                    />
                </div>

                <div className="flex justify-end gap-5">
                    <Button
                        onClick={handleClose}
                        className="rounded-lg border border-[#1A3353] px-10"
                    >
                        Cancel
                    </Button>

                    <Button
                        onClick={() => {
                            downnLoadReport();
                            // Handle Z-Out action here
                            console.log('Selected rows:', selectedRowKeys);
                        }}
                        loading={downloadLoader}
                        disabled={selectedRowKeys.length === 0}
                        className="rounded-lg bg-purpleLight px-10 text-white"
                    >
                        Download
                    </Button>
                </div>
            </Modal>
            {/* <DownloadReportModal
                open={downlaodReportModal}
                loading={downloadLoader}
                onCancel={() => {
                    setReportDownloadModal(false);
                }}
                onDownload={(format) => {
                    downnLoadReport(format);
                }}
            /> */}
        </>
    );
};

export default ZOutHistoryModal;
