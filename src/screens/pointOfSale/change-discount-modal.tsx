import { Button, ConfigProvider, Form, Input, Modal, Select } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { useDebounce } from '~/hooks/useDebounce';
import { DiscountListOnPricePage } from '~/redux/actions/createDiscountAction';
import { cartValidate } from '~/redux/actions/purchased-action';
const { Option } = Select;

interface ModalProps {
    visible: boolean;
    createdByUserId: string;
    onConfirm: (data: any) => void;
    onCancel: () => void;
    discountedItem: any;
    facilityId: string;
    userId: string;
    authUser: string;
}

const ChangeDiscountModal = ({
    visible,
    onConfirm,
    onCancel,
    discountedItem,
    facilityId,
    userId,
    authUser,
    createdByUserId,
}: ModalProps) => {
    console.log(discountedItem, 'discount Item');
    const [form] = Form.useForm();
    const [discountedValue, setDiscountedValue] = useState(0);
    const [errorMsg, setErrorMsg] = useState('');
    const dispatch = useDispatch();
    const selectedDiscountType = Form.useWatch('discountType', form);
    const customDiscountType = Form.useWatch('customDiscountType', form);
    const customDiscount = Form.useWatch('customDiscount', form);
    const quantity = Form.useWatch('quantity', form);
    const PromotionLabel = Form.useWatch('promotionLabel', form);
    const [label, setLabel] = useState<any>();

    const getItemType = (it: any) => {
        if (it?.itemType) return it.itemType;
        if (it?.isVoucher) return 'voucher';
        if (it?.isProduct) return 'product';
        if (it?.isPackage) return 'service';
        if (it?.isCustomPackage) return 'custom_package';
        return 'service';
    };

    const getItemId = (it: any, type: string) =>
        type === 'product' ? it?.productId : it?._id;

    const validateCart = async (
        discountType: string,
        customDiscount?: { type: string; value: number }
    ) => {
        try {
            console.log(discountedItem, 'discounted item');
            setErrorMsg('');
            const itemType = getItemType(discountedItem);
            const itemId = getItemId(discountedItem, itemType);

            const payload = {
                facilityId,
                userId,
                items: [
                    {
                        itemType,
                        itemId: itemId,
                        quantity,
                        discountedBy: createdByUserId,

                        variantId: discountedItem?.isProduct
                            ? discountedItem?.variantId
                            : null,
                        promotionId:
                            discountType !== 'custom'
                                ? discountType
                                : undefined,
                        ...(discountType === 'custom' && {
                            discount: {
                                type: customDiscount?.type,
                                value: Number(customDiscount?.value),
                                // discountedBy: authUser,
                                discountedBy: createdByUserId,
                            },
                        }),
                    },
                ],
            };
            console.log(payload, 'payload');
            const response = await dispatch(cartValidate(payload)).unwrap();
            setDiscountedValue(response?.data?.items?.[0]?.discountAmount || 0);
            return { success: true };
        } catch (error: any) {
            // setErrorMsg(
            //     error?.data?.validationErrors?.[0] ||
            //         'Invalid discount configuration'
            // );
            return { success: false };
        }
    };

    const validateCartDebounced = useDebounce(
        (
            discountType: string,
            customDiscount?: { type: string; value: number }
        ) => {
            validateCart(discountType, customDiscount);
        },
        500
    );

    const fetchDiscountList = (search = '', page: number) => {
        return dispatch(
            DiscountListOnPricePage({
                pricingId: discountedItem?._id,
                currentPage: page,
                pageSize: 10,
                search,
                isActive: true,
            })
        )
            .unwrap()
            .then((response: any) => {
                return response?.data?.data?.map((item: any) => ({
                    ...item,
                    discountValue: item.value,
                    value: item._id,
                    label: item?.name,
                    id: item._id,
                    promotionLabel: item?.promotionLabel,
                }));
            });
    };

    useEffect(() => {
        if (!visible || !discountedItem) return;

        const {
            name = '',
            quantity = 1,
            price = 0,
            discountedValue = 0,
            promotion,
            discount,
        } = discountedItem;

        const hasPromotion = Boolean(promotion?._id);
        console.log(promotion, 'promotion');
        const customDiscountType = hasPromotion
            ? promotion?.type
            : discount?.type || 'Percentage';
        const unitCustomDiscount = hasPromotion
            ? promotion?.value
            : discount?.value || 0;

        form.setFieldsValue({
            description: name,
            quantity,
            unitPrice: Number(price).toFixed(2),
            customDiscount: unitCustomDiscount,
            customDiscountType,
            discountType: hasPromotion ? promotion._id : 'custom',
        });
        setDiscountedValue(quantity * discountedValue);
        if (promotion?.promotionLabel) {
            setLabel({
                visible: true,
                labelName: promotion?.promotionLabel,
            });
        }
    }, [visible, discountedItem, form]);

    useEffect(() => {
        if (
            selectedDiscountType === 'custom' &&
            customDiscountType &&
            customDiscount !== undefined
        ) {
            validateCartDebounced('custom', {
                type: customDiscountType,
                value: Number(customDiscount),
            });
        }
    }, [customDiscountType, customDiscount]);

    const calculateTotal = () => {
        const subtotal = discountedItem?.quantity * discountedItem?.price;
        return Math.max(subtotal - discountedValue, 0);
    };

    const handleFinish = () => {
        onConfirm({
            discountedValue: discountedValue / quantity,
            ...(selectedDiscountType === 'custom'
                ? {
                      discount: {
                          type: customDiscountType,
                          value: Number(customDiscount),
                          discountedBy: createdByUserId,
                      },
                  }
                : {
                      promotion: {
                          _id: selectedDiscountType,
                          type: customDiscountType,
                          discountedBy: createdByUserId,
                          value: Number(customDiscount / quantity),
                          promotionLabel: PromotionLabel,
                          promotionLabelKey: label?.labelName,
                      },
                  }),
        });
        form.resetFields();
        setErrorMsg('');
    };

    const handleCancel = () => {
        setDiscountedValue(0);
        setErrorMsg('');
        form.resetFields();
        onCancel();
    };

    useEffect(() => {
        form.validateFields(['customDiscount']);
    }, [customDiscountType]);

    return (
        <ConfigProvider
            theme={{
                components: {
                    Form: {
                        verticalLabelMargin: -5,
                    },
                },
            }}
        >
            <Modal
                title={<div className="border-b-2">Edit Transaction Item</div>}
                open={visible}
                footer={false}
                onCancel={handleCancel}
                className="w-[45%]"
            >
                <Form form={form} onFinish={handleFinish} layout="vertical">
                    <div className="p-6">
                        <Form.Item
                            name="description"
                            label="Description"
                            className="mb-5"
                        >
                            <Input
                                disabled
                                className="w-full rounded-lg border p-3"
                            />
                        </Form.Item>

                        <div className="mb-5 flex gap-4">
                            <Form.Item
                                name="quantity"
                                label="Quantity"
                                className="mb-0 flex-1"
                            >
                                <Input
                                    disabled
                                    type="number"
                                    className="w-full rounded-lg border p-3"
                                />
                            </Form.Item>
                            <Form.Item
                                name="unitPrice"
                                label="Unit Price"
                                className="mb-0 flex-1"
                            >
                                <Input
                                    disabled
                                    type="number"
                                    className="w-full rounded-lg border p-3"
                                />
                            </Form.Item>
                        </div>

                        <div className="mb-5 flex gap-4">
                            <div className="w-[50%]">
                                <Form.Item
                                    name="discountType"
                                    label="Discount"
                                    className="mb-0 flex-1"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select a discount',
                                        },
                                    ]}
                                >
                                    <InfiniteScrollSelect
                                        className="border-b border-[#d1d5db]"
                                        fetchOptions={fetchDiscountList}
                                        onChange={(value: any, option) => {
                                            console.log(option, 'option');
                                            form.setFieldsValue({
                                                customDiscount:
                                                    option?.discountValue *
                                                        discountedItem?.quantity ||
                                                    0,
                                                customDiscountType:
                                                    option?.type || 'Flat',
                                                discountType: value,
                                            });
                                            validateCartDebounced(value);
                                            if (option?.promotionLabel) {
                                                console.log(
                                                    'sksdfksdkfkdsfkd',
                                                    option?.promotionLabel
                                                );
                                                setLabel({
                                                    visible: true,
                                                    labelName:
                                                        option?.promotionLabel,
                                                });
                                            } else {
                                                setLabel(null);
                                            }
                                        }}
                                        defaultOptions={[
                                            {
                                                value: 'custom',
                                                label: 'Custom',
                                            },
                                        ]}
                                        placeholder="Select Discount"
                                    />
                                </Form.Item>
                            </div>
                            <div className="flex w-[50%] flex-col">
                                <label className="text-[13px] font-medium text-[#1a3353]">
                                    Custom Discount
                                    <span className="ps-2 text-gray-500">
                                        (Flat ₹ or %)
                                    </span>
                                </label>
                                <div className="flex gap-2">
                                    <Form.Item
                                        name="customDiscountType"
                                        className="mb-0 w-1/3"
                                    >
                                        <Select
                                            disabled={
                                                selectedDiscountType !==
                                                'custom'
                                            }
                                        >
                                            <Option value="Flat">
                                                Flat (₹)
                                            </Option>
                                            <Option value="Percentage">
                                                Percentage (%)
                                            </Option>
                                        </Select>
                                    </Form.Item>
                                    <Form.Item
                                        name="customDiscount"
                                        className="mb-0 flex-1"
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (
                                                        value === undefined ||
                                                        value === null ||
                                                        value === ''
                                                    )
                                                        return Promise.reject(
                                                            'Please enter discount value'
                                                        );

                                                    const numberValue =
                                                        Number(value);
                                                    if (numberValue < 0)
                                                        return Promise.reject(
                                                            'Discount cannot be negative'
                                                        );

                                                    if (
                                                        customDiscountType ===
                                                            'Percentage' &&
                                                        numberValue > 100
                                                    )
                                                        return Promise.reject(
                                                            'Percentage cannot exceed 100'
                                                        );

                                                    if (
                                                        customDiscountType ===
                                                            'Flat' &&
                                                        numberValue >
                                                            discountedItem?.price
                                                    )
                                                        return Promise.reject(
                                                            'Flat discount exceeds unit price'
                                                        );

                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        <Input
                                            disabled={
                                                selectedDiscountType !==
                                                'custom'
                                            }
                                            type="number"
                                            placeholder="Enter value"
                                            min={0}
                                            step="any"
                                            onChange={(e) => {
                                                const value =
                                                    parseFloat(
                                                        e.target.value
                                                    ) || 0;
                                                form.setFieldsValue({
                                                    customDiscount: value,
                                                });
                                            }}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        </div>

                        {errorMsg && (
                            <div className="mb-4 text-sm text-red-600">
                                {errorMsg}
                            </div>
                        )}
                        {label?.visible && (
                            <>
                                <Form.Item
                                    name="promotionLabel"
                                    label={label?.labelName}
                                    className="mb-0 flex-1"
                                >
                                    <Input
                                        type="text"
                                        className="w-full rounded-lg border p-3"
                                    />
                                </Form.Item>
                            </>
                        )}
                        <Form.Item noStyle shouldUpdate>
                            {() => (
                                <div className="mt-8 border-t border-gray-200 pt-5">
                                    <div className="mb-2 flex items-center justify-between">
                                        <span className="text-lg font-semibold text-gray-800">
                                            Total
                                        </span>
                                        <span className="text-2xl font-bold text-gray-900">
                                            ₹ {calculateTotal()?.toFixed(2)}
                                        </span>
                                    </div>
                                    {discountedValue > 0 && (
                                        <div className="text-xl text-green-600">
                                            Discount applied: ₹{' '}
                                            {discountedValue?.toFixed(2)}
                                        </div>
                                    )}
                                </div>
                            )}
                        </Form.Item>
                    </div>

                    <div className="mt-1 flex justify-end px-6 pb-6">
                        <Button
                            className="border-1 border-[#1A3353]"
                            onClick={handleCancel}
                        >
                            Cancel
                        </Button>

                        <Form.Item shouldUpdate noStyle>
                            {() => {
                                const hasErrors = form
                                    .getFieldsError()
                                    .some(({ errors }) => errors.length > 0);

                                return (
                                    <Button
                                        htmlType="submit"
                                        className="bg-purpleLight text-white"
                                        style={{ marginLeft: 10 }}
                                        disabled={!!errorMsg || hasErrors}
                                    >
                                        Confirm
                                    </Button>
                                );
                            }}
                        </Form.Item>
                    </div>
                </Form>
            </Modal>
        </ConfigProvider>
    );
};

export default ChangeDiscountModal;
