import { Button, Checkbox, ConfigProvider, Input, Modal, Table } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';

interface Props {
    visible: boolean;
    handleClose: () => void;
    handleConfirm: (voucherData: any[]) => void;
    defaultCoupons: any[];
    selectedVoucherIds: string[];
    voucherData: any[];
    fetchCoupons: (
        page: number,
        search: string
    ) => Promise<{ coupons: any[]; totalPagesFromAPI: number } | undefined>;
}

const PAGE_SIZE = 10;

const CouponModal = ({
    visible,
    handleClose,
    handleConfirm,
    defaultCoupons,
    fetchCoupons,
    selectedVoucherIds,
    voucherData,
}: Props) => {
    const [search, setSearch] = useState('');
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [selectedCoupons, setSelectedCoupons] = useState<any[]>(voucherData);
    const [selectedCouponIds, setSelectedCouponIds] =
        useState<string[]>(selectedVoucherIds);
    const [hasMore, setHasMore] = useState(true);
    const [couponList, setCouponList] = useState(defaultCoupons || []);
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    const debouncedRequest = useDebounce((callback) => callback(), 300);

    const dispatch = useAppDispatch();

    /** Fetch coupons */
    const fetchCouponData = async (page: number, reset = false) => {
        try {
            const result: any = await fetchCoupons(page, search);
            if (!result) return;

            const { coupons, totalPagesFromAPI } = result;
            setCouponList((prev) => (reset ? coupons : [...prev, ...coupons]));
            setTotalPages(totalPagesFromAPI);
            setHasMore(page < totalPagesFromAPI);
        } catch (err) {
            console.error('Failed to fetch vouchers', err);
        }
    };

    /** Initial + search fetch */
    useEffect(() => {
        setPage(1);
        debouncedRequest(() => {
            fetchCouponData(1, true);
        });
    }, [search]);

    /** Infinite scroll handler */
    const handleScroll = () => {
        const container = scrollContainerRef.current;
        if (!container || !hasMore) return;

        const scrollBottom = container.scrollTop + container.clientHeight;
        if (scrollBottom + 100 >= container.scrollHeight) {
            const nextPage = page + 1;
            if (nextPage <= totalPages) {
                setPage(nextPage);
                fetchCouponData(nextPage);
            }
        }
    };

    const handleCheckboxChange = (record: any) => {
        setSelectedCouponIds((prevIds) => {
            const updatedIds = new Set(prevIds);
            const isSelected = updatedIds.has(record._id);

            if (isSelected) {
                updatedIds.delete(record._id);
                setSelectedCoupons((prevCoupons) =>
                    prevCoupons.filter((item) => item._id !== record._id)
                );
            } else {
                updatedIds.add(record._id);
                setSelectedCoupons((prevCoupons) => [
                    ...prevCoupons,
                    { ...record, isVoucherApply: true },
                ]);
            }
            return Array.from(updatedIds);
        });
    };

    const columns = [
        {
            title: 'Sr. No.',
            key: 'srNo',
            width: 80,
            render: (_: any, record: any, index: number) => (
                <ConfigProvider
                    theme={{
                        token: {
                            colorPrimary: 'primary',
                        },
                    }}
                >
                    <Checkbox
                        className=""
                        checked={selectedCouponIds?.includes(record._id)}
                    >
                        {/* {(page - 1) * PAGE_SIZE + index + 1}
                         */}
                        {index + 1}
                    </Checkbox>
                </ConfigProvider>
            ),
        },
        { title: 'Voucher Name', dataIndex: 'name', key: 'name' },
        { title: 'Price', dataIndex: 'price', key: 'price' },
        {
            title: 'Voucher Date',
            key: 'couponDate',
            render: (record: any) =>
                dayjs(record.startDate).format('ddd, MMM D, YYYY, h:mm A'),
        },
    ];

    console.log('Coupon List ------------------', couponList);

    return (
        <Modal
            open={visible}
            onCancel={handleClose}
            title="Active Voucher"
            centered
            width={1000}
            footer={[
                <Button
                    key="cancel"
                    className="mt-5 h-12 w-32 border border-[#1A3353] text-xl"
                    onClick={handleClose}
                >
                    Cancel
                </Button>,
                <Button
                    key="confirm"
                    type="primary"
                    className="mt-5 h-12 w-32 bg-purpleLight text-xl text-white"
                    onClick={() => handleConfirm(selectedCoupons)}
                >
                    Confirm
                </Button>,
            ]}
        >
            <Input
                placeholder="Search"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                style={{ marginBottom: 16 }}
            />
            <ConfigProvider
                theme={{
                    components: { Table: { cellPaddingBlock: 6 } },
                }}
            >
                <div
                    style={{ height: '30vh', overflowY: 'auto' }}
                    onScroll={handleScroll}
                    ref={scrollContainerRef}
                >
                    <Table
                        columns={columns}
                        dataSource={couponList}
                        pagination={false}
                        rowKey="purchaseId"
                        scroll={{ x: 600 }}
                        className="mb-5"
                        onRow={(record: any) => ({
                            onClick: () => handleCheckboxChange(record),
                        })}
                    />
                </div>
            </ConfigProvider>
        </Modal>
    );
};

export default CouponModal;
