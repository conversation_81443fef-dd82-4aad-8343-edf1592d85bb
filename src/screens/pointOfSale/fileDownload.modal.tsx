import React, { useState } from 'react';
import { Modal, Radio, Button } from 'antd';

interface DownloadReportModalProps {
    open: boolean;
    loading: boolean;
    onCancel: () => void;
    onDownload: (format: 'pdf' | 'xlsx') => void;
}

const DownloadReportModal: React.FC<DownloadReportModalProps> = ({
    open,
    onCancel,
    onDownload,
    loading = false,
}) => {
    const [selectedFormat, setSelectedFormat] = useState<'pdf' | 'xlsx'>('pdf');

    const handleDownload = () => {
        onDownload(selectedFormat);
    };

    return (
        <Modal
            title="Download Report as"
            open={open}
            onCancel={onCancel}
            footer={null}
            centered
            maskClosable={false}
        >
            <div className="items-left flex flex-col gap-2">
                <Radio.Group
                    value={selectedFormat}
                    onChange={(e) => setSelectedFormat(e.target.value)}
                    className="items-left flex flex-row gap-6"
                >
                    <Radio value="pdf">PDF</Radio>

                    <Radio value="xlsx">XLSX (Excel Sheet)</Radio>
                </Radio.Group>

                <div className="mt-8 flex gap-4">
                    <Button onClick={onCancel}>Cancel</Button>
                    <Button
                        className="fw-500 w-[100px] rounded-2xl bg-purpleLight py-3 text-xl text-white"
                        type="primary"
                        onClick={handleDownload}
                        loading={loading}
                    >
                        Download
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default DownloadReportModal;
