import { Config<PERSON>rovider, Switch, Select } from 'antd';
import { useEffect, useState } from 'react';
import CommonTable from '~/components/common/commonTable';
import AddPaymentModal from './add-payment-modal';
import clsx from 'clsx';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    AllPaymentMethodList,
    AddedPaymentMethodList,
    updatePaymentStatus,
} from '~/redux/actions/payment-method.action';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';

const { Option } = Select;

const PaymentMethodListing = () => {
    const [isAddPaymentModalVisible, setIsAddPaymentModalVisible] =
        useState(false);
    const [selectedFacilityId, setSelectedFacilityId] = useState<string | null>(
        null
    );

    const showPaymentModal = () => {
        setIsAddPaymentModalVisible(true);
    };

    const hidePaymentModal = () => {
        setIsAddPaymentModalVisible(false);
    };
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState<boolean>(false);
    const [currentRecord, setCurrentRecord] = useState<any>(null);
    const dispatch = useAppDispatch();

    useEffect(() => {
        dispatch(FacilitiesList({ page: 1, pageSize: 30 }))
            .unwrap()
            .then((response: any) => {
                console.log(response?.data?.data?.list?.[0]._id, 'response');
                if (response?.data?.data?.list?.length > 0) {
                    setSelectedFacilityId(response?.data?.data?.list?.[0]._id);
                    dispatch(
                        AllPaymentMethodList({
                            facilityId: response?.data?.data?.list?.[0]._id,
                        })
                    ).unwrap();
                }
            });
    }, [dispatch]);
    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        addPaymenthodsList: state.paymentmethod_store.addPaymenthodsList,
    }));
    console.log(store.addPaymenthodsList, 'lksajdashkdh');
    useEffect(() => {
        if (selectedFacilityId) {
            dispatch(
                AddedPaymentMethodList({
                    facilityId: selectedFacilityId,
                })
            );
        }
    }, [selectedFacilityId]);
    const handleStatusChange = (record: any) => {
        setCurrentRecord(record);
        setConfirmationModalVisible(true);
    };
    const Columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: 'Status',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    handleBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Switch
                            id="swtich-off"
                            className={clsx(
                                'rounded-full transition-colors',
                                record.isActive
                                    ? 'bg-switch-on'
                                    : 'bg-switch-off'
                            )}
                            checkedChildren="ON"
                            unCheckedChildren="OFF"
                            checked={record.isActive || false}
                            onChange={() => handleStatusChange(record)}
                        />
                    </ConfigProvider>
                );
            },
        },
    ];

    const handleConfirmStatusChange = async () => {
        if (selectedFacilityId && currentRecord._id) {
            const apiPayload = {
                facilityId: selectedFacilityId,
                paymentMethodId: currentRecord._id,
                isActive: !currentRecord.isActive,
            };
            await dispatch(updatePaymentStatus(apiPayload));

            dispatch(
                AddedPaymentMethodList({ facilityId: selectedFacilityId })
            );
        }
        setConfirmationModalVisible(false);
    };

    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
    };
    return (
        <div>
            <div className="mt-6">
                <Select
                    placeholder="Select the branch name"
                    onChange={(value: any) => setSelectedFacilityId(value)}
                    value={selectedFacilityId}
                >
                    {store.facilityList?.map((facility: any) => (
                        <Option key={facility._id} value={facility._id}>
                            {facility.facilityName}
                        </Option>
                    ))}
                </Select>
                <CommonTable
                    className="min-w-min"
                    columns={Columns}
                    openModal={showPaymentModal}
                    dataSource={store.addPaymenthodsList}
                    bulkAction={false}
                    backButton={true}
                    heading="Payment Methods"
                    // addNewLink="/setting/create-announcementsmm"
                    addNewTitle="Add New"
                    addNewModal={true}
                />
            </div>
            {isAddPaymentModalVisible && (
                <AddPaymentModal
                    visible={isAddPaymentModalVisible}
                    onClose={hidePaymentModal}
                    selectedFacilityId={selectedFacilityId}
                    facilityList={store.facilityList}
                />
            )}
            {!!confirmationModalVisible && (
                <CommonConfirmationModal
                    visible={confirmationModalVisible}
                    onConfirm={handleConfirmStatusChange}
                    onCancel={handleCancelStatusChange}
                    message="Are you sure you want to change the status?"
                />
            )}
        </div>
    );
};

export default PaymentMethodListing;
