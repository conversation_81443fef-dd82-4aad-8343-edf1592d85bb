import React, { useEffect, useState } from 'react';
import { Form, Input, Button, ConfigProvider } from 'antd';
import 'antd/dist/reset.css';
import Alertify from '~/services/alertify';
import { useDispatch } from 'react-redux';
import { useLocation } from 'wouter';
import { useLoader } from '~/hooks/useLoader';
import { AppDispatch } from '~/redux/store';
import { RoleType } from '~/types/enums';
import PasswordChecklist from '~/components/common/password-checklist';

const { Item: FormItem } = Form;

interface SignInValues {
    email: string;
    password: string;
}

const SignUp: React.FC = () => {
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const [password, setPassword] = useState<string>('');
    const [_, setLocation] = useLocation();
    const [loader, startLoader, endLoader] = useLoader();

    const onFinish = (values: SignInValues) => {
        console.log('Success:', values);
        startLoader();
        const data = {
            email: values.email,
            type: 'email',
            password: values.password,
        };
        // dispatch(LoginUser(data))
        //     .unwrap()
        //     .then((res: any) => {
        //         console.log('Res----------', res);
        //         if (res?.status === 200 || res?.status === 201) {
        //             Alertify.success('Logged in successfully');
        //             if (res?.data?.data?.user?.role === RoleType.ORGANIZATION) {
        //                 setLocation('/setup-checklist');
        //             } else if (
        //                 res?.data?.data?.user?.role === RoleType.SUPER_ADMIN
        //             ) {
        //                 setLocation('/organizations');
        //             } else {
        //                 setLocation('/dashboard');
        //             }
        //         }
        //     })
        //     .finally(endLoader);
    };

    return (
        <div className="lg:flex">
            <div
                className="w-1/2 @sm:hidden"
                style={{
                    background: 'linear-gradient(to bottom, #5B2F93, #8143D1)',
                }}
            >
                <div
                    className="fixed top-0 flex h-full self-center lg:w-[50%]"
                    style={{
                        background:
                            'radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%) center no-repeat',
                    }}
                >
                    <img
                        src="/icons/hopfit-logo.svg"
                        alt="Sign In"
                        className="mx-auto w-[50%]"
                    />
                </div>
            </div>
            <div className=" lg:w-[50%] lg:px-40 lg:py-16 @sm:h-screen  @sm:px-10 ">
                <div className="flex h-full w-full flex-col justify-center  ">
                    <div className="lg:hidden">
                        <img
                            src="menuIcons/logo.svg"
                            alt="Sign In"
                            className="mx-auto w-[40%]  @sm:py-10"
                        />
                    </div>
                    <h2 className="text-4xl font-bold text-[#1A3353] lg:mb-6 @sm:py-8 @sm:text-center">
                        Setup your Account
                    </h2>

                    <ConfigProvider
                        theme={{
                            components: {
                                Input: {
                                    activeBg: 'transparent',
                                    hoverBorderColor: '#E6EBF1',
                                },
                            },
                        }}
                    >
                        <Form
                            form={form}
                            name="sign_in"
                            onFinish={onFinish}
                            layout="vertical"
                            // className="space-y-4"
                        >
                            <FormItem
                                label={
                                    <p className="text-[#1A3353]">First Name</p>
                                }
                                className="@sm:w-full"
                                name="firstName"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please enter your first name!',
                                    },
                                ]}
                            >
                                <div className="">
                                    <Input
                                        name="firstName"
                                        type="text"
                                        placeholder="Enter your first name"
                                        className="h-16 rounded-md"
                                    />
                                </div>
                            </FormItem>
                            <FormItem
                                label={
                                    <p className="text-[#1A3353]">Last Name</p>
                                }
                                className="@sm:w-full"
                                name="lastName"
                                rules={[
                                    {
                                        required: false,
                                        message: 'Please enter your last Name!',
                                    },
                                ]}
                            >
                                <div className="">
                                    <Input
                                        name="lastName"
                                        type="text"
                                        placeholder="Enter your last name"
                                        className="h-16 rounded-md"
                                    />
                                </div>
                            </FormItem>
                            <FormItem
                                label={
                                    <p className="text-[#1A3353]">
                                        Email Address
                                    </p>
                                }
                                className="@sm:w-full"
                                name="email"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter your email!',
                                    },
                                    {
                                        type: 'email',
                                        message:
                                            'The input is not valid E-mail!',
                                    },
                                ]}
                            >
                                <div className="">
                                    <Input
                                        name="email"
                                        type="email"
                                        placeholder="Enter your email"
                                        className="h-16 rounded-md"
                                    />
                                </div>
                            </FormItem>
                            <FormItem
                                label={
                                    <p className="text-[#1A3353]">Password</p>
                                }
                                name="password"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter your password!',
                                    },
                                ]}
                            >
                                <div className="">
                                    <Input.Password
                                        onChange={(e) =>
                                            setPassword(e.target.value)
                                        }
                                        placeholder="Enter your password"
                                        className="h-16 rounded-md"
                                    />
                                </div>
                            </FormItem>
                            <FormItem
                                label={
                                    <p className="text-[#1A3353]">
                                        Confirm Password
                                    </p>
                                }
                                name="confirmPassword"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please enter your confirm password!',
                                    },
                                ]}
                            >
                                <div className="">
                                    <Input.Password
                                        placeholder="Enter your confirm password"
                                        className="h-16 rounded-md"
                                    />
                                </div>
                            </FormItem>
                            <div className="my-4">
                                <PasswordChecklist password={password} />
                            </div>
                            <FormItem>
                                <Button
                                    type="primary"
                                    loading={loader}
                                    htmlType="submit"
                                    className="mt-10  w-full border-0 bg-primary py-8      text-xl  text-[#ffffff] @sm:w-full"
                                >
                                    Sign Up
                                </Button>
                            </FormItem>
                            <FormItem>
                                <Button
                                    type="primary"
                                    htmlType="reset"
                                    onClick={() => setLocation('/signin')}
                                    className="mt-2  w-full border-1 border-[#1A3353] bg-[white] py-8 text-xl  text-[#1A3353] @sm:w-full"
                                >
                                    Cancel
                                </Button>
                            </FormItem>
                            {/* <FormItem>
                                <a
                                    href="/reset-password"
                                    className="flex justify-end text-[#455560]"
                                >
                                    Forgot password?
                                </a>
                            </FormItem> */}
                        </Form>
                    </ConfigProvider>
                </div>
            </div>
        </div>
    );
};

export default SignUp;
