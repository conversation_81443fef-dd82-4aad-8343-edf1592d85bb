import React, { useEffect, useState } from 'react';
import {
    Form,
    Input,
    Button,
    ConfigProvider,
    Typography,
    GetProps,
} from 'antd';
import 'antd/dist/reset.css';
import Alertify from '~/services/alertify';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import {
    ClearForgetEmail,
    SetForgetEmail,
    SetOtpVerificationCode,
} from '~/redux/slices/auth-slice';
import { ForgetPassword, VerifyOtp } from '~/redux/actions/auth-actions';
import { AppDispatch } from '~/redux/store';

type OTPProps = GetProps<typeof Input.OTP>;

const { Title } = Typography;

const { Item: FormItem } = Form;

interface SignInValues {
    otp: string;
}

const VerifyOtpScreen: React.FC = () => {
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const [_, setLocation] = useLocation();
    const [timeLeft, setTimeLeft] = useState(25);
    const [otp, setOtp] = useState('');
    const [isTimerActive, setIsTimerActive] = useState(true);
    const forgetEmail = useSelector(
        (state: any) => state.auth_store.forgetEmail
    );
    const organizationId = useSelector(
        (state: any) => state.auth_store.organizationId
    );

    const onChange: OTPProps['onChange'] = (text) => {
        console.log('onChange:', text);
        setOtp(text);
    };

    const sharedProps: OTPProps = {
        onChange,
    };

    const onFinish = (values: SignInValues) => {
        console.log('Success:', values);
        const data = {
            email: forgetEmail,
            type: 'email',
            otp: Number(otp),
            forgotPasswordRequest: true,
        };
        console.log('Data---------------', data);
        dispatch(VerifyOtp({data, organizationId})).then((res: any) => {
            console.log('Res----------', res);
            if (res?.payload?.status === 200 || res?.payload?.status === 201) {
                Alertify.success('Otp verified successfully');
                dispatch(
                    SetOtpVerificationCode(
                        res?.payload?.data?.data?.otpVerificationCode
                    )
                );
                if (res?.payload?.data?.data?.forgotPasswordRequest) {
                    setLocation('/forget-setpassword');
                }
            }
        });
    };

    useEffect(() => {
        let timer: number | undefined;
        if (isTimerActive && timeLeft > 0) {
            timer = window.setInterval(() => {
                setTimeLeft((prevTime) => {
                    if (prevTime <= 1) {
                        setIsTimerActive(false);
                        return 0;
                    }
                    return prevTime - 1;
                });
            }, 1000);
        }

        return () => {
            clearInterval(timer);
        };
    }, [isTimerActive, timeLeft]);

    const formatTime = (time: number) => {
        const minutes = String(Math.floor(time / 60)).padStart(2, '0');
        const seconds = String(time % 60).padStart(2, '0');
        return `${minutes}:${seconds}`;
    };

    const handleResendOtp = () => {
        const data = {
            email: forgetEmail,
            type: 'email',
        };
        dispatch(ForgetPassword({ data, organizationId })).then((res: any) => {
            console.log('Res----------', res);
            if (res?.payload?.status === 200 || res?.payload?.status === 201) {
                Alertify.success('Otp Sent on your email');
                setTimeLeft(25);
                setIsTimerActive(true);
            }
        });
    };

    return (
        <div className="h-screen lg:flex">
            <div
                className="w-1/2 @sm:hidden"
                style={{
                    background: 'linear-gradient(to bottom, #5B2F93, #8143D1)',
                }}
            >
                <div
                    className="flex h-full w-full self-center"
                    style={{
                        background:
                            'radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%) center no-repeat',
                    }}
                >
                    <img
                        // src="/assets/knox-logo.png"
                        src="/icons/hopfit-logo.svg"
                        alt="Sign In"
                        className="mx-auto w-[50%]"
                    />
                </div>
            </div>
            <div className=" lg:w-1/2 lg:px-[6%] @sm:h-screen">
                <div className="flex h-full w-full flex-col justify-center  p-6">
                    <div className="lg:hidden">
                        <img
                            src="/assets/knox-logo.png"
                            alt="Sign In"
                            className="mx-auto w-[40%]  @sm:pb-10"
                        />
                    </div>
                    <h2 className="text-4xl font-bold text-[#455560] @sm:text-center">
                        Verification
                    </h2>
                    <h2 className="text-xl text-[#455560] lg:w-[77%] lg:pb-6 lg:pt-5 @sm:pt-6 @sm:text-center @sm:text-2xl">
                        Enter your 6 digits code that you received on your
                        email.
                    </h2>
                    <ConfigProvider
                        theme={{
                            components: {
                                Input: {
                                    activeBg: 'transparent',
                                },
                            },
                        }}
                    >
                        <Form
                            form={form}
                            name="sign_in"
                            onFinish={onFinish}
                            layout="vertical"
                            className=""
                        >
                            <FormItem
                                label=""
                                name="otp"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter otp',
                                    },
                                ]}
                            >
                                <div className="flex w-full flex-row justify-center @sm:pt-8">
                                    <Input.OTP length={6} {...sharedProps} />
                                </div>
                            </FormItem>
                            <div className=" -mt-4 flex justify-between">
                                <span>{formatTime(timeLeft)}</span>
                            </div>
                            <FormItem>
                                <Button
                                    type="primary"
                                    htmlType="submit"
                                    className="mt-10 w-full border-0 bg-primary p-8 text-xl font-semibold text-[#ffffff] @sm:w-full"
                                >
                                    Continue
                                </Button>
                            </FormItem>
                        </Form>
                    </ConfigProvider>
                    <div className="mt-4 flex justify-center">
                        If you didn't receive a code!{' '}
                        {!isTimerActive && (
                            <span
                                onClick={handleResendOtp}
                                className="ml-2 cursor-pointer text-[#8143D1]"
                            >
                                Resend
                            </span>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default VerifyOtpScreen;
