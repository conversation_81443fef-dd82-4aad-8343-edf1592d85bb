import React, { useEffect } from 'react';
import { Form, Input, Button, ConfigProvider } from 'antd';
import 'antd/dist/reset.css';
import Alertify from '~/services/alertify';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import { getQueryParams } from '~/utils/getQueryParams';
import { ResetPassword, SetNewPassword } from '~/redux/actions/auth-actions';
import { useLoader } from '~/hooks/useLoader';
import {
    ClearForgetEmail,
    ClearOrganizationId,
    ClearOtpVerificationCode,
} from '~/redux/slices/auth-slice';
import { AppDispatch } from '~/redux/store';

const { Item: FormItem } = Form;

interface SignInValues {
    password: string;
    confirmPassword: string;
}

const ForgetSetPasswordScreen: React.FC = () => {
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const [_, setLocation] = useLocation();
    const [loader, startLoader, endLoader] = useLoader();

    const { forgetEmail, otpVerificationCode, organizationId } = useSelector(
        (state: any) => state.auth_store
    );

    const onFinish = (values: SignInValues) => {
        console.log('Success:', values);
        startLoader();
        const data = {
            type: 'email',
            email: forgetEmail,
            password: values.password,
            confirmPassword: values.confirmPassword,
            otpVerificationCode: otpVerificationCode,
        };
        dispatch(ResetPassword({ data, organizationId }))
            .unwrap()
            .then((res: any) => {
                console.log('Res----------', res);
                if (res?.status === 200 || res?.status === 201) {
                    setLocation('/signin');
                    dispatch(ClearForgetEmail());
                    dispatch(ClearOrganizationId());
                    dispatch(ClearOtpVerificationCode());
                }
            })
            .finally(endLoader);
    };

    return (
        // <div className="flex min-h-screen w-full items-center justify-center bg-gray-100">
        //     <div className="flex w-full overflow-hidden rounded-lg bg-white shadow-lg">
        //         <div className="flex w-1/2 items-center justify-center bg-[#8143d1]">
        //             <img
        //                 src="/assets/signin.png"
        //                 alt="Sign In"
        //                 className="max-h-full max-w-full object-contain p-8"
        //             />
        //         </div>
        //         <div className="flex w-1/2 items-center p-8 px-[6%] ">
        //             <div className="flex h-full w-full flex-col justify-center p-6">
        //                 <h2 className="mb-6 text-2xl font-bold text-[#455560]">
        //                     Set Password
        //                 </h2>
        //                 <h2 className="mb-6 text-xl font-bold text-[#455560]">
        //                     Choose a new password for your account
        //                 </h2>
        //                 <ConfigProvider
        //                     theme={{
        //                         components: {
        //                             Input: {
        //                                 activeBg: 'transparent',
        //                             },
        //                         },
        //                     }}
        //                 >
        //                     <Form
        //                         form={form}
        //                         name="sign_in"
        //                         onFinish={onFinish}
        //                         layout="vertical"
        //                         className="space-y-4"
        //                     >
        //                         <FormItem
        //                             label="Password"
        //                             name="password"
        //                             rules={[
        //                                 {
        //                                     required: true,
        //                                     message:
        //                                         'Please enter your password!',
        //                                 },
        //                             ]}
        //                         >
        //                             <div className="border-b-2 border-b-gray-200">
        //                                 <Input.Password
        //                                     variant="borderless"
        //                                     placeholder="Enter your password"
        //                                     className="rounded-md"
        //                                 />
        //                             </div>
        //                         </FormItem>
        //                         <FormItem
        //                             label="Confirm Password"
        //                             name="confirmPassword"
        //                             rules={[
        //                                 {
        //                                     required: true,
        //                                     message:
        //                                         'Please enter your confirm password!',
        //                                 },
        //                             ]}
        //                         >
        //                             <div className="border-b-2 border-b-gray-200">
        //                                 <Input.Password
        //                                     variant="borderless"
        //                                     placeholder="Enter your confirm password"
        //                                     className="rounded-md"
        //                                 />
        //                             </div>
        //                         </FormItem>
        //                         <FormItem>
        //                             <Button
        //                                 loading={loader}
        //                                 type="primary"
        //                                 htmlType="submit"
        //                                 className="text-14 mt-10 w-full border-0 bg-[#8143d1] p-8 font-semibold text-[#ffffff] @sm:w-full"
        //                             >
        //                                 Continue
        //                             </Button>
        //                         </FormItem>
        //                         {/* <FormItem>
        //                             <a
        //                                 href="/forgot-password"
        //                                 className="flex justify-end text-indigo-600 hover:text-indigo-700"
        //                             >
        //                                 Forgot your password?
        //                             </a>
        //                         </FormItem> */}
        //                     </Form>
        //                 </ConfigProvider>
        //             </div>
        //         </div>
        //     </div>
        // </div>
        <div className="h-screen lg:flex">
            <div
                className="w-1/2 @sm:hidden"
                style={{
                    background: 'linear-gradient(to bottom, #5B2F93, #8143D1)',
                }}
            >
                <div
                    className="flex h-full w-full self-center"
                    style={{
                        background:
                            'radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%) center no-repeat',
                    }}
                >
                    <img
                        // src="/assets/knox-logo.png"
                        src="/icons/hopfit-logo.svg"
                        alt="Sign In"
                        className="mx-auto w-[50%]"
                    />
                </div>
            </div>
            <div className="flex w-1/2 items-center p-8 px-[6%] ">
                <div className="flex h-full w-full flex-col justify-center p-6">
                    <h2 className="mb-6 text-2xl font-bold text-[#455560]">
                        Set Password
                    </h2>
                    <h2 className="mb-6 text-xl font-bold text-[#455560]">
                        Choose a new password for your account
                    </h2>
                    <ConfigProvider
                        theme={{
                            components: {
                                Input: {
                                    activeBg: 'transparent',
                                },
                            },
                        }}
                    >
                        <Form
                            form={form}
                            name="sign_in"
                            onFinish={onFinish}
                            layout="vertical"
                            className="space-y-4"
                        >
                            <FormItem
                                label="Password"
                                name="password"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter your password!',
                                    },
                                ]}
                            >
                                <div className="border-b-2 border-b-gray-200">
                                    <Input.Password
                                        variant="borderless"
                                        placeholder="Enter your password"
                                        className="rounded-md"
                                    />
                                </div>
                            </FormItem>
                            <FormItem
                                label="Confirm Password"
                                name="confirmPassword"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please enter your confirm password!',
                                    },
                                ]}
                            >
                                <div className="border-b-2 border-b-gray-200">
                                    <Input.Password
                                        variant="borderless"
                                        placeholder="Enter your confirm password"
                                        className="rounded-md"
                                    />
                                </div>
                            </FormItem>
                            <FormItem>
                                <Button
                                    loading={loader}
                                    type="primary"
                                    htmlType="submit"
                                    className="text-14 mt-10 w-full border-0 bg-[#8143d1] p-8 font-semibold text-[#ffffff] @sm:w-full"
                                >
                                    Continue
                                </Button>
                            </FormItem>
                            {/* <FormItem>
                                <a
                                    href="/forgot-password"
                                    className="flex justify-end text-indigo-600 hover:text-indigo-700"
                                >
                                    Forgot your password?
                                </a>
                            </FormItem> */}
                        </Form>
                    </ConfigProvider>
                </div>
            </div>
        </div>
    );
};

export default ForgetSetPasswordScreen;
