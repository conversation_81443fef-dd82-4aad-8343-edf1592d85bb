import React, { useEffect, useState } from 'react';
import { Form, Input, Button, ConfigProvider, Select } from 'antd';
import 'antd/dist/reset.css';
import Alertify from '~/services/alertify';
import { useDispatch } from 'react-redux';
import { useLocation } from 'wouter';
import { ForgetPassword, organizationList } from '~/redux/actions/auth-actions';
import { SetForgetEmail, SetOrganizationId } from '~/redux/slices/auth-slice';
import { AppDispatch } from '~/redux/store';

const { Item: FormItem } = Form;

interface SignInValues {
    email: string;
}

const ForgetPasswordScreen: React.FC = () => {
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const [_, setLocation] = useLocation();
    const [organizationOptions, setOrganizationOptions] = useState<any[]>([]);
    const [selectedOrgId, setSelectedOrgId] = useState<string | null>(null);
    const [orgFetching, setOrgFetching] = useState(false);

    const onFinish = (values: SignInValues) => {
        console.log('Success:', values);
        const data = {
            organizationId: selectedOrgId,
            email: values.email,
            type: 'email',
        };
        console.log('Data---------------', data);
        dispatch(ForgetPassword({ data, organizationId: selectedOrgId })).then(
            (res: any) => {
                console.log('Res----------', res);
                if (
                    res?.payload?.status === 200 ||
                    res?.payload?.status === 201
                ) {
                    Alertify.success('Otp Sent on your email');
                    dispatch(SetForgetEmail(values.email));
                    dispatch(SetOrganizationId(selectedOrgId));
                    setLocation('/verify-otp');
                }
            }
        );
    };

    const searchOrganizations = async (search: string) => {
        if (!search || search.length < 2) return;
        setOrgFetching(true);
        const payload = {
            search: search,
        };
        try {
            const res = await dispatch(organizationList({ payload })).unwrap();
            console.log('Res----------', res);
            setOrganizationOptions(
                res?.data?.data?.list?.map((org: any) => ({
                    label: org.organizationName,
                    value: org._id,
                })) || []
            );
        } catch (err) {
            Alertify.error('Failed to fetch organizations');
        } finally {
            setOrgFetching(false);
        }
    };

    return (
        <div className="h-screen lg:flex">
            <div
                className="w-1/2 @sm:hidden"
                style={{
                    background: 'linear-gradient(to bottom, #5B2F93, #8143D1)',
                }}
            >
                <div
                    className="flex h-full w-full self-center"
                    style={{
                        background:
                            'radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%) center no-repeat',
                    }}
                >
                    <img
                        // src="/assets/knox-logo.png"
                        src="/icons/hopfit-logo.svg"
                        alt="Sign In"
                        className="mx-auto w-[50%]"
                    />
                </div>
            </div>
            <div className=" lg:w-1/2 lg:px-[6%] @sm:h-screen">
                <div className="flex h-full w-full flex-col justify-center  p-6">
                    <div className="lg:hidden">
                        <img
                            src="menuIcons/logo_svg.svg "
                            alt="Sign In"
                            className="mx-auto w-[40%]  @sm:pb-10"
                        />
                    </div>
                    <h2 className=" text-4xl font-bold text-[#455560] @sm:text-center">
                        Forgot Password
                    </h2>
                    <h2 className="text-xl text-[#455560] lg:w-[77%] lg:pb-6 lg:pt-5 @sm:pt-3 @sm:text-center @sm:text-2xl">
                        Enter your email for the verification proccess,we will
                        send 6 digits code to your email.
                    </h2>
                    <ConfigProvider
                        theme={{
                            components: {
                                Input: {
                                    activeBg: 'transparent',
                                },
                            },
                        }}
                    >
                        <Form
                            form={form}
                            name="sign_in"
                            onFinish={onFinish}
                            layout="vertical"
                            className="@sm:py-10"
                        >
                            <FormItem
                                label={<p className="">Organization</p>}
                                className="@sm:w-full"
                                name="organizationId"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please select an organization!',
                                    },
                                ]}
                            >
                                <Select
                                    showSearch
                                    placeholder="Search and select organization"
                                    className="h-16 rounded-md"
                                    filterOption={false}
                                    onSearch={searchOrganizations}
                                    onChange={(value) =>
                                        setSelectedOrgId(value)
                                    }
                                    options={organizationOptions}
                                    loading={orgFetching}
                                />
                            </FormItem>
                            <FormItem
                                label={
                                    <p className="text-[#455560]">
                                        Email Address
                                    </p>
                                }
                                name="email"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter your email',
                                    },
                                    {
                                        type: 'email',
                                        message:
                                            'The input is not valid E-mail!',
                                    },
                                ]}
                            >
                                <div className="    ">
                                    <Input
                                        type="email"
                                        placeholder="Enter your email"
                                        className="h-16 rounded-md"
                                    />
                                </div>
                            </FormItem>
                            <FormItem>
                                <Button
                                    type="primary"
                                    htmlType="submit"
                                    className="w-full  border-0 bg-[#8143d1] py-10 text-xl     text-[#ffffff]   @sm:w-full"
                                >
                                    Continue
                                </Button>
                            </FormItem>
                        </Form>
                    </ConfigProvider>
                </div>
            </div>
        </div>
    );
};

export default ForgetPasswordScreen;
