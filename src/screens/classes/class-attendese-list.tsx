import { MoreOutlined } from '@ant-design/icons';
import { Button, Dropdown, Menu, Pagination, Table } from 'antd';
import Title from 'antd/es/typography/Title';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { getQueryParams } from '~/utils/getQueryParams';
import { useParams } from 'wouter';
import { useLoader } from '~/hooks/useLoader';

import {
    capitalizeFirstLetter,
    formatDate,
    formatTime,
} from '~/components/common/function';
import { navigate } from 'wouter/use-location';
import {
    CheckInButtonChip,
    PendingButtonChip,
} from '~/components/common/chip-component';
import {
    cancelEnrollClient,
    classCheckIn,
    classesParticipantList,
} from '~/redux/actions/class-action';
import AddCustomerClassModal from '~/screens/classes/add-customer-class-modal';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
function goBack() {
    window.history.back();
}

const columns = [
    {
        title: 'NAME',
        dataIndex: 'name',
        key: 'name',
        render: (text: string) => capitalizeFirstLetter(text),
    },
    {
        title: 'PHONE NO ',
        dataIndex: 'mobile',
        key: 'mobile',
    },
    {
        title: 'DATE',
        dataIndex: 'enrolledDate',
        key: 'enrolledDate',
        render: (text: string) => formatDate(text),
    },
    {
        title: 'TIME',
        dataIndex: 'enrolledDate',
        key: 'TIME',
        render: (text: string) => formatTime(text),
    },
    {
        title: 'STATUS',
        dataIndex: 'isCheckedIn',
        key: 'isCheckedIn',
        render: (text: boolean) =>
            text ? <CheckInButtonChip /> : <PendingButtonChip />,
    },
];
const ClassAttendeesList = () => {
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const { id } = useParams<{ id: string }>();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;
    const [isEnrollVisible, setIsEnrollVisible] = useState(false);
    const [confirmModalVisible, setConfirmModalVisible] = useState(false);
    const [clientData, setClientData] = useState<any>();

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [loader, startLoader, endLoader] = useLoader();

    const store = useAppSelector((state) => ({
        classesParticipantList: state.class_store.classParticipantList,
        classesParticipantListCount:
            state.class_store.classParticipantListCount,
    }));

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }
    const handleOpenEnroll = () => {
        setIsEnrollVisible(true);
    };

    const handleCloseEnroll = () => {
        setIsEnrollVisible(false);
    };

    useEffect(() => {
        startLoader();
        if (id) {
            const payload = {
                page: currentPage,
                pageSize: pageSizes,
                scheduleId: id,
            };
            dispatch(classesParticipantList(payload))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [id, currentPage, pageSizes]);

    const handleDeleteEnrollment = () => {
        if (!id) return;
        dispatch(
            cancelEnrollClient({ scheduleId: id, users: [clientData._id] })
        )
            .unwrap()
            .then(() => {
                dispatch(
                    classesParticipantList({
                        page: currentPage,
                        pageSize: pageSizes,
                        scheduleId: id,
                    })
                );
                setConfirmModalVisible(false);
                setClientData(null);
            });
    };

    const handleCheckIn = (record: any) => {
        if (!record?._id || !id) return;

        if (record?._id) {
            const payload = {
                enrollmentId: record.enrollmentId,
                isCheckedIn: !record.isCheckedIn,
            };
            dispatch(classCheckIn(payload))
                .unwrap()
                .then(() => {
                    dispatch(
                        classesParticipantList({
                            page: currentPage,
                            pageSize: pageSizes,
                            scheduleId: id,
                        })
                    );
                });
        }
    };

    const selectColumn = [
        {
            title: 'Action',
            dataIndex: '',
            width: '120px',
            align: 'center',
            key: 'action',
            render: (record: any) => {
                const menu = (
                    <Menu>
                        <Menu.Item
                            key="edit"
                            onClick={() => handleCheckIn(record)}
                        >
                            <div className="text-14 text-[#1A3353]">
                                {record?.isCheckedIn
                                    ? 'Mark as unarrived'
                                    : 'Check-In'}
                            </div>
                        </Menu.Item>
                        {!record?.isCheckedIn && (
                            <Menu.Item
                                key="delete"
                                onClick={() => {
                                    setClientData(record);
                                    setConfirmModalVisible(true);
                                }}
                            >
                                <div className="text-14 text-[#1A3353]">
                                    Delete Enrollment
                                </div>
                            </Menu.Item>
                        )}
                    </Menu>
                );
                return (
                    <>
                        <span className="flex justify-center gap-5 ">
                            <div>
                                <Dropdown overlay={menu} trigger={['click']}>
                                    <MoreOutlined
                                        style={{
                                            fontSize: '20px',
                                            cursor: 'pointer',
                                        }}
                                    />
                                </Dropdown>
                            </div>
                        </span>
                    </>
                );
            },
        },
    ];

    const combinedColumns = [...columns, ...selectColumn];

    return (
        <div className="flex flex-col gap-12">
            <div className="flex flex-row justify-between">
                <div className="flex w-full flex-row justify-between py-6">
                    <div className="flex  gap-4">
                        <img
                            src="/icons/back.svg"
                            alt="edit"
                            className="h-[14px] translate-y-3 cursor-pointer"
                            onClick={goBack}
                        />
                        <div className="flex flex-col">
                            <Title className=" text-[#1A3353]" level={4}>
                                List of Clients
                            </Title>
                        </div>
                    </div>
                    <div className="flex items-center gap-10">
                        <Button
                            onClick={handleOpenEnroll}
                            className="rounded-xl bg-purpleLight px-10 text-white"
                        >
                            Enroll +
                        </Button>
                    </div>
                </div>
            </div>
            <div className="rounded-lg border px-8 py-6 lg:w-[100%]">
                <Table
                    dataSource={store.classesParticipantList}
                    columns={combinedColumns}
                    pagination={false}
                    loading={loader}
                />
            </div>
            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={store.classesParticipantListCount}
                    pageSizeOptions={['10', '20', '50']}
                    pageSize={pageSizes}
                    onChange={paginate}
                    hideOnSinglePage
                />
            </div>

            {isEnrollVisible && (
                <AddCustomerClassModal
                    visible={isEnrollVisible}
                    onClose={handleCloseEnroll}
                    scheduleId={id}
                    pageSize={pageSizes}
                    currentPage={currentPage}
                />
            )}

            {confirmModalVisible && (
                <CommonConfirmationModal
                    visible={confirmModalVisible}
                    message={`Are you sure you want to delete ${clientData?.name} from enrolled users?`}
                    onConfirm={handleDeleteEnrollment}
                    onCancel={() => {
                        setConfirmModalVisible(false);
                        setClientData(null);
                    }}
                />
            )}
        </div>
    );
};

export default ClassAttendeesList;
