import {
    Button,
    ConfigProvider,
    Dropdown,
    Menu,
    Pagination,
    Select,
    Tooltip,
} from 'antd';
import React, { useEffect, useMemo, useState } from 'react';

import {
    DeleteOutlined,
    EditOutlined,
    MoreOutlined,
    StopOutlined,
} from '@ant-design/icons';
import { Link, useParams } from 'wouter';
import Title from 'antd/es/typography/Title';
import BookingModal from '../appointment/booking-modal';
import {
    capitalizeFirstLetter,
    formatDate,
    goBack,
} from '~/components/common/function';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { roomListingByFacilityId } from '~/redux/actions/room-action';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { getQueryParams } from '~/utils/getQueryParams';
import { useLoader } from '~/hooks/useLoader';
import { navigate } from 'wouter/use-location';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import dayjs from 'dayjs';
import Alertify from '~/services/alertify';
import { ClassType, DateRangeType } from '~/types/enums';
import SubstituteTrainerModal from '~/screens/courses/substitute-modal';
import {
    classCancelScheduling,
    classDeleteScheduling,
    classSchedulingList,
    getClassSchedulingDetails,
    updateClassScheduling,
} from '~/redux/actions/class-action';
import CancellationTypeModal from '~/screens/appointment/cancellation-type-modal';
import CancelAllScheduleModal from '../appointment/cancelAllModalNew';

const { Option } = Select;

interface IFacility {
    _id: string;
    facilityName: string;
    [key: string]: any;
}

interface ClassesListing {
    type: string;
}
const ClassesListing: React.FC<ClassesListing> = ({ type }) => {
    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
    const [isSubstitute, setIsSubstitute] = useState<boolean>(false);
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const { id } = useParams<{ id: string }>();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;
    const [isSubstituteModalVisible, setIsSubstituteModalVisible] =
        useState(false);
    const [selectedSchedule, setSelectedSchedule] = useState<any>(null);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [loader, startLoader, endLoader] = useLoader();
    const [substituteLoader, startSubtituteLoader, endSubtituteLoader] =
        useLoader();
    const [selectedLocation, setSelectedLocation] = useState<string>();
    const [confirmModalVisible, setConfirmModalVisible] = useState(false);
    const [confirmModalMessage, setConfirmModalMessage] = useState('');
    const [selectedAction, setSelectedAction] = useState<
        'delete' | 'cancel' | null
    >(null);
    const [isEdit, setIsEdit] = useState(false);
    const [selectedRooms, setSelectedRooms] = useState<string>();
    const [cancellationTypeVisible, setCancellationTypeVisible] =
        useState(false);
    const [cancelAllModalVisible, setCancelAllModalVisible] = useState(false);

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        roomList: state.room_store.roomListByFacilityId,
        classSchedulingList: state.class_store.classSchedulingList,
        classSchedulingListCount: state.class_store.classSchedulingListCount,
        classSchedulingDetails: state.class_store.classSchedulingDetails,
    }));

    const RoomOptions = store.roomList?.map((item: any) => ({
        value: item._id,
        label: item.roomName,
        id: item._id,
    }));

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    useEffect(() => {
        startLoader();
        const payload = {
            page: currentPage,
            pageSize: pageSizes,
            ...(selectedLocation && { facilityId: selectedLocation }),
            ...(selectedRooms && { roomId: selectedRooms }),
        };

        dispatch(classSchedulingList(payload))
            .unwrap()
            .then(() => {})
            .finally(endLoader);
    }, [currentPage, pageSizes, selectedLocation, selectedRooms]);

    useEffect(() => {
        if (selectedLocation)
            dispatch(
                roomListingByFacilityId({
                    facilityId: selectedLocation,
                    // status: true,
                })
            );
    }, [selectedLocation]);

    useEffect(() => {
        dispatch(FacilitiesList({ page: 1, pageSize: 50 })).then((res: any) => {
            setSelectedLocation(res?.payload?.data?.data?.list?.[0]?._id);
        });
    }, []);

    const handleClose = () => {
        setIsModalVisible(false);
        setIsSubstitute(false);
        setConfirmModalMessage('');
        setConfirmModalVisible(false);
        setCancellationTypeVisible(false);
        setIsSubstituteModalVisible(false);
        setIsEdit(false);
        setSelectedSchedule(null);
        setSelectedAction(null);
        setCancelAllModalVisible(false);
    };

    const handleSubstituteTrainer = async (toTrainerId: any) => {
        const scheduleId = selectedSchedule?._id;
        if (!scheduleId) return;
        startSubtituteLoader();
        try {
            const result = await dispatch(
                getClassSchedulingDetails({ schedulingId: scheduleId })
            ).unwrap();
            const resData = result?.data?.data;

            const dayKey = new Date(resData?.date)
                .toLocaleString('en-US', {
                    weekday: 'short',
                })
                .toLowerCase();
            const payload: any = {
                facilityId: resData?.facility?._id,
                trainerId: toTrainerId,
                classType: ClassType.CLASSES,
                subType: resData?.subType?._id,
                serviceCategory: resData?.serviceCategory?._id,
                roomId: resData?.room?._id,
                dateRange: resData?.dateRange,
                duration: resData?.duration,
                notes: resData?.Notes,
                scheduleId,
                endDate: dayjs(resData?.date)
                    .startOf('day')
                    .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                schedule: {
                    [dayKey]: [
                        {
                            from: resData?.from,
                            to: resData?.to,
                            durationInMinutes: resData?.duration,
                            classCapacity: parseInt(resData.capacity, 10),
                        },
                    ],
                },
                markType: 'custom',
                from: resData?.from,
                to: resData?.to,
                startDate: dayjs(resData?.date)
                    .startOf('day')
                    .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
            };
            await dispatch(updateClassScheduling(payload))
                .then((res: any) => {
                    if (
                        res?.payload?.status === 200 ||
                        res?.payload?.status === 201
                    ) {
                        dispatch(
                            classSchedulingList({
                                page: currentPage,
                                pageSize: pageSizes,
                                ...(selectedLocation && {
                                    facilityId: selectedLocation,
                                }),
                                ...(selectedRooms && {
                                    roomId: selectedRooms,
                                }),
                            })
                        );
                        handleClose();
                    }
                })
                .finally(endSubtituteLoader);
        } catch (error) {
            console.error('Failed to substitute trainer:', error);
            Alertify.error('Failed to substitute trainer. Please try again.');
        }
    };

    const buildActionMessage = (action: string, record: any) => {
        const formattedDate = formatDate(record.date);
        let message = `Are you sure you want to ${
            action === 'delete' ? 'delete' : 'cancel'
        } this class on ${formattedDate} from ${record.from} to ${record.to}?`;

        if (record.enrolled > 0) {
            message += ` Note: ${record.enrolled} client${
                record.enrolled > 1 ? 's are' : ' is'
            } enrolled in this class.`;
        }
        return message;
    };

    const handleMenuAction = async (
        action: 'edit' | 'delete' | 'cancel' | 'substitute',
        record: any
    ) => {
        setSelectedSchedule({
            _id: record._id,
            facilityId: record?.facility?._id,
            trainerId: record.trainer?._id,
            trainerName: record.trainer?.name,
            serviceCategoryId: record?.serviceCategory?._id,
            subTypeId: record?.subType?._id,
            serviceCategoryName: record?.serviceCategory?.name,
            subTypeName: record?.subType?.name,
            date: dayjs(record.date)
                .startOf('day')
                .format('YYYY-MM-DDT00:00:00[Z]'),
            from: record?.from,
            to: record?.to,
            enrolled: record?.enrolled,
        });

        const actionHandlers: Record<string, () => void> = {
            edit: () => {
                setIsEdit(true);
                setIsModalVisible(true);
            },
            substitute: () => {
                // setIsEdit(true);
                // setIsModalVisible(true);
                // setIsSubstitute(true);
                setIsSubstituteModalVisible(true);
            },
            cancel: () => {
                setSelectedAction('cancel');
                setCancellationTypeVisible(true);
            },
            delete: () => {
                setSelectedAction('delete');
                setConfirmModalMessage(buildActionMessage('delete', record));
                setConfirmModalVisible(true);
            },
        };

        actionHandlers[action]?.();
    };

    const handleCancelSchedule = (type: 'Single' | 'Multiple') => {
        if (type === 'Single') {
            setConfirmModalMessage(
                buildActionMessage('cancel', selectedSchedule)
            );
            setConfirmModalVisible(true);
        } else {
            setCancelAllModalVisible(true);
        }
        setCancellationTypeVisible(false);
    };

    const getMenu = (record: any) => (
        <Menu
            onClick={({ key }) =>
                handleMenuAction(key as 'edit' | 'delete' | 'cancel', record)
            }
        >
            {record.scheduleStatus !== 'canceled' && (
                <Menu.Item key="edit">
                    <EditOutlined /> &nbsp; Edit Schedule
                </Menu.Item>
            )}

            {record.scheduleStatus !== 'canceled' && (
                <Menu.Item key="substitute">
                    <EditOutlined /> &nbsp; Substitute Staff
                </Menu.Item>
            )}

            <Menu.Item key="delete">
                <DeleteOutlined /> &nbsp; Delete Schedule
            </Menu.Item>
            {record.scheduleStatus !== 'canceled' && (
                <Menu.Item key="cancel">
                    <StopOutlined /> &nbsp; Cancel Schedule
                </Menu.Item>
            )}
        </Menu>
    );

    const handleDeleteConfirm = () => {
        if (!selectedSchedule?._id) return;
        dispatch(classDeleteScheduling({ schedulingId: selectedSchedule?._id }))
            .unwrap()
            .then(() => {
                dispatch(
                    classSchedulingList({
                        page: currentPage,
                        pageSize: pageSizes,
                    })
                );
            })
            .catch(() => {})
            .finally(handleClose);
    };

    const handleCancelConfirm = (
        dateRange = 'Single',
        startDate?: string,
        endDate?: string
    ) => {
        if (!selectedSchedule?._id) return;
        dispatch(
            classCancelScheduling({
                schedulingId: selectedSchedule?._id,
                payload: { dateRange, startDate, endDate },
            })
        )
            .unwrap()
            .then(() => {
                dispatch(
                    classSchedulingList({
                        page: currentPage,
                        pageSize: pageSizes,
                    })
                );
            })
            .catch(() => {})
            .finally(handleClose);
    };

    const groupedByDate = useMemo(() => {
        const grouped: Record<string, any[]> = {};
        store.classSchedulingList.forEach((item: any) => {
            const dateKey = dayjs(item.date).format('ddd MMMM D YYYY');
            if (!grouped[dateKey]) {
                grouped[dateKey] = [];
            }
            grouped[dateKey].push(item);
        });
        return grouped;
    }, [store.classSchedulingList]);

    return (
        <div className="">
            <div className="flex flex-col gap-10">
                <div className="flex w-full flex-row justify-between py-6">
                    <div className="flex  gap-4">
                        <img
                            src="/icons/back.svg"
                            alt="edit"
                            className="h-[14px] translate-y-3 cursor-pointer"
                            onClick={goBack}
                        />
                        <div className="flex flex-col">
                            <Title className=" text-[#455560]" level={4}>
                                Class Schedule
                            </Title>
                            <p className="text-2xl text-[#455560]">
                                {store.classSchedulingDetails?.name}
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-10">
                        <Button
                            onClick={() => setIsModalVisible(true)}
                            className="border border-[#455560]"
                        >
                            Schedule
                        </Button>
                    </div>
                </div>
                <div className="flex w-full flex-row justify-end gap-8 pb-8 pt-8 ">
                    {/* <div className="lg:w-[20%]">
                        <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                            Service Category
                        </p>
                        <Select
                            maxTagCount="responsive"
                            mode="multiple"
                            placeholder="Select service category"
                            // value={props.selectedServiceCategories}
                            // onChange={handleChangeServiceCategory}
                            style={{ width: '85%' }}
                        >
                            {store.ServiceCategoryListData.map(
                                (service: any) => (
                                    <Option
                                        key={service?._id}
                                        value={service?._id}
                                    >
                                        {service?.name}
                                    </Option>
                                )
                            )}
                        </Select>
                    </div> */}
                    <div className="w-[18%] ">
                        <p className="text-lg font-medium text-[#455560] lg:-mt-7">
                            Location
                        </p>
                        <Select
                            maxTagCount="responsive"
                            placeholder="Select locations"
                            value={selectedLocation}
                            onChange={(value) => setSelectedLocation(value)}
                            className="w-[100%]"
                        >
                            {store.facilityList.map((facility: IFacility) => (
                                <Option
                                    key={facility?._id}
                                    value={facility?._id}
                                >
                                    {facility?.facilityName}
                                </Option>
                            ))}
                        </Select>
                    </div>
                    <div className="  w-[18%]  ">
                        <p className="text-lg font-medium text-[#455560] lg:-mt-7">
                            Room
                        </p>
                        <Select
                            showSearch
                            value={selectedRooms}
                            allowClear
                            maxTagCount="responsive"
                            onChange={(value) => setSelectedRooms(value)}
                            className="w-[100%]"
                            placeholder="Rooms"
                            filterOption={(input, option) =>
                                String(option?.label ?? '')
                                    .toLowerCase()
                                    .includes(input.toLowerCase())
                            }
                            options={RoomOptions}
                        />
                    </div>
                </div>
            </div>

            <div className="w-full overflow-hidden rounded-lg ">
                <div className="grid grid-cols-[1fr_1fr_2fr_1fr_1fr_1fr_1fr] items-center bg-white px-4 py-3 text-16 font-medium text-gray-500">
                    <div className=" text-14 font-semibold text-black"></div>
                    <div className="text-14 font-semibold text-black">
                        Sign-In
                    </div>
                    <div className=" text-14 font-semibold text-black">
                        Class
                    </div>
                    <div className=" text-14 font-semibold text-black">
                        Teacher
                    </div>
                    <div className=" text-14 font-semibold text-black">
                        Location
                    </div>
                    <div className=" text-14 font-semibold text-black">
                        Room
                    </div>
                    <div className=" text-center text-14 font-semibold text-black">
                        Action
                    </div>
                </div>
                {Object.entries(groupedByDate).map(
                    ([dayLabel, sessions], i) => (
                        <div
                            key={i}
                            className="my-4 border-b border-gray-100 pb-2"
                        >
                            <div className="col-span-2 flex items-center gap-2  ps-4">
                                <span className="-mt-3 text-14 font-semibold text-[#455560]">
                                    {dayLabel}
                                </span>
                            </div>

                            {sessions?.map((record, index) => (
                                <div
                                    key={record._id}
                                    className={`mb-1 grid grid-cols-[1fr_1fr_2fr_1fr_1fr_1fr_1fr] items-center rounded ${
                                        record.scheduleStatus === 'canceled'
                                            ? 'bg-[#e0e1e2]'
                                            : index % 2 === 0
                                            ? 'bg-[#f8f9fa]'
                                            : 'bg-white'
                                    } px-4 py-3`}
                                >
                                    <div>{`${record.from} - ${record.to}`}</div>
                                    <div>
                                        <Link
                                            to={`/class-attendees/${record._id}`}
                                            className="text-14 text-primary"
                                        >
                                            Sign-In ({record.checkIns || 0}/
                                            {record.enrolled || 0})
                                        </Link>
                                    </div>
                                    <Tooltip
                                        title={`${capitalizeFirstLetter(
                                            record.serviceCategory?.name
                                        )} - ${capitalizeFirstLetter(
                                            record.subType?.name
                                        )}`}
                                    >
                                        <div className="max-w-[250px] overflow-hidden truncate text-ellipsis whitespace-nowrap">
                                            {`${capitalizeFirstLetter(
                                                record.serviceCategory?.name
                                            )} - ${capitalizeFirstLetter(
                                                record.subType?.name
                                            )}`}
                                        </div>
                                    </Tooltip>
                                    <div>
                                        {capitalizeFirstLetter(
                                            record.trainer?.name
                                        )}
                                    </div>
                                    <div>{record.facility?.facilityName}</div>
                                    <div>
                                        {capitalizeFirstLetter(
                                            record.room?.name
                                        )}
                                    </div>
                                    <div className="text-center">
                                        <Dropdown
                                            overlay={getMenu(record)}
                                            trigger={['click']}
                                        >
                                            <MoreOutlined className="cursor-pointer text-18" />
                                        </Dropdown>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )
                )}
            </div>

            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            cellPaddingBlock: 8,
                        },
                    },
                }}
            >
                <div className="flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={store.classSchedulingListCount}
                        pageSizeOptions={['10', '20', '50']}
                        pageSize={pageSizes}
                        onChange={paginate}
                        hideOnSinglePage
                    />
                </div>
            </ConfigProvider>

            {isModalVisible && (
                <BookingModal
                    visible={isModalVisible}
                    onClose={handleClose}
                    tabValue={'classes'}
                    classId={id}
                    isEdit={isEdit}
                    scheduleId={selectedSchedule?._id}
                    facilityId={selectedLocation}
                    isSubstitute={isSubstitute}
                />
            )}

            {isSubstituteModalVisible && (
                <SubstituteTrainerModal
                    visible={isSubstituteModalVisible}
                    onClose={() => {
                        setIsSubstituteModalVisible(false);
                        setSelectedSchedule(null);
                    }}
                    onSubmit={handleSubstituteTrainer}
                    classType={ClassType.CLASSES}
                    selectedSchedule={selectedSchedule}
                    loading={substituteLoader}
                />
            )}

            {confirmModalVisible && (
                <CommonConfirmationModal
                    visible={confirmModalVisible}
                    message={confirmModalMessage}
                    onConfirm={() =>
                        selectedAction === 'delete'
                            ? handleDeleteConfirm()
                            : handleCancelConfirm()
                    }
                    onCancel={handleClose}
                />
            )}

            {cancellationTypeVisible && (
                <CancellationTypeModal
                    visible={cancellationTypeVisible}
                    onConfirm={handleCancelSchedule}
                    onCancel={handleClose}
                />
            )}

            {cancelAllModalVisible && (
                <CancelAllScheduleModal
                    open={cancelAllModalVisible}
                    onClose={handleClose}
                    scheduleData={selectedSchedule}
                    onConfirm={handleCancelConfirm}
                />
            )}
        </div>
    );
};

export default ClassesListing;
