import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input, Modal, Upload } from 'antd';
import React, { useEffect, useState } from 'react';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { UploadImage } from '~/redux/actions/common-action';
import {
    CreatePaymentMethod,
    DetailsAdminPaymentMethod,
    EditAdminPaymentMethod,
    SuperAdminPaymentMethodList,
} from '~/redux/actions/payment-method.action';

interface CreatePaymentModalProps {
    currentRecord: string;
    setCurrentRecord: any;
    isAddPaymentModalVisible: boolean;
    onClose: () => void;
}

const CreatePaymentModal: React.FC<CreatePaymentModalProps> = ({
    currentRecord,
    setCurrentRecord,
    isAddPaymentModalVisible,
    onClose,
}) => {
    const dispatch = useAppDispatch();
    const [imageUrl, setImageUrl] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const [form] = Form.useForm();

    const handleImageUpload = async ({ file, onSuccess, onError }: any) => {
        try {
            setLoading(true);
            const res: any = await dispatch(UploadImage({ file })).unwrap();
            const uploadedUrl = res?.res?.data?.data;
            setImageUrl(uploadedUrl);
            onSuccess();
        } catch (error) {
            onError(error);
        } finally {
            setLoading(false);
        }
    };
    useEffect(() => {
        if (currentRecord) {
            dispatch(
                DetailsAdminPaymentMethod({ paymentMethodId: currentRecord })
            )
                .unwrap()
                .then((res: any) => {
                    console.log('Details:', res);
                    setImageUrl(res?.data?.imageUrl);
                    form.setFieldsValue({
                        name: res?.data?.name,
                    });
                });
        }
    }, [currentRecord, dispatch, form]);
    const createPaymentMethod = async (values: any) => {
        if (!currentRecord) {
            const payload = {
                imageUrl: imageUrl,
                ...values,
            };
            // console.log('Payload--------------:', payload);
            try {
                setLoading(true);
                await dispatch(CreatePaymentMethod(payload)).unwrap();
                onClose();
            } catch (error) {
                console.error('Error submitting form:', error);
            } finally {
                dispatch(SuperAdminPaymentMethodList({}))
                    .unwrap()
                    .then(() => {
                        setLoading(false);
                    });
                onClose();
                setLoading(false);
                setImageUrl('');
                form.setFieldsValue({
                    name: '',
                });
            }
        } else {
            const payload = {
                imageUrl: imageUrl,
                ...values,
                paymentMethodId: currentRecord,
            };
            // console.log('Edit Payload--------------:', payload);
            try {
                setLoading(true);
                await dispatch(EditAdminPaymentMethod(payload)).unwrap();
                onClose();
            } catch (error) {
                console.error('Error submitting form:', error);
            } finally {
                dispatch(SuperAdminPaymentMethodList({}))
                    .unwrap()
                    .then(() => {
                        setLoading(false);
                    });
                onClose();
                setLoading(false);
                setCurrentRecord(null);
                setImageUrl('');
                form.setFieldsValue({
                    name: '',
                });
            }
        }
    };
    // console.log('currentRecord:', currentRecord);

    return (
        <Modal
            title={<p className="w-fit text-2xl text-[#1A3353]">Add New</p>}
            className="lg:w-[35%]"
            footer={null}
            open={isAddPaymentModalVisible}
            onCancel={onClose}
        >
            {/* {currentRecord && <p>{currentRecord}</p>} */}
            <div className="flex w-full items-center gap-4">
                <Form
                    form={form}
                    layout="vertical"
                    size="normal"
                    autoComplete="off"
                    onFinish={createPaymentMethod}
                    className="w-2/3"
                >
                    <Form.Item
                        label="Payment Method Name"
                        name="name"
                        rules={[
                            {
                                required: true,
                                message: 'Please select branch name(s)',
                            },
                        ]}
                    >
                        <Input placeholder="Payment Method Name" />
                    </Form.Item>
                </Form>
                <Upload
                    name="avatar"
                    listType="picture-card"
                    className="w-1/3 overflow-hidden"
                    showUploadList={false}
                    customRequest={handleImageUpload}
                >
                    {imageUrl ? (
                        <img
                            src={imageUrl}
                            className="lg:object-contain @sm:rounded-3xl @sm:object-cover"
                            alt="avatar"
                            style={{
                                width: '100%',
                                height: '100%',
                            }}
                        />
                    ) : (
                        <button
                            style={{
                                border: 0,
                                background: 'none',
                            }}
                            type="button"
                        >
                            {loading ? <LoadingOutlined /> : <PlusOutlined />}
                            <div>Upload</div>
                        </button>
                    )}
                </Upload>
            </div>
            <div className="mt-6 flex justify-end gap-4">
                <Button
                    className="w-1/5 rounded-lg border-1 border-black py-2"
                    onClick={onClose}
                >
                    CANCEL
                </Button>
                <Button
                    className="w-1/5 rounded-lg border-1 border-[#8143D1] bg-[#8143D1] py-2 text-white"
                    // htmlType="submit"
                    onClick={() => form.submit()}
                >
                    {loading ? <LoadingOutlined /> : 'SAVE'}
                </Button>
            </div>
        </Modal>
    );
};

export default CreatePaymentModal;
