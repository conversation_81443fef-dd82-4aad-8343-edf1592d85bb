import React from 'react';
import { Modal, DatePicker, Button } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { CancelScheduling } from '~/redux/actions/scheduling-action';

const { RangePicker } = DatePicker;

interface CancelScheduleModalProps {
    open: boolean;
    onClose: () => void;
    scheduleData?: any
}

const CancelAllScheduleModal: React.FC<CancelScheduleModalProps> = ({
    open,
    onClose,
    scheduleData
}) => {
    const dispatch = useAppDispatch();

    const [selectedRange, setSelectedRange] = React.useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

    const handleDelete = () => {
        if (selectedRange) {
            const startDate = dayjs(selectedRange[0]).format('YYYY-MM-DD');
            const endDate = dayjs(selectedRange[1]).format('YYYY-MM-DD');

            const result = {
                dateRange: "Multiple",
                startDate,
                endDate
            };

            dispatch(
                CancelScheduling({
                    scheduleId: scheduleData?.events?.[0]?.id || scheduleData?.id,
                    payload: result
                })
            ).unwrap().then((res:any)=>{
                if(res?.status === 200 || res?.status === 201)
                onClose()
            })
        }

    };

    return (
        <Modal
            open={open}
            onCancel={onClose}
            footer={null}
            closeIcon={<CloseOutlined className="text-xl text-black" />}
            centered
            width={500}
            className="custom-modal"
        >
            <div className="p-2 sm:p-4">
                <h2 className="text-lg sm:text-xl font-semibold text-[#1A3353] mb-6">
                    Are you sure you want to cancel schedules?
                </h2>

                <div className="mb-6">
                    <label className="block font-semibold text-[#1A3353] mb-2">
                        Select Date Range
                    </label>
                    <RangePicker
                        className="w-full"
                        onChange={(dates) => setSelectedRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
                        placeholder={['Select date', 'End date']}
                        popupClassName="z-[9999]"
                    />
                </div>

                <div className="flex justify-end space-x-3 mt-4">
                    <Button
                        onClick={onClose}
                        className="border border-[#d9d9d9] text-black hover:!border-black"
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        className="bg-[#A879E4] hover:!bg-[#9165c4] border-none"
                        onClick={handleDelete}
                        disabled={!selectedRange}
                    >
                        Delete
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default CancelAllScheduleModal;
