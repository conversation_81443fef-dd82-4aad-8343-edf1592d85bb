import React, { useEffect, useState } from 'react';
import {
    Select,
    DatePicker,
    TimePicker,
    Input,
    Form,
    Button,
    ConfigProvider,
} from 'antd';
import { useSelector } from 'react-redux';
import { ClassType, RoleType } from '~/types/enums';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { GetFacilityListByStaffId } from '~/redux/actions/facility-action';
import {
    TrainerListing,
    PricingListingByUserAndType,
    PricingListingByUserAndSubType,
} from '~/redux/actions/appointment-action';
import { useDebounce } from '~/hooks/useDebounce';
import dayjs from 'dayjs';
import TextArea from 'antd/es/input/TextArea';
import {
    ServiceCategoryListByPackageId,
    activeServiceCategoyListv1,
} from '~/redux/actions/serviceCategoryAction';
import {
    roomListingByServiceCategory,
    roomListingByScheduling,
} from '~/redux/actions/room-action';
import {
    BookedSchedulingDetails,
    CreateBookScheduling,
    UpdateBookedScheduling,
    BookedCalendarData,
} from '~/redux/actions/scheduling-action';
import FormClientDetails from './form-client-detail';

const { Option, OptGroup } = Select;
interface BookingFormProps {
    visible?: boolean;
    onClose?: any;
    scheduleId?: string;
    isEdit?: boolean;
    clientId?: string;
    purchaseId?: string;
    packageId?: string;
    facilityId?: string;
    slotSelectedInfo?: any;
    scanBooking?: boolean;
    scanBookingData?: any;
}

const BookingForm: React.FC<BookingFormProps> = ({
    visible,
    onClose,
    scheduleId,
    isEdit,
    clientId,
    purchaseId,
    packageId,
    facilityId,
    slotSelectedInfo,
    scanBooking,
    scanBookingData,
}) => {
    // Api start
    const { role, user } = useSelector((state: any) => state.auth_store);

    const [form] = Form.useForm();

    const [selectedLocation, setSelectedLocation] = useState(facilityId);
    const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(dayjs());
    const [startTime, setStartTime] = useState<dayjs.Dayjs | null>(null);
    const [endTime, setEndTime] = useState<dayjs.Dayjs | null>(null);
    const [clientData, setClientData] = useState<any>();
    const [durationOption, setDurationOption] = useState<any>([]);
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const [
        submitCheckInLoader,
        startSubmitCheckInLoader,
        endSubmitCheckInLoader,
    ] = useLoader();
    const [remainingSessions, setRemainingSessions] = useState('');
    const [serviceCategoryId, setServiceCategoryId] = useState<any>(null);
    const [subCategoryId, setSubcategoryId] = useState<any>(null);
    const [roomData, setRoomData] = useState<any>([]);
    const [serviceData, setServiceData] = useState<any>([]);
    const [pricingData, setPricingData] = useState<any>([]);
    const [selectedRoomReason, setSelectedRoomReason] = useState<string | null>(
        null
    );
    const [selectedRoomAvailability, setSelectedRoomAvailability] = useState<
        boolean | null
    >(null);

    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        facilityListByStaffId: state.facility_store.facilityListByStaffId,
    }));

    const debouncedRequest = useDebounce((callback) => callback(), 300);

    const [loader, startLoader, endLoader] = useLoader();

    useEffect(() => {
        if (slotSelectedInfo) {
            setStartTime(dayjs(slotSelectedInfo.startDate));
            setStartDate(dayjs(slotSelectedInfo.startDate));
        }
    }, [slotSelectedInfo]);

    useEffect(() => {
        const updateFields: any = {};
        const fetchFacilityIfTrainer = async () => {
            if (role === RoleType.TRAINER) {
                const res: any = await dispatch(
                    GetFacilityListByStaffId({ staffId: user._id })
                );
                const firstFacilityId = res?.payload?.data?.data?.[0]?._id;
                if (!facilityId && firstFacilityId)
                    updateFields.location = firstFacilityId;
            }
        };

        const setInitialValues = async () => {
            await fetchFacilityIfTrainer();
            if (clientId) setClientData({ clientId });
            if (facilityId) updateFields.location = facilityId;
            if (Object.keys(updateFields).length > 0)
                form.setFieldsValue(updateFields);
        };
        setInitialValues();
    }, [visible]);

    const transformServiceData = (apiData: any[]) => {
        if (!Array.isArray(apiData)) return [];
        return apiData.map((service: any) => ({
            category: service.name,
            _id: service._id,
            options: service.appointmentType.map((appointment: any) => ({
                label: `${appointment.name} - ${appointment.durationInMinutes} mins`,
                value: JSON.stringify({
                    serviceId: service._id,
                    appointmentId: appointment._id,
                }),
                durationInMinutes: appointment.durationInMinutes,
            })),
        }));
    };

    const fetchRoomData = async ({
        date = startDate,
        startsTime = startTime,
        endsTime = endTime,
        serviceId = serviceCategoryId,
        scheduleId,
        updateFirst = true,
    }: any) => {
        try {
            const obj: any = {
                serviceId,
                facilityId: selectedLocation,
                classType: ClassType.BOOKING,
                date: dayjs(date).format('YYYY-MM-DD'),
                startTime: dayjs(startsTime).format('HH:mm'),
                endTime: dayjs(endsTime).format('HH:mm'),
            };
            if (scheduleId) obj.scheduleId = scheduleId;
            const res = await dispatch(roomListingByScheduling(obj)).unwrap();

            let firstAvailableRoom: any = null;
            const roomOptions =
                res?.data?.data?.map((item: any) => {
                    if (!firstAvailableRoom && item.isAvailable)
                        firstAvailableRoom = item._id;
                    return {
                        value: item._id,
                        label: item.roomName,
                        id: item._id,
                        isAvailable: item.isAvailable,
                        reason: item.reason,
                    };
                }) || [];

            setRoomData(roomOptions);
            if (firstAvailableRoom && updateFirst) {
                form.setFieldsValue({ roomId: firstAvailableRoom });
            }
        } catch (err) {
            console.error('fetchRoomData error:', err);
            setRoomData([]);
        }
    };

    useEffect(() => {
        if (role !== RoleType.TRAINER && selectedLocation)
            dispatch(
                TrainerListing({ facilityId: selectedLocation, isActive: true })
            );
    }, [selectedLocation]);

    useEffect(() => {
        if (!scheduleId || purchaseId) return;

        const fetchSchedulingDetails = async () => {
            try {
                const res = await dispatch(
                    BookedSchedulingDetails({ scheduleId })
                ).unwrap();
                const scheduleData = res?.data?.data;
                if (!scheduleData) return;

                const {
                    facilityId,
                    clientId,
                    clientName,
                    serviceCategoryId,
                    subTypeId,
                    subTypeDuration,
                    date,
                    from,
                    duration,
                    purchaseId,
                    roomId,
                    notes,
                } = scheduleData;

                const scheduleDate = dayjs(date);
                const start = dayjs(from, 'HH:mm');
                const end = start.add(duration, 'minute');

                setSelectedLocation(facilityId);
                setClientData({ clientId, clientName });
                setServiceCategoryId(serviceCategoryId);
                setSubcategoryId(subTypeId);
                setStartDate(scheduleDate);
                setStartTime(start);
                setEndTime(end);

                setDurationOption(
                    Array.from({ length: 5 }, (_, i) => {
                        const value = subTypeDuration * (i + 1);
                        return { label: `${value} Minutes`, value };
                    })
                );

                const [serviceCategories, packageResponse] = await Promise.all([
                    dispatch(
                        activeServiceCategoyListv1({
                            classType: ClassType.BOOKING,
                        })
                    ).unwrap(),
                    dispatch(
                        PricingListingByUserAndSubType({
                            classType: 'bookings',
                            clientUserId: clientId,
                            serviceCategoryId,
                            subTypeId,
                            pageSize: 50,
                        })
                    ).unwrap(),
                ]);
                setServiceData(
                    transformServiceData(serviceCategories?.data?.data?.list)
                );
                await fetchRoomData({
                    date: scheduleDate,
                    startsTime: start,
                    endsTime: end,
                    scheduleId,
                    serviceId: serviceCategoryId,
                    updateFirst: false,
                });

                const packageList = packageResponse?.data?.data || [];
                setPricingData(
                    packageList?.map((item: any) => ({
                        value: item._id,
                        packageId: item.packageId,
                        label: `${item.packageName}`.trim(),
                        remainingSession: item.remainingSession,
                        _raw: item,
                    })) || []
                );
                const selectedPackage = packageList.find(
                    (pkg: any) => pkg._id === purchaseId
                );

                if (selectedPackage) {
                    setRemainingSessions(selectedPackage.remainingSession);
                }

                form.setFieldsValue({
                    client: clientId,
                    roomId,
                    notes,
                    package: selectedPackage
                        ? {
                              value: selectedPackage._id,
                              label: selectedPackage.packageName,
                          }
                        : undefined,
                    serviceType: JSON.stringify({
                        serviceId: serviceCategoryId,
                        appointmentId: subTypeId,
                    }),
                    subType: subTypeId,
                    duration,
                });
            } catch (error) {
                console.error('Error fetching scheduling details:', error);
            }
        };

        fetchSchedulingDetails();
    }, [dispatch, scheduleId]);

    const getNextTimeSlot = (step: number) => {
        const now = dayjs(startDate);
        const minutes = now.minute();
        const nextMinutes = Math.ceil(minutes / step) * step;
        return now.minute(nextMinutes).second(0).millisecond(0);
    };

    const handleAppointmentTypeChange = (appointmentData: any) => {
        setDurationOption(
            Array.from({ length: 5 }, (_, i) => {
                const multiple = appointmentData.durationInMinutes * (i + 1);
                return {
                    label: `${multiple} Minutes`,
                    value: multiple,
                };
            })
        );
        const startTimeValue = getNextTimeSlot(
            appointmentData.durationInMinutes
        );

        if (!scheduleId) {
            setStartTime(startTimeValue);
        }
        form.setFieldsValue({ duration: appointmentData.durationInMinutes });
        const calculatedEndTime = startTimeValue.add(
            appointmentData.durationInMinutes,
            'minute'
        );
        setEndTime(calculatedEndTime);
    };

    const setDataOnBasisOfPackage = (service: any) => {
        const appointmentType = service.appointmentType?.[0];
        if (!service || !appointmentType)
            return { serviceType: null, duration: null };
        const serviceType = JSON.stringify({
            serviceId: service._id,
            appointmentId: appointmentType?._id,
        });
        const duration = appointmentType?.durationInMinutes;
        const startTimeValue = getNextTimeSlot(duration);
        const calculatedEndTime = startTimeValue.add(duration, 'minute');
        setStartDate(dayjs());
        setStartTime(startTimeValue);
        setEndTime(calculatedEndTime);
        fetchRoomData({
            startsTime: startTimeValue,
            endsTime: calculatedEndTime,
            serviceId: service._id,
        });
        setServiceCategoryId(service._id);
        setSubcategoryId(appointmentType?._id);
        return { serviceType, duration };
    };

    useEffect(() => {
        if (scheduleId) return;
        const fetchInitialBookingData = async () => {
            startLoader();

            try {
                const [serviceCategoriesRes, pricingRes] = await Promise.all([
                    packageId
                        ? dispatch(
                              ServiceCategoryListByPackageId({ packageId })
                          ).unwrap()
                        : dispatch(
                              activeServiceCategoyListv1({
                                  classType: ClassType.BOOKING,
                              })
                          ).unwrap(),
                    clientId
                        ? dispatch(
                              PricingListingByUserAndType({
                                  userId: clientId,
                                  classType: 'bookings',
                              })
                          ).unwrap()
                        : Promise.resolve(null),
                ]);
                const list =
                    (packageId
                        ? serviceCategoriesRes?.data?.data
                        : serviceCategoriesRes?.data?.data?.list) || [];
                const packageList = pricingRes?.data?.data || [];
                setPricingData(
                    packageList.map((item: any) => ({
                        value: item._id,
                        packageId: item.packageId,
                        label: item.packageName?.trim(),
                        remainingSession: item.remainingSession,
                        _raw: item,
                    }))
                );
                setServiceData(transformServiceData(list));
                let serviceType = null;
                let duration = null;
                let durationOptions: any = [];
                if (purchaseId) {
                    const selectedPackage = packageList.find(
                        (pkg: any) => pkg._id === purchaseId
                    );
                    setRemainingSessions(selectedPackage.remainingSession);
                    const updatedData = setDataOnBasisOfPackage(list?.[0]);
                    duration = updatedData.duration;
                    serviceType = updatedData.serviceType;
                    durationOptions = Array.from({ length: 5 }, (_, i) => {
                        const multiple = updatedData.duration * (i + 1);
                        return {
                            label: `${multiple} Minutes`,
                            value: multiple,
                        };
                    });
                }

                setDurationOption(durationOptions);
                form.setFieldsValue({
                    package: purchaseId,
                    serviceType,
                    duration,
                });

                if (!clientId) {
                    setPricingData([]);
                    return;
                }
            } catch (error) {
                console.error('Error in fetchInitialBookingData:', error);
            } finally {
                endLoader();
            }
        };

        fetchInitialBookingData();
    }, [selectedLocation]);

    const FacilityOptions = (store.facilityList || []).map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    useEffect(() => {
        if (!facilityId && FacilityOptions?.length > 0) {
            setSelectedLocation(FacilityOptions[0]?.id);
            form.setFieldsValue({
                location: FacilityOptions[0]?.id,
            });
        }
    }, [FacilityOptions]);

    const LocationOptionByStaff = (store.facilityListByStaffId || []).map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const handleServiceTypeChange = async (value: string, option: any) => {
        const selectedOption = JSON.parse(value);
        const serviceId = selectedOption.serviceId;
        setServiceCategoryId(serviceId);
        const appointmentId = selectedOption.appointmentId;
        const durationInMinutes = option.durationInMinutes;
        setSubcategoryId(appointmentId);
        form.setFieldsValue({ subType: undefined, roomId: null });

        handleAppointmentTypeChange({ durationInMinutes });

        const startTimeValue = getNextTimeSlot(durationInMinutes);
        const calculatedEndTime = startTimeValue.add(
            durationInMinutes,
            'minute'
        );

        const activeClient = clientData;
        if (activeClient && serviceId && appointmentId) {
            const payload = {
                classType: ClassType.BOOKING,
                clientUserId: activeClient?.clientId || activeClient.value,
                serviceCategoryId: serviceId,
                subTypeId: appointmentId,
                pageSize: 50,
                page: 1,
                search: '',
                isNewBooking: true,
            };

            const response = await dispatch(
                PricingListingByUserAndSubType(payload)
            ).unwrap();
            const packages = response?.data?.data;
            setPricingData(
                packages?.map((item: any) => ({
                    value: item._id,
                    packageId: item.packageId,
                    label: `${item.packageName}`.trim(),
                    remainingSession: item.remainingSession,
                    _raw: item,
                })) || []
            );

            if (packages?.length > 0) {
                const firstPackage = packages[0];
                form.setFieldsValue({
                    package: {
                        value: firstPackage._id,
                        label: firstPackage.packageName,
                    },
                });
                setRemainingSessions(firstPackage.remainingSession);
            } else {
                // Reset package field when no packages found
                form.setFieldsValue({ package: undefined });
                setRemainingSessions('');

                // Set first service value
                if (serviceData?.length > 0) {
                    const firstService = serviceData[0];
                    if (firstService.children?.length > 0) {
                        const firstAppointment = firstService.children[0];
                        const firstServiceValue = firstAppointment.value;

                        form.setFieldsValue({ serviceType: firstServiceValue });
                    }
                }
            }
        }
        fetchRoomData({
            startsTime: startTimeValue,
            endsTime: calculatedEndTime,
            serviceId: serviceId,
        });
    };

    const handleClientSelect = async (client: any) => {
        setClientData(client);
        try {
            const pricingRes = await dispatch(
                PricingListingByUserAndType({
                    userId: client.value || client.userId,
                    classType: 'bookings',
                })
            ).unwrap();
            const packageList = pricingRes?.data?.data || [];
            setPricingData(
                packageList?.map((item: any) => ({
                    value: item._id,
                    packageId: item.packageId,
                    label: `${item.packageName}`.trim(),
                    remainingSession: item.remainingSession,
                    _raw: item,
                })) || []
            );

            // Set package in form
            form.setFieldsValue({
                package: null,
                serviceType: null,
                duration: null,
            });

            const serviceCategoriesRes = await dispatch(
                activeServiceCategoyListv1({
                    classType: ClassType.BOOKING,
                })
            ).unwrap();
            setServiceData(
                transformServiceData(serviceCategoriesRes?.data?.data?.list)
            );
        } catch (error) {
            console.error('Error in handleClientSelect:', error);
        }
    };

    const handlePackageChange = async (data: any) => {
        try {
            const res = await dispatch(
                ServiceCategoryListByPackageId({ packageId: data.packageId })
            ).unwrap();
            const list = res?.data?.data || [];
            setServiceData(transformServiceData(list));

            const packageEntry = pricingData.find(
                (p: any) => p.value === data.value
            );
            setRemainingSessions(packageEntry?.remainingSession || '');
            const { duration, serviceType } = setDataOnBasisOfPackage(
                list?.[0]
            );
            form.setFieldsValue({
                serviceType,
                duration,
            });
        } catch (err) {
            console.error('Error in handlePackageChange:', err);
        }
    };

    const handleDuration = (value: any) => {
        if (startTime) {
            const calculatedEndTime = startTime.add(value, 'minute');
            setEndTime(calculatedEndTime);
            fetchRoomData({
                date: startDate,
                startsTime: startTime,
                endsTime: calculatedEndTime,
            });
        } else {
            setEndTime(null);
        }
    };

    const handleStartTimeChange = (time: any) => {
        setStartTime(time);
        if (time) {
            const duration = form.getFieldValue('duration');
            const calculatedEndTime = dayjs(time).add(
                Number(duration),
                'minute'
            );
            setEndTime(calculatedEndTime);
            fetchRoomData({
                date: startDate,
                startsTime: time,
                endsTime: calculatedEndTime,
            });
        } else {
            setEndTime(null);
        }
    };

    useEffect(() => {
        const prepopulateScanBookingData = async () => {
            if (!scanBooking || !scanBookingData || !selectedLocation) return;

            try {
                const {
                    clientId: scanClientId,
                    clientName,
                    clientEmail,
                    clientPhone,
                    facilityId: scanFacilityId,
                    serviceCategoryId: scanServiceCategoryId,
                    subTypeId: scanSubTypeId,
                    purchaseId: scanPurchaseId,
                    packageId: scanPackageId,
                    packageName: scanPackageName,
                    date: scanDate,
                    subTypeDuration,
                } = scanBookingData;

                // Set client data
                const clientData = {
                    clientId: scanClientId,
                    value: scanClientId,
                    clientName: clientName,
                };
                setClientData(clientData);

                // Update facility if different
                if (scanFacilityId && scanFacilityId !== selectedLocation) {
                    setSelectedLocation(scanFacilityId);
                    form.setFieldValue('location', scanFacilityId);
                }

                // Set client form fields
                form.setFieldsValue({
                    client: scanClientId,
                });

                // Set service category and subtype
                setServiceCategoryId(scanServiceCategoryId);
                setSubcategoryId(scanSubTypeId);

                // Fetch service categories for bookings
                const serviceRes = await dispatch(
                    activeServiceCategoyListv1({
                        classType: ClassType.BOOKING,
                    })
                ).unwrap();

                const serviceList = serviceRes?.data?.data?.list || [];
                setServiceData(transformServiceData(serviceList));

                // Find matching service and appointment type
                const matchedService = serviceList.find(
                    (service: any) => service._id === scanServiceCategoryId
                );

                let fallbackDuration = 30; // Default duration

                if (matchedService) {
                    const matchedAppointment =
                        matchedService.appointmentType?.find(
                            (apt: any) => apt._id === scanSubTypeId
                        );

                    if (matchedAppointment) {
                        const serviceTypeValue = JSON.stringify({
                            serviceId: scanServiceCategoryId,
                            appointmentId: scanSubTypeId,
                        });

                        form.setFieldValue('serviceType', serviceTypeValue);

                        fallbackDuration =
                            matchedAppointment.durationInMinutes || 30;

                        // Set duration options
                        const durationOptions = Array.from(
                            { length: 5 },
                            (_, i) => {
                                const multiple = fallbackDuration * (i + 1);
                                return {
                                    label: `${multiple} Minutes`,
                                    value: multiple,
                                };
                            }
                        );
                        setDurationOption(durationOptions);

                        form.setFieldValue('duration', fallbackDuration);

                        // Set default times if not editing
                        if (!isEdit && !scheduleId) {
                            const defaultStartTime =
                                getNextTimeSlot(fallbackDuration);
                            const defaultEndTime = defaultStartTime.add(
                                fallbackDuration,
                                'minute'
                            );

                            setStartTime(defaultStartTime);
                            setEndTime(defaultEndTime);
                            setStartDate(dayjs());
                        }
                    }
                }

                // Handle package data if purchaseId exists
                if (scanPurchaseId) {
                    try {
                        const pricingRes = await dispatch(
                            PricingListingByUserAndSubType({
                                classType: 'bookings',
                                clientUserId: scanClientId,
                                serviceCategoryId: scanServiceCategoryId,
                                subTypeId: scanSubTypeId,
                                pageSize: 50,
                                page: 1,
                                search: '',
                                isNewBooking: true,
                            })
                        ).unwrap();

                        const packages = pricingRes?.data?.data || [];
                        setPricingData(
                            packages.map((item: any) => ({
                                value: item._id,
                                packageId: item.packageId,
                                label: item.packageName?.trim(),
                                remainingSession: item.remainingSession,
                                _raw: item,
                            }))
                        );

                        // Find matching package by purchaseId or packageId
                        const matchedPackage = packages.find(
                            (pkg: any) =>
                                pkg._id === scanPurchaseId ||
                                pkg.packageId === scanPackageId
                        );

                        if (matchedPackage) {
                            form.setFieldValue('package', {
                                value: matchedPackage._id,
                                label:
                                    matchedPackage.packageName ||
                                    scanPackageName,
                            });
                            setRemainingSessions(
                                matchedPackage.remainingSession
                            );
                        }
                    } catch (err) {
                        console.error(
                            'Error fetching package data for scan booking:',
                            err
                        );
                    }
                }

                // Fetch room data with proper timing
                setTimeout(async () => {
                    const currentDate = startDate || dayjs();
                    const currentStartTime =
                        startTime || getNextTimeSlot(fallbackDuration);
                    const currentEndTime =
                        endTime ||
                        currentStartTime.add(fallbackDuration, 'minute');

                    // Update state if not already set
                    if (!startTime) setStartTime(currentStartTime);
                    if (!endTime) setEndTime(currentEndTime);
                    if (!startDate) setStartDate(currentDate);

                    try {
                        await fetchRoomData({
                            date: currentDate,
                            startsTime: currentStartTime,
                            endsTime: currentEndTime,
                            serviceId: scanServiceCategoryId,
                            scheduleId: scheduleId,
                            updateFirst: true,
                        });
                    } catch (error) {
                        console.error(
                            'Error fetching room data for scan booking:',
                            error
                        );
                    }
                }, 1000);
            } catch (error) {
                console.error('Error prepopulating scan booking data:', error);
            }
        };

        // Only run this effect for scan bookings
        if (scanBooking && scanBookingData) {
            prepopulateScanBookingData();
        }
    }, [scanBooking, scanBookingData, selectedLocation, dispatch]);

    // Update the existing clientId/facilityId useEffect to exclude scan bookings
    // useEffect(() => {
    //     if (clientId && !scanBooking) {
    //         const clientObj = { clientId: clientId, value: clientId };
    //         setClientData(clientObj);
    //         handleClientSelect(clientObj);
    //     }

    //     if (facilityId && !scanBooking) {
    //         form.setFieldValue('location', facilityId);
    //     }
    // }, [clientId, facilityId, scanBooking]);

    const onFinish = (values: any, checkIn = false) => {
        const serviceData = JSON.parse(values.serviceType);

        // Extract individual values
        const serviceId = serviceData.serviceId;
        const appointmentId = serviceData.appointmentId;
        const purchaseIdValue = values.package?.value || values.package;
        const payload: any = {
            facilityId: values.location,
            clientId: clientData.value ? clientData.value : clientData.clientId,
            classType: 'bookings',
            roomId: values.roomId,
            notes: values.notes,
            dateRange: 'Single',
            purchaseId: purchaseIdValue,
            serviceCategory: serviceId,
            sendConfirmation: values.sendConfirmation,
            subType: appointmentId,
            duration: values.duration,
            date: dayjs(startDate)
                .startOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
            from: dayjs(startTime).format('HH:mm'),
            to: dayjs(endTime).format('HH:mm'),
            ...(!scheduleId && { checkIn }),
        };

        if (scheduleId) {
            startSubmitLoader();
            dispatch(
                UpdateBookedScheduling({ payload: { ...payload, scheduleId } })
            )
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;

                    if (status === 200 || status === 201) {
                        form.resetFields();
                        setStartDate(null);
                        setStartTime(null);
                        setEndTime(null);
                        setSelectedLocation('');
                        onClose();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [selectedLocation],
                            })
                        );
                    }
                })
                .finally(endSubmitLoader);
        } else {
            if (checkIn) startSubmitCheckInLoader();
            else startSubmitLoader();
            dispatch(CreateBookScheduling({ payload: payload }))
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;

                    if (status === 200 || status === 201) {
                        form.resetFields();
                        setStartDate(null);
                        setStartTime(null);
                        setEndTime(null);
                        setSelectedLocation('');
                        onClose();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [selectedLocation],
                            })
                        );
                    }
                })
                .finally(() => {
                    endSubmitLoader();
                    endSubmitCheckInLoader();
                });
        }
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Form: {
                        verticalLabelMargin: '-10px',
                    },
                },
            }}
        >
            <Form
                className=" flex flex-col lg:gap-5"
                name="schedule-form"
                form={form}
                variant="borderless"
                layout="horizontal"
                size="large"
                onFinish={onFinish}
                disabled={!isEdit && !!scheduleId}
            >
                <div className="flex flex-row">
                    <div className="w-[40%]  lg:-translate-y-16  ">
                        {(selectedLocation || clientData) && (
                            <FormClientDetails
                                facilityId={selectedLocation}
                                onClientSelect={handleClientSelect}
                                clientData={clientData}
                                isEdit={clientId || scheduleId ? true : false}
                            />
                        )}
                    </div>
                    <div className="w-[60%] border-s-2 ps-6">
                        <div className="flex w-full justify-between ">
                            <div className="  lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left  lg:w-[55px]">
                                            Location
                                        </p>
                                    }
                                    name="location"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select location',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        className="border-b-1"
                                        allowClear
                                        defaultValue={selectedLocation}
                                        onChange={(value) => {
                                            setSelectedLocation(value);
                                            form.setFieldsValue({
                                                staff: '',
                                                client: '',
                                                phone: '',
                                                email: '',
                                            });
                                        }}
                                        placeholder="Select facility"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={
                                            role === RoleType.TRAINER
                                                ? LocationOptionByStaff
                                                : FacilityOptions
                                        }
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className=" flex w-full justify-between ">
                            <div className="lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left @sm:w-full">
                                            Service
                                        </p>
                                    }
                                    name="serviceType"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select a service',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="border-b-1"
                                        showSearch
                                        placeholder="Select Service"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        onChange={handleServiceTypeChange}
                                        disabled={
                                            !clientData ||
                                            (!isEdit && !!scheduleId)
                                        }
                                        style={
                                            !clientData ||
                                            (!isEdit && scheduleId)
                                                ? {
                                                      backgroundColor:
                                                          '#f5f5f5',
                                                      color: '#a0a0a0',
                                                      cursor: 'not-allowed',
                                                  }
                                                : {}
                                        }
                                    >
                                        {serviceData.map(
                                            (group: any, i: number) => (
                                                <OptGroup
                                                    key={`${group.category}-${i}`}
                                                    label={group.category}
                                                >
                                                    {group.options.map(
                                                        (
                                                            service: any,
                                                            j: number
                                                        ) => (
                                                            <Option
                                                                key={`${service.value}-${j}`}
                                                                value={
                                                                    service.value
                                                                }
                                                                durationInMinutes={
                                                                    service.durationInMinutes
                                                                }
                                                            >
                                                                {service.label}
                                                            </Option>
                                                        )
                                                    )}
                                                </OptGroup>
                                            )
                                        )}
                                    </Select>
                                </Form.Item>
                            </div>
                        </div>

                        <div className="flex w-full gap-4  lg:w-[95%] @sm:-mt-5">
                            <div className="flex w-full justify-between gap-3  lg:flex-row lg:items-end ">
                                <div className="w-[100%] ">
                                    <Form.Item
                                        className="w-full"
                                        label={
                                            <p className="text-left">Package</p>
                                        }
                                        name="package"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please select package',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            className="border-b-1"
                                            placeholder="Select Package"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={pricingData}
                                            onChange={(value, option: any) =>
                                                handlePackageChange(option)
                                            }
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        </div>

                        <div className="  s flex w-full justify-between gap-4 lg:mb-7 lg:w-[95%] @sm:-mt-5">
                            <div className="w-[23%]"></div>
                            <Input
                                type="text"
                                // style={{
                                //     borderBottom: '1px solid #e5e7eb',
                                //     borderRadius: '0px',
                                // }}
                                // className="w-[95%] -translate-y-3"
                                style={{
                                    backgroundColor: '#f5f5f5',
                                    color: '#a0a0a0',
                                    cursor: 'not-allowed',
                                    borderRadius: '0px',
                                }}
                                disabled
                                value={
                                    remainingSessions === 'unlimited'
                                        ? 'Unlimited'
                                        : remainingSessions
                                }
                                placeholder="No. of session left"
                            />
                        </div>

                        <div className=" flex justify-between lg:w-[95%]  lg:flex-row lg:items-center @sm:flex-col">
                            <p className=" text-[13px] font-medium text-[#1A3353] lg:ms-2  @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                Select Date
                            </p>
                            <DatePicker
                                popupClassName="custom-datepicker"
                                value={startDate}
                                format="DD/MM/YYYY"
                                onChange={(date) => {
                                    setStartDate(date);
                                    fetchRoomData({ date });
                                }}
                                className="lg:w-[80%] @sm:w-[100%]"
                                style={{
                                    borderBottom: '1px solid #e5e7eb',
                                    borderRadius: '0px',
                                    backgroundColor: !serviceCategoryId
                                        ? '#f5f5f5'
                                        : undefined,
                                    color: !serviceCategoryId
                                        ? '#a0a0a0'
                                        : undefined,
                                    cursor: !serviceCategoryId
                                        ? 'not-allowed'
                                        : undefined,
                                }}
                                disabled={
                                    !serviceCategoryId ||
                                    (!isEdit && !!scheduleId)
                                }
                            />
                        </div>

                        <div className=" mt-6 flex flex-row items-center ">
                            <div className="flex justify-between  lg:flex-row  lg:items-center @sm:flex-col @lg:w-[21%] @xl:w-[19%] @2xl:w-[19%]">
                                <p className="  text-[13px] font-medium text-[#1A3353] lg:ms-2   lg:w-[65px] @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                    Start Time
                                </p>
                            </div>
                            <div className="flex flex-row items-center  @2xl:w-[76%] ">
                                <div className="w-[20%] ">
                                    <TimePicker
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                            backgroundColor: !serviceCategoryId
                                                ? '#f5f5f5'
                                                : undefined,
                                            color: !serviceCategoryId
                                                ? '#a0a0a0'
                                                : undefined,
                                            cursor: !serviceCategoryId
                                                ? 'not-allowed'
                                                : undefined,
                                        }}
                                        format="HH:mm"
                                        value={startTime}
                                        minuteStep={5}
                                        // className="lg:w-[80%] @sm:w-[100%]"
                                        onChange={handleStartTimeChange}
                                        disabled={
                                            !serviceCategoryId ||
                                            (!isEdit && !!scheduleId)
                                        }
                                    />
                                </div>

                                <div className=" w-[38%] lg:translate-y-3 @xl:translate-x-3 ">
                                    <Form.Item
                                        className="PA-form-duration-input"
                                        label={
                                            <p className=" text-left  @sm:w-full">
                                                Duration
                                            </p>
                                        }
                                        name="duration"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select duration',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            className="ms-2 w-[90%] border-b-1"
                                            placeholder="Select Duration"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            onChange={handleDuration}
                                            options={durationOption}
                                            disabled={
                                                !serviceCategoryId ||
                                                (!isEdit && !!scheduleId)
                                            }
                                            style={{
                                                backgroundColor:
                                                    !serviceCategoryId
                                                        ? '#f5f5f5'
                                                        : undefined,
                                                color: !serviceCategoryId
                                                    ? '#a0a0a0'
                                                    : undefined,
                                                cursor: !serviceCategoryId
                                                    ? 'not-allowed'
                                                    : undefined,
                                            }}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" flex w-[38%] justify-end lg:flex-row   lg:items-center @sm:flex-col @xl:gap-0 ">
                                    <p className="translate-x-3 font-medium text-[#1A3353] lg:w-[40%] @lg:text-[12px] @xl:w-[35%] @xl:text-[13px]">
                                        End Time
                                    </p>
                                    <TimePicker
                                        format="HH:mm"
                                        disabled
                                        value={endTime}
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                    />
                                </div>
                            </div>
                        </div>

                        <div className=" mt-6 flex w-full justify-between">
                            <div className="lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left @sm:w-full">
                                            Room
                                        </p>
                                    }
                                    name="roomId"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select room',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        placeholder="Select Room"
                                        value={form.getFieldValue('roomId')} // or controlled via state
                                        onChange={(value) => {
                                            form.setFieldValue('roomId', value);
                                            const selected = roomData.find(
                                                (room: any) =>
                                                    room.value === value
                                            );
                                            if (selected) {
                                                setSelectedRoomReason(
                                                    selected.reason || null
                                                );
                                                setSelectedRoomAvailability(
                                                    selected.isAvailable
                                                );
                                            } else {
                                                setSelectedRoomReason(null);
                                                setSelectedRoomAvailability(
                                                    null
                                                );
                                            }
                                        }}
                                        optionLabelProp="label"
                                        className="border-b"
                                    >
                                        {roomData.map((room: any) => (
                                            <Select.Option
                                                key={room.value}
                                                value={room.value}
                                                label={room.label} // this ensures correct label is used in the input
                                            >
                                                <div
                                                    style={{
                                                        display: 'flex',
                                                        justifyContent:
                                                            'space-between',
                                                        alignItems: 'center',
                                                        padding: '6px 12px',
                                                        fontWeight: 500,
                                                    }}
                                                >
                                                    <span
                                                        style={{
                                                            color: '#1A3353',
                                                        }}
                                                    >
                                                        {room.label}
                                                    </span>
                                                    <span
                                                        style={{
                                                            fontSize: '12px',
                                                            fontWeight: 600,
                                                            color: room.isAvailable
                                                                ? '#16A34A'
                                                                : 'red',
                                                        }}
                                                    >
                                                        {room.isAvailable
                                                            ? 'Available'
                                                            : 'Not Available'}
                                                    </span>
                                                </div>
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                                {selectedRoomReason &&
                                    !selectedRoomAvailability && (
                                        <div
                                            style={{
                                                color: '#ff4d4f',
                                                marginTop: '4px',
                                                fontSize: '13px',
                                            }}
                                        >
                                            {selectedRoomReason}
                                        </div>
                                    )}
                            </div>
                        </div>
                        <div className=" flex w-full justify-between lg:mb-4">
                            <div className=" lg:w-[95%] @sm:w-full">
                                <Form.Item label="Notes" name="notes">
                                    <TextArea
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                        }}
                                        autoSize={{ minRows: 1, maxRows: 2 }}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                    </div>
                </div>
                {(!scheduleId || (scheduleId && isEdit)) && (
                    <>
                        <div className="flex w-[100%] flex-row gap-5 pe-10 lg:justify-end @sm:justify-center">
                            <Form.Item>
                                <div
                                    className=""
                                    style={{ display: 'flex', gap: '10px' }}
                                >
                                    <Button
                                        loading={submitCheckInLoader}
                                        className="border border-[#1A3353] px-20 py-7 text-2xl"
                                        onClick={() =>
                                            onFinish(
                                                form.getFieldsValue(),
                                                true
                                            )
                                        }
                                    >
                                        Save & Check in
                                    </Button>
                                </div>
                            </Form.Item>
                            <Form.Item>
                                <div
                                    className=""
                                    style={{ display: 'flex', gap: '10px' }}
                                >
                                    <Button
                                        loading={submitLoader}
                                        className="bg-purpleLight px-20 py-7 text-2xl"
                                        type="primary"
                                        htmlType="submit"
                                    >
                                        Save
                                    </Button>
                                </div>
                            </Form.Item>
                        </div>
                    </>
                )}
            </Form>
        </ConfigProvider>
    );
};

export default BookingForm;
