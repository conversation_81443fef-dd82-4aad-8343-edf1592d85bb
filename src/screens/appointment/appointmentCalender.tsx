import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Calendar, dayjsLocalizer } from 'react-big-calendar';
import dayjs from 'dayjs';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import {
    AimOutlined,
    CalendarOutlined,
    CloseOutlined,
    CreditCardOutlined,
    DeleteOutlined,
    DownOutlined,
    EditOutlined,
    EyeOutlined,
    FilterOutlined,
    InfoCircleOutlined,
    LeftOutlined,
    MinusSquareOutlined,
    RightOutlined,
    UnorderedListOutlined,
    UserOutlined,
} from '@ant-design/icons';
import {
    Avatar,
    Button,
    Checkbox,
    ConfigProvider,
    Dropdown,
    Form,
    Input,
    Menu,
    Modal,
    Popover,
    Select,
    DatePicker,
} from 'antd';
import ScheduleModal from '~/components/staff/scheduleModal';
import AddFacilityAvailabilityModal from '../gym/addFacilityAvailabilityModal';
import EditFacilityAvailabilityModal from '../gym/editFacilityAvailabilityModal';
import BookAppointmentModal from './bookAppointmentModal';
import { capitalizeFirstLetter, goBack } from '~/components/common/function';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    BookedAppointmentList,
    CanceledBookedAppointment,
    DeleteBookedAppointment,
    StaffAvailabilityDelete,
    StaffAvailabilityList,
    TrainerListing,
} from '~/redux/actions/appointment-action';
import {
    FacilitiesList,
    GetFacilityListByStaffId,
} from '~/redux/actions/facility-action';
import { useSelector } from 'react-redux';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { useLoader } from '~/hooks/useLoader';
import DeleteModal from '~/components/common/deleteModal';
import { SetFacilityId } from '~/redux/slices/appointment-slice';
import { Link } from 'wouter';
import { ServiceCategoryListByStaffId } from '~/redux/actions/serviceCategoryAction';
import BookingModal from './booking-modal';
import {
    BookedCalendarData,
    CancelScheduling,
    CheckInScheduling,
} from '~/redux/actions/scheduling-action';
import {
    roomListing,
    roomListingByFacilityId,
} from '~/redux/actions/room-action';
import SchedulingReportModal from './schedulingReportAtGlanceModal';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import CancelAllScheduleModal from './CancelAllModal';
const { Option } = Select;
const { RangePicker } = DatePicker;

const localizer = dayjsLocalizer(dayjs);

interface Event {
    id: number;
    title: string;
    trainerName?: string;
    start: Date;
    end: Date;
    instructor: string;
    resourceId: string;
    classType?: string;
    capacity?: string;
    paymentStatus?: string;
}

interface Resource {
    id: string;
    title: string;
}

const CustomHeader = ({ date, label }: any) => {
    return <div>{dayjs(date).format('ddd, DD MMMM YYYY')}</div>;
};

const AppointmentCalendar: React.FC = () => {
    const dispatch = useAppDispatch();
    const [view, setView] = useState<string>('day');
    const [date, setDate] = useState<Date>(new Date());
    const [isEditStaffAvailable, setIsStaffAvailable] =
        useState<boolean>(false);
    const [availabilityId, setAvailabilityId] = useState<string>('');
    const { role, user } = useSelector((state: any) => state.auth_store);
    const [slotUpdateInfo, setSlotUpdateInfo] = useState<any>(null);
    const [slotSelectedInfo, setSlotSelectedInfo] = useState<any>(null);

    const [selectedSlotInfo, setSelectedSlotInfo] = useState<any>(null);

    const [selectedLocation, setSelectedLocation] = useState<string>();
    const [selectedCategories, setSelectedCategories] = useState<any>([]);
    const [selectedType, setSelectedType] = useState<string | null>();
    const [selectedInstructor, setSelectedInstructor] = useState<string[]>([]);
    const [selectedRooms, setSelectedRooms] = useState<string[]>([]);
    const [selectedFilterType, setSelectedFilterType] =
        useState<string>('instructors');
    const [selectedEvent, setSelectedEvent] = useState<any>();
    const [openScheduleModal, setOpenScheduleModal] = useState<boolean>(false);
    const [openBookAppointmentModal, setOpenBookAppointmentModal] =
        useState<boolean>(false);
    const [scheduleData, setScheduleData] = useState<any>(null);
    const [scheduleSelectedEventData, setScheduleSelectedEventData] =
        useState<any>(null);
    const [openAddFacilityModal, setOpenAddFacilityModal] =
        useState<boolean>(false);
    const [openEditFacilityModal, setOpenEditFacilityModal] =
        useState<boolean>(false);
    const [isCalender, setIsCalender] = useState<boolean>(false);
    const [popoverVisible, setPopoverVisible] = useState<boolean>(false);
    const [popoverContent, setPopoverContent] = useState<any>(null);
    const [popoverPosition, setPopoverPosition] = useState<{
        top: number;
        left: number;
    }>({ top: 0, left: 0 });
    const [eventDropdownVisible, setEventDropdownVisible] =
        useState<boolean>(false);
    const [eventDropdownPosition, setEventDropdownPosition] = useState<{
        top: number;
        left: number;
    }>({ top: 0, left: 0 });
    const [eventDropdownContent, setEventDropdownContent] = useState<any>(null);
    const [addEventDropdownVisible, setAddEventDropdownVisible] =
        useState<boolean>(false);
    const [addEventDropdownPosition, setAddEventDropdownPosition] = useState<{
        top: number;
        left: number;
    }>({ top: 0, left: 0 });
    const [isCourseEnabled, setIsCourseEnabled] = useState(false);

    const [selectedWidth, setSelectedWidth] = useState(
        window.innerWidth > 768 ? 150 : 130
    );
    const [modalVisible, setModalVisible] = useState(false);
    const [schedulingReportAtGlanceModel, setSchedulingReportAtGlanceModel] =
        useState<boolean>(false);

    const [deleteTrainerId, setDeleteTrainerId] = useState<string | null>(null);
    const [isAddUnavailability, setIsAddUnavailability] =
        useState<boolean>(false);

    const [CancelScheduleModal, setCancelScheduleModal] =
        useState<boolean>(false);

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        calendarSchedulingList: state.scheduling_store.calendarSchedulingList,
        facilityListByStaffId: state.facility_store.facilityListByStaffId,
        trainerList: state.appointment_store.trainerList,
        staffAvailabilityList: state.appointment_store.staffAvailabilityList,
        facilityId: state.appointment_store.facilityId,
        ServiceCategoryListByStaffId:
            state.service_category_store.ServiceCategoryListByStaffId,
        roomList: state.room_store.roomList,
        roomListByFacilityId: state.room_store.roomListByFacilityId,
        role: state.auth_store.role,
    }));

    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasAvailabilityWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_AVAILABILITY &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_AVAILABILITY_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasAvailabilityUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_AVAILABILITY &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_AVAILABILITY_UPDATE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasBookingUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_UPDATE_BOOKING
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasBookingWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_SCHEDULE_BOOKING
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasBookingCheckInPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_CHECKIN
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasBookingCancelPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_CANCEL
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    useEffect(() => {
        let startDate: Date;
        let endDate: Date;

        if (view === 'day') {
            startDate = dayjs(date).startOf('day').toDate();
            endDate = dayjs(date).endOf('day').toDate();
        } else if (view === 'week') {
            startDate = dayjs(date).startOf('week').toDate();
            endDate = dayjs(date).endOf('week').toDate();
        } else {
            startDate = dayjs(date).startOf('day').toDate();
            endDate = dayjs(date).endOf('day').toDate();
        }

        dispatch(
            BookedCalendarData({
                facilityId: [selectedLocation],
                trainerId: selectedInstructor,
                roomId: selectedRooms,
                classType: selectedType,
                serviceCategory: selectedCategories,
                startDate: startDate,
                endDate: endDate,
            })
        );
    }, [
        selectedLocation,
        selectedType,
        selectedInstructor,
        selectedCategories,
        selectedRooms,
        openBookAppointmentModal,
        date,
        view,
        CancelScheduleModal,
    ]);

    useEffect(() => {
        if (selectedLocation)
            dispatch(
                roomListing({
                    facilityId: selectedLocation,
                    status: true,
                })
            );
    }, [selectedLocation]);

    useEffect(() => {
        if (selectedLocation)
            dispatch(
                roomListingByFacilityId({
                    roomIds: selectedRooms,
                    facilityId: selectedLocation,
                    serviceCategoryId: selectedCategories,
                    classType: [...(selectedType ? [selectedType] : [])],
                })
            );
    }, [selectedLocation, selectedRooms]);

    useEffect(() => {
        dispatch(
            ServiceCategoryListByStaffId({
                facilityIds: selectedLocation ? [selectedLocation] : [],
                trainerIds: selectedInstructor,
                classType: selectedType,
            })
        );
    }, [selectedLocation, selectedInstructor, selectedType]);

    useEffect(() => {
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_setup_courses',
            })
        ).then((response: any) => {
            setIsCourseEnabled(response?.payload?.data?.data?.isEnabled);
        });
    }, []);

    const [filteredResources, setFilteredResources] = useState<Resource[]>([]);
    const [loader, startLoader, endLoader] = useLoader();
    const [isDeleteModalVisible, setDeleteIsModalVisible] = useState(false);
    const [deleteDates, setDeleteDates] = useState<any>([]);
    const [slotDeleteModal, setSlotDeleteModal] = useState<boolean>(false);
    const [deleteMessage, setDeleteMessage] = useState<string>('');
    const [deleteType, setDeleteType] = useState<string>('');
    const [isStaffEdit, setIsStaffEdit] = useState<boolean>(false);

    const handleCancel = () => {
        setDeleteIsModalVisible(false);
        setDeleteDates([]);
    };

    const onChange = (dates: any) => {
        setDeleteDates(dates);
    };

    const resources = useMemo(() => {
        return store.trainerList?.map((item: any) => ({
            title: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
            id: item.userId,
        }));
    }, [store.trainerList]);

    const roomResources = useMemo(() => {
        return store.roomListByFacilityId?.map((item: any) => ({
            title: item.roomName,
            id: item._id,
        }));
    }, [store.roomListByFacilityId]);

    useEffect(() => {
        if (role === RoleType.TRAINER && !hasAvailabilityWritePermission) {
            setFilteredResources([
                {
                    title: capitalizeFirstLetter(
                        `${user?.firstName} ${user?.lastName}`
                    ),
                    id: user._id,
                },
            ]);
        } else {
            if (selectedInstructor?.length === 0) {
                setFilteredResources(resources);
            } else {
                const filtered = resources?.filter((resource) =>
                    selectedInstructor?.includes(resource.id)
                );
                setFilteredResources(filtered);
            }
        }
    }, [selectedInstructor, resources]);

    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    const TrainerOptions = store.trainerList?.map((item: any) => ({
        value: item.userId,
        label: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
        id: item._id,
    }));

    const RoomOptions = store.roomList?.map((item: any) => ({
        value: item._id,
        label: item.roomName,
        id: item._id,
    }));

    // location options by staff(for trainer role)
    const LocationOptionByStaff = store.facilityListByStaffId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const ServiceTypeOption = store.ServiceCategoryListByStaffId?.map(
        (item: any) => ({
            value: item.payRateId,
            label: item.name,
            id: item.payRateId,
        })
    );

    const openModal = () => {
        setModalVisible(true);
    };
    const closeModal = () => {
        setModalVisible(false);
    };

    useEffect(() => {
        if (role === RoleType.TRAINER)
            dispatch(GetFacilityListByStaffId({ staffId: user._id })).then(
                (res: any) => {
                    setSelectedLocation(res?.payload?.data?.data?.[0]?._id);
                    dispatch(SetFacilityId(res?.payload?.data?.data?.[0]?._id));
                }
            );
        else
            dispatch(FacilitiesList({ page: 1, pageSize: 30 })).then(
                (res: any) => {
                    setSelectedLocation(
                        res?.payload?.data?.data?.list?.[0]?._id
                    );
                    dispatch(SetFacilityId(res?.payload?.data?.data?.[0]?._id));
                }
            );
    }, []);

    useEffect(() => {
        const startDate =
            view === 'day'
                ? dayjs(date).startOf('day').toDate()
                : dayjs(date).startOf('week').toDate();
        const endDate =
            view === 'day'
                ? dayjs(date).endOf('day').toDate()
                : dayjs(date).endOf('week').toDate();

        const data = {
            startDate: startDate,
            endDate: endDate,
        } as any;

        if (
            selectedInstructor?.length > 0 &&
            !selectedInstructor?.includes('All instructors')
        ) {
            data['userIds'] = selectedInstructor;
        }
        if (
            selectedLocation &&
            selectedLocation !== 'All available locations'
        ) {
            data['facilityIds'] = [selectedLocation];
        }

        if (selectedType) data.classType = selectedType;
        if (selectedCategories.length) data.payRateIds = selectedCategories;
        if (role === RoleType.TRAINER && !hasAvailabilityWritePermission) {
            data['userIds'] = [user?._id];
            // data['facilityIds'] = [LocationOptionByStaff[0]?.id];
            if (!data['facilityIds'] || data['facilityIds'].length === 0) {
                console.warn('Facility ID is required for TRAINER role.');
                return;
            }
        }

        dispatch(StaffAvailabilityList({ reqData: data }));
    }, [
        selectedInstructor,
        selectedLocation,
        selectedCategories,
        selectedType,
        view,
        date,
        openBookAppointmentModal,
    ]);

    useEffect(() => {
        // if (role !== RoleType.TRAINER) {
        if (
            selectedLocation &&
            selectedLocation !== 'All available locations'
        ) {
            dispatch(
                TrainerListing({
                    facilityId: selectedLocation,
                    isForFilter: true,
                    isActive: true,
                })
            );
        } else if (role === RoleType.WEBMASTER && store.facilityId) {
            dispatch(
                TrainerListing({
                    facilityId: store.facilityId,
                    isForFilter: true,
                    isActive: true,
                })
            );
        }
        // }
        setSelectedInstructor([]);
    }, [selectedLocation]);

    const handleNavigate = (action: 'PREV' | 'NEXT' | 'TODAY') => {
        let newDate = date;

        switch (action) {
            case 'PREV':
                newDate =
                    view === 'day'
                        ? dayjs(date).subtract(1, 'day').toDate()
                        : view === 'week'
                        ? dayjs(date).subtract(1, 'week').toDate()
                        : dayjs(date).subtract(1, 'month').toDate();
                break;
            case 'NEXT':
                newDate =
                    view === 'day'
                        ? dayjs(date).add(1, 'day').toDate()
                        : view === 'week'
                        ? dayjs(date).add(1, 'week').toDate()
                        : dayjs(date).add(1, 'month').toDate();
                break;
            case 'TODAY':
                newDate = new Date();
                break;
        }

        setDate(newDate);
    };

    // const eventStyleGetter = (event: Event) => {
    //     let className = 'text-[.8vw] p-1 overflow-hidden';
    //     const classType = event?.events?.[0]?.classType || event.classType;
    //     if (classType === 'personalAppointment') {
    //         className += ' rounded-0 border-0';
    //     } else if (classType === 'bookings') {
    //         className += ' rounded-0 border-0';
    //     } else if (event.title === 'Unavailable') {
    //         className += ' rounded-0 border-0';
    //     }
    //     return { className };
    // };

    const eventStyleGetter = (event: Event) => {
        let className = 'text-xl p-1 overflow-hidden';
        const classType = event?.events?.[0]?.classType || event.classType;
        if (classType === 'personalAppointment') {
            className += ' bg-[#E4EBF5] text-[#000]';
        } else if (classType === 'bookings') {
            className += ' bg-[#E6F5EB] text-[#000]';
        } else if (event.title === 'Unavailable') {
            className += ' bg-red-200 text-red-800';
        } else if (classType === 'courses') {
            className += ' bg-[#F5E3C9] text-[#000]';
        } else if (classType === 'classes') {
            className += ' bg-[#e8e1f4] text-[#000]';
        }
        return { className };
    };

    const openScheduleUpdateModal = () => {
        setOpenScheduleModal(true);
        setAddEventDropdownVisible(false);
        setIsStaffAvailable(false);
        setIsStaffEdit(
            role === RoleType.TRAINER && !hasAvailabilityWritePermission
                ? true
                : false
        );
    };

    const closeScheduleUpdateModal = () => {
        setOpenScheduleModal(false);
        setIsStaffAvailable(false);
        setSlotUpdateInfo(null);
        setSelectedSlotInfo(null);
        setIsAddUnavailability(false);
    };

    const openScheduleUpdateModalFromEvent = (
        isMultiple = false,
        isMarkUnavailable = false
    ) => {
        const isCurrentlyUnavailable =
            slotUpdateInfo?.availabilityStatus === 'unavailable';

        const isFromMarkAvailable =
            !isMarkUnavailable && isCurrentlyUnavailable;

        const formType = isMarkUnavailable ? 'unavailable' : 'available';
        setIsStaffAvailable(true);
        setSlotUpdateInfo((prev: any) => ({
            ...prev,
            isMultiple,
            isMarkUnavailable: isMarkUnavailable,
            isFromMarkAvailable: isFromMarkAvailable,
            formType: formType,
            availabilityStatus: isMarkUnavailable
                ? 'unavailable'
                : prev.availabilityStatus,
        }));
        setAddEventDropdownVisible(false);
        setIsCalender(true);
        setOpenScheduleModal(true);
    };

    const generateMessage = (eventDetails: any) => {
        const startTime = new Date(eventDetails.start);

        const serviceTypes: any = {
            personalAppointment: 'personal appointment',
            classes: 'classes',
            bookings: 'booking',
            courses: 'course',
        };
        const options: any = { year: 'numeric', month: 'long', day: 'numeric' };
        const eventDate = startTime.toLocaleDateString('en-US', options);

        let message: any;
        if (eventDetails.classType === 'courses') {
            message = (
                <p className="text-[#1a3353]">
                    The following {serviceTypes[eventDetails.classType]} will be
                    cancelled on <strong>{eventDate}</strong>.
                </p>
            );
        } else {
            message = (
                <p className="text-[#1a3353]">
                    The following {serviceTypes[eventDetails.classType]} for
                    <strong> {eventDetails.clientName}</strong> will be
                    cancelled on
                    <strong> {eventDate}</strong>.
                </p>
            );
        }
        setDeleteMessage(message);
    };

    const deleteSlotAvailability = () => {
        if (selectedSlotInfo) {
            const { dateId, slotId } = selectedSlotInfo;
            const payload = {
                type: 'slot',
                dateId: dateId,
                slotId: slotId,
            };
            // console.log('Payload--------------------------', payload);
            dispatch(
                StaffAvailabilityDelete({
                    payload: payload,
                    view: view,
                    date: date,
                    isStaffDetails:
                        role === RoleType.TRAINER && isStaffEdit
                            ? isStaffEdit
                            : false,
                })
            ).then((res: any) => {
                if (
                    res?.payload?.status === 200 ||
                    res?.payload?.status === 201
                ) {
                    setSlotDeleteModal(false);
                    setDeleteMessage('');
                    setDeleteType('');
                }
            });
        }
    };

    const deleteDayAvailability = () => {
        // if (selectedSlotInfo) {
        // const { facilityId, trainerId, dateId } = selectedSlotInfo;
        const start = dayjs(deleteDates[0]).format(
            'YYYY-MM-DDTHH:mm:ss.SSS[Z]'
        );
        const end = dayjs(deleteDates[1]).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');

        const payload = {
            type: 'custom',
            startDate: start,
            endDate: end,
            facilityId: selectedLocation,
            userId: deleteTrainerId,
        };
        dispatch(
            StaffAvailabilityDelete({
                payload: payload,
                view: view,
                date: date,
                isStaffDetails:
                    role === RoleType.TRAINER && isStaffEdit
                        ? isStaffEdit
                        : false,
            })
        ).then((res: any) => {
            if (res?.payload?.status === 200 || res?.payload?.status === 201) {
                setDeleteIsModalVisible(false);
                setDeleteTrainerId(null);
                setDeleteDates([]);
            }
        });
        // }
    };
    const confirmCheckin = () => {
        dispatch(
            CheckInScheduling({
                scheduleId: scheduleData.id,
            })
        );
        setSlotDeleteModal(false);
        setDeleteMessage('');
        setDeleteType('');
        setScheduleData(null);
    };

    const deleteBookedAppointment = () => {
        dispatch(
            CancelScheduling({
                scheduleId: scheduleData?.events?.[0]?.id || scheduleData?.id,
            })
        )
            .unwrap()
            .then((res: any) => {
                dispatch(
                    BookedCalendarData({
                        facilityId: [selectedLocation],
                        trainerId: selectedInstructor,
                        roomId: selectedRooms,
                        classType: selectedType,
                        serviceCategory: selectedCategories,
                    })
                );
            });
        setSlotDeleteModal(false);
        setDeleteMessage('');
        setDeleteType('');
        setScheduleData(null);
        setScheduleSelectedEventData(null);
    };
    // For permissions

    const addEventMenu = (
        <Menu className=" pb-5">
            <div className="flex  justify-end ">
                <CloseOutlined
                    onClick={() => setAddEventDropdownVisible(false)}
                    className="cursor-pointer"
                />
            </div>
            {(store.role === RoleType.ORGANIZATION ||
                hasBookingWritePermission) &&
                slotUpdateInfo?.availabilityStatus !== 'unavailable' && (
                    <Menu.Item
                        onClick={() => {
                            setOpenBookAppointmentModal(true);
                            setAddEventDropdownVisible(false);
                        }}
                        className="rounded-none border-b border-[#bfc2c4]  bg-white text-[#455560] "
                        key="1"
                    >
                        <span className="pe-5">
                            <CalendarOutlined />
                        </span>
                        Book Session
                    </Menu.Item>
                )}
            {(store.role === RoleType.ORGANIZATION ||
                hasAvailabilityUpdatePermission) && (
                <Menu.Item
                    onClick={() => openScheduleUpdateModalFromEvent()}
                    className="rounded-none border-b border-[#bfc2c4] bg-white text-[#455560] "
                    key="1"
                >
                    <span className="pe-5">
                        <EditOutlined />
                    </span>
                    {slotUpdateInfo?.availabilityStatus === 'unavailable'
                        ? 'Edit Unavailability'
                        : 'Edit Availability'}
                </Menu.Item>
            )}

            {/* <Menu.Item
                onClick={() => openScheduleUpdateModalFromEvent(true)}
                key="2"
                className="rounded-none border-b  border-[#455560] bg-white text-[#455560]"
            >
                <span>
                    <EditOutlined className="pe-5" />
                </span>
                Edit Multiple Availability
            </Menu.Item> */}
            {/* <Menu.Item
                key="3"
                className="rounded-none border-b  border-[#455560] bg-white text-[#455560]"
            >
                <span>
                    <Checkbox className="pe-5" />
                </span>
                Mark as unavailable
            </Menu.Item>
            <Menu.Item
                key="4"
                className="rounded-none border-b  border-[#455560] bg-white text-[#455560]"
            >
                <span>
                    <EditOutlined className="pe-5" />
                </span>
                Edit facility availability
            </Menu.Item> */}
            <Menu.Item
                onClick={() => {
                    setSlotDeleteModal(true), setAddEventDropdownVisible(false);
                    setDeleteMessage(
                        `Are you sure you want to delete time slot from ${selectedSlotInfo?.slotFrom} to ${selectedSlotInfo?.slotTo} ?`
                    );
                    setDeleteType('timeSlot');
                }}
                key="5"
                className="rounded-none border-b  border-[#bfc2c4] bg-white text-[#455560]"
            >
                <span>
                    <DeleteOutlined className="pe-5" />
                </span>
                {slotUpdateInfo?.availabilityStatus === 'unavailable'
                    ? 'Remove unavailable Shift(s)'
                    : 'Remove Shift(s)'}
            </Menu.Item>
            {/* <Menu.Item
                onClick={() => {
                    setDeleteIsModalVisible(true),
                        setAddEventDropdownVisible(false);
                }}
                key="6"
                className="rounded-none border-b  border-[#bfc2c4] bg-white text-[#455560]"
            >
                <span>
                    <DeleteOutlined className="pe-5" />
                </span>
                Delete Custom Availability
            </Menu.Item> */}
            {(store.role === RoleType.ORGANIZATION ||
                hasAvailabilityUpdatePermission) &&
                slotUpdateInfo?.availabilityStatus !== 'unavailable' && (
                    <Menu.Item
                        onClick={() =>
                            openScheduleUpdateModalFromEvent(
                                false,
                                slotUpdateInfo?.availabilityStatus !==
                                    'unavailable'
                            )
                        }
                        key="6"
                        className="rounded-none border-b  border-[#bfc2c4] bg-white text-[#455560]"
                    >
                        <span>
                            {/* <EditOutlined className="pe-5" /> */}
                            <MinusSquareOutlined className="pe-5" />
                        </span>
                        {slotUpdateInfo?.availabilityStatus === 'unavailable'
                            ? 'Mark as available'
                            : 'Mark as unavailable'}
                    </Menu.Item>
                )}
        </Menu>
    );

    const moreMenu = (
        <Menu>
            <Menu.Item onClick={() => openScheduleUpdateModal()} key="1">
                Add Shift(s)
            </Menu.Item>
            {(store.role === RoleType.ORGANIZATION ||
                hasAvailabilityUpdatePermission ||
                hasAvailabilityWritePermission) && (
                <Menu.Item
                    onClick={() => {
                        setIsAddUnavailability(true);
                        openScheduleUpdateModal();
                    }}
                    key="11"
                >
                    Add Unavailability
                </Menu.Item>
            )}
            {/* {role !== RoleType.TRAINER && (
                <>
                    <Menu.Item
                        onClick={() => setOpenAddFacilityModal(true)}
                        key="2"
                    >
                        Add Facility Unavailability
                    </Menu.Item>
                    <Menu.Item
                        onClick={() => {
                            setOpenEditFacilityModal(true);
                        }}
                        key="3"
                    >
                        Edit Facility Unavailability
                    </Menu.Item>
                </>
            )} */}
            {/* <Menu.Item
                onClick={() => setOpenBookAppointmentModal(true)}
                key="4"
            >
                Book Session
            </Menu.Item> */}
            {/* <Menu.Item
                onClick={() => setSchedulingReportAtGlanceModel(true)}
                key="5"
            >
               Schedule at a Glance
            </Menu.Item> */}
            {/* <Menu.Item key="5">Schedule Class</Menu.Item> */}
            <Menu.Item
                onClick={() => {
                    setDeleteIsModalVisible(true),
                        setAddEventDropdownVisible(false);
                }}
                key="6"
            >
                Clear All Shift(s)
            </Menu.Item>
        </Menu>
    );

    const getFilteredEvents = (events: any[]) => {
        if (selectedFilterType === 'rooms')
            return events
                ?.filter((event) => {
                    return (
                        selectedRooms?.length === 0 ||
                        selectedRooms.includes(event.roomId)
                    );
                })
                .map((event) => ({ ...event, resourceId: event.roomId }));
        else
            return events
                ?.filter((event) => {
                    const matchesInstructor =
                        selectedInstructor?.length === 0 ||
                        selectedInstructor.includes(event.trainerId);
                    const matchesCategory =
                        !selectedCategories.length ||
                        selectedCategories.includes(event.classType);

                    return matchesInstructor && matchesCategory;
                })
                ?.map((event) => ({ ...event, resourceId: event.trainerId }));
    };

    const filteredEvents = useMemo(
        () => getFilteredEvents(store.calendarSchedulingList),
        [store.calendarSchedulingList, selectedInstructor, selectedCategories]
    );

    const handleEventClick = (event: any, e: React.MouseEvent) => {
        const isPast = new Date(event.end) < new Date();

        setSelectedEvent(event);
        setScheduleData(event);
        const eventMenu = (
            <Menu className="z-10 w-[150px] p-5">
                <div className="flex justify-end">
                    <CloseOutlined
                        onClick={() => {
                            setEventDropdownVisible(false);
                            setScheduleData(null);
                            setSelectedEvent(null);
                            setScheduleSelectedEventData(null);
                        }}
                        className="cursor-pointer"
                    />
                </div>
                <Menu.Item
                    className="rounded-none border-b   border-[#bfc2c4] bg-white text-[#455560] "
                    key="1"
                    onClick={() => {
                        setScheduleData({
                            ...(event?.events?.length > 1
                                ? scheduleSelectedEventData
                                : event?.events?.[0] || event),
                            isEdit: false,
                        });
                        setScheduleSelectedEventData((prev: any) => ({
                            ...prev,
                            isEdit: false,
                        }));
                        setOpenBookAppointmentModal(true);
                        setEventDropdownVisible(false);
                    }}
                >
                    <span>
                        <EyeOutlined className="pe-5" />
                    </span>
                    View
                </Menu.Item>
                {(store.role === RoleType.ORGANIZATION ||
                    hasBookingUpdatePermission) && (
                    <Menu.Item
                        // className="rounded-none border-b   border-[#455560] bg-white text-[#455560] "
                        className={`rounded-none border-b border-[#bfc2c4] bg-white ${
                            isPast
                                ? 'cursor-not-allowed bg-[#e8f5f3]'
                                : 'text-[#455560]'
                        }`}
                        key="2"
                        onClick={() => {
                            setScheduleData({
                                ...(event?.events?.length > 1
                                    ? scheduleSelectedEventData
                                    : event?.events?.[0] || event),
                                isEdit: true,
                            });
                            setScheduleSelectedEventData((prev: any) => ({
                                ...prev,
                                isEdit: true,
                            }));
                            setOpenBookAppointmentModal(true);
                            setEventDropdownVisible(false);
                        }}
                        disabled={isPast}
                    >
                        <span>
                            <EditOutlined className="pe-5" />
                        </span>
                        Edit
                    </Menu.Item>
                )}
                {(store.role === RoleType.ORGANIZATION ||
                    hasBookingCancelPermission) && (
                    <Menu.Item
                        className={`rounded-none border-b border-[#bfc2c4] bg-white ${
                            isPast
                                ? 'cursor-not-allowed bg-[#e8f5f3]'
                                : 'text-[#455560]'
                        }`}
                        key="2"
                        onClick={() => {
                            setScheduleData({
                                ...(event?.events?.length > 1
                                    ? scheduleSelectedEventData
                                    : event?.events?.[0] || event),
                                isEdit: false,
                            });
                            setSlotDeleteModal(true);
                            generateMessage(event?.events?.[0] || event);
                            setDeleteType('bookedAppointment');
                            setEventDropdownVisible(false);
                        }}
                        disabled={isPast}
                    >
                        <span>
                            <EditOutlined className="pe-5" />
                        </span>
                        Cancel
                    </Menu.Item>
                )}
                {event?.classType === 'personalAppointment' &&
                    (store.role === RoleType.ORGANIZATION ||
                        hasBookingCancelPermission) && (
                        <Menu.Item
                            className={`rounded-none border-b border-[#bfc2c4] bg-white ${
                                isPast
                                    ? 'cursor-not-allowed bg-[#e8f5f3]'
                                    : 'text-[#455560]'
                            }`}
                            key="6"
                            onClick={() => {
                                setScheduleData({
                                    ...(event?.events?.length > 1
                                        ? scheduleSelectedEventData
                                        : event?.events?.[0] || event),
                                    isEdit: false,
                                });
                                setCancelScheduleModal(true);
                                setEventDropdownVisible(false);
                            }}
                            disabled={isPast}
                        >
                            <span>
                                <EditOutlined className="pe-5" />
                            </span>
                            Cancel All
                        </Menu.Item>
                    )}
                {event?.events?.[0]?.classType !== 'courses' &&
                    event?.classType !== 'courses' &&
                    (store.role === RoleType.ORGANIZATION ||
                        hasBookingCheckInPermission) && (
                        <Menu.Item
                            key="5"
                            className={`rounded-none border-b border-[#bfc2c4] bg-white ${
                                isPast
                                    ? 'cursor-not-allowed bg-[#e8f5f3]'
                                    : 'text-[#455560]'
                            }`}
                            onClick={() => {
                                setScheduleData({
                                    ...(event?.events?.length > 1
                                        ? scheduleSelectedEventData
                                        : event?.events?.[0] || event),
                                    isEdit: false,
                                });
                                setScheduleSelectedEventData((prev: any) => ({
                                    ...prev,
                                    isEdit: false,
                                }));
                                setSlotDeleteModal(true);
                                setDeleteMessage(
                                    'Are you sure you want to check in?'
                                );

                                setDeleteType('checkIn');
                                setEventDropdownVisible(false);
                            }}
                            disabled={isPast}
                        >
                            <span className="pe-5">
                                <Checkbox />
                            </span>
                            Check In
                        </Menu.Item>
                    )}
            </Menu>
        );
        setEventDropdownContent(eventMenu);
        setEventDropdownPosition({ top: e.clientY, left: e.clientX });
        if (event?.events?.length >= 1) {
            return;
        } else {
            setEventDropdownVisible(true);
        }
    };

    const CustomTimeSlot = ({ children, event }: any) => {
        const content = (
            <div className="flex flex-col gap-3 lg:w-[180px] ">
                {/* <div className="text-lg font-semibold">{event.title}</div> */}
                {event.trainerName && (
                    <div className="mb-2 border-b pb-2 text-2xl font-semibold">
                        <p className="text-lg text-[#616161]">
                            Trainer - {event.trainerName}
                        </p>
                    </div>
                )}
                <div className="flex items-center gap-3">
                    <Avatar size={14} icon={<UserOutlined />} />
                    <p className="text-lg text-[#616161]">
                        Service Category -{' '}
                        {event?.events?.[0]?.title || event.title}
                    </p>
                </div>
                {event?.events?.[0]?.classType !== 'courses' &&
                    event?.events?.[0]?.classType !== 'classes' &&
                    event?.classType !== 'courses' &&
                    event?.classType !== 'classes' && (
                        <div className="flex items-center gap-3">
                            <Avatar size={14} icon={<UserOutlined />} />
                            <p className="text-lg text-[#616161]">
                                Client Name -{' '}
                                {event?.events?.[0]?.clientName ||
                                    event.clientName}
                            </p>
                        </div>
                    )}

                <div className="flex flex-col gap-3">
                    <div className="flex items-center gap-3">
                        <InfoCircleOutlined />
                        <p className="text-lg text-[#616161]">
                            Date - {dayjs(event.start).format('YYYY-MM-DD')}
                        </p>
                    </div>
                    <div className="flex items-center gap-3">
                        <InfoCircleOutlined />
                        <p className="text-lg text-[#616161]">
                            Time - {dayjs(event.start).format('HH:mm')} -
                            {dayjs(event.end).format('HH:mm')}
                        </p>
                    </div>
                    {/* {view === 'week' &&
                        (event?.events?.[0]?.trainerName ||
                            event.trainerName) && (
                            <div className="flex items-center gap-3">
                                <Avatar size={14} icon={<UserOutlined />} />
                                <p className="text-lg text-[#616161]">
                                    Trainer -{' '}
                                    {event?.events?.[0]?.trainerName ||
                                        event.trainerName}
                                </p>
                            </div>
                        )} */}

                    {/* <div className="flex items-center gap-3">
                        <CreditCardOutlined />
                        <p className="text-lg text-[#616161]">
                            Plan Status - {event.paymentStatus}
                        </p>
                    </div> */}
                </div>
            </div>
        );

        return (
            <Popover content={content} placement="bottom" trigger="hover">
                {children}
            </Popover>
        );
    };

    // useEffect(() => {
    //     const handleResize = () => {
    //         setSelectedWidth(window.innerWidth > 768 ? 150 : 130);
    //     };

    //     window.addEventListener('resize', handleResize);

    //     // Cleanup the event listener on component unmount
    //     return () => {
    //         window.removeEventListener('resize', handleResize);
    //     };
    // }, []);

    const slotPropGetter = useCallback(
        (date: any, resourceId: any) => {
            const dayOfWeek = dayjs(date).format('dddd').toLowerCase();
            const dateString = dayjs(date).format('YYYY-MM-DD');
            const timeString = dayjs(date).format('HH:mm');

            const instructorData = store.staffAvailabilityList?.find(
                (instructor: any) =>
                    view === 'week' && selectedInstructor.length === 1
                        ? instructor?._id.userId === selectedInstructor[0]
                        : instructor?._id.userId === resourceId
            ) as any;

            if (!instructorData) {
                return { style: { backgroundColor: '#fff' } };
            }

            // Check if the date is within the overall availability range
            if (
                dayjs(date).isBefore(instructorData.startDate) ||
                dayjs(date).isAfter(instructorData.endDate)
            ) {
                return { style: { backgroundColor: '#fff' } };
            }

            // Check for unavailability
            const unavailableSlotSchedule = instructorData?.schedule?.find(
                (schedule: any) =>
                    dayjs(schedule.date).format('YYYY-MM-DD') === dateString &&
                    schedule.timeSlots.some(
                        (slot: any) =>
                            slot.availabilityStatus === 'unavailable' &&
                            timeString >= slot.from &&
                            timeString < slot.to
                    )
            );

            const unavailableSlot = unavailableSlotSchedule?.timeSlots.find(
                (slot: any) =>
                    slot.availabilityStatus === 'unavailable' &&
                    timeString >= slot.from &&
                    timeString < slot.to
            );

            if (unavailableSlot) {
                return {
                    style: { backgroundColor: '#f5f5f5' },
                    facilityId: instructorData?._id?.facilityId,
                    staffId: instructorData?._id?.userId,
                    staffName: instructorData?._id?.userId,
                    dateId: unavailableSlotSchedule?.dateId,
                    slotId: unavailableSlot?._id,
                    slotFrom: unavailableSlot?.from,
                    slotTo: unavailableSlot?.to,
                    availabilityStatus: 'unavailable',
                };
            }

            // Check for available slots with dateId from each schedule
            const availableSlotSchedule = instructorData?.schedule?.find(
                (schedule: any) =>
                    dayjs(schedule.date).format('YYYY-MM-DD') === dateString &&
                    schedule.timeSlots.some(
                        (slot: any) =>
                            slot.availabilityStatus === 'available' &&
                            timeString >= slot.from &&
                            timeString < slot.to
                    )
            );

            const availableSlot = availableSlotSchedule?.timeSlots.find(
                (slot: any) =>
                    slot.availabilityStatus === 'available' &&
                    timeString >= slot.from &&
                    timeString < slot.to
            );

            if (availableSlot) {
                return {
                    style: { backgroundColor: '#e8f5f3' },
                    facilityId: instructorData?._id?.facilityId,
                    staffId: instructorData?._id?.userId,
                    staffName: instructorData?._id?.userId,
                    dateId: availableSlotSchedule?.dateId,
                    slotId: availableSlot?._id,
                    slotFrom: availableSlot?.from,
                    slotTo: availableSlot?.to,
                    availabilityStatus: 'available',
                    classType: availableSlot?.classType,
                };
            }
            return { style: { backgroundColor: '#fff' } };
        },
        [store.staffAvailabilityList]
    );

    const handleSlotClick = (slotInfo: any) => {
        const { start, end, resourceId } = slotInfo;
        const slotProps = slotPropGetter(start, resourceId);

        const availabilityStatus = slotProps.availabilityStatus;

        const today = dayjs().startOf('day');
        if (!dayjs(start).isBefore(today)) {
            if (selectedFilterType === 'instructors') {
                if (
                    (availabilityStatus && slotInfo.action !== 'click') ||
                    (!availabilityStatus &&
                        (slotInfo.action === 'select' ||
                            slotInfo.action === 'click'))
                ) {
                    setOpenScheduleModal(true);
                    setSlotSelectedInfo({
                        startDate: start,
                        endDate: end,
                        resourceId: resourceId,
                        from: slotProps.slotFrom,
                        to: slotProps.slotTo,
                    });
                } else {
                    setSlotSelectedInfo({
                        startDate: start,
                        endDate: end,
                        resourceId: resourceId,
                        from: slotProps.slotFrom,
                        to: slotProps.slotTo,
                    });
                }
            } else if (
                selectedFilterType === 'rooms' &&
                (view === 'week' || view === 'day')
            ) {
                setOpenBookAppointmentModal(true);
                setSlotSelectedInfo({
                    startDate: start,
                    endDate: end,
                    from: slotProps.slotFrom,
                    to: slotProps.slotTo,
                    resourceId: resourceId,
                });
            } else {
                setSlotSelectedInfo({
                    startDate: start,
                    endDate: end,
                    resourceId: resourceId,
                    from: slotProps.slotFrom,
                    to: slotProps.slotTo,
                });
            }
        }

        // Handle week view
        if (selectedFilterType === 'instructors')
            setAvailabilityId(
                view === 'week' && selectedInstructor.length === 1
                    ? selectedInstructor[0]
                    : resourceId
            );

        if (
            // slotProps.style.backgroundColor === '#ffA500' ||
            slotProps.style.backgroundColor === '#fff'
        ) {
            return;
        }
        // const timeZone = dayjs.tz.guess();

        // console.log("Start and end--------------", start,end);
        // const currentDate = dayjs().utc().startOf('day');

        // const startDate = currentDate.format();
        // const endDate = currentDate.endOf('day').format();

        const startDate = dayjs(start).startOf('day').toDate();
        const endDate = dayjs(end).endOf('day').toDate();

        setSelectedSlotInfo({
            dateId: slotProps.dateId,
            slotId: slotProps.slotId,
            facilityId: slotProps.facilityId,
            ...(selectedFilterType === 'instructors' && {
                trainerId:
                    view === 'week' && selectedInstructor.length === 1
                        ? selectedInstructor[0]
                        : resourceId,
            }),
            ...(selectedFilterType === 'rooms' && {
                roomId: resourceId,
            }),
            slotFrom: slotProps.slotFrom,
            slotTo: slotProps.slotTo,
            classType: slotProps.classType,
        });
        setSlotUpdateInfo({
            facilityId: slotProps.facilityId,
            staffId: slotProps.staffId,
            ...(selectedFilterType === 'instructors' && {
                trainerId:
                    view === 'week' && selectedInstructor.length === 1
                        ? selectedInstructor[0]
                        : resourceId,
            }),
            ...(selectedFilterType === 'rooms' && {
                roomId: resourceId,
            }),
            startDate: startDate,
            endDate: endDate,
            availabilityStatus: slotProps.availabilityStatus,
            from: slotProps.slotFrom,
            to: slotProps.slotTo,
            classType: slotProps.classType,
        });

        setAddEventDropdownPosition({
            top: slotInfo?.box?.y,
            left: slotInfo?.box?.x,
        });
        setAddEventDropdownVisible(true);
    };

    const onView = useCallback((newView: any) => setView(newView), [setView]);

    const onNavigate = useCallback(
        (newDate: Date) => setDate(newDate),
        [setDate]
    );

    const handleViewChange = useCallback(
        (newView: string) => {
            setView(newView);
        },
        [setView]
    );

    const getClientNameClass = (classType: string, title?: string) => {
        if (title === 'Unavailable') return 'bg-[#f87171] text-white'; // Tailwind red-400

        switch (classType) {
            case 'personalAppointment':
                return 'bg-[#C9D8ED] text-black';
            case 'bookings':
                return 'bg-[#C3E4CE] text-black';
            case 'courses':
            case 'classes':
                return 'bg-[#E0C49D] text-black';
            default:
                return 'bg-[#e5e5e5] text-black'; // subtle fallback
        }
    };

    const CustomEvent = ({ event }: any) => {
        const clientName = event?.clientName || event?.events?.[0]?.clientName;
        const title = event?.title || event?.events?.[0]?.title;
        const classType = event?.classType || event?.events?.[0]?.classType;

        const clientNameClass = getClientNameClass(classType, title);

        return (
            <div className="text-12 font-medium leading-5 text-black">
                <div>{title}</div>
                {clientName && (
                    <div className={`text-10`}>
                        <div className={`w-[85%] ${clientNameClass} p-1 `}>
                            {clientName}
                        </div>
                    </div>
                )}
            </div>
        );
    };

    return (
        <>
            <div className="flex flex-row justify-end gap-4 pb-6">
                {(store.role === RoleType.ORGANIZATION ||
                    hasBookingWritePermission) && (
                    <Button
                        onClick={() => setOpenBookAppointmentModal(true)}
                        className="bg-purpleLight text-white"
                    >
                        Book Session
                    </Button>
                )}
                <Dropdown
                    trigger={['click']}
                    overlay={moreMenu}
                    // className="w-[%]"
                    placement="bottomRight"
                >
                    <Button className="text-[#bfbfbf]">
                        More
                        <span className="">
                            <DownOutlined />
                        </span>
                    </Button>
                </Dropdown>
            </div>
            <div className="h-screen bg-gray-100 p-4">
                <div className="calendar-header mb-4 flex items-center justify-between  ">
                    <div className="flex items-center justify-between  lg:w-[40%] lg:flex-row @sm:w-full @sm:flex-col @sm:gap-2">
                        <div className="flex w-[50%] items-center   @sm:w-full @sm:justify-between ">
                            <div className="flex flex-row justify-between gap-2">
                                <Button
                                    type="primary"
                                    className="today-button rounded  bg-purpleLight text-xl  text-white"
                                    onClick={() => handleNavigate('TODAY')}
                                >
                                    Today
                                </Button>
                                <ConfigProvider
                                    theme={{
                                        token: {
                                            fontSize: 12,
                                        },
                                    }}
                                >
                                    <DatePicker
                                        popupClassName="custom-datepicker"
                                        className="lg:w-[55%] "
                                        picker={
                                            view === 'day'
                                                ? 'date'
                                                : view === 'week'
                                                ? 'week'
                                                : 'month'
                                        }
                                        format={
                                            view === 'day'
                                                ? 'DD-MM-YYYY'
                                                : view === 'week'
                                                ? 'MMM YYYY'
                                                : 'MMMM YYYY'
                                        }
                                        value={dayjs(date)}
                                        onChange={(date) =>
                                            setDate(
                                                dayjs(
                                                    date || new Date()
                                                ).toDate()
                                            )
                                        }
                                        showWeek={false}
                                    />
                                </ConfigProvider>
                            </div>
                            <div className="lg:hidden ">
                                <FilterOutlined
                                    className="text-4xl"
                                    onClick={openModal}
                                />
                            </div>
                        </div>
                        <div className="flex gap-4   @sm:w-full @sm:justify-between">
                            <div className="">
                                <Button
                                    type="default"
                                    className="w-[5px] rounded text-base"
                                    onClick={() => handleNavigate('PREV')}
                                >
                                    <LeftOutlined />
                                </Button>
                                <Button
                                    type="default"
                                    className="w-[5px] rounded text-base "
                                    onClick={() => handleNavigate('NEXT')}
                                >
                                    <RightOutlined />
                                </Button>
                            </div>
                            <div className="flex lg:me-2">
                                <Button
                                    type="default"
                                    className={`rounded border border-[#1a3353] text-lg font-medium text-[#1A3353] lg:w-[40px]  ${
                                        view === 'day'
                                            ? 'bg-gray-300'
                                            : '#ffffff'
                                    }`}
                                    onClick={() => handleViewChange('day')}
                                >
                                    Day
                                </Button>
                                <Button
                                    type="default"
                                    className={`rounded border border-[#1a3353] text-lg font-medium  text-[#1A3353] lg:w-[40px]  ${
                                        view === 'week'
                                            ? 'bg-gray-300'
                                            : 'bg-gray-100'
                                    }`}
                                    onClick={() => handleViewChange('week')}
                                >
                                    Week
                                </Button>
                                {/* <Button
                                    type="default"
                                    className={`rounded border border-[#1a3353] text-lg font-medium  text-[#1A3353] lg:w-[40px]  ${
                                        view === 'month'
                                            ? 'bg-gray-300'
                                            : 'bg-gray-100'
                                    }`}
                                    onClick={() => handleViewChange('month')}
                                >
                                    Month
                                </Button> */}
                            </div>
                        </div>
                    </div>

                    <ConfigProvider
                        theme={{
                            components: {
                                Select: {
                                    optionFontSize: 12,
                                },
                            },
                            token: {
                                fontSize: 12,
                            },
                        }}
                    >
                        <div className="flex items-center justify-end gap-2 @sm:hidden">
                            <Select
                                className="calendar-filters "
                                showSearch
                                allowClear
                                value={selectedLocation}
                                onChange={(value) => setSelectedLocation(value)}
                                // onSearch={(value) =>
                                //     setFacilitySearch(value)
                                // }
                                placeholder="Facility"
                                filterOption={(input, option) =>
                                    String(option?.label ?? '')
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                }
                                options={
                                    role === RoleType.TRAINER
                                        ? LocationOptionByStaff
                                        : FacilityOptions
                                }
                            />
                            {selectedType !== 'bookings' && (
                                <Select
                                    className="w-[100px] "
                                    showSearch
                                    allowClear
                                    value={selectedFilterType}
                                    onChange={(value) => {
                                        setSelectedFilterType(value);
                                        if (value === 'rooms')
                                            setSelectedInstructor([]);
                                        else setSelectedRooms([]);
                                    }}
                                >
                                    <Option
                                        key="instructors"
                                        value="instructors"
                                    >
                                        Instructors
                                    </Option>
                                    <Option key="rooms" value="rooms">
                                        Rooms
                                    </Option>
                                </Select>
                            )}
                            {selectedFilterType === 'rooms' ? (
                                <Select
                                    className="calendar-filters "
                                    mode="multiple"
                                    showSearch
                                    value={selectedRooms}
                                    maxTagCount="responsive"
                                    onChange={(value) =>
                                        setSelectedRooms(value)
                                    }
                                    placeholder="Rooms"
                                    filterOption={(input, option) =>
                                        String(option?.label ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={RoomOptions}
                                />
                            ) : role === RoleType.TRAINER &&
                              !hasAvailabilityWritePermission ? (
                                <Input
                                    disabled
                                    value={`${user.firstName} ${user.lastName}`}
                                />
                            ) : (
                                <Select
                                    className="calendar-filters "
                                    mode="multiple"
                                    showSearch
                                    // allowClear
                                    // defaultValue={selectedInstructor[0]}
                                    value={selectedInstructor}
                                    maxTagCount="responsive" // Enables responsive tag collapsing
                                    onChange={(value) =>
                                        setSelectedInstructor(value)
                                    }
                                    placeholder="Instructor"
                                    filterOption={(input, option) =>
                                        String(option?.label ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={TrainerOptions}
                                />
                            )}
                            <Select
                                className="calendar-select calendar-filters "
                                placeholder="Service Type"
                                // style={{ widt<h: `${selec>tedWidth}px` }}
                                defaultValue={selectedType}
                                onChange={(value) => {
                                    setSelectedType(value);
                                    if (value === 'bookings')
                                        setSelectedFilterType('rooms');
                                }}
                                options={[
                                    {
                                        label: 'All',
                                        value: null,
                                    },
                                    {
                                        label: 'Appointment',
                                        value: 'personalAppointment',
                                    },
                                    {
                                        label: 'Classes',
                                        value: 'classes',
                                    },
                                    {
                                        label: 'Bookings',
                                        value: 'bookings',
                                    },
                                    ...(isCourseEnabled
                                        ? [
                                              {
                                                  label: 'Courses',
                                                  value: 'courses',
                                              },
                                          ]
                                        : []),
                                ]}
                            />
                            {/* {selectedFilterType === 'instructors' && (
                                <Select
                                    maxTagCount="responsive" // Enables responsive tag collapsing
                                    mode="multiple"
                                    placeholder="Service Category"
                                    className="calendar-select calendar-filters"
                                    // style={{ width: `${selectedWidth}px` }}
                                    defaultValue={selectedCategories}
                                    onChange={(value) =>
                                        setSelectedCategories(value)
                                    }
                                    options={ServiceTypeOption}
                                />
                            )} */}

                            {/* <Dropdown
                                trigger={['click']}
                                overlay={moreMenu}
                                className="w-[9%]"
                                placement="bottomRight"
                            >
                                <Button className="text-[#bfbfbf]">
                                    More
                                    <span className="">
                                        <DownOutlined />
                                    </span>
                                </Button>
                            </Dropdown> */}
                        </div>
                    </ConfigProvider>
                </div>
                <Calendar
                    localizer={localizer}
                    toolbar={false}
                    events={filteredEvents}
                    // events={view === 'day' ? filteredEvents : groupedEvents}
                    step={15}
                    popup={true}
                    timeslots={4}
                    startAccessor="start"
                    endAccessor="end"
                    style={{
                        height: 'calc(100vh - 100px)',
                        cursor: 'pointer',
                        backgroundColor: 'white',
                    }}
                    view={view}
                    onView={onView}
                    date={date}
                    onNavigate={onNavigate}
                    resources={
                        view === 'day'
                            ? selectedFilterType === 'rooms'
                                ? roomResources
                                : filteredResources
                            : null
                    }
                    slotPropGetter={(date: any, resourceId: any) =>
                        slotPropGetter(date, resourceId)
                    }
                    resourceIdAccessor={view === 'day' ? 'id' : null}
                    resourceTitleAccessor={view === 'day' ? 'title' : null}
                    eventPropGetter={eventStyleGetter}
                    formats={{
                        timeGutterFormat: (
                            date: any,
                            culture: any,
                            localizer: any
                        ) => localizer.format(date, 'HH:mm', culture),
                        eventTimeRangeFormat: (
                            { start, end }: any,
                            culture: any,
                            localizer: any
                        ) =>
                            `${localizer.format(
                                start,
                                'HH:mm',
                                culture
                            )} - ${localizer.format(end, 'HH:mm', culture)}`,
                    }}
                    components={{
                        event: CustomEvent,
                        eventWrapper: ({ event, children }: any) => (
                            <CustomTimeSlot event={event}>
                                {children}
                            </CustomTimeSlot>
                        ),
                        // eventWrapper: CustomEventWrapper,
                        week: {
                            header: CustomHeader,
                            // event: WeekEventRenderer,
                        },
                    }}
                    onSelectEvent={handleEventClick}
                    selectable
                    onSelectSlot={handleSlotClick}
                    scrollToTime={new Date(2000, 0, 1, 7, 0, 0)}
                />

                {openScheduleModal && (
                    <ScheduleModal
                        onClose={() => closeScheduleUpdateModal()}
                        visible={openScheduleModal}
                        isEdit={isEditStaffAvailable}
                        staffId={slotUpdateInfo?.staffId}
                        staffName={`${user.firstName} ${user.lastName}`}
                        isStaffDetails={
                            role === RoleType.TRAINER && isStaffEdit
                                ? isStaffEdit
                                : false
                        }
                        availabilityId={availabilityId}
                        slotUpdateInfo={slotUpdateInfo}
                        slotSelectedInfo={slotSelectedInfo}
                        isCalender={isCalender}
                        isStaffDetailsPage={false}
                        view={view}
                        date={date}
                        isAddUnavailability={isAddUnavailability}
                    />
                )}

                {openBookAppointmentModal && (
                    <BookingModal
                        onClose={() => {
                            setOpenBookAppointmentModal(false);
                            // dispatch(BookedAppointmentList({}));
                            setScheduleData(null);
                            setScheduleSelectedEventData(null);
                        }}
                        visible={openBookAppointmentModal}
                        scheduleId={
                            scheduleData?.id || scheduleSelectedEventData?.id
                        }
                        facilityId={selectedLocation}
                        isEdit={
                            scheduleData?.isEdit ||
                            scheduleSelectedEventData?.isEdit
                        }
                        tabValue={
                            scheduleData?.classType ||
                            scheduleSelectedEventData?.classType
                        }
                        slotSelectedInfo={slotSelectedInfo}
                        courseId={
                            (scheduleData?.classType === 'courses' &&
                                scheduleData?.packageId) ||
                            scheduleSelectedEventData?.packageId
                        }
                        bookingData={scheduleData || scheduleSelectedEventData}
                    />
                )}

                {openAddFacilityModal && (
                    <AddFacilityAvailabilityModal
                        onClose={() => {
                            setOpenAddFacilityModal(false);
                        }}
                        visible={openAddFacilityModal}
                    />
                )}

                {openEditFacilityModal && (
                    <EditFacilityAvailabilityModal
                        onClose={() => {
                            setOpenEditFacilityModal(false);
                        }}
                        visible={openEditFacilityModal}
                    />
                )}

                <Popover
                    content={popoverContent}
                    trigger="click"
                    open={popoverVisible}
                    onOpenChange={(visible) => setPopoverVisible(visible)}
                    overlayStyle={{
                        position: 'absolute',
                        top: popoverPosition.top,
                        left: popoverPosition.left,
                    }}
                />

                {eventDropdownVisible && (
                    <Dropdown
                        overlay={eventDropdownContent}
                        className="w-[10vw]"
                        trigger={['click']}
                        visible={eventDropdownVisible}
                        onVisibleChange={(visible) => {
                            setEventDropdownVisible(visible);
                            if (!visible) {
                                setScheduleData(null);
                            }
                        }}
                        placement="bottomLeft"
                        overlayStyle={{
                            position: 'absolute',
                            top: eventDropdownPosition.top,
                            left: eventDropdownPosition.left,
                        }}
                    >
                        <div />
                    </Dropdown>
                )}

                {addEventDropdownVisible && (
                    <Dropdown
                        overlay={addEventMenu}
                        className="w-[13vw]"
                        trigger={['click']}
                        visible={addEventDropdownVisible}
                        onVisibleChange={(visible) =>
                            setAddEventDropdownVisible(visible)
                        }
                        placement="bottomLeft"
                        overlayStyle={{
                            position: 'absolute',
                            top: addEventDropdownPosition.top,
                            left: addEventDropdownPosition.left,
                        }}
                    >
                        <div />
                    </Dropdown>
                )}
            </div>

            {slotDeleteModal && (
                <DeleteModal
                    title={
                        deleteType === 'timeSlot'
                            ? 'Confirm Delete'
                            : deleteType === 'checkIn'
                            ? 'Confirm Check In'
                            : 'Early Cancellation'
                    }
                    message={deleteMessage}
                    isVisible={slotDeleteModal}
                    deleteType={deleteType}
                    onDelete={
                        deleteType === 'timeSlot'
                            ? deleteSlotAvailability
                            : deleteType === 'checkIn'
                            ? confirmCheckin
                            : deleteBookedAppointment
                    }
                    onCancel={() => {
                        setSlotDeleteModal(false);
                        setDeleteMessage('');
                        setDeleteType('');
                        setScheduleData(null);
                        setScheduleSelectedEventData(null);
                    }}
                />
            )}
            {/* =========================modal filter=========================== */}
            <div className="lg:hidden">
                <Modal
                    title={<p></p>}
                    visible={modalVisible}
                    footer={false}
                    onCancel={closeModal}
                >
                    <div>
                        <ConfigProvider
                            theme={{
                                components: {
                                    Select: {
                                        optionFontSize: 12,
                                    },
                                },
                                token: {
                                    fontSize: 12,
                                },
                            }}
                        >
                            <div className="flex flex-col gap-16  pt-10">
                                <Select
                                    showSearch
                                    allowClear
                                    defaultValue={selectedLocation}
                                    onChange={(value) =>
                                        setSelectedLocation(value)
                                    }
                                    // onSearch={(value) =>
                                    //     setFacilitySearch(value)
                                    // }
                                    placeholder="Select facility"
                                    filterOption={(input, option) =>
                                        String(option?.label ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={
                                        role === RoleType.TRAINER
                                            ? LocationOptionByStaff
                                            : FacilityOptions
                                    }
                                />
                                <Select
                                    className="calendar-select"
                                    // style={{ width: `${selectedWidth}px` }}
                                    mode="multiple"
                                    defaultValue={selectedCategories}
                                    onChange={(value) =>
                                        setSelectedCategories(value)
                                    }
                                    options={ServiceTypeOption}
                                />
                                {role === RoleType.TRAINER ? (
                                    <Input
                                        disabled
                                        value={`${user.firstName} ${user.lastName}`}
                                    />
                                ) : (
                                    <Select
                                        className=""
                                        mode="multiple"
                                        showSearch
                                        // allowClear
                                        // defaultValue={selectedInstructor[0]}
                                        value={selectedInstructor}
                                        onChange={(value) =>
                                            setSelectedInstructor(value)
                                        }
                                        // onSearch={(value) =>
                                        //     setFacilitySearch(value)
                                        // }
                                        placeholder="Select instructor"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={TrainerOptions}
                                    />
                                )}
                                <Dropdown
                                    trigger={['click']}
                                    overlay={moreMenu}
                                    placement="bottomRight"
                                >
                                    <Button className="flex justify-between">
                                        <p> More </p>
                                        <span>
                                            <DownOutlined />
                                        </span>
                                    </Button>
                                </Dropdown>
                                <Form.Item className=" flex justify-center ">
                                    <Button
                                        onClick={() => goBack()}
                                        htmlType="button"
                                        className="me-6 w-[110px] border-[#1A3353]  bg-[#fff]  text-[#1A3353] "
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        htmlType="submit"
                                        className=" w-[110px] bg-purpleLight text-white"
                                    >
                                        Apply
                                    </Button>
                                </Form.Item>
                            </div>
                        </ConfigProvider>
                    </div>
                </Modal>
            </div>
            <Modal
                title="Are you sure to delete availability ?"
                open={isDeleteModalVisible}
                onOk={deleteDayAvailability}
                onCancel={handleCancel}
                footer={false}
            >
                <div className=" flex justify-center pb-7 pt-10">
                    <Form layout="horizontal" className="w-[100%] ">
                        <Form.Item
                            label={
                                <p className="w-[120px] text-left">
                                    Select Trainer
                                </p>
                            }
                        >
                            <Select
                                // style={{ width: 200 }}
                                placeholder="Select Trainer"
                                className="w-[100%]"
                                showSearch
                                value={deleteTrainerId}
                                onChange={(value) => setDeleteTrainerId(value)}
                                filterOption={(input, option) =>
                                    (option?.label ?? '')
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                }
                                options={TrainerOptions}
                            />
                        </Form.Item>
                        <Form.Item
                            label={
                                <p className="w-[120px] text-left">
                                    Select Date Range
                                </p>
                            }
                        >
                            <RangePicker
                                format="DD/MM/YYYY"
                                className="w-[100%]"
                                onChange={onChange}
                                value={deleteDates}
                            />
                        </Form.Item>
                    </Form>
                </div>
                <div className=" flex justify-end">
                    <Button
                        onClick={() => setDeleteIsModalVisible(false)}
                        htmlType="button"
                        className="me-6 w-[110px] border-[#1A3353]  bg-[#fff]  text-[#1A3353] "
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={deleteDayAvailability}
                        htmlType="submit"
                        className=" w-[110px]  bg-purpleLight  text-white"
                    >
                        Delete
                    </Button>
                </div>
            </Modal>
            {schedulingReportAtGlanceModel && (
                <>
                    <SchedulingReportModal
                        visible={schedulingReportAtGlanceModel}
                        onClose={() => setSchedulingReportAtGlanceModel(false)}
                    />
                </>
            )}
            {CancelScheduleModal && (
                <>
                    <CancelAllScheduleModal
                        open={CancelScheduleModal}
                        onClose={() => setCancelScheduleModal(false)}
                        scheduleData={scheduleData}
                    />
                </>
            )}
        </>
    );
};

export default AppointmentCalendar;
