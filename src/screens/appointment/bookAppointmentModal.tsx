import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
    Select,
    DatePicker,
    TimePicker,
    Input,
    Checkbox,
    Form,
    Button,
    FormProps,
    ConfigProvider,
    Tooltip,
    Radio,
    Switch,
} from 'antd';
// import { Dayjs } from 'dayjs';
import { useSelector } from 'react-redux';
import { RoleType, ClassType, DateRangeType } from '~/types/enums';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { capitalizeFirstLetter } from '~/components/common/function';
import { GetFacilityListByStaffId } from '~/redux/actions/facility-action';
import {
    TrainerListing,
    PricingListingByUserAndSubType,
    StaffAvailabilityListV1,
    serviceBypricingIdV1,
    PricingListingByUserAndType,
} from '~/redux/actions/appointment-action';
import { CustomerList } from '~/redux/actions/customer-action';
import { useDebounce } from '~/hooks/useDebounce';
import dayjs, { Dayjs } from 'dayjs';
import TextArea from 'antd/es/input/TextArea';
import {
    ServiceCategoryListByPackageId,
    activeServiceCategoyListv1,
} from '~/redux/actions/serviceCategoryAction';
import {
    roomListingByServiceCategory,
    roomListingByScheduling,
} from '~/redux/actions/room-action';
import {
    BookedSchedulingDetails,
    CreateBookScheduling,
    UpdateBookedScheduling,
    BookedCalendarData,
    CreateAppointmentScheduling,
    UpdateAppointmentScheduling,
    BookedSchedulingDetailsRecurring,
} from '~/redux/actions/scheduling-action';
import FormClientDetails from './form-client-detail';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { InfoCircleOutlined } from '@ant-design/icons';
import MultipleSchedule from '~/screens/appointment/multipleScheduling';

interface BookingFormProps {
    visible?: boolean;
    onClose?: any;
    scheduleId?: string;
    isEdit?: boolean;
    clientId?: string;
    facilityId?: string;
    slotSelectedInfo?: any;
    slotUpdateInfo?: any;
    bookingData?: any;
    purchaseId?: any;
    scanBooking?: boolean;
    scanBookingData?: any;
}

const daysOfWeek = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

interface TimeSlot {
    from: Dayjs | null | any;
    to: Dayjs | null | any;
    durationInMinutes: any;
}
interface WorkingHours {
    [key: string]: TimeSlot[];
}
const { Option, OptGroup } = Select;

const populateWorkingHours = (slots: any) => {
    const updated: WorkingHours = {};

    Object.keys(slots).forEach((day) => {
        const slot = slots[day];

        // If the slot object has from/to, push as array
        if (slot?.from && slot?.to) {
            updated[day] = [
                {
                    from: slot.from,
                    to: slot.to,
                    durationInMinutes: slot.durationInMinutes,
                },
            ];
        } else {
            // If empty, make sure it’s an empty array
            updated[day] = [];
        }
    });

    return updated;
};

const BookAppointmentModal: React.FC<BookingFormProps> = ({
    visible,
    onClose,
    scheduleId,
    isEdit,
    clientId,
    facilityId,
    slotSelectedInfo,
    bookingData,
    purchaseId,
    scanBooking,
    scanBookingData,
}: any) => {
    // Api start
    const { role, user } = useSelector((state: any) => state.auth_store);

    const [dateRange, setDateRange] = useState<'Single' | 'Multiple'>('Single');
    const [datePickerOpen, setDatePickerOpen] = useState(false);
    const [dateSelectedTime, setDateSelectedTime] = useState<string>();

    const [workingHours, setWorkingHours] = useState<WorkingHours>({
        mon: [{ from: null, to: null, durationInMinutes: null }],
        tue: [{ from: null, to: null, durationInMinutes: null }],
        wed: [{ from: null, to: null, durationInMinutes: null }],
        thu: [{ from: null, to: null, durationInMinutes: null }],
        fri: [{ from: null, to: null, durationInMinutes: null }],
        sat: [{ from: null, to: null, durationInMinutes: null }],
        sun: [{ from: null, to: null, durationInMinutes: null }],
    });

    const cleanupWorkingHours = (input: WorkingHours): WorkingHours => {
        const cleaned: WorkingHours = {};

        Object.entries(input).forEach(([day, slots]) => {
            const validSlots = slots.filter(
                (slot) =>
                    slot.from !== null &&
                    slot.to !== null &&
                    slot.durationInMinutes !== null
            );

            if (validSlots.length > 0) {
                cleaned[day] = validSlots;
            }
        });

        return cleaned;
    };

    const [form] = Form.useForm();

    const [selectedLocation, setSelectedLocation] = useState(
        facilityId ? facilityId : null
    );
    const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(dayjs());
    const [isChecked, setIsChecked] = useState(false);
    const [startTime, setStartTime] = useState<dayjs.Dayjs | null>(null);
    const [endTime, setEndTime] = useState<dayjs.Dayjs | null>(null);
    const [startTimeMinuteStep, setStartTimeMinuteStep] = useState<any>(1);
    const [appointmentTypeOption, setAppointmentTypeOption] = useState<any>([]);
    const [durationOption, setDurationOption] = useState<any>([]);
    const [remainingSessions, setRemainingSessions] = useState('');
    const [clientData, setClientData] = useState<any>();
    const [serviceCategoryId, setServiceCategoryId] = useState<any>(null);
    const [subCategoryId, setSubcategoryId] = useState<any>(null);
    const [staffOption, setStaffOption] = useState<any>(null);
    const [roomData, setRoomData] = useState<any>([]);
    const [staffErrorMessage, setStaffErrorMessage] = useState<string>('');
    const [repeatOption, setRepeatOption] = useState<string>('weekly');

    const [selectedDay, setSelectedDay] = useState<string>('mon');
    const autoSelectedByPurchaseIdDone = useRef(false);
    const isPurchaseSelectionInProgress = useRef(false);

    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        roomList: state.room_store.roomList,
        user: state.auth_store.user,
        facilityList: state.facility_store.facilityList,
        facilityListByStaffId: state.facility_store.facilityListByStaffId,
        trainerListForForms: state.appointment_store.trainerListForForms,
        pricingList: state.appointment_store.pricingList,
        customerListForForms: state.customer_store.customerListForForms,
        ServiceCategoryListByPackageId:
            state.service_category_store.ServiceCategoryListByPackageId,
        activeServiceCategoyListv1:
            state.service_category_store.ActiveServiceCategoryListV1,
        pricingListUserAndSubType:
            state.appointment_store.pricingListUserAndSubType,
    }));

    const debouncedRequest = useDebounce((callback) => callback(), 300);

    const [loader, startLoader, endLoader] = useLoader();
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const [
        submitCheckInLoader,
        startSubmitCheckInLoader,
        endSubmitCheckInLoader,
    ] = useLoader();

    useEffect(() => {
        if (slotSelectedInfo) {
            setStartTime(dayjs(slotSelectedInfo.startDate));
            setStartDate(dayjs(slotSelectedInfo.startDate));
        }
    }, [slotSelectedInfo]);

    useEffect(() => {
        if (role === RoleType.TRAINER) {
            dispatch(GetFacilityListByStaffId({ staffId: user._id })).then(
                (res: any) => {
                    form.setFieldsValue({
                        location: res?.payload?.data?.data?.[0]?._id,
                    });
                }
            );
            form.setFieldsValue({
                staffName: `${store.user?.firstName} ${store.user?.lastName}`,
            });
        }
    }, [visible]);

    useEffect(() => {
        if (selectedLocation) {
            dispatch(
                TrainerListing({ facilityId: selectedLocation, isActive: true })
            );
        }
    }, [selectedLocation]);

    useEffect(() => {
        startLoader();
        const requestParams = {
            page: 1,
            pageSize: 20,
        } as any;

        if (selectedLocation) {
            requestParams.locationId = [selectedLocation];

            debouncedRequest(() => {
                dispatch(
                    CustomerList({
                        ...requestParams,
                        isActive: true,
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        if (clientId) {
                            const client = res?.data?.data?.list.find(
                                (item: any) => item.userId === clientId
                            );
                            // dispatch(
                            //     PricingListingByUserAndType({
                            //         userId: clientId,
                            //         classType: 'personalAppointment',
                            //     })
                            // )
                            form.setFieldsValue({
                                email: client?.email,
                                phone: client?.mobile,
                                client: clientId,
                            });
                        }
                    })
                    .finally(endLoader);
            });
            dispatch(
                activeServiceCategoyListv1({
                    classType: ClassType.PERSONAL_APPOINTMENT,
                })
            );
        }
    }, [selectedLocation]);

    const transformServiceData = (apiData: any) => {
        return apiData.map((service: any) => ({
            category: service.name,
            options: service.appointmentType.map((appointment: any) => ({
                label: `${appointment.name} - ${appointment.durationInMinutes} mins`,
                value: JSON.stringify({
                    serviceId: service._id,
                    appointmentId: appointment._id,
                    // duration: appointment.durationInMinutes,
                }),
            })),
        }));
    };
    const serviceOptions = transformServiceData(
        store.activeServiceCategoyListv1
    );
    const PricingOption = useMemo(() => {
        const allPackages = store.pricingList || [];

        if (purchaseId) {
            const selectedPkg = allPackages.find((p) => p._id === purchaseId);
            return selectedPkg
                ? [
                      {
                          value: selectedPkg._id,
                          label: selectedPkg.packageName,
                          id: selectedPkg._id,
                          packageId: selectedPkg.packageId,
                      },
                  ]
                : [];
        }

        return allPackages.map((item: any) => ({
            value: item._id,
            label: item.packageName,
            id: item._id,
            packageId: item.packageId,
        }));
    }, [purchaseId, store.pricingList]);

    const handleDayClick = (day: string) => {
        setSelectedDay(day);
    };

    const fetchRecurringDetails = async ({
        newDateRange,
        newStartDate,
        newRepeatOption,
        newEndDate,
    }: {
        newDateRange?: string;
        newStartDate?: dayjs.Dayjs | null;
        newRepeatOption?: string;
        newEndDate?: dayjs.Dayjs | null;
    }) => {
        if (!scheduleId || !isEdit) return;

        const effectiveDateRange = newDateRange || dateRange;
        const effectiveStartDate = newStartDate || startDate;
        const effectiveRepeatOption = newRepeatOption || repeatOption;

        // Determine endDate
        let calculatedEndDate: string | undefined;
        if (effectiveRepeatOption === 'weekly') {
            calculatedEndDate = dayjs(effectiveStartDate)
                .add(1, 'year')
                .format('YYYY-MM-DD');
        } else if (effectiveRepeatOption === 'custom') {
            // Prefer newly passed endDate or fallback to form value
            const endDateValue = newEndDate || form.getFieldValue('endDate');
            if (!endDateValue) {
                console.warn(
                    'End Date is required for custom repeat but not set.'
                );
                return; // ⛔ Exit without hitting API
            }
            if (endDateValue) {
                calculatedEndDate = dayjs(endDateValue).format('YYYY-MM-DD');
            }
        }

        try {
            const res: any = await dispatch(
                BookedSchedulingDetailsRecurring({
                    scheduleId,
                    dateRange: effectiveDateRange,
                    startDate: dayjs(effectiveStartDate).format('YYYY-MM-DD'),
                    markType: effectiveRepeatOption,
                    endDate: calculatedEndDate,
                })
            ).unwrap();
            console.log(
                res?.data?.data,
                'fwenvnewinviewn',
                dayjs(effectiveStartDate).format('YYYY-MM-DD')
            );
            if (res?.data?.data?.slots) {
                const transformed = populateWorkingHours(res.data.data.slots);
                setWorkingHours(transformed);

                const firstDayWithSlot = Object.keys(transformed).find(
                    (day) => transformed[day].length > 0
                );
                console.log(firstDayWithSlot, 'first Day with slot');
                const selectedDay = dayjs(effectiveStartDate)
                    .format('ddd')
                    .toLowerCase();
                if (selectedDay) {
                    setSelectedDay(selectedDay);
                }
            }
        } catch (err) {
            console.error('Error fetching recurring scheduling details', err);
        }
    };

    useEffect(() => {
        const fetchSchedulingDetails = async () => {
            if (!scheduleId) return;

            try {
                // 1. Fetch booking details
                const res: any = await dispatch(
                    BookedSchedulingDetailsRecurring({ scheduleId })
                ).unwrap();
                const scheduleData = res?.data?.data;
                if (!scheduleData) return;

                const {
                    clientEmail,
                    clientPhone,
                    trainerId,
                    facilityId,
                    clientId,
                    clientName,
                    serviceCategoryId,
                    subTypeId,
                    subTypeDuration,
                    roomId,
                    notes,
                    purchaseId,
                    duration,
                    from,
                    date,
                    subTypeList,
                    packageId,
                } = scheduleData;

                // 2. Set static state values
                setClientData({ clientId, clientName });
                setSelectedLocation(facilityId);
                setServiceCategoryId(serviceCategoryId);

                const startDateObj = dayjs(date);
                const startTimeObj = dayjs(from, 'HH:mm');
                const endTimeObj = startTimeObj.add(duration, 'minute');

                setStartDate(startDateObj);
                setStartTime(startTimeObj);
                setEndTime(endTimeObj);
                setStartTimeMinuteStep(subTypeDuration);

                // 3. Setup duration options
                setDurationOption(
                    Array.from({ length: 5 }, (_, i) => {
                        const multiple = subTypeDuration * (i + 1);
                        return {
                            label: `${multiple} Minutes`,
                            value: multiple,
                        };
                    })
                );

                // 4. Set appointment types
                const appointmentTypeList =
                    subTypeList?.map((item: any) => ({
                        value: item._id,
                        label: item.name,
                        id: item._id,
                        ...item,
                    })) || [];
                setAppointmentTypeOption(appointmentTypeList);

                // 5. Fetch in parallel: room list, categories, package list
                const [_, pricingRes] = await Promise.all([
                    dispatch(
                        activeServiceCategoyListv1({
                            classType: ClassType.PERSONAL_APPOINTMENT,
                        })
                    ),
                    dispatch(
                        PricingListingByUserAndSubType({
                            classType: ClassType.PERSONAL_APPOINTMENT,
                            clientUserId: clientId,
                            serviceCategoryId,
                            subTypeId,
                            pageSize: 50,
                        })
                    ).unwrap(),
                ]);
                const matchedRoom = store.roomList?.find(
                    (room: any) => room._id === roomId
                );
                const packageList = pricingRes?.data?.data || [];
                const selectedPackage = packageList.find(
                    (pkg: any) => pkg.purchaseId === purchaseId
                );
                if (selectedPackage) {
                    setRemainingSessions(selectedPackage.remainingSession);
                }

                // 6. Optional: Fetch trainer list and set options

                const reqData = {
                    facilityId,
                    classType: ClassType.PERSONAL_APPOINTMENT,
                    serviceId: serviceCategoryId,
                    subTypeId,
                    date: startDateObj.format('YYYY-MM-DDT00:00:00[Z]'),
                    startTime: startTimeObj.format('HH:mm'),
                    endTime: endTimeObj.format('HH:mm'),
                    scheduleId: scheduleId ? scheduleId : undefined,
                };

                const trainerResponse = await dispatch(
                    StaffAvailabilityListV1({ reqData })
                ).unwrap();
                const trainerOptions =
                    trainerResponse?.data?.data?.map((item: any) => {
                        const name = capitalizeFirstLetter(
                            `${item.firstName} ${item.lastName}`
                        );
                        const availability = item?.isAvailable
                            ? 'Available'
                            : 'Not Available';

                        return {
                            value: item._id,
                            label: (
                                <div
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                    }}
                                >
                                    <span>{name}</span>
                                    <span
                                        style={{
                                            color: item.isAvailable
                                                ? 'green'
                                                : 'red',
                                        }}
                                    >
                                        {availability}
                                    </span>
                                </div>
                            ),
                            name,
                        };
                    }) || [];

                const trainerExists = trainerOptions.some(
                    (t: any) => t.value === trainerId
                );
                if (!trainerExists && trainerId) {
                    const allTrainerListRes = await dispatch(
                        TrainerListing({
                            facilityId: selectedLocation,
                            isActive: true,
                        })
                    ).unwrap();

                    const bookedTrainer = allTrainerListRes?.data?.data?.find(
                        (t: any) => t._id === trainerId
                    );
                    if (bookedTrainer) {
                        const name = capitalizeFirstLetter(
                            `${bookedTrainer.firstName} ${bookedTrainer.lastName}`
                        );
                        trainerOptions.push({
                            value: bookedTrainer._id,
                            label: (
                                <div
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                    }}
                                >
                                    <span>{name}</span>
                                    <span style={{ color: 'gray' }}>
                                        Not Available
                                    </span>
                                </div>
                            ),
                            name,
                        });
                    }
                }

                setStaffOption(trainerOptions);

                await dispatch(
                    roomListingByScheduling({
                        serviceId: serviceCategoryId,
                        facilityId: selectedLocation,
                        classType: ClassType.PERSONAL_APPOINTMENT,
                        date: dayjs(date).format('YYYY-MM-DD'),
                        startTime: dayjs(startTimeObj).format('HH:mm'),
                        endTime: dayjs(
                            startTimeObj.add(duration, 'minute')
                        ).format('HH:mm'),
                        scheduleId: scheduleId,
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        const roomOptions = res?.data?.data?.map(
                            (item: any) => ({
                                value: item._id,
                                label: item.roomName,
                                id: item._id,
                            })
                        );
                        setRoomData(roomOptions);
                    });
                // 7. Set all fields together for minimal re-render
                form.setFieldsValue({
                    email: clientEmail,
                    phone: clientPhone,
                    location: facilityId,
                    client: clientId,
                    roomId: roomId,
                    notes,
                    subType: subTypeId,
                    duration,
                    staff: trainerId,
                    startDate: startDateObj,
                    package: selectedPackage
                        ? {
                              value: selectedPackage.purchaseId,
                              label: selectedPackage.packageName,
                          }
                        : undefined,
                    serviceType: JSON.stringify({
                        serviceId: serviceCategoryId,
                        appointmentId: subTypeId,
                    }),
                });
            } catch (error) {
                console.error('Error fetching scheduling details:', error);
            }
        };

        fetchSchedulingDetails();
    }, [dispatch, scheduleId]);

    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    const ServiceTypeOption = store.ServiceCategoryListByPackageId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const LocationOptionByStaff = store.facilityListByStaffId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const TrainerOptions = store.trainerListForForms?.map((item: any) => ({
        value: item.userId,
        label: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
        id: item._id,
    }));

    const getNextTimeSlot = (step: number = 15) => {
        const now = dayjs();
        const totalMinutes = now.hour() * 60 + now.minute();
        const nextSlot = Math.ceil(totalMinutes / step) * step;

        const nextHour = Math.floor(nextSlot / 60);
        const nextMinute = nextSlot % 60;

        return now.hour(nextHour).minute(nextMinute).second(0).millisecond(0);
    };

    const trainerList = async ({
        date,
        calculatedEndTime,
        startTimer,
        serviceId,
        subTypeId,
    }: any) => {
        const facilityId = selectedLocation;
        let start = startTimer || startTime;
        console.log(dayjs(start).format('HH:mm'), 'sjdksaskjkfjsakfjkas start');
        if (!start) {
            const fallbackStep = startTimeMinuteStep || 30;
            start = getNextTimeSlot(fallbackStep);
            setStartTime(start);
        }

        let end = calculatedEndTime || endTime;
        console.log(dayjs(end).format('HH:mm'), 'sjdksaskjkfjsakfjkas end');

        if (!end) {
            const duration =
                form.getFieldValue('duration') || startTimeMinuteStep || 30;
            end = dayjs(start).add(duration, 'minute');
            setEndTime(end);
        }
        const startDayjs = dayjs(start);
        const endDayjs = dayjs(end);

        if (!startDayjs.isValid() || !endDayjs.isValid()) {
            console.warn('Invalid start or end time → skipping API call');
            return; // stop here
        }
        const reqData = {
            facilityId,
            classType: ClassType.PERSONAL_APPOINTMENT,
            serviceId: serviceId ? serviceId : serviceCategoryId,
            subTypeId: subTypeId ? subTypeId : subCategoryId,
            date: dayjs(date || startDate).format('YYYY-MM-DDT00:00:00[Z]'),
            startTime: dayjs(start).format('HH:mm'),
            endTime: dayjs(end).format('HH:mm'),
            scheduleId: scheduleId ? scheduleId : undefined,
        };
        try {
            const response = await dispatch(
                StaffAvailabilityListV1({ reqData })
            ).unwrap();
            const trainerOptions = response.data.data?.map((item: any) => {
                const name = capitalizeFirstLetter(
                    `${item.firstName} ${item.lastName}`
                );
                const availability = item?.isAvailable
                    ? 'Available'
                    : 'Not Available';

                return {
                    value: item._id,
                    label: (
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                color: item?.isAvailable ? 'green' : 'red',
                            }}
                        >
                            <span>{name}</span>
                            <span>{availability}</span>
                        </div>
                    ),
                    name,
                    id: item._id,
                    availability: item?.isAvailable,
                    reason: !item?.isAvailable ? item?.reason : '',
                };
            });

            setStaffOption(trainerOptions);

            if (trainerOptions?.length > 0 && trainerOptions[0].availability) {
                form.setFieldValue('staff', trainerOptions[0].value);
            } else {
                form.resetFields(['staff']);
            }
        } catch (err) {
            console.error('Trainer fetch failed:', err);
        }
    };

    const handleAppointmentTypeChange = (appointmentData: any) => {
        setDurationOption(
            Array.from({ length: 5 }, (_, i) => {
                const multiple = appointmentData.durationInMinutes * (i + 1);
                return {
                    label: `${multiple} Minutes`,
                    value: multiple,
                };
            })
        );
        const startTimeValue = getNextTimeSlot(
            appointmentData.durationInMinutes
        );
        setStartTime(startTimeValue);
        form.setFieldValue('duration', appointmentData.durationInMinutes);
        const calculatedEndTime = startTimeValue.add(
            appointmentData.durationInMinutes,
            'minute'
        );
        setEndTime(calculatedEndTime);
        setStartTimeMinuteStep(appointmentData.durationInMinutes);
    };
    useEffect(() => {
        if (!isEdit && !scheduleId) {
            const nextSlot = getNextTimeSlot(15);
            // Get duration from form (or fallback to 60 if empty)
            const formValues = form.getFieldsValue();
            console.log(formValues, 'fojfhsdfhshf');
            const formDuration = Number(formValues?.duration);
            const endSlot = nextSlot.add(formDuration, 'minute');
            setStartTime(nextSlot);
            setStartDate(dayjs());
            setEndTime(nextSlot.add(15, 'minute'));
            form.setFieldsValue({
                startDate: dayjs(),
            });
        }
    }, []);

    useEffect(() => {
        if (dateRange !== DateRangeType.MULTIPLE || isEdit) return;

        const selectedDate = dayjs();
        if (!selectedDate) return;
        const dayKey = dayjs(selectedDate).format('ddd').toLowerCase();
        const defaultStartTime = dayjs().startOf('hour').add(30, 'minute');
        const defaultDuration = 30;
        const defaultEndTime = defaultStartTime
            .clone()
            .add(defaultDuration, 'minute');

        setWorkingHours((prev) => {
            const alreadySet = prev?.[dayKey];
            if (alreadySet && alreadySet.length > 0) return prev;

            return {
                ...prev,
                [dayKey]: [
                    {
                        from: defaultStartTime.format('HH:mm'),
                        to: defaultEndTime.format('HH:mm'),
                        durationInMinutes: defaultDuration,
                    },
                ],
            };
        });

        setSelectedDay(dayKey);
    }, [dateRange, isEdit, form]);

    const fetchRoomData = async ({ date, startsTime, endsTime }: any) => {
        if (!serviceCategoryId || !selectedLocation || dateRange === 'Multiple')
            return;
        const startDayjs = dayjs(startsTime);
        const endDayjs = dayjs(endsTime);

        if (!startDayjs.isValid() || !endDayjs.isValid()) {
            console.warn('Invalid start or end time → skipping API call');
            return; // stop here
        }
        await dispatch(
            roomListingByScheduling({
                serviceId: serviceCategoryId,
                facilityId: selectedLocation,
                classType: ClassType.PERSONAL_APPOINTMENT,
                date: dayjs(date).format('YYYY-MM-DD'),
                startTime: startsTime
                    ? dayjs(startsTime).format('HH:mm')
                    : dayjs(startTime).format('HH:mm'),
                endTime: endsTime
                    ? dayjs(endsTime).format('HH:mm')
                    : dayjs(endTime).format('HH:mm'),
            })
        )
            .unwrap()
            .then((res: any) => {
                const roomOptions = res?.data?.data?.map((item: any) => ({
                    value: item._id,
                    label: item.roomName,
                    id: item._id,
                }));
                setRoomData(roomOptions);
                form.setFieldsValue({
                    roomId: roomOptions?.[0]?.value,
                });
            });
    };

    const handleDuration = (value: any) => {
        console.log('lfhsdkhfksdhfkhsdkfhksdhfksdhkfh', value);
        setStaffErrorMessage('');
        let calculatedEndTime;
        if (startTime) {
            calculatedEndTime = startTime.add(value, 'minute');
            setEndTime(calculatedEndTime);
        } else {
            setEndTime(null);
        }
        const formValue = form.getFieldsValue();
        const parsedServiceType = JSON.parse(formValue.serviceType);
        trainerList({
            date: undefined,
            calculatedEndTime: calculatedEndTime,
            serviceId: parsedServiceType.serviceId,
            subTypeId: parsedServiceType.appointmentId,
        });
        fetchRoomData({
            date: startDate,
            startsTime: startTime,
            endsTime: calculatedEndTime,
        });
    };

    const roundToNext15Min = (time: dayjs.Dayjs) => {
        const totalMinutes = time.hour() * 60 + time.minute();
        const nextRounded = Math.ceil(totalMinutes / 15) * 15;
        return time
            .hour(Math.floor(nextRounded / 60))
            .minute(nextRounded % 60)
            .second(0)
            .millisecond(0);
    };

    const handleStartTimeChange = (time: any) => {
        setStaffErrorMessage('');
        if (!time) {
            console.log('skipping ...');
            setEndTime(null);
            return;
        }
        // Round start time
        const roundedStartTime = roundToNext15Min(dayjs(time));
        setStartTime(roundedStartTime);
        form.setFieldValue('startTime', roundedStartTime);

        const duration = Number(form.getFieldValue('duration')) || 60;
        const calculatedEndTime = dayjs(roundedStartTime)
            .add(duration, 'minute')
            .second(0)
            .millisecond(0);
        setEndTime(calculatedEndTime);
        form.setFieldValue('endTime', calculatedEndTime);

        console.log('Rounded Start:', roundedStartTime.format('HH:mm'));
        console.log('Duration:', duration);
        console.log('Calculated End Time:', calculatedEndTime.format('HH:mm'));

        const formValue = form.getFieldsValue();
        const parsedServiceType = JSON.parse(formValue.serviceType || '{}');

        trainerList({
            date: startDate,
            calculatedEndTime,
            startTimer: roundedStartTime,
            serviceId: parsedServiceType?.serviceId,
            subTypeId: parsedServiceType?.appointmentId,
        });

        fetchRoomData({
            date: startDate,
            startsTime: roundedStartTime,
            endsTime: calculatedEndTime,
        });
    };

    const handleServiceTypeChange = async (
        value: string,
        customClientData?: any
    ) => {
        setStaffErrorMessage('');
        // Don't reset package field when called from handleClientSelect
        if (!customClientData) {
            form.resetFields(['roomId', 'package', 'staff']);
        } else {
            form.resetFields(['roomId', 'staff']);
        }
        const selectedOption = JSON.parse(value);

        const serviceId = selectedOption.serviceId;
        const appointmentId = selectedOption.appointmentId;
        const duration = selectedOption.duration;

        if (!serviceId || !appointmentId) return;

        setServiceCategoryId(serviceId);
        setSubcategoryId(appointmentId);
        setRemainingSessions('');

        const service = store.activeServiceCategoyListv1.find(
            (item: any) => item._id === serviceId
        ) || { appointmentType: [] };

        const appointmentType: any = service.appointmentType?.find(
            (item: any) => item._id === appointmentId
        );

        // Step 1: Auto-set duration and time slots
        const fallbackStep = appointmentType?.durationInMinutes || 30;
        const autoStart = getNextTimeSlot(fallbackStep);
        const autoEnd = dayjs(autoStart).add(fallbackStep, 'minute');

        let startTimeToUse = startTime;
        let endTimeToUse = endTime;

        if (!isEdit && !scheduleId) {
            console.log('sd;lfhsdjgfjasfagfgasjfgjsa');
            const roundedStart = getNextTimeSlot(15);
            const fallbackDuration = appointmentType?.durationInMinutes || 60;
            const roundedEnd = dayjs(roundedStart).add(
                fallbackDuration,
                'minute'
            );

            setStartTime(roundedStart);
            setEndTime(roundedEnd);
            startTimeToUse = roundedStart;
            endTimeToUse = roundedEnd;
            form.setFieldValue('startDate', dayjs()); // optional if needed
        }
        setStartTimeMinuteStep(fallbackStep);
        form.setFieldsValue({
            duration: fallbackStep,
        });

        const dayKey = dayjs(startDate).format('ddd').toLowerCase();

        setWorkingHours((prev) => ({
            ...prev,
            [dayKey]: [
                {
                    from: dayjs(autoStart).format('HH:mm'),
                    to: dayjs(autoEnd).format('HH:mm'),
                    durationInMinutes: fallbackStep,
                },
            ],
        }));

        handleDuration(fallbackStep);

        // Step 2: Update duration options
        const durationOptions = Array.from({ length: 5 }, (_, i) => {
            const multiple = fallbackStep * (i + 1);
            return { label: `${multiple} Minutes`, value: multiple };
        });
        setDurationOption(durationOptions);
        setAppointmentTypeOption(appointmentType);

        // Step 3: Fetch room list by service category (static)
        await dispatch(
            roomListingByServiceCategory({
                serviceCategoryId: serviceId,
                facilityId: selectedLocation,
            })
        ).unwrap();

        const activeClient = clientData || customClientData;
        if (activeClient && serviceId && appointmentId) {
            const payload = {
                classType: ClassType.PERSONAL_APPOINTMENT,
                clientUserId: activeClient?.value || activeClient?.clientId,
                serviceCategoryId: serviceId,
                subTypeId: appointmentId,
                pageSize: 50,
                page: 1,
                search: '',
                isNewBooking: true,
            };

            const response = await dispatch(
                PricingListingByUserAndSubType(payload)
            ).unwrap();
            const packages = response?.data?.data;
            if (packages?.length > 0) {
                const firstPackage = packages[0];
                if (!purchaseId) {
                    form.setFieldValue('package', {
                        value: firstPackage.purchaseId,
                        label: firstPackage.packageName,
                    });
                    setRemainingSessions(firstPackage.remainingSession);
                }
                await trainerList({
                    date: startDate,
                    calculatedEndTime: endTimeToUse,
                    startTimer: startTimeToUse,
                    serviceId,
                    subTypeId: appointmentId,
                });
            } else {
                form.setFieldValue('package', undefined);
            }
        } else {
            await trainerList({
                date: startDate,
                calculatedEndTime: endTimeToUse,
                startTimer: startTimeToUse,
                serviceId,
                subTypeId: appointmentId,
            });
        }

        await dispatch(
            roomListingByScheduling({
                serviceId: serviceId,
                facilityId: selectedLocation,
                classType: ClassType.PERSONAL_APPOINTMENT,
                date: dayjs(startDate).format('YYYY-MM-DD'),
                startTime: dayjs(startTimeToUse).format('HH:mm'),
                endTime: dayjs(endTimeToUse).format('HH:mm'),
            })
        )
            .unwrap()
            .then((res: any) => {
                const roomOptions = res?.data?.data?.map((item: any) => ({
                    value: item._id,
                    label: item.roomName,
                    id: item._id,
                }));
                setRoomData(roomOptions);
                form.setFieldsValue({
                    roomId: roomOptions?.[0]?.value,
                });
            });
    };
    useEffect(() => {
        const packageFind = async () => {
            if (!purchaseId || !clientId || !store.pricingList?.length) return;

            isPurchaseSelectionInProgress.current = true;

            try {
                const pricingRes = await dispatch(
                    PricingListingByUserAndType({
                        userId: clientId,
                        classType: ClassType.PERSONAL_APPOINTMENT,
                    })
                ).unwrap();

                const packageList = pricingRes?.data?.data || [];
                const selectedPkg = packageList.find(
                    (pkg: any) => pkg._id === purchaseId
                );
                if (!selectedPkg) return;

                const { packageId, remainingSession, packageName } =
                    selectedPkg;

                form.setFieldsValue({
                    package: {
                        value: selectedPkg._id,
                        label: packageName,
                    },
                });
                setRemainingSessions(remainingSession);

                const res: any = await dispatch(
                    serviceBypricingIdV1({ pricingId: packageId })
                ).unwrap();
                const service = res?.data?.data?.[0];
                const appointmentType = service?.appointmentType?.[0];

                if (service && appointmentType) {
                    const value = JSON.stringify({
                        serviceId: service._id,
                        appointmentId: appointmentType._id,
                    });

                    form.setFieldsValue({ serviceType: value });

                    // Short delay to avoid effect collision
                    setTimeout(() => {
                        handleServiceTypeChange(value, { clientId });
                    }, 100);
                }

                autoSelectedByPurchaseIdDone.current = true;
            } catch (err) {
                console.error('[AutoSelect Package] Error:', err);
            } finally {
                isPurchaseSelectionInProgress.current = false;
            }
        };

        const delayTrigger = setTimeout(() => {
            packageFind();
        }, 300);

        return () => clearTimeout(delayTrigger);
    }, [purchaseId, clientId]);

    const handleClientSelect = async (client: any) => {
        console.log(client, 'client1111');
        if (purchaseId && isPurchaseSelectionInProgress.current) {
            return;
        }

        if (purchaseId && autoSelectedByPurchaseIdDone.current) {
            return;
        }
        setClientData(client);
        if (!scheduleId) {
            try {
                // 1. First check if client has any packages
                const pricingRes = await dispatch(
                    PricingListingByUserAndType({
                        userId: client.value || client.userId,
                        classType: ClassType.PERSONAL_APPOINTMENT,
                    })
                ).unwrap();

                const packageList = pricingRes?.data?.data || [];
                if (packageList.length > 0) {
                    // Client has packages - use the first package to get service
                    const firstPackage = packageList[0];
                    const { packageId, remainingSession, packageName } =
                        firstPackage;
                    if (!purchaseId) {
                        form.setFieldsValue({
                            package: {
                                value: firstPackage._id,
                                label: packageName,
                            },
                        });
                        setRemainingSessions(remainingSession);

                        // Fetch service based on packageId
                        const servicesRes = await dispatch(
                            serviceBypricingIdV1({ pricingId: packageId })
                        ).unwrap();

                        const service = servicesRes?.data?.data?.[0];
                        const appointmentType = service?.appointmentType?.[0];

                        if (service && appointmentType) {
                            const value = JSON.stringify({
                                serviceId: service._id,
                                appointmentId: appointmentType._id,
                            });

                            form.setFieldsValue({
                                serviceType: value,
                            });

                            setTimeout(() => {
                                handleServiceTypeChange(value, client);
                            }, 0);
                            return;
                        }
                    }
                }

                // No packages found - reset package field and fetch services
                if (!purchaseId) {
                    form.setFieldsValue({
                        package: undefined,
                    });
                    setRemainingSessions('');
                }
                // Fetch services as usual
                const res: any = await dispatch(
                    activeServiceCategoyListv1({
                        classType: ClassType.PERSONAL_APPOINTMENT,
                    })
                ).unwrap();

                const serviceList = res?.data?.data?.list || [];
                if (!serviceList.length) return;

                // Select first available service
                const selectedService = serviceList.find(
                    (service: any) => service.appointmentType?.length > 0
                );

                if (!selectedService) return;

                const selectedAppointment = selectedService.appointmentType[0];
                const value = JSON.stringify({
                    serviceId: selectedService._id,
                    appointmentId: selectedAppointment._id,
                });
                if (!purchaseId) {
                    form.setFieldsValue({
                        serviceType: value,
                    });
                }
                setTimeout(() => {
                    handleServiceTypeChange(value, client);
                }, 0);
            } catch (error) {
                console.error('Error in handleClientSelect:', error);
            }
        }
    };

    useEffect(() => {
        const prepopulateScanBookingData = async () => {
            if (!scanBooking || !scanBookingData || !selectedLocation) return;

            try {
                const {
                    clientId: scanClientId,
                    clientName,
                    clientEmail,
                    clientPhone,
                    facilityId: scanFacilityId,
                    serviceCategoryId: scanServiceCategoryId,
                    subTypeId: scanSubTypeId,
                    purchaseId: scanPurchaseId,
                    packageId: scanPackageId,
                    date: scanDate,
                    packageName: scanPackageName,
                } = scanBookingData;

                // Set client data
                const clientData = {
                    clientId: scanClientId,
                    value: scanClientId,
                    clientName: clientName,
                };
                setClientData(clientData);

                if (scanFacilityId && scanFacilityId !== selectedLocation) {
                    setSelectedLocation(scanFacilityId);
                    form.setFieldValue('location', scanFacilityId);
                }

                form.setFieldsValue({
                    client: scanClientId,
                    email: clientEmail,
                    phone: clientPhone,
                });

                setServiceCategoryId(scanServiceCategoryId);
                setSubcategoryId(scanSubTypeId);

                const serviceRes = await dispatch(
                    activeServiceCategoyListv1({
                        classType: ClassType.PERSONAL_APPOINTMENT,
                    })
                ).unwrap();

                const serviceList = serviceRes?.data?.data?.list || [];

                const matchedService = serviceList.find(
                    (service: any) => service._id === scanServiceCategoryId
                );

                let fallbackStep: any;

                if (matchedService) {
                    const matchedAppointment =
                        matchedService.appointmentType?.find(
                            (apt: any) => apt._id === scanSubTypeId
                        );

                    if (matchedAppointment) {
                        const serviceTypeValue = JSON.stringify({
                            serviceId: scanServiceCategoryId,
                            appointmentId: scanSubTypeId,
                        });

                        form.setFieldValue('serviceType', serviceTypeValue);

                        fallbackStep =
                            matchedAppointment.durationInMinutes || 30;
                        setStartTimeMinuteStep(fallbackStep);

                        const durationOptions = Array.from(
                            { length: 5 },
                            (_, i) => {
                                const multiple = fallbackStep * (i + 1);
                                return {
                                    label: `${multiple} Minutes`,
                                    value: multiple,
                                };
                            }
                        );
                        setDurationOption(durationOptions);
                        setAppointmentTypeOption(matchedAppointment);

                        form.setFieldValue('duration', fallbackStep);

                        if (!isEdit && !scheduleId) {
                            const defaultStartTime =
                                getNextTimeSlot(fallbackStep);
                            const defaultEndTime = defaultStartTime.add(
                                fallbackStep,
                                'minute'
                            );

                            setStartTime(defaultStartTime);
                            setEndTime(defaultEndTime);
                            setStartDate(dayjs());

                            form.setFieldsValue({
                                startDate: dayjs(),
                            });
                        }
                    }
                }

                if (scanPurchaseId) {
                    try {
                        const pricingRes = await dispatch(
                            PricingListingByUserAndSubType({
                                classType: ClassType.PERSONAL_APPOINTMENT,
                                clientUserId: scanClientId,
                                serviceCategoryId: scanServiceCategoryId,
                                subTypeId: scanSubTypeId,
                                pageSize: 50,
                                page: 1,
                                search: '',
                                isNewBooking: true,
                            })
                        ).unwrap();

                        const packages = pricingRes?.data?.data || [];
                        const matchedPackage = packages.find(
                            (pkg: any) => pkg.purchaseId === scanPurchaseId
                        );

                        if (matchedPackage) {
                            form.setFieldValue('package', {
                                value: matchedPackage.purchaseId,
                                label:
                                    matchedPackage.packageName ||
                                    scanPackageName,
                            });
                            setRemainingSessions(
                                matchedPackage.remainingSession
                            );
                        }
                    } catch (err) {
                        console.error(
                            'Error fetching package data for scan booking:',
                            err
                        );
                    }
                }

                // Trigger trainer list and room data fetch with proper timing and state
                setTimeout(async () => {
                    // Set up proper time values for API calls
                    const currentDate = startDate || dayjs();
                    const currentStartTime =
                        startTime || getNextTimeSlot(fallbackStep);
                    const currentEndTime =
                        endTime || currentStartTime.add(fallbackStep, 'minute');

                    // Update state with current time values if not already set
                    if (!startTime) setStartTime(currentStartTime);
                    if (!endTime) setEndTime(currentEndTime);
                    if (!startDate) setStartDate(currentDate);

                    try {
                        await trainerList({
                            date: currentDate,
                            calculatedEndTime: currentEndTime,
                            startTimer: currentStartTime,
                            serviceId: scanServiceCategoryId,
                            subTypeId: scanSubTypeId,
                        });

                        await dispatch(
                            roomListingByScheduling({
                                serviceId: scanServiceCategoryId,
                                facilityId: scanFacilityId,
                                classType: ClassType.PERSONAL_APPOINTMENT,
                                date: dayjs(currentDate).format('YYYY-MM-DD'),
                                startTime:
                                    dayjs(currentStartTime).format('HH:mm'),
                                endTime: dayjs(currentEndTime).format('HH:mm'),
                                scheduleId: scheduleId,
                            })
                        )
                            .unwrap()
                            .then((res: any) => {
                                const roomOptions = res?.data?.data?.map(
                                    (item: any) => ({
                                        value: item._id,
                                        label: item.roomName,
                                        id: item._id,
                                    })
                                );
                                setRoomData(roomOptions);
                                form.setFieldsValue({
                                    roomId: roomOptions?.[0]?.value,
                                });
                            });
                    } catch (error) {
                        console.error(
                            'Error fetching trainer/room data for scan booking:',
                            error
                        );
                    }
                }, 1000);
            } catch (error) {
                console.error('Error prepopulating scan booking data:', error);
            }
        };

        // Only run this effect for scan bookings
        if (scanBooking && scanBookingData) {
            prepopulateScanBookingData();
        }
    }, [scanBooking, scanBookingData, selectedLocation, dispatch]);

    useEffect(() => {
        if (clientId && !scanBooking) {
            // Add !scanBooking condition
            const clientObj = { clientId: clientId, value: clientId };
            setClientData(clientObj);
            handleClientSelect(clientObj);
        }

        if (facilityId && !scanBooking) {
            // Add !scanBooking condition
            form.setFieldValue('location', facilityId);
        }
    }, [clientId, facilityId, scanBooking]);

    useEffect(() => {
        if (clientId) {
            const clientObj = { clientId: clientId, value: clientId };
            setClientData(clientObj);

            handleClientSelect(clientObj);
        }

        if (facilityId) {
            form.setFieldValue('location', facilityId);
        }
    }, [clientId, facilityId]); // add deps
    const fetchPackageListing = async (searchText = '', page = 1) => {
        if (clientData && serviceCategoryId && subCategoryId) {
            const payload = {
                classType: ClassType.PERSONAL_APPOINTMENT,
                clientUserId: clientData.value,
                serviceCategoryId: serviceCategoryId,
                subTypeId: subCategoryId,
                pageSize: 10,
                page,
                search: searchText,
                isNewBooking: true,
            };

            const response = await dispatch(
                PricingListingByUserAndSubType(payload)
            ).unwrap();
            console.log(response, '------------>>>>>>>>>>>>>>>>');

            return (
                response?.data?.data?.map((item: any) => ({
                    value: item.purchaseId,
                    label: `${item.packageName}`.trim(),
                })) || []
            );
        }
    };

    const handlePackageChange = (packageId: string) => {
        setStaffErrorMessage('');
        const packageData = store.pricingListUserAndSubType.find(
            (item: any) => item._id === packageId
        );
        setRemainingSessions(packageData?.remainingSession);
        trainerList({ date: undefined, calculatedEndTime: undefined });
    };

    const onFinish = (values: any, checkIn = false) => {
        const serviceData = JSON.parse(values.serviceType);
        const purchaseIdValue = values.package?.value || values.package;
        // Extract individual values
        const serviceId = serviceData.serviceId;
        const appointmentId = serviceData.appointmentId;
        const dateRangeData: any = dateRange;
        const isMultiple = dateRangeData === 'Multiple';
        console.log(clientData, 'skdasjdkjaskdjs');
        const payload = {
            facilityId: values.location,
            trainerId: values.staff,
            // role === RoleType.TRAINER ? store.user?._id : values.staff,
            clientId:
                clientData.value || clientData.userId || clientData.clientId,
            classType: 'personalAppointment',
            roomId: values.roomId,
            notes: values.notes,
            dateRange,
            purchaseId: purchaseIdValue,
            serviceCategory: serviceId,
            sendConfirmation: values.sendConfirmation,
            subType: appointmentId,
            duration: values.duration,
            ...(isMultiple
                ? {
                      startDate: dayjs(values.startDate)
                          .startOf('day')
                          .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                      endDate: dayjs(values.endDate)
                          .endOf('day')
                          .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                      schedule: cleanupWorkingHours(workingHours),
                      markType: repeatOption,
                  }
                : {
                      date: dayjs(startDate)
                          .startOf('day')
                          .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                      from: dayjs(startTime).format('HH:mm'),
                      to: dayjs(endTime).format('HH:mm'),
                  }),
            ...(!scheduleId && { checkIn }),
        };

        if (scheduleId) {
            startSubmitLoader();
            dispatch(
                UpdateAppointmentScheduling({
                    payload: { ...payload, scheduleId },
                })
            )
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;

                    if (status === 200 || status === 201) {
                        form.resetFields();
                        setStartDate(null);
                        setStartTime(null);
                        setEndTime(null);
                        setSelectedLocation('');
                        onClose();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [selectedLocation],
                            })
                        );
                    }
                })
                .finally(endSubmitLoader);
        } else {
            if (checkIn) startSubmitCheckInLoader();
            else startSubmitLoader();
            dispatch(CreateAppointmentScheduling({ payload: payload }))
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;

                    if (status === 200 || status === 201) {
                        form.resetFields();
                        setStartDate(null);
                        setStartTime(null);
                        setEndTime(null);
                        setSelectedLocation('');
                        onClose();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [selectedLocation],
                            })
                        );
                    }
                })
                .finally(() => {
                    endSubmitLoader();
                    endSubmitCheckInLoader();
                });
        }
    };
    const handleStaffChange = (value: any) => {
        const selectedStaff = staffOption.find(
            (staff: any) => staff.value === value
        );
        if (selectedStaff) {
            const isAvailable = selectedStaff.availability;
            if (!isAvailable) {
                setStaffErrorMessage(selectedStaff.reason);
            } else {
                setStaffErrorMessage('');
            }
        }
    };

    const handleDateRange = async (value: any) => {
        setDateRange(value);
        form.setFieldsValue({
            // startDate: dayjs(slotUpdateInfo?.startDate),
            // endDate: dayjs(slotUpdateInfo?.endDate),
            // selectedDate: dayjs(slotUpdateInfo?.startDate),
        });
        // updateAvailabilityTimeSlots(null, false, value);

        if (scheduleId && isEdit) {
            fetchRecurringDetails({ newDateRange: value });
        }
    };

    const handleDateChange = (date: any) => {
        if (!date) return;
        const dayOfWeek = date.format('ddd').toLowerCase();
        setSelectedDay(dayOfWeek);
    };

    const updateAvailabilityTimeSlots = (
        date: any,
        updateDayOfWeek: boolean,
        range: string
    ) => {
        if (updateDayOfWeek) handleDateChange(date);
        const values = form.getFieldsValue();
        if (
            (isEdit && values?.startDate && values?.endDate) ||
            values.selectedDate
        ) {
            const startDate =
                range === 'Single'
                    ? dayjs(values.selectedDate).startOf('day').toDate()
                    : dayjs(values.startDate).startOf('day').toDate();
            const endDate =
                range === 'Single'
                    ? dayjs(values.selectedDate).endOf('day').toDate()
                    : dayjs(values.endDate).endOf('day').toDate();
            // dispatch(
            //     StaffAvailabilityDetails({
            //         payload: {
            //             facilityId: slotUpdateInfo?.facilityId,
            //             trainerId: slotUpdateInfo?.trainerId,
            //             startDate,
            //             endDate,
            //             from: slotUpdateInfo.from,
            //             to: slotUpdateInfo.to,
            //             dateRange: range,
            //         },
            //     })
            // ).then((res: any) => {
            //     const staffAvailabilityDetails = res.payload?.data?.data;
            //     const { timeSlots } = staffAvailabilityDetails;
            //     if (!timeSlots) return;
            //     setWorkingHours(timeSlots);
            // });
        }
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Form: {
                        verticalLabelMargin: '-10px',
                    },
                },
            }}
        >
            <Form
                className=" flex flex-col lg:gap-5 "
                name="schedule-form"
                form={form}
                layout="horizontal"
                variant="borderless"
                size="large"
                onFinish={onFinish}
                disabled={!isEdit && !!scheduleId}
            >
                <div className="flex flex-row items-start">
                    <div className="w-[40%] lg:-translate-y-16  ">
                        <FormClientDetails
                            facilityId={selectedLocation}
                            onClientSelect={handleClientSelect}
                            clientData={clientData}
                            isEdit={clientId || scheduleId ? true : false}
                        />
                    </div>

                    <div className="w-[60%] border-s-2 ps-6">
                        <div className="flex w-full justify-between ">
                            <div className="  lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left">Location</p>
                                    }
                                    name="location"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select location',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        className="border-b-1"
                                        allowClear
                                        value={selectedLocation}
                                        onChange={(value) => {
                                            setSelectedLocation(value);
                                        }}
                                        placeholder="Select facility"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={
                                            role === RoleType.TRAINER
                                                ? LocationOptionByStaff
                                                : FacilityOptions
                                        }
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className=" flex w-full justify-between ">
                            <div className="lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left @sm:w-full">
                                            Service
                                        </p>
                                    }
                                    name="serviceType"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select a service',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="border-b-1 "
                                        showSearch
                                        placeholder="Select Service"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        onChange={handleServiceTypeChange}
                                        disabled={
                                            !clientData ||
                                            (!!scheduleId && !isEdit)
                                        }
                                        style={
                                            !clientData ||
                                            (!isEdit && scheduleId)
                                                ? {
                                                      backgroundColor:
                                                          '#f5f5f5',
                                                      color: '#a0a0a0',
                                                      cursor: 'not-allowed',
                                                  }
                                                : {}
                                        }
                                    >
                                        {serviceOptions.map(
                                            (group: any, index: number) => (
                                                <OptGroup
                                                    key={`${group.category}-${index}`}
                                                    label={group.category}
                                                >
                                                    {group.options.map(
                                                        (service: any) => (
                                                            <Option
                                                                key={
                                                                    service.value
                                                                }
                                                                value={
                                                                    service.value
                                                                }
                                                            >
                                                                {service.label}
                                                            </Option>
                                                        )
                                                    )}
                                                </OptGroup>
                                            )
                                        )}
                                    </Select>
                                </Form.Item>
                            </div>
                        </div>

                        <div className="flex w-full gap-4  lg:w-[95%] @sm:-mt-5">
                            <div className="flex w-full justify-between gap-3  lg:flex-row lg:items-end ">
                                <div className="w-[100%] ">
                                    {/* <Tooltip title={!clientData ? "Please select a client" : ""} placement="top"></Tooltip> */}

                                    <Form.Item
                                        className=" w-full"
                                        label={
                                            <p className=" text-left ">
                                                Package
                                            </p>
                                        }
                                        name="package"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please select package',
                                            },
                                        ]}
                                    >
                                        <InfiniteScrollSelect
                                            fetchOptions={fetchPackageListing}
                                            // options={clientOptions}
                                            onChange={(value, option) => {
                                                handlePackageChange(value);
                                            }}
                                            placeholder="Select Package"
                                            className="w-[100%] border-b border-[#d1d5db]"
                                            disabled={
                                                !clientData ||
                                                (!!scheduleId && !isEdit) ||
                                                !serviceCategoryId
                                            }
                                        />
                                    </Form.Item>
                                </div>
                                {/* <div className="w-[30%]">
                                        <Form.Item
                                            label={''}
                                            name="sendConfirmation"
                                            valuePropName="checked"
                                            rules={[
                                                {
                                                    required: false,
                                                    message: 'Please select client',
                                                },
                                            ]}
                                        >
                                            <Checkbox className="  text-lg ">
                                                Pay Later
                                            </Checkbox>
                                        </Form.Item>
                                    </div> */}
                            </div>
                        </div>

                        <div className="  flex w-full justify-between gap-4 rounded-none lg:mb-7 lg:w-[95%] @sm:-mt-5">
                            <div className="w-[23%]"></div>
                            <Input
                                type="text"
                                style={{
                                    backgroundColor: '#f5f5f5',
                                    color: '#a0a0a0',
                                    cursor: 'not-allowed',
                                    borderRadius: '0px',
                                }}
                                // className="w-[95%] -translate-y-3"
                                disabled
                                value={remainingSessions}
                                placeholder="No. of session left"
                            />
                        </div>

                        <div className="mb-8 flex w-full justify-between ">
                            <div className="flex items-center ps-2 lg:w-[100%]">
                                <p className=" text-[13px] font-medium text-[#1A3353] lg:w-[20%]">
                                    Date Range
                                </p>
                                <Radio.Group
                                    value={dateRange}
                                    onChange={(e) =>
                                        handleDateRange(e.target.value)
                                    }
                                >
                                    <Radio
                                        className="text-[#455560]"
                                        value="Single"
                                    >
                                        Single
                                    </Radio>
                                    <Radio
                                        className="text-[#455560]"
                                        value="Multiple"
                                    >
                                        Multiple
                                    </Radio>
                                </Radio.Group>
                            </div>
                        </div>

                        {dateRange === DateRangeType.SINGLE ? (
                            <div className=" flex justify-between lg:w-[95%]  lg:flex-row lg:items-center @sm:flex-col">
                                <p className=" text-[13px] font-medium text-[#1A3353] lg:ms-2  @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                    Select Date
                                </p>
                                <DatePicker
                                    popupClassName="custom-datepicker"
                                    value={startDate}
                                    format="DD/MM/YYYY"
                                    onChange={(date) => {
                                        setStartDate(date);
                                        handleDateChange(date);
                                        const value = form.getFieldsValue();
                                        const parsedServiceType = JSON.parse(
                                            value.serviceType
                                        );

                                        setStaffErrorMessage('');
                                        trainerList({
                                            date,
                                            serviceId:
                                                parsedServiceType.serviceId,
                                            subTypeId:
                                                parsedServiceType.appointmentId,
                                        });
                                        fetchRoomData({ date });
                                    }}
                                    className="lg:w-[80%] @sm:w-[100%]"
                                    style={{
                                        borderBottom: '1px solid #e5e7eb',
                                        borderRadius: '0px',
                                        backgroundColor: !serviceCategoryId
                                            ? '#f5f5f5'
                                            : undefined,
                                        color: !serviceCategoryId
                                            ? '#a0a0a0'
                                            : undefined,
                                        cursor: !serviceCategoryId
                                            ? 'not-allowed'
                                            : undefined,
                                    }}
                                    disabled={
                                        !serviceCategoryId ||
                                        (!isEdit && scheduleId)
                                    }
                                />
                            </div>
                        ) : (
                            <>
                                <div className="flex w-[100%] flex-col items-center">
                                    <div className=" lg:w-[100%] @sm:w-full">
                                        <Form.Item
                                            label="Start Date"
                                            name="startDate"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please enter start date',
                                                },
                                            ]}
                                        >
                                            <DatePicker
                                                popupClassName="custom-datepicker"
                                                placeholder="DD/MM/YYYY"
                                                format="DD/MM/YYYY"
                                                className=" lg:w-[95%] "
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                    backgroundColor:
                                                        !serviceCategoryId
                                                            ? '#f5f5f5'
                                                            : undefined,
                                                    color: !serviceCategoryId
                                                        ? '#a0a0a0'
                                                        : undefined,
                                                    cursor: !serviceCategoryId
                                                        ? 'not-allowed'
                                                        : undefined,
                                                }}
                                                onChange={(date) => {
                                                    updateAvailabilityTimeSlots(
                                                        date,
                                                        true,
                                                        dateRange
                                                    );
                                                    if (scheduleId && isEdit) {
                                                        fetchRecurringDetails({
                                                            newStartDate: date,
                                                        });
                                                    }
                                                }}
                                                disabled={
                                                    !serviceCategoryId ||
                                                    (!isEdit && scheduleId)
                                                }
                                            />
                                        </Form.Item>
                                    </div>

                                    <div className="lg:w-[100%] @sm:w-full">
                                        <Form.Item label="Repeat" required>
                                            {/* <Select
                                                variant="borderless"
                                                className="border-b border-[#d1d5db]"
                                                placeholder="Select Repeat Type"
                                                value={repeatOption}
                                                onChange={(value) => {
                                                    setRepeatOption(value);
                                                    if (scheduleId && isEdit) {
                                                        fetchRecurringDetails({
                                                            newRepeatOption:
                                                                value,
                                                        });
                                                    }
                                                }}
                                                options={[
                                                    {
                                                        label: 'Repeat Weekly',
                                                        value: 'weekly',
                                                    },
                                                    {
                                                        label: 'Custom',
                                                        value: 'custom',
                                                    },
                                                ]}
                                            /> */}
                                            <Radio.Group
                                                value={repeatOption} // 'weekly' | 'custom'
                                                onChange={(e) => {
                                                    const value = e.target
                                                        .value as
                                                        | 'weekly'
                                                        | 'custom';
                                                    setRepeatOption(value);
                                                    if (scheduleId && isEdit) {
                                                        fetchRecurringDetails({
                                                            newRepeatOption:
                                                                value,
                                                        });
                                                    }
                                                }}
                                                optionType="default" // or "button" if you prefer button-style radios
                                            >
                                                <Radio value="weekly">
                                                    Repeat Weekly
                                                </Radio>
                                                <Radio value="custom">
                                                    Custom
                                                </Radio>
                                            </Radio.Group>
                                        </Form.Item>
                                    </div>

                                    {repeatOption === 'custom' && (
                                        <div className="lg:w-[100%] @sm:w-full">
                                            <Form.Item
                                                label="End Date"
                                                name="endDate"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Please enter end date',
                                                    },
                                                ]}
                                            >
                                                <DatePicker
                                                    popupClassName="custom-datepicker"
                                                    placeholder="DD/MM/YYYY"
                                                    format="DD/MM/YYYY"
                                                    className="lg:w-[95%]"
                                                    style={{
                                                        borderBottom:
                                                            '1px solid #e5e7eb',
                                                        borderRadius: '0px',
                                                        backgroundColor:
                                                            !serviceCategoryId
                                                                ? '#f5f5f5'
                                                                : undefined,
                                                        color: !serviceCategoryId
                                                            ? '#a0a0a0'
                                                            : undefined,
                                                        cursor: !serviceCategoryId
                                                            ? 'not-allowed'
                                                            : undefined,
                                                    }}
                                                    disabled={
                                                        !serviceCategoryId ||
                                                        (!isEdit && scheduleId)
                                                    }
                                                    // disabledDate={(currentDate) =>
                                                    //     currentDate &&
                                                    //     currentDate.isBefore(
                                                    //         dayjs().startOf('day')
                                                    //     )
                                                    // }
                                                    onChange={(date) => {
                                                        updateAvailabilityTimeSlots(
                                                            date,
                                                            false,
                                                            dateRange
                                                        );
                                                        if (
                                                            scheduleId &&
                                                            isEdit &&
                                                            date
                                                        ) {
                                                            fetchRecurringDetails(
                                                                {
                                                                    newEndDate:
                                                                        date,
                                                                }
                                                            );
                                                        }
                                                    }}
                                                    disabledDate={(current) =>
                                                        current &&
                                                        current <
                                                            dayjs(
                                                                form.getFieldValue(
                                                                    'startDate'
                                                                )
                                                            ).startOf('day')
                                                    }
                                                />
                                            </Form.Item>
                                        </div>
                                    )}
                                </div>
                            </>
                        )}

                        {/* <div className="pt-5">
                            {(dateRange !== DateRangeType.SINGLE ||
                                dateRange === DateRangeType.MULTIPLE) && (
                                <>
                                    <div className="flex flex-row items-center justify-between  pb-9 text-[13px] font-medium text-[#1A3353]">
                                        Select Days
                                        <div className=" flex flex-wrap lg:w-[80%] lg:gap-3  @xl:gap-8">
                                            {daysOfWeek?.map((day) => (
                                                <div className="">
                                                    <Button
                                                        shape="circle"
                                                        className={`p-2   ${
                                                            selectedDay?.includes(
                                                                day
                                                            )
                                                                ? 'bg-[#455560] text-white'
                                                                : 'bg-white'
                                                        }`}
                                                        onClick={() =>
                                                            handleDayClick(day)
                                                        }
                                                    >
                                                        {capitalizeFirstLetter(
                                                            day.slice(0, 3)
                                                        )}
                                                    </Button>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                    <div className="mb-8 flex flex-row">
                                        <div className="w-[20%]"></div>
                                        <Switch
                                            checked={isChecked}
                                            onChange={() =>
                                                setIsChecked((prev) => !prev)
                                            }
                                        />
                                        <span className="ml-3 text-[14px] text-[#1A3353]">
                                            Duplicate for all days
                                        </span>
                                    </div>
                                </>
                            )}
                        </div> */}

                        {/* <div className=" flex justify-between lg:w-[95%]  lg:flex-row lg:items-center @sm:flex-col">
                            <p className=" text-[13px] font-medium text-[#1A3353] lg:ms-2  @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                Select Date
                            </p>
                            <DatePicker
                                popupClassName="custom-datepicker"
                                value={startDate}
                                format="DD/MM/YYYY"
                                onChange={(date) => {
                                    setStartDate(date);
                                    const value = form.getFieldsValue();
                                    const parsedServiceType = JSON.parse(
                                        value.serviceType
                                    );

                                    setStaffErrorMessage('');
                                    trainerList({
                                        date,
                                        serviceId: parsedServiceType.serviceId,
                                        subTypeId:
                                            parsedServiceType.appointmentId,
                                    });
                                    fetchRoomData({ date });
                                }}
                                className="lg:w-[80%] @sm:w-[100%]"
                                style={{
                                    borderBottom: '1px solid #e5e7eb',
                                    borderRadius: '0px',
                                    backgroundColor: !serviceCategoryId
                                        ? '#f5f5f5'
                                        : undefined,
                                    color: !serviceCategoryId
                                        ? '#a0a0a0'
                                        : undefined,
                                    cursor: !serviceCategoryId
                                        ? 'not-allowed'
                                        : undefined,
                                }}
                                disabled={
                                    !serviceCategoryId ||
                                    (!isEdit && scheduleId)
                                }
                            />
                        </div> */}

                        {dateRange === DateRangeType.SINGLE ? (
                            <>
                                <div className=" mt-6 flex flex-row items-center bg-white">
                                    <div className="flex justify-between lg:flex-row lg:items-center @sm:flex-col @lg:w-[21%] @xl:w-[19%] @2xl:w-[19%]">
                                        <p className="text-[13px] font-medium text-[#1A3353] lg:ms-2 lg:w-[65px] @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                            Start Time
                                        </p>
                                    </div>
                                    <div className="flex flex-row items-center @2xl:w-[76%]">
                                        <div className="w-[20%]">
                                            <TimePicker
                                                format="HH:mm"
                                                value={startTime}
                                                minuteStep={5}
                                                onChange={handleStartTimeChange}
                                                disabled={
                                                    !serviceCategoryId ||
                                                    (!isEdit && scheduleId)
                                                }
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                    backgroundColor:
                                                        !serviceCategoryId
                                                            ? '#f5f5f5'
                                                            : undefined,
                                                    color: !serviceCategoryId
                                                        ? '#a0a0a0'
                                                        : undefined,
                                                    cursor: !serviceCategoryId
                                                        ? 'not-allowed'
                                                        : undefined,
                                                }}
                                            />
                                        </div>
                                        <div className="w-[38%] lg:translate-y-3 @xl:translate-x-3">
                                            <Form.Item
                                                className="PA-form-duration-input"
                                                label={
                                                    <p className="text-left @sm:w-full">
                                                        Duration
                                                    </p>
                                                }
                                                name="duration"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Please select duration',
                                                    },
                                                ]}
                                            >
                                                <Select
                                                    showSearch
                                                    className="ms-2 w-[90%] border-b-1"
                                                    placeholder="Select Duration"
                                                    options={durationOption}
                                                    onChange={handleDuration}
                                                    disabled={
                                                        !serviceCategoryId ||
                                                        (!isEdit && scheduleId)
                                                    }
                                                    style={{
                                                        backgroundColor:
                                                            !serviceCategoryId
                                                                ? '#f5f5f5'
                                                                : undefined,
                                                        color: !serviceCategoryId
                                                            ? '#a0a0a0'
                                                            : undefined,
                                                        cursor: !serviceCategoryId
                                                            ? 'not-allowed'
                                                            : undefined,
                                                    }}
                                                />
                                            </Form.Item>
                                        </div>
                                        <div className="flex w-[38%] justify-end lg:flex-row lg:items-center @sm:flex-col @xl:gap-0">
                                            <p className="translate-x-3 font-medium text-[#1A3353] lg:w-[40%] @lg:text-[12px] @xl:w-[35%] @xl:text-[13px]">
                                                End Time
                                            </p>
                                            <TimePicker
                                                format="HH:mm"
                                                value={endTime}
                                                disabled
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                    backgroundColor: '#f5f5f5',
                                                    color: '#a0a0a0',
                                                    cursor: 'not-allowed',
                                                }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </>
                        ) : (
                            <Form.Item label="Weekly Schedule">
                                <MultipleSchedule
                                    form={form}
                                    initialWorkingHours={workingHours}
                                    onChange={(updatedSlots: any) =>
                                        console.log(
                                            'Updated slot-----------',
                                            updatedSlots
                                        )
                                    }
                                    {...{
                                        dateRange,
                                        workingHours,
                                        setWorkingHours,
                                        daySelected: selectedDay,
                                        handleDuration,
                                        handleStartTimeChange,
                                        durationOption,
                                        serviceCategoryId,
                                        isEdit,
                                        scheduleId,
                                        startTime,
                                        endTime,
                                        durationonFOrm:
                                            form.getFieldValue('duration'),
                                    }}
                                />
                            </Form.Item>
                        )}

                        {/* <div className=" mt-6 flex flex-row items-center bg-white">
                            <div className="flex justify-between  lg:flex-row  lg:items-center @sm:flex-col @lg:w-[21%] @xl:w-[19%] @2xl:w-[19%]">
                                <p className="  text-[13px] font-medium text-[#1A3353] lg:ms-2   lg:w-[65px] @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                    Start Time
                                </p>
                            </div>
                            <div className="flex flex-row items-center @2xl:w-[76%]">
                                <div className="w-[20%]">
                                    <TimePicker
                                        format="HH:mm"
                                        value={startTime}
                                        minuteStep={5}
                                        // className="lg:w-[80%] @sm:w-[100%]"
                                        onChange={handleStartTimeChange}
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                            backgroundColor: !serviceCategoryId
                                                ? '#f5f5f5'
                                                : undefined,
                                            color: !serviceCategoryId
                                                ? '#a0a0a0'
                                                : undefined,
                                            cursor: !serviceCategoryId
                                                ? 'not-allowed'
                                                : undefined,
                                        }}
                                        disabled={
                                            !serviceCategoryId ||
                                            (!isEdit && scheduleId)
                                        }
                                    />
                                </div>

                                <div className=" w-[38%] lg:translate-y-3 @xl:translate-x-3  ">
                                    <Form.Item
                                        className="PA-form-duration-input"
                                        label={
                                            <p className=" text-left  @sm:w-full">
                                                Duration
                                            </p>
                                        }
                                        name="duration"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select duration',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            className="ms-2 w-[90%] border-b-1"
                                            placeholder="Select Duration"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes( d
                                                        input.toLowerCase()
                                                    )
                                            }
                                            onChange={handleDuration}
                                            options={durationOption}
                                            disabled={
                                                !serviceCategoryId ||
                                                (!isEdit && scheduleId)
                                            }
                                            style={{
                                                backgroundColor:
                                                    !serviceCategoryId
                                                        ? '#f5f5f5'
                                                        : undefined,
                                                color: !serviceCategoryId
                                                    ? '#a0a0a0'
                                                    : undefined,
                                                cursor: !serviceCategoryId
                                                    ? 'not-allowed'
                                                    : undefined,
                                            }}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" flex w-[38%] justify-end lg:flex-row   lg:items-center @sm:flex-col @xl:gap-0 ">
                                    <p className="translate-x-3 font-medium text-[#1A3353] lg:w-[40%] @lg:text-[12px] @xl:w-[35%] @xl:text-[13px]">
                                        End Time
                                    </p>
                                    <TimePicker
                                        format="HH:mm"
                                        disabled
                                        value={endTime}
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                    />
                                </div>
                            </div>
                        </div> */}

                        <div className=" mt-6 flex w-full justify-between ">
                            <div className="lg:w-[95%] @sm:w-full">
                                {/* {role === RoleType.TRAINER ? (
                                    <Form.Item
                                        label="Staff"
                                        name="staffName"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select the Staff name',
                                            },
                                        ]}
                                    >
                                        <Input
                                            style={{
                                                borderBottom:
                                                    '1px solid #e5e7eb',
                                                borderRadius: '0px',
                                            }}
                                            defaultValue={store.user?.firstName}
                                            disabled
                                        />
                                    </Form.Item>
                                ) : ( */}
                                <Form.Item
                                    label={<p className="text-left">Staff</p>}
                                    name="staff"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select staff',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="border-b-1"
                                        showSearch
                                        placeholder="Select instructor"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={staffOption}
                                        onChange={handleStaffChange}
                                        disabled={
                                            !serviceCategoryId ||
                                            (!isEdit && scheduleId)
                                        }
                                        style={{
                                            backgroundColor: !serviceCategoryId
                                                ? '#f5f5f5'
                                                : undefined,
                                            color: !serviceCategoryId
                                                ? '#a0a0a0'
                                                : undefined,
                                            cursor: !serviceCategoryId
                                                ? 'not-allowed'
                                                : undefined,
                                        }}
                                        optionLabelProp="name"
                                        // labelRender={(value) => {
                                        //     const selected = staffOption.find(opt => opt.value === value);
                                        //     return selected?.title || '';
                                        // }}
                                    />
                                </Form.Item>
                                {/* )} */}
                                {staffErrorMessage && (
                                    <>
                                        <div className="flex flex-row items-center">
                                            <div className=" @sm:flex-col @lg:w-[20%] @xl:w-[20%] @2xl:w-[20%]"></div>
                                            <div className=" -translate-y-3 text-lg font-semibold text-red-500">
                                                <InfoCircleOutlined className="text-red-500" />
                                                &nbsp;
                                                {staffErrorMessage}
                                            </div>
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>

                        {/* <div className="">
                            <div className="flex w-full justify-between ">
                                <div className=" lg:w-[95%] @sm:w-full">
                                    <Form.Item
                                        label={
                                            <p className="text-left">Client</p>
                                        }
                                        name="client"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please select client',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            placeholder="Select Client"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={ClientOptions}
                                            onChange={handleClientChange}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                            <div className="flex w-full justify-between lg:mt-5 ">
                                <div className="  lg:w-[95%] @sm:w-full">
                                    <Form.Item
                                        label={
                                            <p className="text-left lg:w-[55px]">
                                                Phone
                                            </p>
                                        }
                                        name="phone"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please select client',
                                            },
                                        ]}
                                    >
                                        <Input
                                            placeholder="Phone Number"
                                            readOnly
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                            <div className="flex w-full justify-between lg:mt-5 ">
                                <div className=" lg:w-[95%] @sm:w-full">
                                    <Form.Item
                                        label={
                                            <p className="text-left lg:w-[55px]">
                                                Email
                                            </p>
                                        }
                                        name="email"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please select client',
                                            },
                                        ]}
                                    >
                                        <Input
                                            placeholder="Email"
                                            type="email"
                                            readOnly
                                        />
                                    </Form.Item>
                                    <div className="lg:-mt-8 @sm:-mt-6">
                                <Form.Item
                                    label={''}
                                    name="sendConfirmation"
                                    valuePropName="checked"
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please select client',
                                        },
                                    ]}
                                >
                                    <Checkbox className="  text-lg lg:ps-[140px]">
                                        Send a confirmation
                                    </Checkbox>
                                </Form.Item>
                            </div>
                                </div>
                            </div>
                        </div> */}

                        {dateRange === DateRangeType.SINGLE && (
                            <div className=" flex w-full justify-between ">
                                <div className="lg:w-[95%] @sm:w-full">
                                    <Form.Item
                                        label={
                                            <p className=" text-left  @sm:w-full">
                                                Room
                                            </p>
                                        }
                                        name="roomId"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please select room',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            className="border-b-1"
                                            placeholder="Select Room"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={roomData}
                                            disabled={
                                                !serviceCategoryId ||
                                                (!isEdit && scheduleId)
                                            }
                                            style={{
                                                backgroundColor:
                                                    !serviceCategoryId
                                                        ? '#f5f5f5'
                                                        : undefined,
                                                color: !serviceCategoryId
                                                    ? '#a0a0a0'
                                                    : undefined,
                                                cursor: !serviceCategoryId
                                                    ? 'not-allowed'
                                                    : undefined,
                                            }}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        )}

                        <div className=" flex w-full justify-between lg:mb-4">
                            <div className=" lg:w-[95%] @sm:w-full">
                                <Form.Item label="Notes" name="notes">
                                    <TextArea
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                        }}
                                        autoSize={{ minRows: 1, maxRows: 2 }}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                    </div>
                </div>
                {(!scheduleId || (scheduleId && isEdit)) && (
                    <>
                        <div className="flex w-[100%] flex-row gap-5 pe-10 lg:justify-end @sm:justify-center">
                            <Form.Item>
                                {dateRange !== 'Multiple' && (
                                    <div
                                        className=""
                                        style={{ display: 'flex', gap: '10px' }}
                                    >
                                        <Button
                                            loading={submitCheckInLoader}
                                            className="border border-[#1A3353] px-20 py-7 text-2xl"
                                            onClick={() =>
                                                onFinish(
                                                    form.getFieldsValue(),
                                                    true
                                                )
                                            }
                                        >
                                            Save & Check in
                                        </Button>
                                    </div>
                                )}
                            </Form.Item>
                            <Form.Item>
                                <div
                                    className=""
                                    style={{ display: 'flex', gap: '10px' }}
                                >
                                    <Button
                                        loading={submitLoader}
                                        className="bg-purpleLight px-20 py-7 text-2xl"
                                        type="primary"
                                        htmlType="submit"
                                    >
                                        Save
                                    </Button>
                                </div>
                            </Form.Item>
                        </div>
                    </>
                )}
            </Form>
        </ConfigProvider>
    );
};

export default BookAppointmentModal;
