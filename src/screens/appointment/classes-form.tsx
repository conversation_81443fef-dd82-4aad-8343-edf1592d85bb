import React, { useEffect, useState } from 'react';
import {
    Button,
    ConfigProvider,
    DatePicker,
    Form,
    FormProps,
    Input,
    Radio,
    Select,
    Switch,
    TimePicker,
} from 'antd';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { ClassType, DateRangeType, RoleType } from '~/types/enums';
import {
    FacilitiesList,
    GetFacilityListByStaffId,
} from '~/redux/actions/facility-action';
import { activeServiceCategoyListv1 } from '~/redux/actions/serviceCategoryAction';
import { StaffAvailabilityListV1 } from '~/redux/actions/appointment-action';
import { capitalizeFirstLetter } from '~/components/common/function';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useLocation } from 'wouter';
import { roomListingByScheduling } from '~/redux/actions/room-action';
import {
    BookedSchedulingDetailsRecurring,
    classSchedulingList,
    createClassScheduling,
    updateClassScheduling,
} from '~/redux/actions/class-action';
import { useLoader } from '~/hooks/useLoader';
import debounce from 'lodash.debounce';
import MultipleSchedule from '~/screens/appointment/multipleScheduling';
import Alertify from '~/services/alertify';

const { TextArea } = Input;
const { Option, OptGroup } = Select;
dayjs.extend(utc);
dayjs.extend(timezone);

interface ClassesFormProps {
    onClose?: any;
    title?: string;
    scheduleId?: string;
    isEdit?: boolean;
    facilityId?: string;
    slotSelectedInfo?: any;
    classId?: string;
    isSubstitute?: boolean;
}

interface TimeSlot {
    from: Dayjs | null | any;
    to: Dayjs | null | any;
    durationInMinutes: any;
    classCapacity: null | string;
}

interface WorkingHours {
    [key: string]: TimeSlot[];
}

const ClassesForm: React.FC<ClassesFormProps> = ({
    onClose,
    scheduleId,
    classId,
    isEdit,
    facilityId,
    isSubstitute = false,
}) => {
    const [_, setLocation] = useLocation();
    const [selectedDay, setSelectedDay] = useState<string>('mon');
    const [workingHours, setWorkingHours] = useState<WorkingHours>({
        mon: [],
        tue: [],
        wed: [],
        thu: [],
        fri: [],
        sat: [],
        sun: [],
    });

    const { role, user } = useSelector((state: any) => state.auth_store);
    const dispatch = useAppDispatch();
    const [form] = Form.useForm();
    const [selectedLocation, setSelectedLocation] = useState(
        facilityId ? facilityId : null
    );
    const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(dayjs());
    const [endDate, setEndDate] = useState<dayjs.Dayjs | null>(dayjs());
    const [startTime, setStartTime] = useState<dayjs.Dayjs | null>(null);
    const [endTime, setEndTime] = useState<dayjs.Dayjs | null>(null);
    const [startTimeMinuteStep, setStartTimeMinuteStep] = useState<any>(1);
    const [durationOption, setDurationOption] = useState<any>([]);
    const [serviceCategoryId, setServiceCategoryId] = useState<any>(null);
    const [subCategoryId, setSubcategoryId] = useState<any>(null);
    const [staffOption, setStaffOption] = useState<any>(null);
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const [roomData, setRoomData] = useState<any>([]);
    const [dateRange, setDateRange] = useState<'Single' | 'Multiple'>('Single');
    const [repeatOption, setRepeatOption] = useState<string>('custom');

    const store = useAppSelector((state) => ({
        roomList: state.room_store.roomList,
        facilityList: state.facility_store.facilityList,
        facilityListByStaffId: state.facility_store.facilityListByStaffId,
        trainerList: state.appointment_store.trainerList,
        ServiceCategoryListData:
            state.service_category_store.ServiceCategoryListData,
        ActiveServiceCategoryListV1:
            state.service_category_store.ActiveServiceCategoryListV1,
        classSchedulingDetails: state.class_store.classSchedulingDetails,
    }));

    const populateWorkingHours = (slots: any) => {
        const updated: WorkingHours = {};

        Object.keys(slots).forEach((day) => {
            const slot = slots[day];
            if (slot?.from && slot?.to) {
                updated[day] = [
                    {
                        from: slot.from,
                        to: slot.to,
                        durationInMinutes: slot.durationInMinutes,
                        classCapacity: slot.classCapacity,
                    },
                ];
            } else {
                updated[day] = [];
            }
        });

        return updated;
    };

    const isFieldDisabled = (fieldName: string) => {
        if (isSubstitute) {
            return fieldName !== 'staff';
        }
        return !isEdit && !!scheduleId;
    };

    const cleanupWorkingHours = (input: WorkingHours): WorkingHours => {
        const cleaned: WorkingHours = {};

        Object.entries(input).forEach(([day, slots]) => {
            const validSlots = slots.filter(
                (slot) =>
                    slot.from !== null &&
                    slot.to !== null &&
                    slot.durationInMinutes !== null
            );

            if (validSlots.length > 0) {
                cleaned[day] = validSlots;
            }
        });

        return cleaned;
    };

    const transformServiceData = (apiData: any) => {
        return apiData.map((service: any) => ({
            category: service.name,
            options: service.appointmentType.map((appointment: any) => ({
                label: `${appointment.name} - ${appointment.durationInMinutes} mins`,
                value: JSON.stringify({
                    serviceId: service._id,
                    appointmentId: appointment._id,
                    duration: appointment.durationInMinutes,
                }),
            })),
        }));
    };

    const serviceOptions = transformServiceData(
        store.ActiveServiceCategoryListV1
    );

    const fetchRecurringDetails = async ({
        newDateRange = dateRange,
        newStartDate = startDate,
        newEndDate = endDate,
    }: {
        newDateRange?: string;
        newStartDate?: dayjs.Dayjs | null;
        newEndDate?: dayjs.Dayjs | null;
    }) => {
        try {
            if (!newStartDate || !newEndDate) return;

            const calculatedStartDate = newStartDate.format('YYYY-MM-DD');
            const calculatedEndDate = newEndDate.format('YYYY-MM-DD');

            const res: any = await dispatch(
                BookedSchedulingDetailsRecurring({
                    scheduleId,
                    dateRange: newDateRange,
                    startDate: calculatedStartDate,
                    endDate: calculatedEndDate,
                    markType: repeatOption,
                })
            ).unwrap();
            const data = res?.data?.data;
            if (newDateRange === 'Multiple') {
                if (data?.slots) {
                    const transformed = populateWorkingHours(data?.slots);
                    setWorkingHours(transformed);

                    const firstDayWithSlot = Object.entries(transformed).find(
                        ([, daySlots]) => daySlots.length > 0
                    );

                    if (firstDayWithSlot) {
                        const [day, daySlots] = firstDayWithSlot;

                        setSelectedDay(day);
                        setStartTime(
                            dayjs(`${calculatedStartDate}T${daySlots[0].from}`)
                        );
                        setEndTime(
                            dayjs(`${calculatedStartDate}T${daySlots[0].to}`)
                        );
                    }
                } else Alertify.error('No Slots found');
            } else {
                setStartDate(dayjs(data.date).startOf('day'));
                setEndDate(dayjs(data.date).startOf('day'));
                form.setFieldsValue({
                    startDate: dayjs(data.date).startOf('day'),
                    room: data.room?._id,
                    duration: data.duration,
                    classCapacity: data.capacity,
                });
            }
        } catch (err) {
            console.error('Error fetching recurring scheduling details', err);
        }
    };

    const handleDateRange = async (value: any) => {
        setDateRange(value);
        // if (scheduleId && isEdit)
        //     fetchRecurringDetails({
        //         newDateRange: value,
        //         newEndDate: value === 'Single' ? startDate : endDate,
        //     });
    };

    const handleStartDateChange = (
        date: dayjs.Dayjs | null,
        isRecurring = false
    ) => {
        if (!date) return;
        setStartDate(date);
        const dayOfWeek = date.format('ddd').toLowerCase();
        setSelectedDay(dayOfWeek);
        if (isRecurring && scheduleId && isEdit) {
            let calculatedEndDate = endDate;
            if (repeatOption === 'weekly') {
                calculatedEndDate = dayjs(date).add(1, 'year');
                setEndDate(calculatedEndDate);
                form.setFieldValue('endDate', calculatedEndDate);
            }
            fetchRecurringDetails({
                newStartDate: date,
                newEndDate: calculatedEndDate,
            });
        }
    };

    const handleEndDateChange = (date: dayjs.Dayjs | null) => {
        if (!date) return;
        setEndDate(date);
        if (scheduleId && isEdit) {
            fetchRecurringDetails({
                newEndDate: date,
            });
        }
    };

    useEffect(() => {
        if (
            !scheduleId ||
            !store.classSchedulingDetails ||
            !form ||
            dateRange !== 'Single'
        )
            return;

        const data = store.classSchedulingDetails;

        const serviceOptions = transformServiceData(
            store.ActiveServiceCategoryListV1
        );
        const selectedService = serviceOptions
            .flatMap((group: any) => group.options)
            .find((option: any) => {
                try {
                    const parsed = JSON.parse(option.value);
                    return (
                        parsed.serviceId === data.serviceCategory?._id &&
                        parsed.appointmentId === data.subType?._id
                    );
                } catch {
                    return false;
                }
            });

        const selectedServiceValue = selectedService?.value ?? '';
        const selectedDuration = selectedService
            ? JSON.parse(selectedService.value)?.duration
            : null;
        setStartDate(dayjs(data.date).startOf('day'));
        setEndDate(dayjs(data.date).startOf('day'));
        form.setFieldsValue({
            location: data.facility?._id,
            serviceType: selectedServiceValue,
            startDate: dayjs(data.date).startOf('day'),
            staff: data.trainer?._id,
            room: data.room?._id,
            duration: data.duration,
            classCapacity: data.capacity,
            Notes: data.notes,
        });

        setSelectedLocation(data.facility?._id);
        setServiceCategoryId(data.serviceCategory?._id);
        setSubcategoryId(data.subType?._id);

        const baseDate = dayjs(data.date?.split('T')[0]);

        if (dateRange === 'Single') {
            setStartTime(
                dayjs(`${baseDate.format('YYYY-MM-DD')}T${data.from}`)
            );
            setEndTime(dayjs(`${baseDate.format('YYYY-MM-DD')}T${data.to}`));
            setStartTimeMinuteStep(data.duration);
        } else {
            const firstDayWithSlot = Object.entries(data.slots).find(
                ([, slot]: any) => slot?.from
            );

            if (firstDayWithSlot) {
                const [day, slot]: [string, any] = firstDayWithSlot;
                setStartTime(
                    dayjs(`${baseDate.format('YYYY-MM-DD')}T${slot.from}`)
                );
                setEndTime(
                    dayjs(`${baseDate.format('YYYY-MM-DD')}T${slot.to}`)
                );
                setStartTimeMinuteStep(slot.durationInMinutes);
                setSelectedDay(day);
            }
        }

        const durationOptions = Array.from({ length: 5 }, (_, i) => {
            const multiple = selectedDuration * (i + 1);
            return { label: `${multiple} Minutes`, value: multiple };
        });
        setDurationOption(durationOptions);
    }, [store.classSchedulingDetails, scheduleId, isEdit, form]);

    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    const LocationOptionByStaff = store.facilityListByStaffId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const handleFormClose = () => {
        form.resetFields();
        onClose();
        setLocation('');
        setServiceCategoryId('');
    };

    useEffect(() => {
        if (role === RoleType.TRAINER || role === RoleType.WEBMASTER) {
            dispatch(GetFacilityListByStaffId({ staffId: user._id }));
        } else {
            dispatch(FacilitiesList({ page: 1, pageSize: 10 })).unwrap();
        }
    }, []);

    useEffect(() => {
        if (facilityId) form.setFieldValue('location', facilityId);
        dispatch(
            activeServiceCategoyListv1({
                classType: ClassType.CLASSES,
            })
        ).unwrap();
    }, []);

    const fetchAvailableRooms = debounce(() => {
        if (!serviceCategoryId || !selectedLocation) return;
        dispatch(
            roomListingByScheduling({
                serviceId: serviceCategoryId,
                facilityId: selectedLocation || facilityId,
                classType: ClassType.CLASSES,
                date: dayjs(startDate).format('YYYY-MM-DD'),
                startTime: dayjs(startTime).format('HH:mm'),
                endTime: dayjs(endTime).format('HH:mm'),
            })
        )
            .unwrap()
            .then((res: any) => {
                const roomOptions = res?.data?.data?.map((item: any) => ({
                    value: item._id,
                    label: item.roomName,
                    id: item._id,
                }));
                setRoomData(roomOptions);
                form.setFieldsValue({
                    roomId: roomOptions?.[0]?.value,
                });
            });
    }, 300);

    const fetchAvailableStaff = debounce(() => {
        if (!serviceCategoryId || !selectedLocation) return;
        const reqData = {
            facilityId: selectedLocation || facilityId,
            classType: ClassType.CLASSES,
            serviceId: serviceCategoryId,
            subTypeId: subCategoryId,
            date: dayjs(startDate).format('YYYY-MM-DDT00:00:00[Z]'),
            startTime: dayjs(startTime).format('HH:mm'),
            endTime: dayjs(endTime).format('HH:mm'),
        };
        try {
            dispatch(StaffAvailabilityListV1({ reqData }))
                .unwrap()
                .then((res: any) => {
                    const staffOptions = res?.data?.data?.map((item: any) => ({
                        value: item._id,
                        label: capitalizeFirstLetter(
                            `${item.firstName} ${item.lastName}`
                        ),
                    }));
                    setStaffOption(staffOptions || []);
                });
        } catch (error) {
            console.error('Error fetching available staff:', error);
            setStaffOption([]);
        }
    }, 300);

    const handleStartTimeChange = (time: any) => {
        setStartTime(time);
        let calculatedEndTime;
        if (time) {
            const duration = form.getFieldValue('duration');
            calculatedEndTime = dayjs(time).add(Number(duration), 'minute');
            setEndTime(calculatedEndTime);
        } else {
            setEndTime(null);
        }
    };

    const getNextTimeSlot = (step: number) => {
        const now = dayjs(startDate);
        const minutes = now.minute();
        const nextMinutes = Math.ceil(minutes / step) * step;
        return now.minute(nextMinutes).second(0).millisecond(0);
    };

    const handleDuration = (value: any) => {
        let calculatedEndTime;
        if (startTime) {
            calculatedEndTime = startTime.add(value, 'minute');
            setEndTime(calculatedEndTime);
        } else {
            setEndTime(null);
        }
    };

    const handleServiceTypeChange = async (value: string) => {
        form.resetFields(['roomId', 'staff']);
        const selectedOption = JSON.parse(value);

        const serviceId = selectedOption.serviceId;
        const appointmentId = selectedOption.appointmentId;
        const duration = selectedOption.duration;

        if (!serviceId || !appointmentId) return;

        setServiceCategoryId(serviceId);
        setSubcategoryId(appointmentId);

        const service = store.ActiveServiceCategoryListV1.find(
            (item: any) => item._id === serviceId
        ) || { appointmentType: [] };

        const appointmentType: any = service.appointmentType?.find(
            (item: any) => item._id === appointmentId
        );

        // Step 1: Auto-set duration and time slots
        const fallbackStep = appointmentType?.durationInMinutes || 30;
        const autoStart = getNextTimeSlot(fallbackStep);
        const autoEnd = dayjs(autoStart).add(fallbackStep, 'minute');

        setStartTime(autoStart);
        setEndTime(autoEnd);
        setStartTimeMinuteStep(fallbackStep);

        form.setFieldsValue({
            duration: fallbackStep,
        });
        const dayKey = dayjs(startDate).format('ddd').toLowerCase();

        setWorkingHours((prev) => ({
            ...prev,
            [dayKey]: [
                {
                    from: dayjs(autoStart).format('HH:mm'),
                    to: dayjs(autoEnd).format('HH:mm'),
                    durationInMinutes: fallbackStep,
                    classCapacity: null,
                },
            ],
        }));

        handleDuration(fallbackStep);

        // Step 2: Update duration options
        const durationOptions = Array.from({ length: 5 }, (_, i) => {
            const multiple = fallbackStep * (i + 1);
            return { label: `${multiple} Minutes`, value: multiple };
        });
        setDurationOption(durationOptions);
    };

    useEffect(() => {
        if (!isEdit) {
            form.setFieldsValue({
                startDate: dayjs(),
            });
        }
    }, []);

    useEffect(() => {
        if (dateRange !== DateRangeType.MULTIPLE || isEdit) return;

        const selectedDate = dayjs();
        if (!selectedDate) return;
        const dayKey = dayjs(selectedDate).format('ddd').toLowerCase();
        const defaultStartTime = dayjs().startOf('hour').add(30, 'minute');
        const defaultDuration = 30;
        const defaultEndTime = defaultStartTime
            .clone()
            .add(defaultDuration, 'minute');

        setWorkingHours((prev) => {
            const alreadySet = prev?.[dayKey];
            if (alreadySet && alreadySet.length > 0) return prev;

            return {
                ...prev,
                [dayKey]: [
                    {
                        from: defaultStartTime.format('HH:mm'),
                        to: defaultEndTime.format('HH:mm'),
                        durationInMinutes: defaultDuration,
                        classCapacity: null,
                    },
                ],
            };
        });

        setSelectedDay(dayKey);
    }, [dateRange, isEdit, form]);

    useEffect(() => {
        const canCallAPI =
            serviceCategoryId &&
            (selectedLocation || facilityId) &&
            dayjs(startDate).isValid() &&
            dayjs(startTime).isValid() &&
            dayjs(endTime).isValid();
        if (canCallAPI) {
            fetchAvailableStaff();
            if (dateRange === 'Single') fetchAvailableRooms();
        }
    }, [serviceCategoryId, selectedLocation, startTime, endTime, startDate]);

    useEffect(() => {
        if (scheduleId || store.ActiveServiceCategoryListV1?.length === 0)
            return;

        const today = dayjs();

        const firstService = store.ActiveServiceCategoryListV1?.[0];
        const firstAppointment = firstService?.appointmentType?.[0];

        if (!firstService || !firstAppointment) return;

        const fallbackStep = firstAppointment.durationInMinutes || 30;
        const autoStart = getNextTimeSlot(fallbackStep);
        const autoEnd = dayjs(autoStart).add(fallbackStep, 'minute');

        const serviceTypeValue = JSON.stringify({
            serviceId: firstService._id,
            appointmentId: firstAppointment._id,
            duration: fallbackStep,
        });

        const emptyWorkingHours = [
            'mon',
            'tue',
            'wed',
            'thu',
            'fri',
            'sat',
            'sun',
        ].reduce((acc, day) => {
            acc[day] = [
                {
                    from: null,
                    to: null,
                    durationInMinutes: fallbackStep,
                    classCapacity: null,
                },
            ];
            return acc;
        }, {} as WorkingHours);

        setWorkingHours(emptyWorkingHours);
        setServiceCategoryId(firstService._id);
        setSubcategoryId(firstAppointment._id);
        setStartTime(autoStart);
        setEndTime(autoEnd);
        setStartTimeMinuteStep(fallbackStep);

        // Duration dropdown options
        const durationOptions = Array.from({ length: 5 }, (_, i) => {
            const multiple = fallbackStep * (i + 1);
            return { label: `${multiple} Minutes`, value: multiple };
        });
        setDurationOption(durationOptions);

        form.setFieldsValue({
            startDate: today,
            serviceType: serviceTypeValue,
            duration: fallbackStep,
        });
        setStartDate(today);
        setEndDate(today);
    }, [store.ActiveServiceCategoryListV1, scheduleId]);

    useEffect(() => {
        if (!scheduleId && staffOption?.length) {
            form.setFieldValue('staff', staffOption[0].value);
        }
        if (!scheduleId && roomData?.length) {
            form.setFieldValue('room', roomData[0].value);
        }
    }, [staffOption, scheduleId, roomData]);

    const onFinish: FormProps['onFinish'] = async (values: any) => {
        startSubmitLoader();
        const dateRangeData: any = dateRange;
        const isMultiple = dateRangeData === 'Multiple';
        const dayKey = new Date(values.startDate)
            .toLocaleString('en-US', {
                weekday: 'short',
            })
            .toLowerCase();

        const payload: any = {
            facilityId: values.location,
            trainerId: values.staff,
            classType: ClassType.CLASSES,
            subType: subCategoryId,
            serviceCategory: serviceCategoryId,
            duration: values.duration,
            dateRange,
            notes: values.notes,
            ...(isMultiple
                ? {
                      startDate: dayjs(values.startDate)
                          .startOf('day')
                          .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                      endDate: dayjs(values.endDate)
                          .startOf('day')
                          .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                      schedule: cleanupWorkingHours(workingHours),
                      markType: repeatOption,
                  }
                : {
                      endDate: dayjs(values.startDate)
                          .startOf('day')
                          .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                      schedule: {
                          [dayKey]: [
                              {
                                  from: dayjs(startTime).format('HH:mm'),
                                  to: dayjs(endTime).format('HH:mm'),
                                  durationInMinutes: values.duration,
                                  classCapacity: parseInt(
                                      values.classCapacity,
                                      10
                                  ),
                              },
                          ],
                      },
                      markType: 'custom',
                      from: dayjs(startTime).format('HH:mm'),
                      to: dayjs(endTime).format('HH:mm'),
                      roomId: values.room,
                      startDate: dayjs(startDate)
                          .startOf('day')
                          .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                  }),
        };

        if (isEdit && scheduleId) {
            payload.scheduleId = scheduleId;
            await dispatch(updateClassScheduling(payload))
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;
                    if (status === 200 || status === 201) {
                        handleFormClose();
                    }
                })
                .finally(endSubmitLoader);
        } else {
            await dispatch(createClassScheduling(payload))
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;
                    if (status === 200 || status === 201) {
                        handleFormClose();
                    }
                })
                .finally(endSubmitLoader);
        }
        await dispatch(
            classSchedulingList({
                page: 1,
                pageSize: 10,
            })
        ).unwrap();
    };

    return (
        <ConfigProvider
            theme={{
                components: {
                    Form: {
                        verticalLabelMargin: '-10px',
                    },
                },
            }}
        >
            <Form
                layout="horizontal"
                className=" flex flex-col  "
                name="book-classes"
                size="large"
                variant="borderless"
                form={form}
                disabled={!isEdit && !!scheduleId}
                onFinish={onFinish}
                initialValues={
                    {
                        // startDate: dayjs(new Date()).format('DD/MM/YYYY'),
                    }
                }
            >
                <div className="flex flex-row items-start">
                    <div className="w-[40%] ">
                        <div className="flex w-[100%] justify-between  ">
                            <div className="  lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left  lg:w-[55px]">
                                            Location
                                        </p>
                                    }
                                    name="location"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select location',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        className="border-b-1"
                                        allowClear
                                        value={selectedLocation}
                                        disabled={isFieldDisabled('location')}
                                        onChange={(value) => {
                                            setSelectedLocation(value);
                                        }}
                                        placeholder="Select facility"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={
                                            role === RoleType.TRAINER
                                                ? LocationOptionByStaff
                                                : FacilityOptions
                                        }
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className=" flex w-[100%] justify-between ">
                            <div className="lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left @sm:w-full">
                                            Service
                                        </p>
                                    }
                                    name="serviceType"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select a service',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="border-b-1 "
                                        showSearch
                                        placeholder="Select Service"
                                        disabled={isFieldDisabled(
                                            'serviceType'
                                        )}
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        onChange={handleServiceTypeChange}
                                    >
                                        {serviceOptions.map((group: any) => (
                                            <OptGroup
                                                key={group.category}
                                                label={group.category}
                                            >
                                                {group.options.map(
                                                    (service: any) => (
                                                        <Option
                                                            key={service.value}
                                                            value={
                                                                service.value
                                                            }
                                                        >
                                                            {service.label}
                                                        </Option>
                                                    )
                                                )}
                                            </OptGroup>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </div>
                        </div>
                        <div className=" flex justify-between lg:mt-5 lg:w-[95%] lg:flex-row lg:items-center @sm:mt-7 @sm:flex-col">
                            <Form.Item
                                className="w-full"
                                label="Notes"
                                name="Notes"
                            >
                                <TextArea
                                    disabled={isFieldDisabled('notes')}
                                    autoSize={{ minRows: 1, maxRows: 2 }}
                                    placeholder="Enter a Note"
                                    style={{
                                        borderBottom: '1px solid #e5e7eb',
                                        borderRadius: '0px',
                                    }}
                                />
                            </Form.Item>
                        </div>
                    </div>
                    <div className="w-[60%] border-l-2 ps-9 ">
                        <div className="mb-8 flex w-full justify-between ">
                            <div className="flex items-center ps-2 lg:w-[100%]">
                                <p className=" text-[13px] font-medium text-[#1A3353] lg:w-[20%]">
                                    Date Range
                                </p>
                                <Radio.Group
                                    value={dateRange}
                                    disabled={isFieldDisabled('dateRange')}
                                    onChange={(e) =>
                                        handleDateRange(e.target.value)
                                    }
                                >
                                    <Radio
                                        className="text-[#455560]"
                                        value="Single"
                                    >
                                        Single
                                    </Radio>
                                    <Radio
                                        className="text-[#455560]"
                                        value="Multiple"
                                    >
                                        Multiple
                                    </Radio>
                                </Radio.Group>
                            </div>
                        </div>

                        {dateRange === DateRangeType.SINGLE ? (
                            <div>
                                <div className="flex w-[100%] flex-row justify-between ">
                                    <div className=" lg:w-[100%] @sm:w-full ">
                                        <Form.Item
                                            label="Select Date"
                                            name="startDate"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please select start date',
                                                },
                                            ]}
                                        >
                                            <DatePicker
                                                popupClassName="custom-datepicker"
                                                onChange={(
                                                    today: Dayjs | null
                                                ) =>
                                                    handleStartDateChange(today)
                                                }
                                                format="DD/MM/YYYY"
                                                className="lg:w-[80%] @sm:w-[100%]"
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                    backgroundColor:
                                                        !serviceCategoryId
                                                            ? '#f5f5f5'
                                                            : undefined,
                                                    color: !serviceCategoryId
                                                        ? '#a0a0a0'
                                                        : undefined,
                                                    cursor: !serviceCategoryId
                                                        ? 'not-allowed'
                                                        : undefined,
                                                }}
                                                disabled={
                                                    !serviceCategoryId ||
                                                    isFieldDisabled('startDate')
                                                }
                                            />
                                        </Form.Item>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <>
                                <div className="flex w-[100%] flex-col items-center">
                                    <div className=" lg:w-[100%] @sm:w-full">
                                        <Form.Item
                                            label="Start Date"
                                            name="startDate"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please enter start date',
                                                },
                                            ]}
                                        >
                                            <DatePicker
                                                popupClassName="custom-datepicker"
                                                placeholder="DD/MM/YYYY"
                                                format="DD/MM/YYYY"
                                                className=" lg:w-[95%] "
                                                onChange={(
                                                    today: Dayjs | null
                                                ) =>
                                                    handleStartDateChange(
                                                        today,
                                                        true
                                                    )
                                                }
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                    backgroundColor:
                                                        !serviceCategoryId
                                                            ? '#f5f5f5'
                                                            : undefined,
                                                    color: !serviceCategoryId
                                                        ? '#a0a0a0'
                                                        : undefined,
                                                    cursor: !serviceCategoryId
                                                        ? 'not-allowed'
                                                        : undefined,
                                                }}
                                                disabledDate={(currentDate) =>
                                                    currentDate &&
                                                    currentDate.isBefore(
                                                        dayjs().startOf('day')
                                                    )
                                                }
                                                disabled={
                                                    isFieldDisabled(
                                                        'startDate'
                                                    ) || !serviceCategoryId
                                                }
                                            />
                                        </Form.Item>
                                    </div>

                                    <div className="lg:w-[100%] @sm:w-full">
                                        <Form.Item label="Repeat" required>
                                            <Radio.Group
                                                value={repeatOption}
                                                disabled={isFieldDisabled(
                                                    'repeat'
                                                )}
                                                onChange={(e) =>
                                                    setRepeatOption(
                                                        e.target.value
                                                    )
                                                }
                                            >
                                                <Radio value="weekly">
                                                    Repeat Weekly
                                                </Radio>
                                                <Radio value="custom">
                                                    Custom
                                                </Radio>
                                            </Radio.Group>
                                        </Form.Item>
                                    </div>
                                    {repeatOption === 'custom' && (
                                        <div className="lg:w-[100%] @sm:w-full">
                                            <Form.Item
                                                label="End Date"
                                                name="endDate"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Please enter end date',
                                                    },
                                                ]}
                                            >
                                                <DatePicker
                                                    popupClassName="custom-datepicker"
                                                    placeholder="DD/MM/YYYY"
                                                    format="DD/MM/YYYY"
                                                    onChange={(
                                                        date: Dayjs | null
                                                    ) =>
                                                        handleEndDateChange(
                                                            date
                                                        )
                                                    }
                                                    className="lg:w-[95%]"
                                                    style={{
                                                        borderBottom:
                                                            '1px solid #e5e7eb',
                                                        borderRadius: '0px',
                                                        backgroundColor:
                                                            !serviceCategoryId
                                                                ? '#f5f5f5'
                                                                : undefined,
                                                        color: !serviceCategoryId
                                                            ? '#a0a0a0'
                                                            : undefined,
                                                        cursor: !serviceCategoryId
                                                            ? 'not-allowed'
                                                            : undefined,
                                                    }}
                                                    disabled={
                                                        !serviceCategoryId ||
                                                        isFieldDisabled(
                                                            'endDate'
                                                        )
                                                    }
                                                    disabledDate={(current) =>
                                                        current &&
                                                        current <
                                                            dayjs(
                                                                form.getFieldValue(
                                                                    'startDate'
                                                                )
                                                            ).startOf('day')
                                                    }
                                                />
                                            </Form.Item>
                                        </div>
                                    )}
                                </div>
                            </>
                        )}

                        {dateRange === DateRangeType.SINGLE ? (
                            <>
                                <div className=" mt-6 flex flex-row items-center bg-white">
                                    <div className="flex justify-between lg:flex-row lg:items-center @sm:flex-col @lg:w-[21%] @xl:w-[19%] @2xl:w-[19%]">
                                        <p className="text-[13px] font-medium text-[#1A3353] lg:ms-2 lg:w-[65px] @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                            Start Time
                                        </p>
                                    </div>
                                    <div className="flex flex-row items-center @2xl:w-[76%]">
                                        <div className="w-[20%]">
                                            <TimePicker
                                                format="HH:mm"
                                                value={startTime}
                                                minuteStep={5}
                                                onChange={handleStartTimeChange}
                                                disabled={
                                                    !serviceCategoryId ||
                                                    isFieldDisabled('startTime')
                                                }
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                    backgroundColor:
                                                        !serviceCategoryId
                                                            ? '#f5f5f5'
                                                            : undefined,
                                                    color: !serviceCategoryId
                                                        ? '#a0a0a0'
                                                        : undefined,
                                                    cursor: !serviceCategoryId
                                                        ? 'not-allowed'
                                                        : undefined,
                                                }}
                                            />
                                        </div>
                                        <div className="w-[38%] lg:translate-y-3 @xl:translate-x-3">
                                            <Form.Item
                                                className="PA-form-duration-input"
                                                label={
                                                    <p className="text-left @sm:w-full">
                                                        Duration
                                                    </p>
                                                }
                                                name="duration"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Please select duration',
                                                    },
                                                ]}
                                            >
                                                <Select
                                                    showSearch
                                                    className="ms-2 w-[90%] border-b-1"
                                                    placeholder="Select Duration"
                                                    options={durationOption}
                                                    onChange={handleDuration}
                                                    disabled={
                                                        !serviceCategoryId ||
                                                        isFieldDisabled(
                                                            'duration'
                                                        )
                                                    }
                                                    style={{
                                                        backgroundColor:
                                                            !serviceCategoryId
                                                                ? '#f5f5f5'
                                                                : undefined,
                                                        color: !serviceCategoryId
                                                            ? '#a0a0a0'
                                                            : undefined,
                                                        cursor: !serviceCategoryId
                                                            ? 'not-allowed'
                                                            : undefined,
                                                    }}
                                                />
                                            </Form.Item>
                                        </div>
                                        <div className="flex w-[38%] justify-end lg:flex-row lg:items-center @sm:flex-col @xl:gap-0">
                                            <p className="translate-x-3 font-medium text-[#1A3353] lg:w-[40%] @lg:text-[12px] @xl:w-[35%] @xl:text-[13px]">
                                                End Time
                                            </p>
                                            <TimePicker
                                                format="HH:mm"
                                                value={endTime}
                                                disabled
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                    backgroundColor: '#f5f5f5',
                                                    color: '#a0a0a0',
                                                    cursor: 'not-allowed',
                                                }}
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div className="flex w-[100%] justify-between pt-5 ">
                                    <div className="lg:w-[95%] @sm:w-full">
                                        <Form.Item
                                            label={
                                                <p className="text-start">
                                                    Class
                                                    <br />
                                                    Capacity
                                                </p>
                                            }
                                            name="classCapacity"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Class capacity is required',
                                                },
                                            ]}
                                        >
                                            <Input
                                                type="text"
                                                disabled={isFieldDisabled(
                                                    'classCapacity'
                                                )}
                                                className="border-b"
                                                placeholder="Enter Class Capacity"
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                }}
                                            />
                                        </Form.Item>
                                    </div>
                                </div>
                            </>
                        ) : (
                            <Form.Item label="Weekly Schedule">
                                <MultipleSchedule
                                    form={form}
                                    initialWorkingHours={workingHours}
                                    onChange={(updatedSlots: any) =>
                                        console.log(
                                            'Updated slot-----------',
                                            updatedSlots
                                        )
                                    }
                                    disabled={isFieldDisabled(
                                        'multipleSchedule'
                                    )}
                                    {...{
                                        dateRange,
                                        workingHours,
                                        setWorkingHours,
                                        daySelected: selectedDay,
                                        handleDuration,
                                        handleStartTimeChange,
                                        durationOption,
                                        serviceCategoryId,
                                        isEdit,
                                        scheduleId,
                                        startTime,
                                        endTime,
                                        showClassCapacity: true,
                                    }}
                                />
                            </Form.Item>
                        )}

                        <div className="flex w-[100%] justify-between pt-5 ">
                            <div className="lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={<p className="text-left ">Staff</p>}
                                    name="staff"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select staff',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="border-b"
                                        showSearch
                                        disabled={isFieldDisabled('staff')}
                                        placeholder="Select instructor"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={staffOption}
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        {dateRange === DateRangeType.SINGLE && (
                            <div className="mt-5 flex w-[100%] justify-between ">
                                <div className="lg:w-[95%] @sm:w-full">
                                    <Form.Item
                                        label={
                                            <p className="text-left ">Room</p>
                                        }
                                        name="room"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please select room',
                                            },
                                        ]}
                                    >
                                        <Select
                                            placeholder="Select Room"
                                            className="border-b"
                                            showSearch
                                            filterOption={(
                                                input: any,
                                                option: any
                                            ) =>
                                                (option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={roomData}
                                            disabled={isFieldDisabled('room')}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        )}

                        {(!scheduleId || (scheduleId && isEdit)) && (
                            <div className="flex flex-row gap-5 lg:justify-end @sm:justify-center">
                                <Form.Item>
                                    <div
                                        className="mt-10"
                                        style={{ display: 'flex' }}
                                    >
                                        <Button
                                            onClick={handleFormClose}
                                            className="border border-[#1A3353] px-20 py-7 text-2xl"
                                        >
                                            Cancel
                                        </Button>
                                    </div>
                                </Form.Item>
                                <Form.Item>
                                    <div
                                        className="mt-10"
                                        style={{ display: 'flex', gap: '10px' }}
                                    >
                                        <Button
                                            className="bg-purpleLight px-20 py-7 text-2xl"
                                            type="primary"
                                            htmlType="submit"
                                            loading={submitLoader}
                                        >
                                            Save
                                        </Button>
                                    </div>
                                </Form.Item>
                            </div>
                        )}
                    </div>
                </div>
            </Form>
        </ConfigProvider>
    );
};

export default ClassesForm;
