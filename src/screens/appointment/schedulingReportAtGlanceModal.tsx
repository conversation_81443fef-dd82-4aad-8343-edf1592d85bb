import { Modal, DatePicker, Form, FormProps, Button, Input, Select } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { TrainerListing } from '~/redux/actions/appointment-action';
import { capitalizeFirstLetter } from '~/components/common/function';
import { SchedulingReport } from '~/redux/actions/report.action';
import { useWatch } from 'antd/es/form/Form';
dayjs.extend(utc);
dayjs.extend(timezone);

interface SchedulingReportModalProps {
    visible: boolean;
    onClose: () => void;

}

const SchedulingReportModal: React.FC<SchedulingReportModalProps> = ({
    visible,
    onClose,

}) => {

    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const [trainerList, setTrainerList] = useState([]);
    const selectedClassTypes = useWatch('classType', form);

    const shouldShowStaff =
        selectedClassTypes?.includes('personalAppointment') ||
        selectedClassTypes?.includes('courses');
    useEffect(() => {
        dispatch(FacilitiesList({}));
    }, []);
    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        trainerList: state.appointment_store.trainerList,
    }));
    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));
    const onFinish: FormProps['onFinish'] = async (values) => {
        const payload = {
            facilityIds: [values?.facilityId],
            startDate: values?.startDate?.format('YYYY-MM-DD HH:mm:ss'),
            endDate: values?.endDate?.format('YYYY-MM-DD HH:mm:ss'),
            responseType: 'stream',
            staffIds: values?.staffIds,
            classType: values?.classType,
        }
        dispatch(SchedulingReport(payload))
            .unwrap()
            .then((res: any) => {
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    const blob = new Blob([res.data], { type: 'text/csv;charset=utf-8;' });
                    const url = window.URL.createObjectURL(blob);

                    const link = document.createElement('a');
                    link.href = url;
                    const date = new Date();
                    const istOffset = 330 * 60 * 1000; // IST is UTC+5:30
                    const istDate = new Date(date.getTime() + istOffset);
                    const istTimestamp = istDate.toISOString().replace(/T/, '_').replace(/:/g, '-').split('.')[0];

                    const filename = `scheduling_Report_${istTimestamp}`
                    link.setAttribute(
                        'download',
                        `${filename}.csv`
                    );

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            })
            .catch((error: any) => {
                console.error('Export failed:', error);
            }).finally(() => {
                onClose();
            })

    }
    const handleFacilityChange = (value: string) => {
        console.log(value, '-------------------> value')
        dispatch(TrainerListing({
            facilityId: value,
            isActive: true,
        })).then((res: any) => {

            const TrainerOptions = res?.payload?.data?.data?.map((item: any) => ({
                value: item.userId,
                label: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
                id: item._id,
            }));
            setTrainerList(TrainerOptions);
        });

    }
    const classTypes = [
        {
            label: 'Personal Appointment',
            value: 'personalAppointment',
        },
        {
            label: 'Classes',
            value: 'classes',
        },
        {
            label: 'Bookings',
            value: 'bookings',
        },
        {
            label: 'Courses',
            value: 'courses',
        },
    ];

    return (
        <Modal
            title={
                <div className="border-b pb-2 text-[#1A3353] ">
                    Schedule At a Glance
                </div>
            }
            open={visible}
            centered
            onCancel={onClose}
            footer={null}
            className="lg:w-[55%]"
            style={{ top: 10 }}
        >
            <div className="    ">
                <Form
                    layout="vertical"
                    className="w-full"
                    form={form}
                    onFinish={onFinish}
                >
                    <Form.Item
                        label="Location"
                        name="facilityId"
                        rules={[
                            {
                                required: true,
                                message: 'Please select a location.',
                            },
                        ]}
                    >
                        <Select
                            placeholder="Select facility"
                            filterOption={(input, option) =>
                                String((option && (option as { label: string }).label) ?? '')
                                    .toLowerCase()
                                    .includes(input.toLowerCase())
                            }
                            options={FacilityOptions}
                            onChange={handleFacilityChange}
                        />
                    </Form.Item>
                    <Form.Item
                        label="Class Type"
                        name="classType"
                        rules={[
                            {
                                required: true,
                                message: 'Please select a Class Type.',
                            },
                        ]}
                    >
                        <Select
                            mode="multiple"
                            showSearch
                            placeholder="Class Type"
                            filterOption={(input, option) =>
                                String((option && (option as { label: string })?.label) ?? '')
                                    .toLowerCase()
                                    .includes(input.toLowerCase())
                            }
                            options={classTypes}
                        />
                    </Form.Item>
                    {shouldShowStaff && (
                        <Form.Item
                            label="Staff"
                            name="staffIds"
                            rules={[
                                {
                                    required: false,
                                    message: 'Please select a Trainer.',
                                },
                            ]}
                        >
                            <Select
                                mode="multiple"
                                showSearch
                                placeholder="Instructor"
                                filterOption={(input, option) =>
                                    String((option && (option as { label: string })?.label) ?? '')
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                }
                                options={trainerList}
                            />
                        </Form.Item>
                    )}
                    <div className="flex space-x-4">
                        <Form.Item
                            label={<span className="font-medium">Start Date</span>}
                            name="startDate"
                            rules={[{ required: false, message: 'Please select a start date' }]}
                            className="w-full"
                        >
                            <DatePicker className="w-full" format="DD-MMM-YYYY" />
                        </Form.Item>
                        <Form.Item
                            label={<span className="font-medium">End Date</span>}
                            name="endDate"
                            rules={[{ required: false, message: 'Please select an end date' }]}
                            className="w-full"
                        >
                            <DatePicker className="w-full" format="DD-MMM-YYYY" />
                        </Form.Item>
                    </div>

                    <div className="flex justify-end space-x-4 pt-6">
                        <Button
                            onClick={onClose}
                            className="border border-gray-400 text-black px-6 py-2"
                        >
                            Cancel
                        </Button>
                        <Button
                            className="bg-[#B79DF9] text-white px-6 py-2"
                            type="primary"
                            htmlType="submit"
                        >
                            Export
                        </Button>
                    </div>
                </Form>
            </div>
        </Modal>
    );
};

export default SchedulingReportModal;
