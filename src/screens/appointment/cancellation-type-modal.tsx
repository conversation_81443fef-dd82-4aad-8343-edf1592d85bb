import React, { useState } from 'react';
import { Modal, Button, Radio } from 'antd';

interface propsType {
    visible: boolean;
    onConfirm: (type: 'Single' | 'Multiple') => void;
    onCancel: () => void;
}

const CancellationTypeModal: React.FC<propsType> = ({
    visible,
    onConfirm,
    onCancel,
}) => {
    const [dateRange, setDateRange] = useState<'Single' | 'Multiple'>('Single');
    return (
        <Modal
            title={<div className="border-b-2">Cancellation Type</div>}
            open={visible}
            className="w-[30vw]"
            onCancel={onCancel}
            footer={[
                <div className="mt-14">
                    <Button
                        className="border-1 border-[#1A3353]"
                        key="back"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        style={{ marginLeft: 10 }}
                        key="submit"
                        className="bg-purpleLight text-white"
                        onClick={() => onConfirm(dateRange)}
                    >
                        Confirm
                    </Button>
                </div>,
            ]}
        >
            <div className="mb-8 flex w-full justify-between ">
                <div className="flex items-center ps-2 lg:w-[100%]">
                    <Radio.Group
                        value={dateRange}
                        onChange={(e) =>
                            setDateRange(e.target.value)
                        }
                    >
                        <Radio
                            className="text-[#455560]"
                            value="Single"
                        >
                            Single
                        </Radio>
                        <Radio
                            className="text-[#455560]"
                            value="Multiple"
                        >
                            Multiple
                        </Radio>
                    </Radio.Group>
                </div>
            </div>
        </Modal>
    );
};

export default CancellationTypeModal;
