import { useEffect, useState } from 'react';
import { TimePicker, Button, Select, Switch, Form, Input } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
// import { capitalizeFirstLetter } from '../common/function';
import { capitalizeFirstLetter } from '~/components/common/function';

interface TimeSlot {
    from: Dayjs | null;
    to: Dayjs | null;
    payRateIds: any;
    showStartTimeError?: boolean;
    showEndTimeError?: boolean;
    showServiceCategoryError?: boolean;
    showClassCapacity?: boolean;
}

interface WorkingHours {
    [key: string]: TimeSlot[];
}

const MultipleSchedule = ({
    dateRange,
    workingHours,
    setWorkingHours,
    daySelected,
    dateSelectedTime,
    isEdit,
    serviceCategoryId,
    handleDuration,
    scheduleId,
    handleStartTimeChange,
    durationOption,
    durationonFOrm,
    showClassCapacity = false,
}: any) => {
    const [selectedDay, setSelectedDay] = useState<string>('');
    const [duplicateStatus, setDuplicateStatus] = useState<{
        [day: string]: boolean[];
    }>({});
    console.log('workingHours -----------------', workingHours);
    console.log('durationOption -----------------', durationonFOrm);

    const daysOfWeek = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

    const handleDayClick = (day: string) => {
        setSelectedDay(day);
    };

    useEffect(() => {
        if (daySelected) {
            setSelectedDay(daySelected);
        }
    }, [daySelected]);

    // console.log('Selcted Date--------', selectedDay, dateSelectedTime);

    const handleRemoveTimeSlot = (index: number) => {
        setWorkingHours((prev: any) => ({
            ...prev,
            [selectedDay]: prev[selectedDay]?.filter(
                (_: any, i: any) => i !== index
            ),
        }));
    };

    const onStartTimeChange = (time: Dayjs | null, index: number) => {
        const existingDuration =
            workingHours[selectedDay]?.[index]?.durationInMinutes;
        const duration = durationonFOrm
            ? durationonFOrm
            : existingDuration ?? 30;
        const from = time ? time.format('HH:mm') : null;
        const to = time ? time.add(duration, 'minute').format('HH:mm') : null;

        setWorkingHours((prev: any) => {
            const newSlots = [...(prev[selectedDay] || [])];
            newSlots[index] = {
                ...newSlots[index],
                from,
                to,
                durationInMinutes: duration,
            };
            return { ...prev, [selectedDay]: newSlots };
        });

        // Also call parent handler if needed
        if (handleStartTimeChange) handleStartTimeChange(time);
    };

    const onCapacityChange = (value: string, index: number) => {
        setWorkingHours((prev: any) => {
            const newSlots = [...(prev[selectedDay] || [])];
            newSlots[index] = {
                ...newSlots[index],
                classCapacity: parseInt(value, 10),
            };
            return { ...prev, [selectedDay]: newSlots };
        });
    };
    const onDurationChange = (duration: number, index: number) => {
        console.log('Duration-----', duration);
        const fromTimeStr = workingHours[selectedDay]?.[index]?.from;
        const from = fromTimeStr ? dayjs(fromTimeStr, 'HH:mm') : null;
        const to = from ? from.add(duration, 'minute').format('HH:mm') : null;

        setWorkingHours((prev: any) => {
            const newSlots = [...(prev[selectedDay] || [])];
            newSlots[index] = {
                ...newSlots[index],
                durationInMinutes: duration,
                to,
            };
            return { ...prev, [selectedDay]: newSlots };
        });

        if (handleDuration) handleDuration(duration);
    };

    const handleDuplicateForAllDays = (e: any, index: number) => {
        setWorkingHours((prev: any) => {
            const newWorkingHours: WorkingHours = { ...prev };
            const slotToDuplicate = workingHours[selectedDay][index];

            const isSlotValid = slotToDuplicate.from && slotToDuplicate.to;

            if (e.target.checked && isSlotValid) {
                daysOfWeek.forEach((day) => {
                    if (day !== selectedDay) {
                        newWorkingHours[day] = [{ ...slotToDuplicate }];
                    }
                });
            } else {
                daysOfWeek.forEach((day) => {
                    if (day !== selectedDay) {
                        newWorkingHours[day] = [];
                    }
                });
            }

            setDuplicateStatus((prevStatus) => ({
                ...prevStatus,
                [selectedDay]: {
                    ...prevStatus[selectedDay],
                    [index]: e.target.checked,
                },
            }));
            console.log(newWorkingHours)
            return newWorkingHours;
        });
    };

    useEffect(() => {
        if (selectedDay) {
            setWorkingHours((prev: any) => {
                const updatedWorkingHours = { ...prev };
                const existing = updatedWorkingHours[selectedDay];

                if (!existing || existing?.length === 0) {
                    updatedWorkingHours[selectedDay] = [
                        {
                            from: null,
                            to: null,
                            durationInMinutes: null,
                            classCapacity: null,
                        },
                    ];
                }

                return updatedWorkingHours;
            });
        }
    }, [selectedDay]);

    const parseTimeString = (
        dateStr: string | undefined,
        timeStr: Dayjs | string | null
    ): Dayjs | null => {
        if (!timeStr) return null;
        let date = dayjs(dateStr);
        const time = dayjs(timeStr, 'HH:mm');
        date = date.hour(time.hour()).minute(time.minute());
        console.log('Updated date with new time:', dayjs(date.format()));
        return dayjs(date);
    };

    // const getDisabledEndHours = (time: any) => {
    //     const startTime = dayjs(time, 'HH:mm');
    //     if (!startTime) return [];
    //     const startMinute = startTime.minute();
    //     const startHour = startTime.hour();

    //     return startHour !== null
    //         ? Array.from({ length: 24 }, (_, i) => i).filter(
    //               (hour) =>
    //                   hour <= (startMinute < 59 ? startHour - 1 : startHour)
    //           )
    //         : [];
    // };

    // const getDisabledEndMinutes = (selectedHour: any, time: any) => {
    //     const startTime = time ? dayjs(time, 'HH:mm') : null;

    //     if (!startTime) return [];
    //     const startMinute = startTime.minute();
    //     const startHour = startTime.hour();
    //     return selectedHour === startHour
    //         ? Array.from({ length: 60 }, (_, i) => i).filter(
    //               (minute) => minute <= startMinute
    //           )
    //         : [];
    // };

    return (
        <div className="w-full rounded-2xl">
            {dateRange !== 'Single' && (
                <>
                    <div className="flex flex-row items-center justify-between   pb-9 text-[13px] font-medium text-[#1A3353]">
                        <div className=" flex flex-wrap lg:w-[90%] lg:gap-3  @xl:gap-8">
                            {daysOfWeek?.map((day) => (
                                <div className="">
                                    <Button
                                        shape="circle"
                                        className={`p-2   ${selectedDay?.includes(day)
                                            ? 'bg-[#455560] text-white'
                                            : 'bg-white'
                                            }`}
                                        onClick={() => handleDayClick(day)}
                                    >
                                        {capitalizeFirstLetter(day.slice(0, 3))}
                                    </Button>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="w-[95%] ">
                        {workingHours[selectedDay]?.map(
                            (slot: any, index: number) => {
                                console.log('Slot-------------', slot);
                                return (
                                    <>
                                        <div className=" flex w-full flex-col rounded-xl">
                                            {index > 0 && (
                                                <div className="flex justify-end">
                                                    <div
                                                        onClick={() =>
                                                            handleRemoveTimeSlot(
                                                                index
                                                            )
                                                        }
                                                    >
                                                        <CloseOutlined />
                                                    </div>
                                                </div>
                                            )}
                                            <div className="flex flex-row justify-between   xl:w-[100%] @2xl:w-[100%]">
                                                <div className="flex w-[30%] items-center  ">
                                                    <p className="font-medium text-[#1A3353] lg:w-[50%] lg:text-[10px] @xl:w-[45%] @xl:text-[13px] @2xl:text-[13px]">
                                                        Start Time
                                                    </p>
                                                    <TimePicker
                                                        format="HH:mm"
                                                        value={parseTimeString(
                                                            dateSelectedTime,
                                                            slot.from
                                                        )}
                                                        minuteStep={5}
                                                        onChange={(time) =>
                                                            onStartTimeChange(
                                                                time,
                                                                index
                                                            )
                                                        }
                                                        style={{
                                                            borderBottom:
                                                                '1px solid #e5e7eb',
                                                            borderRadius: '0px',
                                                            backgroundColor:
                                                                !serviceCategoryId
                                                                    ? '#f5f5f5'
                                                                    : undefined,
                                                            color: !serviceCategoryId
                                                                ? '#a0a0a0'
                                                                : undefined,
                                                            cursor: !serviceCategoryId
                                                                ? 'not-allowed'
                                                                : undefined,
                                                        }}
                                                        disabled={
                                                            !serviceCategoryId ||
                                                            (!isEdit &&
                                                                scheduleId)
                                                        }
                                                    />
                                                </div>

                                                <div className="flex w-[30%] items-center gap-1 ">
                                                    <p className="font-medium text-[#1A3353] lg:w-[30%] lg:text-[10px] @xl:w-[30%] @xl:text-[13px] @2xl:text-[13px]">
                                                        Duration
                                                    </p>
                                                    <Select
                                                        showSearch
                                                        className="border-b-1 lg:w-[70%] @xl:w-[70%]"
                                                        placeholder="Select Duration"
                                                        value={
                                                            slot.durationInMinutes
                                                        }
                                                        filterOption={(
                                                            input,
                                                            option
                                                        ) =>
                                                            String(
                                                                option?.label ??
                                                                ''
                                                            )
                                                                .toLowerCase()
                                                                .includes(
                                                                    input.toLowerCase()
                                                                )
                                                        }
                                                        onChange={(val) =>
                                                            onDurationChange(
                                                                val,
                                                                index
                                                            )
                                                        }
                                                        options={durationOption}
                                                        disabled={
                                                            !serviceCategoryId ||
                                                            (!isEdit &&
                                                                scheduleId)
                                                        }
                                                        style={{
                                                            backgroundColor:
                                                                !serviceCategoryId
                                                                    ? '#f5f5f5'
                                                                    : undefined,
                                                            color: !serviceCategoryId
                                                                ? '#a0a0a0'
                                                                : undefined,
                                                            cursor: !serviceCategoryId
                                                                ? 'not-allowed'
                                                                : undefined,
                                                        }}
                                                    />
                                                </div>
                                                <div className="flex w-[30%] items-center ">
                                                    <p className="w-[40%] font-medium text-[#1A3353] lg:text-[10px] @xl:text-[13px] @2xl:text-[13px]">
                                                        End Time
                                                    </p>
                                                    <TimePicker
                                                        format="HH:mm"
                                                        disabled
                                                        value={parseTimeString(
                                                            dateSelectedTime,
                                                            slot.to
                                                        )}
                                                        style={{
                                                            borderBottom:
                                                                '1px solid #e5e7eb',
                                                            borderRadius: '0px',
                                                            backgroundColor:
                                                                '#f5f5f5',
                                                            color: '#a0a0a0',
                                                            cursor: 'not-allowed',
                                                        }}
                                                    />
                                                </div>
                                            </div>

                                            <div
                                                className={`mt-5 flex flex-row items-center pt-5 ${showClassCapacity
                                                    ? 'justify-between'
                                                    : 'justify-end'
                                                    }`}
                                            >
                                                {showClassCapacity && (
                                                    <div className=" flex w-[60%] justify-end  lg:flex-row   lg:items-center @sm:flex-col @xl:gap-0 ">
                                                        <p className="w-[55%] translate-x-3 font-medium text-[#1A3353] @lg:text-[12px]  @xl:text-[13px]">
                                                            Class Capacity{' '}
                                                            <span className="text-2xl text-red-400">
                                                                *
                                                            </span>
                                                        </p>
                                                        <Input
                                                            type="text"
                                                            className="border-b"
                                                            placeholder="Enter Class Capacity"
                                                            value={
                                                                slot.classCapacity ||
                                                                ''
                                                            }
                                                            style={{
                                                                borderBottom:
                                                                    '1px solid #e5e7eb',
                                                                borderRadius:
                                                                    '0px',
                                                            }}
                                                            onChange={(e) =>
                                                                onCapacityChange(
                                                                    e.target
                                                                        .value,
                                                                    index
                                                                )
                                                            }
                                                        />
                                                    </div>
                                                )}
                                                <div className="flex justify-end lg:w-[50%]">
                                                    <div className="mb-8">
                                                        <Switch
                                                            checked={
                                                                duplicateStatus[
                                                                selectedDay
                                                                ]?.[index] ||
                                                                false
                                                            }
                                                            onChange={(
                                                                checked
                                                            ) =>
                                                                handleDuplicateForAllDays(
                                                                    {
                                                                        target: {
                                                                            checked,
                                                                        },
                                                                    },
                                                                    index
                                                                )
                                                            }
                                                        />
                                                        <span className="ml-3 text-[14px] text-[#1A3353]">
                                                            Duplicate for all
                                                            days
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </>
                                );
                            }
                        )}
                    </div>
                </>
            )}
        </div>
    );
};

export default MultipleSchedule;
