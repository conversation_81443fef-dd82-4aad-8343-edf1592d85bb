import React, { useState } from 'react';
import { Mo<PERSON>, DatePicker, Button, Row, Col, Input } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { getSchedulingCancellationDetails } from '~/redux/actions/class-action';

const { RangePicker } = DatePicker;

interface CancelScheduleModalProps {
  open: boolean;
  onClose: () => void;
  scheduleData?: any;
  onConfirm: (type: 'Single' | 'Multiple', startDate: string, endDate: string) => void;
}

interface TimeSlot {
  from: string | null;
  to: string | null;
  durationInMinutes: string | null;
}

interface WorkingHours {
  [key: string]: TimeSlot;
}

const formatDate = (date: Dayjs) =>
  date.startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');

const CancelAllScheduleModal: React.FC<CancelScheduleModalProps> = ({
  open,
  onClose,
  scheduleData,
  onConfirm,
}) => {
  const dispatch = useAppDispatch();

  const [selectedRange, setSelectedRange] = useState<[Dayjs, Dayjs] | null>(null);
  const [workingHours, setWorkingHours] = useState<WorkingHours>({});

  const handleDateChange = async (dates: any) => {
    try {
      if (!dates) return;
      setSelectedRange(dates);
      const [start, end] = dates;
      const res: any = await dispatch(
        getSchedulingCancellationDetails({
          scheduleId: scheduleData?._id || scheduleData?.id,
          startDate: formatDate(start),
          endDate: formatDate(end),
        }),
      ).unwrap();
      setWorkingHours(res.data?.data?.slots || {});
    } catch (error) {
      console.error(error);
    }
  };

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      closeIcon={<CloseOutlined className="text-xl text-black" />}
      centered
      width={500}
      className="custom-modal"
    >
      <div className="p-2 sm:p-4">
        <h2 className="text-lg sm:text-xl font-semibold text-[#1A3353] mb-6">
          Are you sure you want to cancel schedules?
        </h2>

        <div className="flex items-center mb-3">
          <p className="font-medium text-[#1A3353] lg:w-[50%]">Trainer Name</p>
          <Input value={scheduleData?.trainerName || 'N/A'} disabled className="mt-1" />
        </div>

        <div className="flex items-center mb-3">
          <p className="font-medium text-[#1A3353] lg:w-[50%]">Service</p>
          <Input value={scheduleData?.subTypeName || 'N/A'} disabled className="mt-1" />
        </div>

        <div className="mb-6">
          <label className="block font-semibold text-[#1A3353] mb-2">
            Select Date Range
          </label>
          <RangePicker
            className="w-full"
            onChange={handleDateChange}
            placeholder={['Start date', 'End date']}
            popupClassName="z-[9999]"
            format="DD/MM/YYYY"
          />
        </div>

        {selectedRange && <div className="border border-gray-200 rounded-lg p-4 mt-4">
          <Row className="font-semibold mb-3">
            <Col span={6}>Weekly Schedule</Col>
            <Col span={6}>Start Time</Col>
            <Col span={6}>Duration</Col>
            <Col span={6}>End Time</Col>
          </Row>

          {weekDays.map((day) => {
            const key = day.toLowerCase();
            const slot = workingHours[key];
            return (
              <Row key={day} className="mb-2">
                <Col span={6}>{day}</Col>
                {slot?.from ? (
                  <>
                    <Col span={6}>{slot.from}</Col>
                    <Col span={6}>{slot.durationInMinutes} mins</Col>
                    <Col span={6}>{slot.to}</Col>
                  </>
                ) : (
                  <Col span={18}>Unavailable</Col>
                )}
              </Row>
            );
          })}
        </div>}

        <div className="mt-14 flex justify-end">
          <Button onClick={onClose} className="border border-[#1A3353]">
            Cancel
          </Button>
          <Button
            className="bg-purpleLight text-white ml-3"
            disabled={!selectedRange}
            onClick={() =>
              selectedRange &&
              onConfirm('Multiple', formatDate(selectedRange[0]), formatDate(selectedRange[1]))
            }
          >
            Confirm
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CancelAllScheduleModal;
