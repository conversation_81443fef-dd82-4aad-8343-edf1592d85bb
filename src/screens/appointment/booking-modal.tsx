import { Modal, Radio } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import BookAppointmentModal from './bookAppointmentModal';
import ClassesForm from './classes-form';
import CoursesForm from './courses-form';
import BookingForm from './booking-form';
import FormClientDetails from './form-client-detail';
import { useSelector } from 'react-redux';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { getCoursesSchedulingDetails } from '~/redux/actions/courses-action';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import { getClassSchedulingDetails } from '~/redux/actions/class-action';

interface BookingModalProps {
    visible: boolean;
    onClose: () => void;
    tabValue?: string;
    scheduleId?: string;
    clientId?: string;
    facilityId?: string;
    isEdit?: boolean;
    purchaseId?: string;
    packageId?: string;
    slotSelectedInfo?: any;
    courseId?: string;
    classId?: string;
    bookingData?: any;
    scanBooking?: boolean;
    scanBookingData?: any;
    isSubstitute?: boolean;
}

const BookingModal: React.FC<BookingModalProps> = ({
    visible,
    onClose,
    tabValue,
    scheduleId,
    clientId,
    facilityId,
    purchaseId,
    packageId,
    isEdit,
    slotSelectedInfo,
    classId,
    courseId,
    bookingData,
    scanBooking,
    scanBookingData,
    isSubstitute,
}) => {
    // console.log(isEdit, 'isEdit');
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const { role } = useSelector((state: any) => state.auth_store);

    const hasBookingUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_UPDATE_BOOKING
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasBookingReadPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_SCHEDULE_READ
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const [selectedId, setSelectedId] = useState<string>(
        tabValue || 'personalAppointment'
    );
    const [isCourseEnabled, setIsCourseEnabled] = useState(false);

    const dispatch = useAppDispatch();

    console.log('Scan booking----------', scanBooking, scanBookingData);

    useEffect(() => {
        if (scanBooking && scanBookingData) {
            setSelectedId(scanBookingData?.bookingType);
        }
    }, [scanBooking, scanBookingData]);

    useEffect(() => {
        if (tabValue && tabValue === 'courses' && scheduleId)
            dispatch(
                getCoursesSchedulingDetails({ schedulingId: scheduleId })
            ).unwrap();
        if (tabValue && tabValue === 'classes' && scheduleId)
            dispatch(
                getClassSchedulingDetails({ schedulingId: scheduleId })
            ).unwrap();
        setSelectedId(tabValue || 'personalAppointment');
    }, [tabValue]);

    useEffect(() => {
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_setup_courses',
            })
        ).then((response: any) => {
            setIsCourseEnabled(response?.payload?.data?.data?.isEnabled);
        });
    }, []);

    const mappedForm = [
        {
            id: 'personalAppointment',
            title: `${
                scheduleId && isEdit
                    ? 'Edit'
                    : scheduleId && !isEdit
                    ? 'View'
                    : 'Add'
            } Appointment`,
            appointment: (
                <BookAppointmentModal
                    onClose={onClose}
                    scheduleId={scheduleId}
                    clientId={clientId}
                    facilityId={facilityId}
                    isEdit={isEdit}
                    slotSelectedInfo={slotSelectedInfo}
                    bookingData={bookingData}
                    scanBooking={scanBooking}
                    scanBookingData={scanBookingData}
                />
            ),
        },
        {
            id: 'classes',
            title: `${
                scheduleId && isEdit
                    ? 'Edit'
                    : scheduleId && !isEdit
                    ? 'View'
                    : 'Add'
            } Class(es)`,
            appointment: (
                <ClassesForm
                    onClose={onClose}
                    scheduleId={scheduleId}
                    facilityId={facilityId}
                    isEdit={isEdit}
                    slotSelectedInfo={slotSelectedInfo}
                    classId={classId}
                    isSubstitute={isSubstitute}
                />
            ),
        },
        ...(isCourseEnabled
            ? [
                  {
                      id: 'courses',
                      title: `${
                          scheduleId && isEdit
                              ? 'Edit'
                              : scheduleId && !isEdit
                              ? 'View'
                              : 'Add'
                      } Schedule`,
                      appointment: (
                          <CoursesForm
                              onClose={onClose}
                              scheduleId={scheduleId}
                              facilityId={facilityId}
                              isEdit={isEdit}
                              slotSelectedInfo={slotSelectedInfo}
                              courseId={courseId}
                          />
                      ),
                  },
              ]
            : []),
        ...(hasBookingUpdatePermission ||
        hasBookingReadPermission ||
        role === RoleType.ORGANIZATION
            ? [
                  {
                      id: 'bookings',
                      title: `${
                          scheduleId && isEdit
                              ? 'Edit'
                              : scheduleId && !isEdit
                              ? 'View'
                              : 'Add'
                      } Booking`,
                      appointment: (
                          <BookingForm
                              onClose={onClose}
                              scheduleId={scheduleId}
                              clientId={clientId}
                              facilityId={facilityId}
                              isEdit={isEdit}
                              purchaseId={purchaseId}
                              packageId={packageId}
                              slotSelectedInfo={slotSelectedInfo}
                              scanBooking={scanBooking}
                              scanBookingData={scanBookingData}
                          />
                      ),
                  },
              ]
            : []),
    ];

    const handleRadioChange = (e: any) => {
        setSelectedId(e.target.value);
    };

    const selectedForm = mappedForm.find((form: any) => form.id === selectedId);
    const modalTitle = selectedForm ? selectedForm.title : 'Book';

    return (
        <div>
            <Modal
                title={
                    <div className="text-center text-4xl text-[#1A3353]">
                        {modalTitle}
                    </div>
                }
                open={visible}
                centered
                onCancel={onClose}
                footer={null}
                className={`${
                    tabValue === 'courses' ? 'lg:w-[90%]' : 'lg:w-[90%]'
                }`}
                // style={{ top: 10 }}
            >
                <div className="  ">
                    {/* -----------------radio---------------------- */}
                    {!tabValue && (
                        <div className="flex">
                            <div className="w-[40%]"></div>
                            <div className="flex flex-row items-center justify-between  border-s-2 lg:mt-10 lg:w-[60%] lg:pb-10  @sm:py-7">
                                <p className="ps-7 text-xl font-medium text-[#1A3353] @sm:w-[45%]">
                                    {/* Booking type */}
                                </p>
                                <div className="lg:pe-10">
                                    <Radio.Group
                                        value={selectedId}
                                        onChange={handleRadioChange}
                                    >
                                        <Radio value={'personalAppointment'}>
                                            Appointment
                                        </Radio>
                                        <Radio value={'bookings'}>
                                            Booking
                                        </Radio>
                                        {/* <Radio value={'classes'}>Classes</Radio> */}
                                        {/* <Radio value={'courses'}>Courses</Radio> */}
                                    </Radio.Group>
                                </div>
                            </div>
                        </div>
                    )}
                    {/* Render selected component */}
                    <div className="">
                        {
                            mappedForm?.find(
                                (form: any) => form.id === selectedId
                            )?.appointment
                        }
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default BookingModal;
