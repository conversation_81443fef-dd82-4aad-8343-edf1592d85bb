import React, { useState } from 'react';
import { Button, Form, Input, TimePicker } from 'antd';
import dayjs from 'dayjs';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { PricingListByActiveStatus } from '~/redux/actions/pricing-actions';
import { useAppDispatch } from '~/hooks/redux-hooks';

const TestScreen = () => {
    const [time, setTime] = useState(null);

    // const handleTimeChange = (time, timeString) => {
    //     setTime(time);
    //     console.log('Time selected:', timeString);
    // };

    // const handleBlur = () => {
    //     if (time) {
    //         console.log('Time saved on blur:', time.format('HH:mm'));
    //     }
    // };

    const [form] = Form.useForm();
    const dispatch = useAppDispatch();

    const fetchPricingOptions = async (page: number) => {
        const response = await dispatch(
            PricingListByActiveStatus({ page, pageSize: 10 })
        ).unwrap();
        return response?.data?.data?.list?.map((item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        }));
    };

    return (
        <>
            {/* <TimePicker
                value={time}
                onChange={handleTimeChange}
                onBlur={handleBlur}
                format="HH:mm"
                minuteStep={5}
            /> */}
          <Form form={form} layout="vertical">
                <Form.Item
                    label="Package Name"
                    name="packageName"
                    rules={[{ required: true, message: 'Please select a package!' }]}
                >
                    <InfiniteScrollSelect
                        fetchOptions={fetchPricingOptions}
                        onChange={(value) => form.setFieldsValue({ packageName: value })}
                        placeholder="Select Package"
                    />
                </Form.Item>

                <Form.Item
                    label="Pricing"
                    name="pricing"
                    rules={[{ required: true, message: 'Please input pricing!' }]}
                >
                    <Input placeholder="Enter Pricing" />
                </Form.Item>

                <Form.Item>
                    <Button type="primary" htmlType="submit">
                        Save
                    </Button>
                </Form.Item>
            </Form>
        </>
    );
};

export default TestScreen;
