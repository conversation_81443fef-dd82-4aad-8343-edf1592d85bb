export enum RoleType {
    SUPER_ADMIN = 'superAdmin',
    ORGANIZATION = 'organization',
    BRANCH_ADMIN = 'branchAdmin',
    TRAINER = 'trainer',
    FRONT_DESK_ADMIN = 'frontDeskAdmin',
    USER = 'user',
    WEBMASTER = 'webMaster',
}

export const Staff_Roles = {
    trainer: 'TRAINER',
    frontDeskAdmin: 'FRONT DESK ADMIN',
    webMaster: `Web Master`,
} as any;

export const ActivityLevel = {
    ROOKIE: 'Rookie',
    BEGINNER: 'Beginner',
    INTERMEDIATE: 'Intermediate',
    ADVANCE: 'Advance',
    TRUE_BEAST: 'True Beast',
};

export const AvailabilityType = {
    AVAILABLE: 'available',
    UNAVAILABLE: 'unavailable',
};

export const DateRangeType = {
    SINGLE: 'Single',
    MULTIPLE: 'Multiple',
    ONGOING: 'Ongoing',
};

export const ClassType = {
    PERSONAL_APPOINTMENT: 'personalAppointment',
    CLASSES: 'classes',
    BOOKING: 'bookings',
    COURSES: 'courses',
};

export enum PaymentStatus {
    PENDING = 'pending',
    COMPLETED = 'completed',
    FAILED = 'failed',
}

export enum PERMISSIONS_ENUM {
    SCHEDULING_SCHEDULE_READ = 'scheduling.read',
    SCHEDULING_SCHEDULE_BOOKING = 'scheduling.schedule.booking',
    SCHEDULING_SCHEDULE_PERSONAL_APPOINTMENT = 'scheduling.schedule.personalAppointment',
    SCHEDULING_SCHEDULE_CLASS = 'scheduling.schedule.class',
    SCHEDULING_SCHEDULE_COURSE = 'scheduling.schedule.course',

    SCHEDULING_CANCEL = 'scheduling.cancel',
    SCHEDULING_CHECKIN = 'scheduling.checkin',
    SCHEDULING_UPDATE_BOOKING = 'scheduling.update.booking',
    SCHEDULING_UPDATE_PERSONAL_APPOINTMENT = 'scheduling.update.personalAppointment',
    SCHEDULING_UPDATE_CLASS = 'scheduling.update.class',
    SCHEDULING_UPDATE_COURSE = 'scheduling.update.course',

    // Pricing
    PRICING_WRITE = 'pricing.write',
    PRICING_READ = 'pricing.read',
    PRICING_UPDATE = 'pricing.update',
    PRICING_DELETE = 'pricing.delete',

    // Clients
    CLIENTS_WRITE = 'clients.write',
    CLIENTS_READ = 'clients.read',
    CLIENTS_UPDATE = 'clients.update',
    CLIENTS_DELETE = 'clients.delete',

    // Facility
    FACILITY_WRITE = 'facility.write',
    FACILITY_READ = 'facility.read',
    FACILITY_UPDATE = 'facility.update',
    FACILITY_DELETE = 'facility.delete',

    // Staff
    STAFF_WRITE = 'staff.write',
    STAFF_READ = 'staff.read',
    STAFF_UPDATE = 'staff.update',
    STAFF_DELETE = 'staff.delete',

    // Room
    ROOM_WRITE = 'room.write',
    ROOM_READ = 'room.read',
    ROOM_UPDATE = 'room.update',
    ROOM_DELETE = 'room.delete',

    // Purchase
    PURCHASE_EXPORT = 'purchase.export',
    PURCHASE_WRITE = 'purchase.write',
    PURCHASE_READ = 'purchase.read',
    PURCHASE_UPDATE = 'purchase.update',
    PURCHASE_PACKAGE_UPDATE_START = 'purchase.package.update.start',
    PURCHASE_PACKAGE_UPDATE_SESSION = 'purchase.package.update.session',

    // Amenities
    AMENITIES_WRITE = 'amenities.write',
    AMENITIES_READ = 'amenities.read',
    AMENITIES_UPDATE = 'amenities.update',
    AMENITIES_DELETE = 'amenities.delete',

    // Attributes
    ATTRIBUTES_WRITE = 'attributes.write',
    ATTRIBUTES_READ = 'attributes.read',
    ATTRIBUTES_UPDATE = 'attributes.update',
    ATTRIBUTES_DELETE = 'attributes.delete',

    // Payment Methods
    PAYMENT_METHOD_WRITE = 'payment.method.write',
    PAYMENT_METHOD_READ = 'payment.method.read',
    PAYMENT_METHOD_UPDATE = 'payment.method.update',
    PAYMENT_METHOD_DELETE = 'payment.method.delete',

    // Authentication
    AUTH_USER_CREATE = 'auth.user.create',
    AUTH_USER_READ = 'auth.user.read',
    AUTH_USER_UPDATE = 'auth.user.update',
    AUTH_USER_DELETE = 'auth.user.delete',

    // Files
    FILE_UPLOAD = 'file.upload',
    FILE_READ = 'file.read',
    FILE_DELETE = 'file.delete',

    // Roles
    ROLE_WRITE = 'role.write',
    ROLE_READ = 'role.read',
    ROLE_UPDATE = 'role.update',
    ROLE_DELETE = 'role.delete',

    // Policies
    POLICY_WRITE = 'policy.write',
    POLICY_READ = 'policy.read',
    POLICY_UPDATE = 'policy.update',
    POLICY_DELETE = 'policy.delete',
    POLICY_ASSIGN = 'policy.assign',

    // Transactions
    TRANSACTION_WRITE = 'transaction.write',
    TRANSACTION_READ = 'transaction.read',
    TRANSACTION_UPDATE = 'transaction.update',
    TRANSACTION_RECONCILIATION = 'transaction.reconciliation',

    // Membership
    MEMBERSHIP_WRITE = 'membership.write',
    MEMBERSHIP_READ = 'membership.read',
    MEMBERSHIP_UPDATE = 'membership.update',
    MEMBERSHIP_DELETE = 'membership.delete',

    // Merchandise
    MERCHANDISE_WRITE = 'merchandise.write',
    MERCHANDISE_READ = 'merchandise.read',
    MERCHANDISE_UPDATE = 'merchandise.update',
    MERCHANDISE_DELETE = 'merchandise.delete',

    // Announcements
    ANNOUNCEMENT_WRITE = 'announcement.write',
    ANNOUNCEMENT_READ = 'announcement.read',
    ANNOUNCEMENT_UPDATE = 'announcement.update',
    ANNOUNCEMENT_DELETE = 'announcement.delete',

    // Pay Rate
    PAY_RATE_WRITE = 'pay.rate.write',
    PAY_RATE_READ = 'pay.rate.read',
    PAY_RATE_UPDATE = 'pay.rate.update',
    PAY_RATE_DELETE = 'pay.rate.delete',

    // FOR Orders
    PURCHASE_INVOICE_READ = 'purchase.invoice.read',
    PURCHASE_INVOICE_WRITE = 'purchase.invoice.write',
    PURCHASE_INVOICE_PAYMENT = 'purchase.invoice.payment',

    // For Services
    ORGANIZATION_SERVICE_WRITE = 'organization.service.write',
    ORGANIZATION_SERVICE_READ = 'organization.service.read',
    ORGANIZATION_SERVICE_UPDATE = 'organization.service.update',
    ORGANIZATION_SERVICE_DELETE = 'organization.service.delete',

    // For availability
    SCHEDULING_AVAILABILITY_WRITE = 'staff.availability.write',
    SCHEDULING_AVAILABILITY_UPDATE = 'staff.availability.update',
    SCHEDULING_AVAILABILITY_READ = 'staff.availability.read',

    // For discounts
    PROMOTION_WRITE = 'promotion.write',
    PROMOTION_READ = 'promotion.read',
    PROMOTION_UPDATE = 'promotion.update',
    PROMOTION_DELETE = 'promotion.delete',
    PROMOTION_APPLY = 'promotion.apply',

    // For coupons
    COUPON_WRITE = 'voucher.write',
    COUPON_READ = 'voucher.read',
    COUPON_UPDATE = 'voucher.update',
    COUPON_DELETE = 'voucher.delete',
}

export enum SUBJECT_TYPE {
    AUTHENTICATION_CLIENT_ONBOARDING = 'authentication-customer-onboarding',
    POS = 'pos-pos',
    POS_DISCOUNT = 'pos-apply-discount',
    POS_Z_OUT = 'pos-z-out',
    SCHEDULING_AVAILABILITY = 'scheduling-availability',
    SCHEDULING_BOOKING = 'scheduling-booking',
    SCHEDULING_CLASS = 'scheduling-class',
    SCHEDULING_COURSE = 'scheduling-course',
    SCHEDULING_PERSONAL_APPOINTMENT = 'scheduling-personal-appointment',

    CLASS_SETUP_SERVICE_CATEGORY_SUBTYPE = 'class-setup-service-categorysubtype',
    AUTHENTICATION_STAFF_ONBOARDING = 'authentication-staff-onboarding',
    AUTHENTICATION_PERMISSIONS = 'authentication-permissions',
    AUTHENTICATION_BRANCH_ONBOARDING = 'authentication-branch-onboarding',
    CLASS_SETUP_ROOMS = 'class-setup-rooms',
    PRICING_PRICING = 'services-pricing',
    ORDER = 'orders-order',
    ORDERS_ORDER_EXPORT = 'orders-order-export',
    REPORTS_REPORT_EXPORT = 'reports-report-export',
    SERVICES_SERVICE_SETUP = 'services-service-setup',
    SERVICES_DISCOUNTS = 'services-discount-management',
    PURCHASE_PURCHASE = 'purchase-purchase',
}
