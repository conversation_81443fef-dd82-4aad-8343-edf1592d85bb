// AUTH
export const LOGIN_ADMIN = '/auth/login';
export const SET_NEW_PASSWORD = '/auth/set-password';
export const FORGET_PASSWORD_REQUEST_OTP = '/auth/forget-password-request-otp';
export const VERIFY_OTP = '/auth/verify-otp';
export const FORGET_RESET_PASSWORD = '/auth/reset-password';
export const CHANGE_PASSWORD = '/auth/change-password';
export const VALIDATE_PIN = '/admin/staff/pinLogin';
export const VALIDATE_MODULE_PIN = '/auth/refresh-session';
export const LOGOUT_USER = '/auth/logout';
export const SELECT_ORGANIZATION_LIST = '/mobile-auth/organization/list';

// COMMON

export const COUNTRY_LIST = '/general/states';
export const CITY_LIST = '/general/get/cityDetails';
export const UPLOAD_SINGLE_IMAGE = '/general/upload-image';
export const CITY_LIST_BY_ID = '/general';

// Admin Customer List

export const ADMIN_CUSTOMER_LIST = '/admin/user/get-all-users-list';
export const CREATE_CLIENTS = '/clients/register';
export const DELETE_CLIENT = '/clients/delete';
export const CLIENTS_DETAILS = '/clients';
export const UPDATE_CLIENTS = '/clients/update';
export const CLIENTS_LIST = '/clients/list';
export const UPDATE_BASICASSESSMENT = '/clients/updateAssessment';
export const UPDATE_POLICIES = '/clients/updatePolicy';
export const UPDATE_CLIENT_STATUS = '/clients/updateStatus';
export const CLIENTS_LIST_v1 = '/clients/list/v1';
export const CLIENTS_UPLOAD_DOCUMENT = '/clients/upload-file';
export const CLIENTS_DOCUMENTS_LISTING = '/clients/list-documents';
export const CLIENT_CREATE_DOCUMENT = '/clients/create-document';
export const CLIENT_DELETE_DOCUMENT = '/clients/delete-document';

// Admin Gym List

export const ADMIN_GYM_LIST = '/admin/gym/get-all-gym-list';
export const CREATE_GYM_ORBOARDING = '/admin/gym/register';
export const GYM_DETAILS = '/admin/gym/profile';
export const UPDATE_GYM_ORBOARDING = '/admin/gym/update';

//  Admin Attribute

export const ATTRIBUTE_LIST = '/admin/attribute/list';
export const ATTRIBUTE_TYPE_LIST = '/admin/attribute/filter';
export const CREATE_ATTRIBUTE = '/admin/attribute/create';
export const UPDATE_ATTRIBUTE = '/admin/attribute/update';
export const DELETE_ATTRIBUTE = '/admin/attribute/delete';
export const UPDATE_ATTRIBUTE_STATUS = '/admin/attribute/updateStatus';
export const ATTRIBUTE_DETAILS = '/admin/attribute/details';
export const PARENT_ATTRIBUTE_LIST = '/admin/attribute/parentAttributes';

// Admin Amenities

export const AMENITIES_LIST = '/admin/amenities/list';
export const CREATE_AMENITIES = '/admin/amenities/create';
export const AMENITIES_DETAILS = '/admin/amenities/details';
export const UPDATE_AMENITIES = '/admin/amenities/update';
export const AMENITIES_GROUP_LIST = '/admin/amenities/groupList';
export const DELETE_AMENITY = '/admin/amenities/delete';

//  Admin Organization

export const ORGANIZATION_LIST = '/organization/list';
export const CREATE_ORGANIZATION = '/organization/register';
export const ORGANIZATION_DETAILS = '/organization/details';
export const UPDATE_ORGANIZATION = '/organization/update';

// staffs

export const STAFF_LIST = '/staff/list';
export const REGISTER_STAFF = '/admin/staff/register';
export const COMPLETE_STAFF_PROFILE = '/staff/complete-profile';
export const STAFF_PIN_SET = '/staff/pin/set';
export const UPDATE_STAFF_PROFILE = '/admin/staff/update/Profile';
export const UPDATE_STAFF_STATUS = '/admin/staff/updateStatus';
export const GET_STAFF_LISTS = '/admin/staff/list';
export const GET_STAFF_DETAILS = '/admin/staff/details';
export const CREATE_STAFF_ADD_AVAILABILITY = '/admin/staff/add/availability';
export const STAFF_AVAILABILITY_LIST = '/admin/staff/availability/list';
export const STAFF_TRAINER_LIST = '/admin/staff/trainers/list';
export const STAFF_TRAINER_FOR_PAY_RATE_LIST =
    '/admin/staff/trainersForPayRate/list';
export const STAFF_AVAILABILITY_DETAILS = '/admin/staff/getDetails';
export const STAFF_AVAILABILITY_UPDATE_DETAILS =
    '/admin/staff/update/availability';
export const STAFF_AVAILABILITY_DELETE = '/admin/staff/delete/availability';
export const GET_STAFF_PERMISSIONS = '/admin/staff/permissions';
export const SERVICE_CATEGORY_LIST_STAFF_ID =
    '/admin/staff/avail-service-category';
export const SEND_MAIL = '/organization/sendMail';
export const UPDATE_ORGANIZATION_STATUS = '/organization/status';
export const EMAIL_OR_MOBILE_VALIDATION = '/admin/staff/checkMailorMobile/';
export const STAFF_TRAINER_LIST_V1 = '/admin/staff/trainers/list/v1';
export const SERVICE_BY_PRICING_V1 = '/pricing/services-by-pricingId';
export const STAFF_PERMISSIONS = '/policy/';
// Facility

export const FACILITY_LIST = '/facility/list';
export const CREATE_FACILITY = '/facility/register';
export const UPDATE_FACILITY = '/facility/update';
export const FACILITY_DETAILS = '/facility/details';
export const FACILITY_LIST_BY_STAFFID = '/facility/listByStaffID';
export const EDIT_FACILITY_AVAILABILITY = '/facility/updateAvailability';
export const ADD_FACILITY_UNAVAILABILITY = '/facility/addUnavailability';
export const UPDATE_FACILITY_UNAVAILABILITY = '/facility/updateUnavailability';
export const UPDATE_ALL_FACILITY_UNAVAILABILITY =
    '/facility/bulkUpdate/unavailability';
export const DELETE_FACILITY_UNAVAILABILITY = '/facility/deleteUnavailability';
export const FACILITY_AVAILABILITY_DETAILS = '/facility/availabilityDetails';
export const UPDATE_FACILITY_STATUS = '/facility/status';
export const UPDATE_FACILITY_STORE_STATUS = '/facility/store-status';

// SaveSettings

export const SAVE_SETTINGS = '/organization/updateSettings';

export const GET_SETTINGS = '/organization/settings';

export const GENERATE_PIN = '/admin/staff/reset/pin';

//Appointment Types
export const CREATE_SERVICE_CATEGORY = '/organization/add/services';
export const SERVICE_CATEGORY_LIST = '/organization/services/list';
export const ACTIVE_SERVICE_CATEGORY_LIST =
    '/organization/active-services/list';
export const SERVICE_CATEGORY_LIST_PACKAGE_ID = '/pricing/get-services';
export const CRETAE_APPOINTMENT_TYPE =
    '/organization/add/services/appointmentType';
export const DELETE_APPOINTMENT_TYPE =
    '/organization/delete/services/appointmentType';
export const SERVICE_CATEGORY_DETAILS = '/organization/services';
export const UPDATE_SERVICE_CATEGORY_DETAILS = '/organization/edit/services';
export const APPOINTMENT_TYPE_DETAILS = '/organization/services';
export const UPDATE_APPOINTMENT_TYPE =
    '/organization/edit/services/appointmentType';
export const ACTIVE_SERVICE_CATEGORY = '/organization/v1/active-services/list';

export const UPDATE_APPOINTMENT_TYPE_STATUS =
    '/organization/updateStatus/services/appointmentType';
export const ACTIVE_SERVICE_CATEGORY_V1 =
    '/organization/list/groupSubTypeByService';

export const ACTIVE_SERVICE_CATEGORY_FOR_PRICING =
    '/organization/list/servicesFromPackage';

// pricing
export const PRICE_LIST = '/pricing/list';
export const COPY_PRICING = '/pricing/copy';
export const CREATE_PRICING = '/pricing'; //will also be used for update pricing API, Create is POST and Update is PATCH
export const PRICING_DETAILS = '/pricing';
export const PRICE_LIST_BY_USERID = '/pricing/get';
export const PRICE_LIST_BY_USER_AND_TYPE = '/pricing/pricingByUserAndType';
export const PRICE_LIST_FOR_SHARE_PASS = '/pricing/pricingForSharePass';
export const PRICE_LIST_BY_SERVICE_CATEGORY =
    '/pricing/list-by-service-category';
export const PRICING_LIST_BY_SUBTYPE = '/pricing/list-by-subType';
export const ASSIGN_PRICING_TO_SUBTYE = '/organization/services/assign-pricing';
export const REMOVE_PRICING_TO_SUBTYE =
    '/organization/services/remove-assigned-pricing';
export const PRICING_LIST_BY_ACTIVE_STATUS = '/pricing/active-list';
export const UPDATE_PRICING_STATUS = '/pricing/status';
export const BUNDLE_PRICING_LIST = '/pricing/list/bundled-pricing';
export const CREATE_BUNDLE_PRICING = '/pricing/bundled-pricing';
export const PRICING_LIST_BY_USERANDSUBTYPE =
    '/pricing/pricingByUserAndSubType';

// export const EDIT_PRICING

// Book Appointment
export const CREATE_BOOK_APPOINTMENT = '/appointment/create';
export const BOOKED_APPOINTMENT_LIST = '/appointment/getAll';
export const BOOKED_APPOINTMENT_DETAILS = '/appointment/get';
export const UPDATE_BOOKED_APPOINTMENT = '/appointment/update';
export const DELETE_BOOKED_APPOINTMENT = '/appointment/delete';

// Scheduling
export const CREATE_SCHEDULING = '/scheduling';
export const CREATE_BOOKING_SCHEDULING = '/scheduling/booking';
export const UPDATE_BOOKING_SCHEDULING = '/scheduling/booking/update';
export const CREATE_APPOINTMENT_SCHEDULING = '/scheduling/personal-appointment';
export const UPDATE_APPOINTMENT_SCHEDULING =
    '/scheduling/personal-appointment/update';

export const SCHEDULING_LIST = '/scheduling/get/list';
export const SCHEDULING_LIST_V1 = '/scheduling/get/list/v1';
export const SCHEDULING_LIST_EXPORT = '/scheduling/export/list';
export const EXPORT_CHECK_IN_HISTORY = '/scheduling/export/user';
export const SCHEDULING_DETAILS = '/scheduling/get';
export const SCHEDULING_DETAILS_RECURRING = '/scheduling/get-recurring';
export const UPDATE_SCHEDULING = '/scheduling/edit';
export const CANCEL_SCHEDULING = '/scheduling/cancel';
export const CHECK_IN_SCHEDULING = '/scheduling/checkIn';
export const DELETE_SCHEDULING = '/scheduling/delete';
export const SCHEDULING_STAFF_AVAILABILITY_LIST =
    '/scheduling/staff-availability';
export const CANCEL_SCHEDULEING_APPOINTMENT = '/scheduling/cancel';
export const CLIENT_SCHEDULING_LIST = '/scheduling/get/list/user';

// Pay Rate
export const PAY_RATE_LIST = '/payRate/list';
export const PAY_RATE = '/payRate';
export const CREATE_PAY_RATE = '/payRate/create';
export const ALL_CLASS_TYPES = '/payRate/allClassType';
export const ALL_SERVICE_CATEGORIES = '/payRate/allServiceCategory';

// Class
export const CLASS_SCHEDULING = 'scheduling/classes';
export const CLASS_SCHEDULING_LISTING = 'scheduling/classes/list';
export const CREATE_CLASS_SCHEDULE = 'scheduling/classes/create';
export const UPDATE_CLASS_SCHEDULE = 'scheduling/classes/v2/update';
export const CLASS_ENROLL_CUSTOMER = 'scheduling/classes/enroll';
export const CANCEL_ENROLL_CUSTOMER = 'scheduling/classes/enroll/cancel';
export const CLASSES_CUSTOMER_LIST = 'scheduling/classes/customer/list';
export const CLASS_PARTICIPANT_LIST = 'scheduling/classes/participant/list';
export const CLASS_CHECK_IN = 'scheduling/classes/checkin';
export const CLASS_SCHEDULING_DETAILS = '/scheduling/classes/get-recurring';
export const CLASS_SCHEDULING_CANCELLATION_DETAILS =
    '/scheduling/classes/getCancellationDetails';

//room
export const CREATE_ROOM = '/rooms/create';
export const ROOM_LIST = '/rooms/list';
export const ROOM_DETAIL = '/rooms';
export const UPDATE_ROOM = '/rooms/update';
export const UPDATE_ROOM_STATUS = '/rooms/status';

// Feature Api
export const CREATE_FEATURE = '/feature/create';
export const FEATRUE_LISTING = '/feature/list';
export const FEATURE_DETAIL = '/feature';
export const UPDATE_FEATURE = '/feature/update';
export const UPDATE_FEATURE_STATUS = '/feature/status';
export const ROOM_LIST_BY_SERVICE_CATEGORY =
    '/rooms/room-list/serviceCategoryId';
export const ROOM_LIST_BY_FACILITY_ID = '/rooms/room-list/byFacilitiesId';
export const ROOM_LIST_BY_SCHEDULING = '/rooms/list/scheduling';

// Announcement
export const ANNOUNCEMENT = '/announcements';

// Membership
export const CREATE_MEMBERSHIP = '/membership/create';
export const MEMBERSHIP_LISTING = '/membership/list';
export const UPDATE_MEMBERSHIP_STATUS = '/membership/status';
export const MEMBERSHIP_DETAIL = '/membership';
export const UPDATE_MEMBERSHIP = '/membership/update';
// Revenue Category
export const REVENUE_CATEGORY = '/organization/revenue-category';
// Purchase
// export const PURCHASE_PRICING_PACKAGES = '/purchase';
export const PURCHASE_PRICING_PACKAGES = '/purchase/v2';
export const PURCHASE_PRICING_LIST_BY_CLASS_TYPE = '/purchase/list';
export const MEMBERSHIP_LIST = '/purchase/all-listType';
export const ACTIVE_PURCHASE_PRICING_LIST_BY_USERID =
    '/purchase/active-pricing';
export const INACTIVE_PURCHASE_PRICING_LIST_BY_USERID =
    '/purchase/in-active-pricing';
export const UPDATE_PURCHASE_DATA = '/purchase/update-session';

// Orders

export const ORDER_LIST = '/purchase/invoice/list';
export const ORDER_DETAILS_BY_ID = '/purchase/invoice';
export const DOWNLOAD_INVOICE_DETAILS = '/purchase/invoice';
export const UPDATE_ORDER_PAYMNET_STATUS = '/purchase/invoice/update-payment';
export const ORDER_LIST_EXPORT = '/purchase/invoice/export';
export const INVOICE_CANCEL = '/purchase/invoice/cancel';
export const CART_VALIDATE = '/cart/validate';

// Membership suspension
export const MEMBERSHIP_SUSPENSION = '/purchase/membership/suspend';
export const MEMBERSHIP_SUSPENSION_REVOKE =
    '/purchase/membership/suspend/resume';

// Reconciliation

export const GET_RECONCILIATION = '/transactions/reconciliation/get';
export const CREATE_RECONCILIATION = '/transactions/reconciliation/create';

// Merchnadise category api
export const CATEGORY_LIST = '/category';
export const CREATE_CATEGORY = '/category/create';
export const CATEGORY_DELETE = '/category';
export const GET_CATEGORY = '/category';
export const SUB_CATEGORY_LIST = '/category/subCategorylist';

// Merchnadise Attribute Api

export const ATTRIBUTES_LIST = '/attribute';
export const DYNAMIC_ATTRIBUTE_LIST = '/product-attribute';
export const ACTIVE_DYNAMIC_ATTRIBUTE_LIST =
    '/product-attribute/active-attribute/list';
export const ADD_NEW_DYNAMIC_ATTRIBUTES = '/product-attribute/create';
export const GET_DYNAMIC_ATTRIBUTE_DETAILS = '/product-attribute';
export const DELETE_DYNAMIC_ATTRIBUTE = '/product-attribute';
export const UPDATE_DYNAMIC_ATTRIBUTE = '/product-attribute';
export const UPDATE_DYNAMIC_ATTRIBUTE_STATUS = '/product-attribute/status';
export const ADD_NEW_SUB_ATTRIBUTES = '/attribute/create';
export const UPDATE_SUB_ATTRIBUTE = '/attribute';
export const GET_BRANDS_DATA = '/attribute/brand/brand-list';
export const DELETE_SUB_ATTRIBUTE = '/attribute';

// Merchnadise Product APi
export const CREATE_PRODUCT = '/product/create';
export const PRODUCT_LIST = '/product/list';
export const GET_PRODUCT_DETAILS = '/product';
export const UPDATE_PRODUCT = '/product';
export const UPDATE_PRODUCT_STATUS = '/product/status';
export const DELETE_PRODUCT = '/product';
export const EXPORT_PRODUCT_LIST = '/product/export';
export const BULK_UPLOAD_PRODUCT = '/product/bulkUpload';
export const BULK_UPDATE_PRODUCT = '/product/bulkUpdate';

//Inventory API
export const SEARCH_BY_SKU_DETAILS = '/inventory/product/sku_search';
export const CREATE_NEW_INVENTORY = '/inventory/create';
export const INVENTORY_LIST = '/inventory';
export const UPDATE_INVENTORY = '/inventory';
export const STORE_INVENTORY_LIST = '/inventory/store-inventory';

// Share Pass

export const SHARE_PASS_TO_OTHER = '/clients/sharePass';
export const SHARE_PASS_List = '/clients/sharePassList';
export const MUTLIPLE_SHARE_PASS = '/clients/share-pass-multiple';
export const INSTANT_CHECK_IN_ORDER_PAGE = 'scheduling/instant-booked&checkin';
export const INSTANT_SINGLE_CHECK_IN = '/scheduling/instant-single-checkin';

export const ALL_PAYMENT_LIST = '/payment-method/list';
export const ADD_PAYMENT_METHOD = '/payment-method/add';
export const ADDED_PAYMENT_METHOD_LIST =
    '/payment-method/addedPaymentMethodList';
export const SUPER_ADMIN_PAYMENT_METHOD_LIST = '/payment-method/list';
export const UPDATE_PAYMENT_METHOD_STATUS = '/payment-method/updateStatus';
export const CREATE_PAYMENT_METHOD = '/payment-method/create';
export const EDIT_ADMIN_PAYMENT_METHOD = '/payment-method/update';
export const GET_OR_DELETE_ADMIN_PAYMENT_METHOD = '/payment-method';
export const UPLOAD_BULK_ORDER = '/orders/bulk/v1';

// for role and permissions on Super admin
export const PERMISSIONS_LIST = '/settings-options/organization-settings';
export const UPDATE_SUPER_ADMIN_PERMISSIONS =
    '/organization-settings/grant-settings/';
export const GET_SETTING_ACTIVE_STATUS = '/organization-settings/';
export const FOR_ROLES_PERMISSIONS_ON_SUPER_ADMIN = '/admin/role';
export const GET_ALL_POLICIES_LIST = '/admin/policy/list';
export const GET_SUBJECT_ACTIONS = '/policy';
export const UPDATE_PERMISSIONS_ON_ACTION_ID = '/admin/policy';

// Courses
export const COURSE_LISTING = '/course/list';
export const COURSES_STATUS_UPDATE = '/course/update-status';
export const COURSES_CUSTOMER_LIST = '/course/customer-list';
export const COURSE_SCHEDULING_LIST = '/course/scheduling-list';
export const COURSE_SCHEDULING_CUSTOMER_LIST =
    '/course/scheduling-customer-list';
export const COURSE_DELETE_SCHEDULING = '/course/delete-schedule';
export const COURSE_CANCEL_SCHEDULING = '/course/cancel-schedule';
export const COURSES_ENROLL_CUSTOMER = '/course/enroll-schedule';
export const COURSE_DELETE_ENROLLMENT = '/course/delete-enrollment';
export const COURSE_CHEKIN = '/course/check-in';
export const CREATE_COURSE_SCHEDULE = '/course/create-schedule';
export const COURSE_SCHEDULING_DETAILS = '/course/scheduling-details';
export const UPDATE_COURSES_SCHEDULE = '/course/update-schedule';
export const COURSE_DETAILS = '/course/details';
export const COURSE_SCHEDULING_DETAILS_RECURRING = '/course/get-recurring';
export const CREATE_COURSE_RECURRING = '/course/create-schedule/v1';
export const UPDATE_COURSE_RECURRING = '/update-recurringSchedule';

// Report Api
export const SCHEDULING_REPORT = 'purchase/scheduleAtGlance/export';
export const SALES_REPORT = 'purchase/invoice/export';
export const SALES_BY_EMPLOY_REPORT = '/purchase/salesByEmp/export';
export const SALES_BY_CATEGORY_REPORT = '/purchase/salesByCategory/export';
export const SALES_BY_REVENUE_CATEGORY_REPORT =
    '/purchase/salesByRevenueCategory/export';
export const SALES_BY_ITEM_REPORT = '/purchase/salesReport/export';
export const Z_OUT_REPORT = 'purchase/Z-out/export';
export const Z_OUT_HISTORY = '/transactions/reconciliation/history';
export const Z_OUT_HISTORY_DOWNLOAD = '/purchase/Z-outHistory/export';

// get all roles list
export const GET_ALL_ROLES_LIST = '/role/list';

// for role and permissions on Super admin

// CREATE_CUSTOM_PACKAGE

export const CREATE_CUSTOM_PACKAGE = '/custom-package/create';
export const CUSTOM_PACKAGE_LIST = '/custom-package/list';
export const CUSTOM_PACKAGE_DETAILS = '/custom-package/details';
export const CUSTOM_PACKAGE_UPDATE = '/custom-package/update';

// Create Discount
export const PRICING_LIST = '/promotions/pricing-list';
export const DISCOUNT_LIST = '/promotions/list';
export const CREATE_DISCOUNT = '/promotions/create';
export const UPDATE_DISCOUNT = '/promotions';

// Lead Api
export const CLIENT_LEAD_LIST = 'client-lead/fetch-list';
export const CLIENT_LEAD_DETAILS = 'client-lead/fetch-client-detail';
export const ADD_CLIENT_MINOR = '/clients/minor/register';
export const CLIENT_MINOR_LIST = 'client-lead/fetch-client-minor';

// widget api
export const GET_TRAINER_AVAILABILITY = '/widget/get-trainer-availablity';
export const GET_COURSE_LIST = '/widget/get-course-list';
export const GET_COURSE_DETAILS = '/widget/get-course-details';
export const REGISTER_CLIENT = 'widget-auth/register';
export const LOGIN_CLIENT = 'widget-auth/login';
export const CREATE_PAYMENT_ORDER = '/widget-payment/create-payment-order';
export const VERIFY_PAYMENT = '/widget-payment/verify-payment';
export const GET_TRAINER_SLOT = '/widget/get-trainer-time-slots';
export const BOOK_PA_SCHEDULE = '/widget/schedule-personal-appontment';
export const WIDGET_GET_ALL_SERVICE_TYPE = '/widget/get-service-type';
export const WIDGET_PRICING = '/widget/list-by-service-category';
export const FAMILY_SHARE_CLIENT_LIST = '/purchase/share-package/client-list';
export const FAMILY_SHARE_PACKAGE = '/purchase/share-package';

//Return pos api
export const POS_RETURN_ELIGIBLE_LIST = '/purchase/eligible-return-pricing';

// Store Wise Discount

export const INVATORY_BRAND_LIST = '/attribute/brand/brand-list';
export const PRODUCT_LITS_BY_BRAND = '/product/brand-product-list';
export const ALL_PRODUCT_LIST_WITH_DISCOUNT =
    '/promotions/product-list?promotionId';
export const ADD_INVENTORY_DISCOUNT_LIST =
    '/promotions/product-promotions/all?productId';

export const INVENTORY_EXPORT = '/inventory/export';
export const BULK_UPLOAD_INVENTORY = '/inventory/bulkUpload';
export const INVENTORY_EXPORT_PRODUCTS_TEMPLATE =
    '/inventory/export-products-template';

export const VOUCHER = '/voucher';
export const CREATE_COUPON = '/voucher/create';
export const UPDATE_COUPON = '/voucher/update';
export const COUPON_LIST = '/voucher/list';
export const INVENTORY_HISTORY = '/inventory';

// QR Code
export const QR_CODE_PURCHASE_DETAILS = '/purchase/fetch-data-from-qrcode';
export const WIDGET_FACILITY_LIST = '/facility/v2/listByOrg';
export const WIDGET_TRAINER_AVAILABILITY =
    'admin/staff/app/availability/list/by-type';
export const WIDGET_BOOKING_ROOM_AVAILABILITY =
    '/widget/get-rooms-availability-bookings';
