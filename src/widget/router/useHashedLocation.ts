import { useEffect, useRef, useState } from 'react';

type Opts = { replace?: boolean };

function parsePathFromHash() {
  if (typeof window === 'undefined') return '/services';
  const raw = window.location.hash.slice(1) || '/services'; // e.g. "/schedule?x=1"
  return (raw.split('?')[0] || '/services');                // -> "/schedule"
}

export default function useHashedLocation(): [string, (to: string, o?: Opts) => void] {
  const [loc, setLoc] = useState(parsePathFromHash());
  const prev = useRef(loc);

  useEffect(() => {
    const onHash = () => {
      const next = parsePathFromHash();
      if (next !== prev.current) {
        prev.current = next;
        setLoc(next);
      }
    };
    window.addEventListener('hashchange', onHash);
    onHash();
    return () => window.removeEventListener('hashchange', onHash);
  }, []);

  const nav = (to: string, o?: Opts) => {
    const url = `${window.location.pathname}${window.location.search}#${to}`;
    if (o?.replace) {
      history.replaceState(null, '', url);
      const pathOnly = (to.split('?')[0] || to);
      prev.current = pathOnly;
      setLoc(pathOnly); // give Wouter just "/schedule"
    } else {
      window.location.hash = to; // triggers hashchange -> parsePathFromHash() -> "/schedule"
    }
  };

  return [loc, nav];
}
