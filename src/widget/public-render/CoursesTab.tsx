import React, { useEffect, useState } from 'react';
import { useLoader } from '~/hooks/useLoader';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { getCourseDetails, getCourseList } from '~/redux/actions/widget/widget.action';

const CoursesTab = ({ branding }: { branding: any }) => {
    const dispatch = useAppDispatch();
    const [courses, setCourses] = useState<any[]>([]);
    const [selectedCourseId, setSelectedCourseId] = useState<string | null>(null);
    const [courseDetail, setCourseDetail] = useState<any | null>(null);
    const [loadingCourseLoader, startLoadingCoursesLoader, endLoadingCoursesLoader] = useLoader();
    const [courseDetailLoader, startCourseDetailLoader, endCourseDetailLoader] = useLoader();

    useEffect(() => {
        startLoadingCoursesLoader();
        dispatch(getCourseList()).then((res: any) => {
            if (res?.payload?.data?.length) {
                const data = res.payload.data;
                setCourses(data);
                setSelectedCourseId(data[0]._id); // set first course
            }
            endLoadingCoursesLoader();
        });
    }, []);

    useEffect(() => {
        if (!selectedCourseId) return;
        startCourseDetailLoader();
        dispatch(getCourseDetails({ id: selectedCourseId })).then((res: any) => {
            setCourseDetail(res?.payload?.data);
            endCourseDetailLoader();
        });
    }, [selectedCourseId]);

    return (
        <div className="flex w-full min-h-[600px]">
            {/* Left Sidebar */}
            <div className="w-[250px] border-r bg-gray-50">
                <h3 className="text-lg font-semibold p-4">Courses</h3>
                {loadingCourseLoader ? (
                    <p className="px-4 text-sm text-gray-500">Loading...</p>
                ) : (
                    <ul>
                        {courses.map((course: any) => (
                            <li
                                key={course._id}
                                onClick={() => setSelectedCourseId(course._id)}
                                className={`px-4 py-3 cursor-pointer hover:bg-gray-100 ${
                                    selectedCourseId === course._id ? 'bg-gray-200 font-bold' : ''
                                }`}
                            >
                                {course.name}
                            </li>
                        ))}
                    </ul>
                )}
            </div>

            {/* Right Detail View */}
            <div className="flex-1 p-6">
                {courseDetailLoader || !courseDetail ? (
                    <p className="text-gray-500">Loading course details...</p>
                ) : (
                    <div className="flex flex-col md:flex-row bg-white border shadow-sm rounded-lg overflow-hidden">
                        <img
                            src={courseDetail?.serviceCategory?.image || 'https://via.placeholder.com/300'}
                            alt={courseDetail?.name}
                            className="w-full md:w-1/3 h-[250px] object-cover"
                        />
                        <div className="p-6 flex flex-col justify-between">
                            <div>
                                <h2 className="text-2xl font-bold mb-2">{courseDetail.name}</h2>
                                <p className="text-gray-700">{courseDetail.description || 'No description provided.'}</p>

                                <div className="mt-4 space-y-1 text-sm text-gray-600">
                                    <p><strong>Start:</strong> {courseDetail.startDate || 'TBD'}</p>
                                    <p><strong>Instructor:</strong> {courseDetail.instructor || 'N/A'}</p>
                                    <p><strong>Sessions:</strong> {courseDetail.sessions || 'N/A'}</p>
                                    <p><strong>Time:</strong> {courseDetail.time || 'N/A'}</p>
                                </div>
                            </div>
                            <div className="mt-6 flex justify-between items-center">
                                <span className="text-xl font-semibold">
                                    ₹{courseDetail.price} + {courseDetail.tax || 0}% GST
                                </span>
                                <button className="bg-[#1A3353] text-white px-4 py-2 rounded hover:bg-[#14243b]">
                                    Book Now
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default CoursesTab;
