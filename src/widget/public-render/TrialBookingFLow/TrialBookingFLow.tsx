// TrialBookingFlow/TrialBookingFlow.tsx
import React, { useMemo, useState } from 'react';
import SubtypeDetailComponent from './SubtypeDetailComponent';
import SignupComponent from './signup';
import PackagePurchase from './PackagePurchase';
import RazorpayPayment from './RazorpayPayment';
import AppointmentSummary from './AppointmentSummary';
import LoginComponent from './Login';
import ForgotPassword from './forgotPassowrd';
import VerifyOtp from './VerifyOtp';
import SetPassword from './SetPassword';

type Step = 'detail' | 'signup' | 'login' | 'package' | 'payment' | 'booking' | 'forgot' | 'verify' | 'setpass';
type User = any;

const TrialBookingFlow = ({
  slot,
  widgetId,
  onBack,
  initialStep = 'detail',
}: {
  slot: any;
  widgetId: string;
  onBack: () => void;
  initialStep?: Step;
}) => {
  const [userInfo, setUserInfo] = useState<User | null>(() => {
    try {
      const raw = sessionStorage.getItem('hop.user');
      return raw ? JSON.parse(raw) : null;
    } catch {
      return null;
    }
  });

  const [selectedPackage, setSelectedPackage] = useState<any>(null);

  const [loginPrefill, setLoginPrefill] = useState<{ email?: string; phone?: string }>({});
  const [otpCtx, setOtpCtx] = useState<{ identifier: string; type: 'email' | 'mobile';otpVerificationCode?: string; } | null>(
    null
  );
  const resolvedInitial = useMemo<Step>(
    () => (initialStep === 'signup' && userInfo ? 'package' : initialStep),
    [initialStep, userInfo]
  );
  const [step, setStep] = useState<Step>(resolvedInitial);

  console.log('TBF->step:', step, 'slot:', slot);

  if (step === 'detail') {
    return (
      <SubtypeDetailComponent
        slot={slot}
        widgetId={widgetId}
        onBack={onBack}
        onSignupClick={() => setStep(userInfo ? 'package' : 'signup')}
      />
    );
  }

  if (step === 'signup') {
    return (
      <SignupComponent
        slot={slot}
        widgetId={widgetId}
        onBack={onBack}
        onLogin={(prefill) => {                   // ⬅️ when “Proceed to Login” or Login clicked
          setLoginPrefill(prefill || {});
          setStep('login');
        }}
        onSignupSuccess={(user: User) => {
          setUserInfo(user);
          sessionStorage.setItem('hop.user', JSON.stringify(user));
          setStep('package');
        }}
      />
    );
  }

  if (step === 'login') {
    return (
      <LoginComponent
        initialEmail={loginPrefill.email || ''}
        widgetId={widgetId}
        initialPhone={loginPrefill.phone || ''}
        onBack={() => setStep('signup')}
        onShowForgot={(email) => {
          setLoginPrefill((prev) => ({ ...prev, email: email ?? prev.email }));
          setStep('forgot');
        }}
        onLoginSuccess={(user: User) => {
          setUserInfo(user);
          sessionStorage.setItem('hop.user', JSON.stringify(user));
          setStep('package');
        }}
      />
    );
  }
  if (step === 'forgot') {
    return (
      <ForgotPassword
        widgetId={widgetId}
        initialEmail={loginPrefill.email || ''}
        onBack={() => setStep('login')}
        onDone={(p) => {
          console.log(p, "ppppppppppppppppppppppppppppppp")
          setOtpCtx({ identifier: p.identifier, type: p.type });
          setStep('verify');

        }}
      />
    )
  }
  if (step === 'verify' && otpCtx) {
    return (
      <VerifyOtp
        widgetId={widgetId}
        identifier={otpCtx.identifier}
        type={otpCtx.type}
        onBack={() => setStep('forgot')}
      onVerified={({ identifier, type, otpVerificationCode }) => {
        setOtpCtx({ identifier, type, otpVerificationCode });
        setStep('setpass');
      }}
      />
    );
  }
   if (step === 'setpass' && otpCtx) {
    console.log("heyeyeyeyeyeyey")
    return (
     <SetPassword
      widgetId={widgetId}
      identifier={otpCtx.identifier}
      type={otpCtx.type}
      otpVerificationCode={otpCtx.otpVerificationCode!}  
      onBack={() => setStep('verify')}
      onSuccessPrefill={(prefill) => {
        setLoginPrefill((prev) => ({ ...prev, ...prefill }));
        setStep('login'); 
      }}
    />
    );
  }
  if (step === 'package') {
    return (
      <PackagePurchase
        slot={slot}
        widgetId={widgetId}
        user={userInfo}
        onBack={() => setStep(userInfo ? 'detail' : 'signup')}
        onPackageSelect={(pkg: any) => {
          setSelectedPackage(pkg);
          setStep('payment');
        }}
      />
    );
  }

  if (step === 'payment') {
    return (
      <RazorpayPayment
        slot={slot}
        user={userInfo}
        selectedPackage={selectedPackage}
        widgetId={widgetId}
        onPaymentSuccess={() => setStep('booking')}
        onBack={() => setStep('package')}
      />
    );
  }

  if (step === 'booking') {
    return (
      <AppointmentSummary
        slot={slot}
        userInfo={userInfo}
        selectedPackage={selectedPackage}
        widgetId={widgetId}
        onBack={() => setStep('payment')}
        onResetFlow={onBack}
      />
    );
  }

  return null;
};

export default TrialBookingFlow;
