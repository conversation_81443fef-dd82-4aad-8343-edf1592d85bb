import React, { useMemo } from 'react';
import { But<PERSON>, ConfigProvider, Form, Input, message } from 'antd';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { useLocation } from 'wouter';

import {
  ForgetPassword,
} from '~/redux/actions/auth-actions';

type Props = {
  onBack: () => void;
onDone?: (p: { identifier: string; type: 'email' | 'mobile' }) => void;
  initialEmail?: string;
  widgetId?: string;
};

const extractErrMsg = (e: any): string => {
  if (Array.isArray(e?.message) && e.message.length) return e.message[0];
  if (typeof e?.message === 'string' && e.message) return e.message;

  const names = Object.getOwnPropertyNames(e ?? {});
  if (names.includes('message')) {
    const m: any = (e as any).message;
    if (Array.isArray(m) && m.length) return m[0];
    if (typeof m === 'string' && m) return m;
  }

  const r = (e as any)?.response?.data;
  if (Array.isArray(r?.message) && r.message.length) return r.message[0];
  if (typeof r?.message === 'string' && r.message) return r.message;

  if (Array.isArray((e as any)?.errors) && e.errors[0]) return e.errors[0];
  if (typeof (e as any)?.error === 'string') return (e as any).error;

  return 'Could not send reset code';
};

// --- simple helpers ---
const isEmail = (v: string) => /\S+@\S+\.\S+/.test(v);
const normalizeDigits = (v: string) => (v || '').replace(/[^\d]/g, '');
const isIndianMobile = (v: string) => {
  const d = normalizeDigits(v);
  return /^(\d{10}|91\d{10})$/.test(d);
};

export default function ForgotPassword({
  onBack,
  onDone,
  initialEmail = '',
  widgetId,
}: Props) {
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  const [loading, start, end] = useLoader();
  const [, setLocation] = useLocation();

  const selectedOrgId = widgetId;

  const initialValues = useMemo(() => ({ identifier: initialEmail }), [initialEmail]);

  const submit = async () => {
    try {
      const { identifier } = await form.validateFields();
      start();

      const raw = String(identifier).trim();
      const orgId = String(selectedOrgId || '').trim();

      if (!orgId) {
        message.error('Organization not set.');
        return;
      }

      const data: any = {
        organizationId: orgId,
        type: isEmail(raw) ? 'email' : 'mobile',
      };

      let identifierForStore = raw;

      if (data.type === 'email') {
        data.email = raw;
      } else if (isIndianMobile(raw)) {
        const digits = normalizeDigits(raw).slice(-10); // last 10 digits
        data.mobile = digits;
        data.countryCode = '+91';
        identifierForStore = digits;
      } else {
        message.error('Please enter a valid email or 10-digit mobile number.');
        return;
      }

      const res: any = await dispatch(
        ForgetPassword({ data, organizationId: orgId })
      );

      const status = res?.payload?.status ?? res?.status;
      if (status === 200 || status === 201) {
        message.success(
          data.type === 'email' ? 'OTP sent to your email' : 'OTP sent to your mobile'
        );
      onDone?.({ identifier: identifierForStore, type: data.type });
      } else {
        message.info('If an account exists, an OTP has been sent.');
      }
    } catch (e: any) {
      console.log(e, 'eeeeeeee');
      if (!e?.errorFields) {
        const m = extractErrMsg(e);
        message.info(m || 'If an account exists, an OTP has been sent.');
      }
    } finally {
      end();
    }
  };

  return (
    <div
      style={{ fontFamily: "'Poppins', sans-serif" }}
      className="px-6 pb-6 pt-2 lg:p-6"
    >
      <Button
        type="link"
        onClick={onBack}
        style={{ padding: 0, color: '#686D76' }}
        className="mb-5 text-base lg:mb-6 lg:text-lg"
      >
        ← Back
      </Button>

      <div style={{ background: '#fff' }} className="mx-auto sm:w-[100%] lg:w-[50%]">
        <h2 className="mb-4 font-semibold text-[#1a3353] sm:text-5xl lg:mb-6 lg:text-2xl">
          Forgot Password
        </h2>

        <p className="mb-4 text-sm text-[#455560]">
          Enter your registered email or mobile number to receive an OTP.
        </p>

        <ConfigProvider
          theme={{
            token: { controlHeight: 36 },
            components: {
              Input: { fontSize: 14 },
              Form: { itemMarginBottom: 22, verticalLabelMargin: -5 },
            },
          }}
        >
          <Form
            form={form}
            layout="vertical"
            requiredMark={false}
            initialValues={initialValues}
            onFinish={submit}
          >
            <Form.Item
              label={
                <span className="text-[#455560] sm:text-4xl lg:text-base">
                  Email / Mobile<span className="text-red-500"> *</span>
                </span>
              }
              name="identifier"
              rules={[
                { required: true, message: 'Please enter email or mobile' },
                {
                  validator: async (_rule, value) => {
                    const v = String(value || '').trim();
                    if (!v) return Promise.reject('Please enter email or mobile');
                    if (isEmail(v) || isIndianMobile(v)) return Promise.resolve();
                    return Promise.reject('Enter a valid email or 10-digit mobile');
                  },
                },
              ]}
            >
              <Input placeholder="Enter Email/Phone" />
            </Form.Item>

            <button
              type="submit"
              disabled={loading}
              className="w-full rounded-lg bg-[#17336f] py-2 text-white hover:bg-[#17336f] sm:text-4xl lg:py-1 lg:text-lg"
            >
              {loading ? 'Please wait…' : 'Send OTP'}
            </button>
          </Form>
        </ConfigProvider>
      </div>
    </div>
  );
}
