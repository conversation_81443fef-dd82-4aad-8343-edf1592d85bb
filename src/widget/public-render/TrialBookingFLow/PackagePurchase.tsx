import React, { useEffect, useMemo, useState } from 'react';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useWidgetDispatch, useWidgetSelector } from '~/hooks/widget-redux-hooks';
import { widgetPricingService } from '~/redux/actions/widget/widget.action';
import { selectSelectedService, setSelectedService } from '~/redux/slices/widget/selectedService.slice';

type PackagePurchaseProps = {
  slot: any;
  widgetId: string;
  user: any;
  onBack: () => void;
  onPackageSelect: (pkg: any) => void;
};

function rs(v?: number) {
  return typeof v === 'number' ? `Rs ${v.toFixed(2)}` : 'Rs 0.00';
}
function getCategory(p: any): 'Single' | 'Credit Pack' | 'Unlimited' | 'Other' {
  const s = p?.services || {};
  const st = String(s.sessionType || '').toLowerCase();
  const count = Number(s.sessionCount || 0);
  if (st.includes('unlimited')) return 'Unlimited';
  if (st.includes('single') && count <= 1) return 'Single';
  if (count > 1) return 'Credit Pack';
  return 'Other';
}
function subtitle(p: any) {
  const s = p?.services || {};
  const count = Number(s.sessionCount || 0);
  return count ? `${count} Credit${count > 1 ? 's' : ''}` : '';
}
const initials = (name?: string) =>
  (name || 'U').trim().split(/\s+/).map(w => w[0]).join('').slice(0, 2).toUpperCase();

const PackagePurchase: React.FC<PackagePurchaseProps> = ({ slot, widgetId, user, onBack, onPackageSelect }) => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [allPackages, setAllPackages] = useState<any[]>([]);
  const [filter, setFilter] = useState<'All' | 'Single' | 'Credit Pack' | 'Unlimited'>('All');
  const selectedService = useWidgetSelector(selectSelectedService);
  // --- derive payload IDs robustly from slot ---
  const appointmentId = useMemo(
    () =>
      slot?.subTypeId ??
      slot?.subtypeId ??
      slot?.subType?._id ??
      slot?.appointmentId ??
      null,
    [slot]
  );
  const serviceId = useMemo(
    () =>
      slot?.serviceCategoryId ??
      slot?.serviceCategory ??
      slot?.serviceId ??
      slot?.services?.serviceCategory ??
      null,
    [slot]
  );

  // --- fetch when IDs are ready ---
  useEffect(() => {
    let cancelled = false;

    // DEBUG: see what's happening
    console.log('[PackagePurchase] slot:', slot);
    console.log('[PackagePurchase] slot:', selectedService);

    console.log('[PackagePurchase] ids:', { appointmentId, serviceId });

    if (!appointmentId || !serviceId) return; // wait until both exist

    setLoading(true);
    dispatch(widgetPricingService({ appointmentId, serviceId }))
      .unwrap()
      .then((res: any) => {
        console.log(res, "res")
        const list = Array.isArray(res?.data?.data) ? res.data.data : [];
        if (!cancelled) setAllPackages(list);
      })
      .catch((err: any) => {
        console.error('Pricing fetch failed:', err);
        if (!cancelled) setAllPackages([]);
      })
      .finally(() => {
        if (!cancelled) setLoading(false);
      });

    return () => {
      cancelled = true;
    };
  }, [dispatch, appointmentId, serviceId, slot]);

  const filtered = useMemo(
    () => (filter === 'All' ? allPackages : allPackages.filter(p => getCategory(p) === filter)),
    [allPackages, filter]
  );

  const title = slot?.title || slot?.subtypeName || slot?.service?.name || 'Selected Service';
  const trainerName = slot?.trainer
  const trainerImg = selectedService?.sub?.image
  const when = slot?.from +"-"+ slot?.to
  const where = slot?.location
console.log(when,where,"skljfksjf")
  return (
    <div className="px-6 pb-6 pt-2 lg:p-6 font-sans">
      <button onClick={onBack} className="mb-5 text-base lg:mb-6 lg:text-lg text-gray-500 hover:text-gray-700">
        ← Back to schedule
      </button>

      <div className="mb-3">
        <h2 className="m-0 text-2xl lg:text-3xl font-medium text-[#6a564e]">{title}</h2>
        {(when || where) && (
          <p className="mt-1 text-gray-600">
            {when && <span className="font-semibold">{when}</span>}
            {when && where ? ' ' : null}
            {where && <> at <span className="font-normal">{where}</span></>}
          </p>
        )}
        {(trainerName || trainerImg) && (
          <div className="mt-2 flex items-center gap-3">
            {trainerImg ? (
              <img src={trainerImg} alt={trainerName || 'Trainer'} className="w-10 h-10 rounded-full object-cover" />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-sm font-semibold text-gray-700">
                {initials(trainerName)}
              </div>
            )}
            <span className="text-gray-800">with <span className="font-semibold">{trainerName || '—'}</span></span>
          </div>
        )}
      </div>

      <div className="mx-auto sm:w-full lg:w-[70%]">
        <div className="bg-white rounded-xl shadow-sm overflow-hidden divide-y divide-gray-100">
          {loading ? (
            <div className="py-10 flex items-center justify-center">
              <div className="h-10 w-10 rounded-full border-4 border-gray-200 border-t-gray-600 animate-spin" />
            </div>
          ) : filtered.length === 0 ? (
            <div className="py-10 text-center text-gray-500">No pricing options available</div>
          ) : (
            filtered.map((pkg) => {
              const cat = getCategory(pkg);
              const sub = subtitle(pkg);
              return (
                <button
                  key={pkg._id}
                  onClick={() => onPackageSelect(pkg)}
                  className="w-full text-left p-4 hover:bg-gray-50 flex items-center justify-between"
                >
                  <div className="flex flex-col">
                    <span className="text-[#1a3353] font-semibold">{pkg.name}</span>
                    {sub && <span className="text-gray-400 mt-0.5">{sub}</span>}
                  </div>
                  <div className="text-right">
                    <div className="font-bold">{rs(pkg.finalPrice ?? pkg.price)}</div>
                    <div className="text-xs text-gray-500">{cat}</div>
                  </div>
                </button>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default PackagePurchase;
