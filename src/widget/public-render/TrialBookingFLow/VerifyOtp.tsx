// TrialBookingFlow/VerifyOtp.tsx
import React, { useEffect, useMemo, useState } from 'react';
import { Button, ConfigProvider, Form, Input, message } from 'antd';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { useLocation } from 'wouter';

// Thunks & actions (adjust paths if needed)
import {
  ForgetPassword as ForgetPasswordThunk,
  VerifyOtp as VerifyOtpThunk,
} from '~/redux/actions/auth-actions';

// If you use Alertify, uncomment and adjust the import path
// import Alertify from '~/utils/alertify';

type VerifyType = 'email' | 'mobile';

type Props = {
  widgetId: string;                             // organizationId
  identifier: string;                           // email or raw mobile input
  type: VerifyType;                             // 'email' | 'mobile'
  onBack: () => void;
  onVerified?: (result: { identifier: string; type: VerifyType,otpVerificationCode:string }) => void;
};

type SignInValues = { otp: string };

const normalizeDigits = (v: string) => (v || '').replace(/[^\d]/g, '');

const VerifyOtp: React.FC<Props> = ({ widgetId, identifier, type, onBack, onVerified }) => {
  const dispatch = useAppDispatch();
  const [form] = Form.useForm<SignInValues>();
  const [loading, start, end] = useLoader();
  const [, setLocation] = useLocation();

  // Resend cooldown
  const [cooldown, setCooldown] = useState<number>(60);
  useEffect(() => {
    if (cooldown <= 0) return;
    const id = setTimeout(() => setCooldown((t) => t - 1), 1000);
    return () => clearTimeout(id);
  }, [cooldown]);

  const organizationId = useMemo(() => String(widgetId || '').trim(), [widgetId]);
  const shortMobile = useMemo(
    () => (type === 'mobile' ? normalizeDigits(identifier).slice(-10) : ''),
    [identifier, type]
  );

  

  const onFinish = async (values: SignInValues) => {
    try {
      start();

      if (!organizationId) {
        message.error('Organization not set.');
        return;
      }

      const otpNum = Number(values.otp);
      if (!/^\d{6}$/.test(String(values.otp)) || otpNum < 100000 || otpNum > 999999) {
        message.error('OTP must be a 6-digit number.');
        return;
      }

      // --- Build DTO exactly like your snippet, with mobile handling per your DTO ---
      const data: any =
        type === 'email'
          ? {
              email: String(identifier).trim().toLowerCase(),
              type: 'email', // If your API expects uppercase, change to 'EMAIL'
              otp: otpNum,
              forgotPasswordRequest: true,
            }
          : {
              mobile: shortMobile, // 10-digit only
              type: 'mobile', // If your API expects uppercase, change to 'MOBILE'
              otp: otpNum,
              forgotPasswordRequest: true,
            };

      const res: any = await dispatch(VerifyOtpThunk({ data, organizationId }));
      console.log(res,"verify otp")
      const status = res?.payload?.status ?? res?.status;

      if (status === 200 || status === 201) {

        const code =
          res?.payload?.data?.data?.otpVerificationCode ??
          res?.payload?.data?.otpVerificationCode ??
          res?.payload?.otpVerificationCode;
        const forgotPasswordRequest =
          res?.payload?.data?.data?.forgotPasswordRequest ??
          res?.payload?.data?.forgotPasswordRequest ??
          true;

        
        onVerified?.({
          identifier: type === 'email' ? String(identifier).trim().toLowerCase() : shortMobile,
          type,
          otpVerificationCode:code
        });
      } else {
        const msg =
          res?.payload?.data?.message ||
          res?.payload?.message ||
          res?.message ||
          'Invalid OTP';
        message.error(msg);
      }
    } finally {
      end();
    }
  };

  const resend = async () => {
    if (cooldown > 0) return;
    try {
      start();
      const data: any =
        type === 'email'
          ? { organizationId, type: 'email', email: String(identifier).trim().toLowerCase() }
          : { organizationId, type: 'mobile', mobile: shortMobile };

      const res: any = await dispatch(ForgetPasswordThunk({ data, organizationId }));
      const status = res?.payload?.status ?? res?.status;
      if (status === 200 || status === 201) {
        message.success('OTP resent');
        setCooldown(60);
      } else {
        message.info('If an account exists, an OTP has been sent.');
      }
    } finally {
      end();
    }
  };

  return (
    <div style={{ fontFamily: "'Poppins', sans-serif" }} className="px-6 pb-6 pt-2 lg:p-6">
      <Button
        type="link"
        onClick={onBack}
        style={{ padding: 0, color: '#686D76' }}
        className="mb-5 text-base lg:mb-6 lg:text-lg"
      >
        ← Back
      </Button>

      <div style={{ background: '#fff' }} className="mx-auto sm:w-[100%] lg:w-[50%]">
        <h2 className="mb-4 font-semibold text-[#1a3353] sm:text-5xl lg:mb-6 lg:text-2xl">
          Verify OTP
        </h2>

        <p className="mb-4 text-sm text-[#455560]">
          Enter the OTP sent to your {type === 'email' ? 'email' : 'mobile'} (
          {type === 'email' ? String(identifier).trim().toLowerCase() : shortMobile}).
        </p>

        <ConfigProvider
          theme={{
            token: { controlHeight: 36 },
            components: {
              Input: { fontSize: 14 },
              Form: { itemMarginBottom: 22, verticalLabelMargin: -5 },
            },
          }}
        >
          <Form form={form} layout="vertical" requiredMark={false} onFinish={onFinish}>
            <Form.Item
              label={<span className="text-[#455560] sm:text-4xl lg:text-base">OTP</span>}
              name="otp"
              rules={[
                { required: true, message: 'Please enter the OTP' },
                { pattern: /^\d{6}$/, message: 'OTP should be exactly 6 digits' },
              ]}
            >
              <Input placeholder="Enter 6-digit OTP" maxLength={6} inputMode="numeric" />
            </Form.Item>

            <div className="mb-3 flex items-center justify-between">
              <button
                type="button"
                onClick={resend}
                disabled={cooldown > 0 || loading}
                className="rounded-md px-0 text-sm text-[#17336f] underline disabled:cursor-not-allowed disabled:opacity-50"
              >
                {cooldown > 0 ? `Resend in ${cooldown}s` : 'Resend OTP'}
              </button>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full rounded-lg bg-[#17336f] py-2 text-white hover:bg-[#17336f] sm:text-4xl lg:py-1 lg:text-lg"
            >
              {loading ? 'Verifying…' : 'Verify OTP'}
            </button>
          </Form>
        </ConfigProvider>
      </div>
    </div>
  );
};

export default VerifyOtp;
