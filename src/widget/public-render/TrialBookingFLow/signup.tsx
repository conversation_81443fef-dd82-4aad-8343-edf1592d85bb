import React, { useMemo, useState, useCallback } from 'react';
import { Button, Checkbox, ConfigProvider, Form, Input, message } from 'antd';
import { useLoader } from '~/hooks/useLoader';
import { useAppDispatch } from '~/hooks/redux-hooks';
import {
    EmailOrMobileValidation,
    registerClient,
} from '~/redux/actions/widget/widget.action';

type Props = {
    slot: any;
    onBack: () => void;
    widgetId: string;
    onSignupSuccess: (user: any) => void;
    onLogin?: (prefill: { email?: string; phone?: string }) => void;
};

const SignupComponent = ({
    slot,
    onBack,
    widgetId,
    onSignupSuccess,
    onLogin,
}: Props) => {
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const [form] = Form.useForm();

    // cache for both email & phone values
    const validationCache = useMemo(() => new Map<string, boolean>(), []);
    const [emailExists, setEmailExists] = useState(false);
    const [checkingEmail, setCheckingEmail] = useState(false);

    const [phoneExists, setPhoneExists] = useState(false);
    const [checkingPhone, setCheckingPhone] = useState(false);

    const normalizePhone = useCallback(
        (raw: string) => raw.replace(/\D/g, ''),
        []
    );

    const checkAvailability = useCallback(
        async (value: string): Promise<boolean> => {
            if (!value) return true;
            if (validationCache.has(value)) return validationCache.get(value)!;
            try {
                const res = await dispatch(
                    EmailOrMobileValidation({ value, organizationId: widgetId })
                ).unwrap();
                // assuming API returns truthy if exists; invert for "available"
                const isAvailable = !res?.res?.data;
                validationCache.set(value, isAvailable);
                return isAvailable;
            } catch {
                // fail-safe: don't block user if API fails
                return true;
            }
        },
        [dispatch, validationCache, widgetId]
    );

    // ------- Email handlers -------
    const onEmailBlur = useCallback(async () => {
        const value = (form.getFieldValue('email') || '').trim();
        if (!value) {
            setEmailExists(false);
            form.setFields([{ name: 'email', errors: [] }]);
            return;
        }
        setCheckingEmail(true);
        const isAvailable = await checkAvailability(value);
        setCheckingEmail(false);

        if (!isAvailable) {
            setEmailExists(true);
            form.setFields([
                { name: 'email', errors: ['Email already exists'] },
            ]);
        } else {
            setEmailExists(false);
            form.setFields([{ name: 'email', errors: [] }]);
        }
    }, [checkAvailability, form]);

    const onEmailChange = useCallback(() => {
        if (emailExists) {
            setEmailExists(false);
            form.setFields([{ name: 'email', errors: [] }]);
        }
    }, [emailExists, form]);

    // ------- Phone handlers (same behavior as email) -------
    const onPhoneBlur = useCallback(async () => {
        let value: string = (form.getFieldValue('phone') || '').trim();
        value = normalizePhone(value);

        if (!value) {
            setPhoneExists(false);
            form.setFields([{ name: 'phone', errors: [] }]);
            return;
        }

        // simple local validation for India: 10 digits
        if (value.length !== 10) {
            setPhoneExists(false);
            form.setFields([
                {
                    name: 'phone',
                    errors: ['Enter a valid 10-digit mobile number'],
                },
            ]);
            return;
        }

        setCheckingPhone(true);
        const isAvailable = await checkAvailability(value);
        setCheckingPhone(false);

        if (!isAvailable) {
            setPhoneExists(true);
            form.setFields([
                { name: 'phone', errors: ['Mobile number already exists'] },
            ]);
        } else {
            setPhoneExists(false);
            form.setFields([{ name: 'phone', errors: [] }]);
        }
    }, [checkAvailability, form, normalizePhone]);

    const onPhoneChange = useCallback(() => {
        if (phoneExists) {
            setPhoneExists(false);
            form.setFields([{ name: 'phone', errors: [] }]);
        }
    }, [phoneExists, form]);

    const handleLoginClick = useCallback(() => {
        const email = (form.getFieldValue('email') || '').trim();
        const phone = (form.getFieldValue('phone') || '').trim();

        if (typeof onLogin === 'function') {
            onLogin({ email, phone });
            return;
        }

        if (typeof window !== 'undefined') {
            window.dispatchEvent(
                new CustomEvent('hopwidget:login', { detail: { email, phone } })
            );
        }
    }, [onLogin, form]);

    const handleSignup = async () => {
        try {
            const values = await form.validateFields();
            startLoader();

            // Re-check email availability to be safe
            if (values.email) {
                const emailAvailable = await checkAvailability(
                    values.email.trim()
                );
                if (!emailAvailable) {
                    setEmailExists(true);
                    form.setFields([
                        { name: 'email', errors: ['Email already exists'] },
                    ]);
                    handleLoginClick();
                    endLoader();
                    return;
                }
            }

            // Re-check phone availability (same as email)
            const phoneRaw = (values.phone || '').trim();
            const normalizedPhone = normalizePhone(phoneRaw);
            const phoneAvailable = await checkAvailability(normalizedPhone);
            if (!phoneAvailable) {
                setPhoneExists(true);
                form.setFields([
                    { name: 'phone', errors: ['Mobile number already exists'] },
                ]);
                handleLoginClick();
                endLoader();
                return;
            }

            const payload = {
                type: 'mobile',
                firstName: values.firstName,
                lastName: values.lastName,
                mobile: normalizedPhone,
                organizationId: widgetId,
                isUserAcceptTerms: true,
                countryCode: '+91',
                email: values.email?.trim() || undefined,
            };

            const result = await dispatch(registerClient(payload)).unwrap();
            if (result.data.data.user) {
                message.success('Registration successful');
                onSignupSuccess(result.data.data.user);
            }
        } catch (error: any) {
            if (!error?.errorFields)
                message.error(error?.message || 'Signup failed');
        } finally {
            endLoader();
        }
    };

    return (
        <div
            style={{ fontFamily: "'Poppins', sans-serif" }}
            className="px-6 pb-6 pt-2 lg:p-6"
        >
            <Button
                type="link"
                onClick={onBack}
                style={{ padding: 0, color: '#686D76' }}
                className="mb-5 text-base lg:mb-6 lg:text-lg"
            >
                ← Back
            </Button>

            <div
                style={{ background: '#fff' }}
                className="mx-auto flex flex-col gap-3 sm:w-[100%] lg:w-[50%]"
            >
                <h2 className="mb-1 font-semibold text-[#1a3353] sm:text-5xl lg:mb-6 lg:text-2xl">
                    Sign up to Book Trial
                </h2>

                <ConfigProvider
                    theme={{
                        token: { controlHeight: 36 },
                        components: {
                            Input: { fontSize: 14 },
                            Form: {
                                itemMarginBottom: 22,
                                verticalLabelMargin: -5,
                            },
                            Checkbox: { fontSize: 14, colorText: '#455560' },
                        },
                    }}
                >
                    <Form form={form} layout="vertical" requiredMark={false}>
                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    First Name
                                    <span style={{ color: 'red' }}>*</span>
                                </span>
                            }
                            name="firstName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter your first name',
                                },
                            ]}
                        >
                            <Input placeholder="First name" />
                        </Form.Item>

                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    Last Name
                                </span>
                            }
                            name="lastName"
                        >
                            <Input placeholder="Last name" />
                        </Form.Item>

                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    Email
                                </span>
                            }
                            name="email"
                            rules={[
                                {
                                    type: 'email',
                                    message: 'Please enter a valid email',
                                },
                            ]}
                        >
                            <Input
                                placeholder="Enter your email"
                                onBlur={onEmailBlur}
                                onChange={onEmailChange}
                                disabled={checkingEmail}
                            />
                        </Form.Item>

                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    Phone{' '}
                                    <span style={{ color: 'red' }}>*</span>
                                </span>
                            }
                            name="phone"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter your phone number',
                                },
                            ]}
                        >
                            <Input
                                placeholder="Enter your phone number"
                                inputMode="numeric"
                                pattern="\d*"
                                maxLength={14} // allow spaces/dashes; we normalize later
                                onBlur={onPhoneBlur}
                                onChange={onPhoneChange}
                                disabled={checkingPhone}
                            />
                        </Form.Item>

                        <button
                            className="w-full rounded-lg bg-[#17336f] py-2 text-white hover:border-none hover:bg-[#17336f] hover:text-white sm:text-4xl lg:py-1 lg:text-lg"
                            onClick={(e) => {
                                e.preventDefault();
                                handleSignup();
                            }}
                        >
                            Sign up
                        </button>

                        <button
                            type="button"
                            onClick={handleLoginClick}
                            className="mt-3 w-full rounded-lg border border-[#17336f] bg-white py-2 text-[#17336f] hover:bg-[#F4ECFF] sm:text-4xl lg:py-1 lg:text-lg"
                        >
                            Already Existing Customer
                        </button>
                    </Form>
                </ConfigProvider>
            </div>
        </div>
    );
};

export default SignupComponent;
