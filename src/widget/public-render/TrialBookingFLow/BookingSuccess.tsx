import React, { useMemo } from 'react';
import { Result, Button, Descriptions, Card, ConfigProvider } from 'antd';
import { useLocation } from 'wouter';
import { format } from 'date-fns';

import { useWidgetDispatch } from '~/hooks/widget-redux-hooks';

import { resetSelectedService } from '~/redux/slices/widget/selectedService.slice';

function readQuery(): Record<string, string> {
    const raw =
        typeof window !== 'undefined'
            ? window.location.search ||
              (window.location.hash.includes('?')
                  ? '?' + window.location.hash.split('?')[1]
                  : '')
            : '';
    const q = new URLSearchParams(raw);
    const out: Record<string, string> = {};
    q.forEach((v, k) => (out[k] = v));
    return out;
}

export default function BookingSuccess({
    homePath = '/schedule',
}: {
    homePath?: string;
}) {
    const [, navigate] = useLocation();
    const dispatch = useWidgetDispatch(); // ✅
    const q = useMemo(() => readQuery(), []);

    const prettyDate = useMemo(() => {
        if (!q.date) return '—';
        const d = new Date(q.date);
        return isNaN(d.getTime()) ? q.date : format(d, 'EEE, dd MMM yyyy');
    }, [q.date]);

    const handleOk = () => {
        sessionStorage.removeItem('hop.user');

        dispatch(resetSelectedService());

        navigate(homePath);
    };

    return (
        <div className="mx-auto max-w-3xl p-4 md:p-8">
            <style>
                {`
.ant-result .ant-result-content{
    padding: 24px 10px !important;
}
    .ant-result{
    padding: 48px 10px !important;
    }
`}
            </style>

            <ConfigProvider
                theme={{
                    components: {
                        Card: {
                            bodyPadding: 0,
                        },
                    },
                }}
            >
                {' '}
                <Card>
                    <Result
                        status="success"
                        title="Your booking is confirmed"
                        subTitle={
                            q.bookingId
                                ? `Booking ID: ${q.bookingId}`
                                : undefined
                        }
                        extra={[
                            <Button
                                className="bg-[#17336f] text-white"
                                key="ok"
                                onClick={handleOk}
                            >
                                OK
                            </Button>,
                        ]}
                    >
                        <Descriptions column={1} size="small" bordered>
                            <Descriptions.Item label="Service">
                                {q.service || '—'}
                            </Descriptions.Item>
                            <Descriptions.Item label="Trainer">
                                {q.trainer || '—'}
                            </Descriptions.Item>
                            <Descriptions.Item label="Date">
                                {prettyDate}
                            </Descriptions.Item>
                            <Descriptions.Item label="Time">
                                {q.from && q.to ? `${q.from} – ${q.to}` : '—'}
                            </Descriptions.Item>
                            {/* {q.purchaseId ? (
              <Descriptions.Item label="Purchase Ref">{q.purchaseId}</Descriptions.Item>
            ) : null} */}
                        </Descriptions>
                    </Result>
                </Card>
            </ConfigProvider>
        </div>
    );
}
