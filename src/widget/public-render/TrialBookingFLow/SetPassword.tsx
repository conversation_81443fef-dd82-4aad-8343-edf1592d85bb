// TrialBookingFlow/SetPassword.tsx
import React from 'react';
import { Button, ConfigProvider, Form, Input, message } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';

// Thunks/actions (adjust paths if needed)
import {
    ResetPassword as ResetPasswordThunk,

} from '~/redux/actions/auth-actions';

type VerifyType = 'email' | 'mobile';
type FormValues = { password: string; confirmPassword: string };

type Props = {
    widgetId: string;
    identifier: string;
    type: VerifyType;
    onBack: () => void;
    otpVerificationCode: string;
    onSuccessPrefill: (prefill: { email?: string; phone?: string }) => void;
};

const onlyDigits = (v: string) => (v || '').replace(/[^\d]/g, '');
const isEmail = (v: string) => /\S+@\S+\.\S+/.test(v);

const SetPassword: React.FC<Props> = ({
    widgetId,
    identifier,
    type,
    onBack,
    onSuccessPrefill,
    otpVerificationCode

}) => {
    const dispatch = useAppDispatch();
    const [form] = Form.useForm<FormValues>();
    const [loading, startLoader, endLoader] = useLoader();
    const organizationId = String(widgetId || '').trim();
    const normalizedEmail = type === 'email' ? String(identifier || '').trim().toLowerCase() : '';
    const normalizedMobile = type === 'mobile' ? onlyDigits(identifier).slice(-10) : '';

    const onFinish = (values: FormValues) => {
        startLoader();
        const data: any =
            type === 'email'
                ? {
                    type: 'email',
                    email: normalizedEmail,
                    password: values.password,
                    confirmPassword: values.confirmPassword,
                    otpVerificationCode,
                }
                : {
                    type: 'mobile',
                    mobile: normalizedMobile,
                    password: values.password,
                    confirmPassword: values.confirmPassword,
                    otpVerificationCode,
                };

        dispatch(ResetPasswordThunk({ data, organizationId }))
            .unwrap()
            .then((res: any) => {
                if (res?.status === 200 || res?.status === 201) {
                    message.success('Password set successfully');

                    if (type === 'email') {
                        onSuccessPrefill({ email: normalizedEmail });
                    } else {
                        onSuccessPrefill({ phone: normalizedMobile });
                    }
                }
            })
            .finally(endLoader);
    };

    return (
        <div style={{ fontFamily: "'Poppins', sans-serif" }} className="px-6 pb-6 pt-2 lg:p-6">
            <Button
                type="link"
                onClick={onBack}
                style={{ padding: 0, color: '#686D76' }}
                className="mb-5 text-base lg:mb-6 lg:text-lg"
            >
                ← Back
            </Button>

            <div style={{ background: '#fff' }} className="mx-auto sm:w-[100%] lg:w-[50%]">
                <h2 className="mb-4 font-semibold text-[#1a3353] sm:text-5xl lg:mb-6 lg:text-2xl">
                    Set New Password
                </h2>

                <p className="mb-4 text-sm text-[#455560]">
                    Create a strong password for your account.
                </p>

                <ConfigProvider
                    theme={{
                        token: { controlHeight: 36 },
                        components: {
                            Input: { fontSize: 14 },
                            Form: { itemMarginBottom: 22, verticalLabelMargin: -5 },
                        },
                    }}
                >
                    <Form form={form} layout="vertical" requiredMark={false} onFinish={onFinish}>
                        <Form.Item
                            label={<span className="text-[#455560] sm:text-4xl lg:text-base">Password</span>}
                            name="password"
                            rules={[
                                { required: true, message: 'Please enter a new password' },
                                { min: 8, message: 'Password must be at least 8 characters' },
                            ]}
                            
                        >
                            <Input.Password placeholder="Enter new password" />
                        </Form.Item>

                        <Form.Item
                            label={<span className="text-[#455560] sm:text-4xl lg:text-base">Confirm Password</span>}
                            name="confirmPassword"
                            dependencies={['password']}
                            
                            rules={[
                                { required: true, message: 'Please confirm your password' },
                                ({ getFieldValue }) => ({
                                    validator(_, value) {
                                        if (!value || getFieldValue('password') === value) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(new Error('Passwords do not match'));
                                    },
                                }),
                            ]}
                        >
                            <Input.Password placeholder="Re-enter new password" />
                        </Form.Item>

                        <button
                            type="submit"
                            disabled={loading}
                            className="w-full rounded-lg bg-[#17336f] py-2 text-white hover:bg-[#17336f] sm:text-4xl lg:py-1 lg:text-lg"
                        >
                            {loading ? 'Saving…' : 'Set Password'}
                        </button>
                    </Form>
                </ConfigProvider>
            </div>
        </div>
    );
};

export default SetPassword;
