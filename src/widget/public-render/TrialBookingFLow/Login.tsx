import React, { useMemo, useCallback } from 'react';
import { Button, Checkbox, ConfigProvider, Form, Input, message } from 'antd';
import { useLoader } from '~/hooks/useLoader';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { LoginUser } from '~/redux/actions/auth-actions';
import { WidgetLoginUser } from '~/redux/actions/widget/widget.action';

type Props = {
    onBack: () => void;
    onLoginSuccess: (user: any) => void;
    initialEmail?: string;
    initialPhone?: string;
    widgetId?: string; // if your API needs it
     onShowForgot?: (prefillEmail?: string) => void;   // ⬅️ NEW
    onForgotCustom?: (identifier: { email?: string; mobile?: string }) => void; // optional override
};

const LoginComponent = ({
    onBack,
    onLoginSuccess,
    initialEmail = '',
    initialPhone = '',
    widgetId,
    onForgotCustom,
    onShowForgot,
}: Props) => {
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const [form] = Form.useForm();

    const initialValues = useMemo(
        () => ({
            email: initialEmail,
            phone: initialPhone,
            password: '',
        }),
        [initialEmail, initialPhone]
    );

    const ensureIdentifier = useCallback(() => {
        const { email, phone } = form.getFieldsValue(['email', 'phone']);
        if (!email && !phone) {
            message.error('Please enter email or phone.');
            return false;
        }
        return true;
    }, [form]);
    // Capture the text passed to Alertify.error during a single async call
    // async function withAlertifyCapture<T>(fn: () => Promise<T>): Promise<T> {
    //     // eslint-disable-next-line no-undef
    //     const g: any =
    //         typeof window !== 'undefined' ? window : (globalThis as any);
    //     const A = g?.Alertify;
    //     let lastMsg: string | undefined;
    //     const orig = A?.error;

    //     try {
    //         if (A && typeof A.error === 'function') {
    //             A.error = (msg: any) => {
    //                 orig.call(A, msg);
    //                 lastMsg = Array.isArray(msg) ? msg[0] : String(msg ?? '');
    //             };
    //         }
    //         return await fn();
    //     } catch (e: any) {
    //         if (lastMsg) {
    //             const err = new Error(lastMsg);
    //             (err as any)._capturedAlertify = true;
    //             (err as any)._original = e;
    //             throw err;
    //         }
    //         throw e;
    //     } finally {
    //         if (A && orig) A.error = orig;
    //     }
    // }

    // put this near your component
    const extractErrMsg = (e: any): string => {
        // 1) top-level message (array or string)
        if (Array.isArray(e?.message) && e.message.length) return e.message[0];
        if (typeof e?.message === 'string' && e.message) return e.message;

        // 2) non-enumerables (DevTools shows {} but props exist)

        const names = Object.getOwnPropertyNames(e ?? {});
        if (names.includes('message')) {
            const m: any = (e as any).message;
            if (Array.isArray(m) && m.length) return m[0];
            if (typeof m === 'string' && m) return m;
        }

        // 3) Axios-like shapes
        const r = (e as any)?.response?.data;
        if (Array.isArray(r?.message) && r.message.length) return r.message[0];
        if (typeof r?.message === 'string' && r.message) return r.message;

        // 4) other common shapes
        if (Array.isArray((e as any)?.errors) && e.errors[0])
            return e.errors[0];
        if (typeof (e as any)?.error === 'string') return (e as any).error;

        // 5) last resort
        return 'Login failed';
    };

    const handleLogin = useCallback(async () => {
        try {
            const values = await form.validateFields();
            if (!ensureIdentifier()) return;

            startLoader();

            const email = (values.email || '').trim().toLowerCase();
            const phone = (values.phone || '').trim();

            // 👉 Build payload:
            // - if both present, send both (type: 'both')
            // - else behave exactly like before
            const data =
                email && phone
                    ? {
                        type: 'both' as const,
                        email,
                        mobile: phone,
                        countryCode: '+91',
                        password: values.password,
                    }
                    : email
                        ? {
                            type: 'email' as const,
                            email,
                            password: values.password,
                        }
                        : {
                            type: 'mobile' as const,
                            mobile: phone,
                            countryCode: '+91',
                            password: values.password,
                        };

            const res = await dispatch(
                WidgetLoginUser({ data, organizationId: widgetId })
            ).unwrap();

            const user = res?.data?.data?.user ?? res?.user ?? res;
            if (user) {
                message.success('Logged in successfully');
                onLoginSuccess(user);
            } else {
                message.error('Login failed. Please check your credentials.');
            }
        } catch (err: any) {
            console.dir(err, { depth: 10 });
            console.log('ownProps:', Object.getOwnPropertyNames(err));
            message.error(extractErrMsg(err));
        } finally {
            endLoader();
        }
    }, [dispatch, form, ensureIdentifier, startLoader, endLoader, onLoginSuccess, widgetId]);


   const handleForgotPassword = useCallback(async () => {
  const { email } = form.getFieldsValue(['email']);

  if (typeof onShowForgot === 'function') {
    onShowForgot((email || '').trim() || undefined);
    return;
  }

  if (onForgotCustom) {
    onForgotCustom({ email, mobile: undefined });
    return;
  }

  message.info('Please use the Forgot Password screen.');
}, [form, onShowForgot, onForgotCustom]);

    return (
        <div
            style={{ fontFamily: "'Poppins', sans-serif" }}
            className="px-6 pb-6 pt-2 lg:p-6"
        >
            <Button
                type="link"
                onClick={onBack}
                style={{ padding: 0, color: '#686D76' }}
                className="mb-5 text-base lg:mb-6 lg:text-lg"
            >
                ← Back
            </Button>

            <div
                style={{ background: '#fff' }}
                className="mx-auto flex flex-col gap-3 sm:w-[100%] lg:w-[50%]"
            >
                <h2 className="mb-1 font-semibold text-[#1a3353] sm:text-5xl lg:mb-6 lg:text-2xl">
                    {initialEmail
                        ? 'Already Existing Customer'
                        : 'Login to Continue'}
                </h2>

                <ConfigProvider
                    theme={{
                        token: { controlHeight: 36 },
                        components: {
                            Input: { fontSize: 14 },
                            Form: {
                                itemMarginBottom: 22,
                                verticalLabelMargin: -5,
                            },
                            Checkbox: { fontSize: 14, colorText: '#455560' },
                        },
                    }}
                >
                    <Form
                        form={form}
                        layout="vertical"
                        requiredMark={false}
                        initialValues={initialValues}
                    >
                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    Email
                                </span>
                            }
                            name="email"
                            rules={[
                                {
                                    type: 'email',
                                    message: 'Please enter a valid email',
                                },
                            ]}
                        >
                            <Input placeholder="Enter your email" />
                        </Form.Item>

                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    Phone
                                </span>
                            }
                            name="phone"
                            rules={[
                                // optional basic pattern (10 digits)
                                {
                                    pattern: /^\d{10}$/,
                                    message: 'Enter 10-digit mobile number',
                                },
                            ]}
                        >
                            <Input placeholder="Enter your phone number" />
                        </Form.Item>

                        <Form.Item
                            label={
                                <span className="text-[#455560] sm:text-4xl lg:text-base">
                                    Password{' '}
                                    <span style={{ color: 'red' }}>*</span>
                                </span>
                            }
                            name="password"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter your password',
                                },
                            ]}
                        >
                            <Input.Password placeholder="Enter your password" />
                        </Form.Item>
                        
                        <div className="mb-3 flex items-center justify-between">
                            <button
                                type="button"
                                onClick={handleForgotPassword}
                                className="text-right text-sm underline"
                                style={{ color: '#17336f' }}
                            >
                                Forgot password?
                            </button>
                        </div>

                        <button
                            className="w-full rounded-lg bg-[#17336f] py-2 text-white hover:border-none hover:bg-[#17336f] hover:text-white sm:text-4xl lg:py-1 lg:text-lg"
                            onClick={(e) => {
                                e.preventDefault();
                                // ensure at least one identifier is present
                                if (!ensureIdentifier()) return;
                                handleLogin();
                            }}
                            disabled={loader}
                        >
                            {loader ? 'Please wait…' : 'Login'}
                        </button>
                    </Form>
                </ConfigProvider>
            </div>
        </div>
    );
};

export default LoginComponent;
