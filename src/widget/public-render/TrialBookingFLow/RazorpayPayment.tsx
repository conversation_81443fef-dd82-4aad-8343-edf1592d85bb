// RazorpayPayment.tsx
import { <PERSON><PERSON>, Config<PERSON><PERSON><PERSON>, Spin, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useAppDispatch } from '~/hooks/redux-hooks';
import {
  createRazorpayOrder,
  verifyRazorpayPayment,
  bookpersonalAppointment,   // 👈 import the booking thunk
} from '~/redux/actions/widget/widget.action';
import { useLocation } from 'wouter';
import { ClassType } from '~/types/enums';
declare global { interface Window { Razorpay: any; } }

const RazorpayPayment = ({
  slot,
  selectedPackage,
  user,
  widgetId,
  onPaymentSuccess,
  onBack,
}: {
  slot: any;
  selectedPackage: any;
  user: any;
  widgetId: string;
  onPaymentSuccess: () => void;
  onBack: () => void;
}) => {
  const dispatch = useAppDispatch();
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isVerifying, setIsVerifying] = useState(false);
  const [, navigate] = useLocation();
  useEffect(() => {
    if (window.Razorpay) { setIsScriptLoaded(true); return; }
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.async = true;
    script.onload = () => setIsScriptLoaded(true);
    document.body.appendChild(script);
  }, []);

  const initiatePayment = async () => {
    setIsLoading(true);

    const from = slot?.from ?? slot?.time?.from;
    const to = slot?.to ?? slot?.time?.to;
    if (!from || !to) {
      message.error('Time not selected. Please pick a slot before payment.');
      setIsLoading(false);
      return;
    }

    try {
      const res: any = await dispatch(createRazorpayOrder({
        amount: selectedPackage.price,
        packageId: selectedPackage._id, // pricingId
        widgetId,
        userId: user._id,
      })).unwrap();
      console.log(res,
        'pay'
      )
      console.log(res.data, "res payment")
      const { orderId, razorpayKey } = res?.data || res || {};
      console.log(orderId, razorpayKey)
      if (!orderId || !razorpayKey) throw new Error('Order creation failed');

      const options = {
        key: razorpayKey,
        amount: Math.round((selectedPackage.price || 0) * 100),
        currency: 'INR',
        name: 'Your Brand Name',
        description: selectedPackage.name,
        order_id: orderId,
        prefill: {
          name: user?.name || '',
          email: user?.email || '',
          contact: user?.phone || user?.mobile || '',
        },
        notes: { widgetId, packageId: selectedPackage._id },
        theme: { color: '#1A3353' },

        handler: async (response: any) => {
          setIsVerifying(true);
          try {
            const verifyResult: any = await dispatch(verifyRazorpayPayment({
              ...response,
              userId: user._id,
              packageId: selectedPackage._id, // pricingId
              widgetId,
              amount: selectedPackage.price,
              date: slot.date,
            })).unwrap();
            console.log(verifyResult, "verify")
            console.log(verifyResult.data, "data")
            const ok =
              verifyResult?.data?.success ??
              verifyResult?.success ??
              false;
            console.log(ok, "ok")
            const purchaseId =
              verifyResult?.data?.purchaseResult?.purchaseItems[0]?.purchaseIds[0]
            console.log(purchaseId, "purchase ID")
            if (!ok) {
              message.error('Payment verification failed.');
              return;
            }
            if (!purchaseId) {
              message.error('Payment verified but purchase not created.');
              return;
            }
            console.log(slot, "slot")
            let payload
            if (slot.classType === ClassType.PERSONAL_APPOINTMENT) {
              payload = {
                clientId: user._id,
                trainerId: slot.trainerId,
                pricingId: selectedPackage._id,
                purchaseId,
                date: slot.date,
                from,
                to,
                classType: 'personalAppointment',
                dateRange: 'Single',
                serviceCategory: slot.serviceCategory ?? slot.serviceCategoryId,
                subType: slot.subTypeId ?? slot.subtypeId,
                duration: slot.durationInMinutes ?? selectedPackage?.services?.durationInMinutes,
                checkIn: false,
                organizationId: widgetId,
              };
            }
            else if (slot?.classType === ClassType.BOOKING) {
              
              const facilityId =
                slot?.facilityId ??
                slot?.facility?._id ??
                slot?.room?.facilityId;

              const roomId =
                slot?.roomId ??
                slot?.trainerId ??
                slot?.room?._id;

              payload = {
                facilityId,
                clientId: user?._id,
                classType: 'bookings',
                roomId,
                dateRange: 'Single',
                purchaseId,
                serviceCategory: slot?.serviceCategory ?? slot?.serviceCategoryId,
                subType: slot?.subTypeId ?? slot?.subtypeId,
                duration: slot?.durationInMinutes ?? selectedPackage?.services?.durationInMinutes,
                date: slot.date,
                from,
                to,
                checkIn: false,
                organizationId: widgetId,
              };
            }
            console.log(payload, "payload")
            const bookingRes: any = await dispatch(bookpersonalAppointment(payload)).unwrap();
            console.log(bookingRes, "booking")
            const bookedOk = bookingRes?.status === 200 || bookingRes?.data?.success;

            if (bookedOk) {
              message.success('Payment successful and appointment booked!');
              const params = new URLSearchParams({
                purchaseId,
                date: String(slot.date || ''),
                from: String(from),
                to: String(to),
                trainer: String(slot.trainerName || slot.trainer || ''),
                service: String(selectedPackage?.name || ''),
              }).toString();

              navigate(`/booking/success?${params}`);
            } else {
              message.error('Booking failed after payment. Please contact support.');
            }
          } catch (err) {
            console.error('Verification/Booking error', err);
            message.error('Payment verified but booking failed.');
          } finally {
            setIsVerifying(false);
          }
        },

        modal: { ondismiss: () => setIsLoading(false) },
      };

      const rzp = new window.Razorpay(options);
      rzp.open();
    } catch (error) {
      console.error('Payment error', error);
      message.error('Payment failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => { if (isScriptLoaded) initiatePayment(); }, [isScriptLoaded]);

  return (
    <div className="px-6 pb-6 pt-2 lg:p-6">
      {!isVerifying && (
        <Button
          type="link"
          onClick={onBack}
          style={{ padding: 0, color: '#686D76' }}
          className="sm:mb-20 sm:text-4xl lg:mb-6 lg:text-lg"
        >
          ← Back
        </Button>
      )}
      <ConfigProvider theme={{ token: { colorPrimary: '#8143D1' } }}>
        <div className="mx-auto flex h-screen items-center justify-center">
          <Spin size="large" />
        </div>
      </ConfigProvider>
    </div>
  );
};

export default RazorpayPayment;
