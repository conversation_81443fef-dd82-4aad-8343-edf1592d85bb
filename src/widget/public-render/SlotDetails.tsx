// SlotDetails.tsx
import React, { useMemo, useState } from 'react';
import { format, parseISO } from 'date-fns';
import { useLocation } from 'wouter';
import { message } from 'antd';
import TrialBookingFlow from './TrialBookingFLow/TrialBookingFLow';
import {
    useWidgetDispatch,
    useWidgetSelector,
} from '~/hooks/widget-redux-hooks';
import { selectSelectedService } from '~/redux/slices/widget/selectedService.slice';

type Branding = { fontFamily?: string; primaryColor?: string };
type ClassType = 'personalAppointment' | 'bookings';

export type SlotParams = {
    classType: ClassType;
    trainerId: string; // for rooms, pass roomId here
    trainer: string; // for rooms, pass roomName here
    facilityId: string;
    facilityName: string;
    date: string; // yyyy-MM-dd
    from: string; // HH:mm
    to: string; // HH:mm
    durationInMinutes?: number;
    serviceCategoryId?: string;
    subTypeId?: string;
    subtypeName?: string;
    image?: string;
    payRateId?: string;
};

function readQuery(): Record<string, string> {
    const raw =
        typeof window !== 'undefined'
            ? window.location.search ||
              (window.location.hash.includes('?')
                  ? '?' + window.location.hash.split('?')[1]
                  : '')
            : '';
    const q = new URLSearchParams(raw);
    const out: Record<string, string> = {};
    q.forEach((v, k) => (out[k] = v));
    return out;
}

export default function SlotDetails({
    branding,
    organizationId,
    initialParams, // preferred way to pass data
    onBack, // inline back handler
}: {
    branding: Branding;
    organizationId: string;
    initialParams?: Partial<SlotParams>;
    onBack?: () => void;
}) {
    const [, navigate] = useLocation();
    const q = useMemo(() => readQuery(), []);
    const dispatch = useWidgetDispatch();
    const selectedService = useWidgetSelector(selectSelectedService);
    const { parent, sub } = selectedService;

    const params: SlotParams = {
        classType: (initialParams?.classType ??
            (q.classType as ClassType) ??
            'personalAppointment') as ClassType,

        // Support old QS keys roomId/roomName for backward compatibility
        trainerId: initialParams?.trainerId ?? q.trainerId ?? q.roomId ?? '',
        trainer: initialParams?.trainer ?? q.trainer ?? q.roomName ?? '',

        facilityId: initialParams?.facilityId ?? q.facilityId ?? '',
        facilityName: initialParams?.facilityName ?? q.facilityName ?? '',

        date: initialParams?.date ?? q.date ?? '',
        from: initialParams?.from ?? q.from ?? '',
        to: initialParams?.to ?? q.to ?? '',

        durationInMinutes:
            initialParams?.durationInMinutes ??
            (q.durationInMinutes ? Number(q.durationInMinutes) : undefined),

        serviceCategoryId:
            initialParams?.serviceCategoryId ??
            q.serviceCategoryId ??
            undefined,
        subTypeId: initialParams?.subTypeId ?? q.subTypeId ?? undefined,
        subtypeName: initialParams?.subtypeName ?? q.subtypeName ?? undefined,
        image: initialParams?.image ?? q.image ?? undefined,
        payRateId: initialParams?.payRateId ?? q.payRateId ?? undefined,
    };
    function htmlToPlainText(html?: string): string {
        if (!html) return '';
        // keep line breaks for <br> and end of </p>
        const normalized = html
            .replace(/<br\s*\/?>/gi, '\n')
            .replace(/<\/p>/gi, '\n');
        if (typeof window !== 'undefined') {
            const div = document.createElement('div');
            div.innerHTML = normalized;
            const text = div.textContent || div.innerText || '';
            return text.replace(/\u00A0/g, ' ').trim();
        }
        // SSR-safe fallback
        return normalized
            .replace(/<[^>]*>/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
    }

    const resolvedWidgetId = organizationId;
    const title = parent?.name
        ? `${parent?.name}-${sub?.name}`
        : params.subtypeName;

    const dateObj = params.date
        ? parseISO(`${params.date}T00:00:00`)
        : new Date();
    const prettyDate = `${format(dateObj, 'EEEE, d MMM')} @ ${
        params.from || ''
    } - ${params.to || ''}`;

    const [showFlow, setShowFlow] = useState(false);

    // Build the slot object that TrialBookingFlow expects
    const slotForFlow = useMemo(() => {
        if (!params.trainerId) return null;
        return {
            trainer: params.trainer,
            trainerId: params.trainerId,
            image: params.image || '',
            facilityId: params.facilityId || '',
            location: params.facilityName || '',
            facilityName: params.facilityName || '',
            date: params.date,
            from: params.from,
            to: params.to,
            durationInMinutes: params.durationInMinutes,
            subtypeName: params.subtypeName,
            subTypeId: params.subTypeId,
            serviceCategoryId: params.serviceCategoryId,
            serviceCategory: params.serviceCategoryId,
            payRateId: params.payRateId,
            classType: params.classType,
        };
    }, [params]);

    const goBack = () => {
        if (onBack) return onBack();
        if (typeof window !== 'undefined' && window.history.length > 1) {
            window.history.back();
            return;
        }
        navigate('/schedule');
    };

    const onBookClick = () => {
        if (!resolvedWidgetId) {
            message.error('Missing organization id.');
            return;
        }
        if (!slotForFlow) {
            message.error('Missing trainer/room id on slot.');
            return;
        }
        setShowFlow(true);
    };

    // Full flow (signup → package → payment → booking)
    if (showFlow && slotForFlow) {
        return (
            <TrialBookingFlow
                slot={slotForFlow}
                widgetId={String(resolvedWidgetId)}
                onBack={() => setShowFlow(false)}
                initialStep="signup"
            />
        );
    }

    // Details screen
    return (
        <div
            className="mx-auto max-w-[760px] px-3 py-4 font-sans"
            style={{ fontFamily: branding?.fontFamily }}
        >
            <button
                onClick={goBack}
                className="mb-3 inline-flex items-center gap-1 text-gray-700 hover:underline"
            >
                <span aria-hidden>←</span>
                <span>Back to schedule</span>
            </button>
            <img
                src={
                    sub.image ||
                    'https://staginghop.hkstest.uk/assets/Profile_icon.png'
                }
                alt="avatar"
                className="h-14 w-14 rounded-full object-cover"
            />
            <h1 className="mb-4 mt-1 text-[28px] font-semibold md:text-3xl">
                {title}
            </h1>

            <div className="mb-4 text-base text-gray-900">
                <span className="font-semibold">{prettyDate}</span>
                {params.facilityName ? (
                    <>
                        &nbsp; at{' '}
                        <span className="font-semibold">
                            {params.facilityName}
                        </span>
                    </>
                ) : null}
            </div>

            <div className="my-4 flex items-center gap-3">
                <div className="leading-tight">
                    <div className="text-base font-semibold">
                        {params.trainer || '—'}
                    </div>
                </div>
            </div>

            <div className="mb-2 text-sm text-gray-500">
                {params.classType === 'bookings'
                    ? 'Booking Details'
                    : 'Personal Appointment Details'}
            </div>

            <div className="mb-5 rounded-lg border border-gray-200 p-4">
                <div className="mb-2 font-semibold">{title}:</div>
                <div className="text-sm text-gray-800">
                    {htmlToPlainText(parent?.description) ?? ''}
                </div>

                <div className="mt-4 space-y-1 text-sm">
                    <div>
                        <span className="font-semibold">Duration:</span>{' '}
                        {typeof params.durationInMinutes === 'number'
                            ? `${params.durationInMinutes} min`
                            : '—'}
                    </div>
                    {/* <div>
            <span className="font-semibold">Service Type (categoryId):</span>{' '}
            {params.serviceCategoryId || '—'}
          </div>
          <div>
            <span className="font-semibold">Subtype (subTypeId):</span>{' '}
            {params.subTypeId || '—'}
          </div>
          <div>
            <span className="font-semibold">Trainer/Room (id):</span>{' '}
            {params.trainerId || '—'}
          </div> */}
                </div>
            </div>

            <div className="flex gap-3">
                <button
                    onClick={onBookClick}
                    className="flex-1 rounded-lg border border-black bg-[#17336f] py-3 text-white hover:opacity-90 active:opacity-100"
                    // style={{ backgroundColor: branding?.primaryColor || '#000000' }}
                >
                    Book
                </button>
            </div>
        </div>
    );
}
