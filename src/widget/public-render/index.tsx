// App.tsx (the file where you set up routes)
import React, { useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import widgetStore from '~/redux/widget-store';
import { message } from 'antd';
import { Router, Route, Switch, Redirect } from 'wouter';
import useHashedLocation from '../router/useHashedLocation';

import ServiceListing from './ServiceListing';
import ScheduleTab from './ScheduleTab'; 
import './widget.css';
import SlotDetails from './SlotDetails';
import BookingSuccess from './TrialBookingFLow/BookingSuccess';

const widgetBrandingConfig: Record<string, any> = {
  test123: { primaryColor: '#4CAF50', fontFamily: "'Roboto', sans-serif", borderRadius: '12px', backgroundColor: '#f9f9f9' },
  mygym456: { primaryColor: '#FF5722', fontFamily: "'Poppins', sans-serif", borderRadius: '8px', backgroundColor: '#ffffff' },
};
const defaultBranding = { primaryColor: '#1A3353', fontFamily: "'Arial', sans-serif", borderRadius: '10px', backgroundColor: '#ffffff' };

function useHashQuery() {
  if (typeof window === 'undefined') return new URLSearchParams();
  const hash = window.location.hash || '';
  const i = hash.indexOf('?');
  return i >= 0 ? new URLSearchParams(hash.slice(i + 1)) : new URLSearchParams();
}

const App = ({ widgetId, branding }: { widgetId: string; branding: any }) => {
  useEffect(() => {
    if (!window.location.hash) window.location.hash = '/services';
  }, []);

  const containerStyle: React.CSSProperties = {
    fontFamily: branding.fontFamily,
    backgroundColor: branding.backgroundColor,
    borderRadius: branding.borderRadius,
    color: '#222',
  };

  return (
    <div style={containerStyle} className="mx-auto sm:p-0 lg:w-[80%]">
      <Router hook={useHashedLocation}>
        <Switch>
          <Route path="/services">
            <ServiceListing organizationId={widgetId} branding={branding} />
          </Route>

          <Route path="/schedule">
            {() => {
              const qs = useHashQuery();
              return (
                <ScheduleTab
                  branding={branding}
                  widgetId={widgetId}
                  classType="personalAppointment"
                  subId={qs.get('subId') || undefined}
                  subTitle={qs.get('title') || undefined}
                />
              );
            }}
          </Route>

          <Route path="/booking">
            {() => {
              const qs = useHashQuery();
              return (
                <ScheduleTab
                  branding={branding}
                  widgetId={widgetId}
                  classType="bookings"
                  subId={qs.get('subId') || undefined}
                  subTitle={qs.get('title') || undefined}
                />
              );
            }}
          </Route>
          <Route path="/booking/success">
            <BookingSuccess homePath="/services" />
          </Route>
          <Route path="/slot-details">
            <SlotDetails branding={branding} organizationId={widgetId} />
          </Route>
          <Route>
            <Redirect to="/services" />
          </Route>
        </Switch>
      </Router>
    </div>
  );
};

const root = document.getElementById('hop-widget-root');
const widgetId = (root as HTMLElement)?.dataset?.widgetId;
const branding = widgetBrandingConfig[widgetId || ''] || defaultBranding;

if (root && widgetId) {
  message.config({ duration: 2, maxCount: 3, top: 100 });
  ReactDOM.createRoot(root).render(
    <Provider store={widgetStore}>
      <App widgetId={widgetId} branding={branding} />
    </Provider>
  );
} else {
  console.error('❌ Widget root not found or widgetId missing');
}
