// ScheduleTab.tsx
import React, { useEffect, useMemo, useState } from 'react';
import {
  startOfWeek,
  addDays,
  format,
  isSameDay,
  subWeeks,
  addWeeks,
  startOfDay,
} from 'date-fns';

import { Spin } from 'antd';
import {
  widgetFacilityList,
  widgetServiceType,
  getTrainerAvailability, // PA API
  getBookingRoomAvailability, // Booking API
} from '~/redux/actions/widget/widget.action';
import { useLoader } from '~/hooks/useLoader';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import PAFilterBar from './filters/PAFilterBar';
import type { SubtypeGroup, SubtypeValue } from './filters/SubTypeFilter';
import type { FacilityOption } from './filters/FacilityFilter';
import SlotDetails, { SlotParams } from './SlotDetails';
import {
  useWidgetDispatch,
  useWidgetSelector,
} from '~/hooks/widget-redux-hooks';
import { selectSelectedService } from '~/redux/slices/widget/selectedService.slice';

type Branding = { fontFamily?: string; primaryColor?: string };
type ClassType = 'personalAppointment' | 'bookings';
type AnyObj = Record<string, any>;

/* ---------------- helpers ---------------- */

const pickServiceId = (x: AnyObj) =>
  String(x._id ?? x.id ?? x.slug ?? x.key ?? x.serviceId ?? x.value ?? '');
const pickServiceName = (x: AnyObj) =>
  String(x.name ?? x.title ?? x.displayName ?? x.serviceName ?? 'Unnamed');
const pickSubtypeId = (x: AnyObj) =>
  String(
    x._id ??
      x.id ??
      x.slug ??
      x.key ??
      x.appointmentTypeId ??
      x.subtypeId ??
      x.value ??
      '',
  );
const pickSubtypeName = (x: AnyObj) =>
  String(
    x.name ??
      x.title ??
      x.displayName ??
      x.appointmentTypeName ??
      x.subtypeName ??
      x.label ??
      'Untitled',
  );
function extractSubtypesFlexible(m: AnyObj): AnyObj[] {
  const candidates: any[] = [
    m.appointmentType,
    m.appointmentTypes,
    m.subTypes,
    m.subtypes,
    m.subtypeList,
    m.types,
    m.children,
  ];
  for (const c of candidates) if (Array.isArray(c) && c.length) return c;
  for (const v of Object.values(m)) {
    if (Array.isArray(v) && v.length && typeof v[0] === 'object')
      return v as AnyObj[];
  }
  return [];
}
const pickFacilityId = (x: AnyObj) =>
  String(
    x._id ??
      x.id ??
      x.facilityId ??
      x.value ??
      x.slug ??
      x.key ??
      x.code ??
      x.uuid ??
      '',
  );
const pickFacilityName = (x: AnyObj) =>
  String(
    x.name ??
      x.title ??
      x.facilityName ??
      x.displayName ??
      x.label ??
      'Unnamed Facility',
  );

const toIsoMidnightZ = (d: Date) => `${format(d, 'yyyy-MM-dd')}T00:00:00Z`;
const toMinutes = (hhmm: string) => {
  const [h, m] = (hhmm || '').split(':').map(Number);
  if (isNaN(h) || isNaN(m)) return Number.MAX_SAFE_INTEGER;
  return h * 60 + m;
};

/* ---------- normalizers ---------- */
// PA
function normalizeFromPAApi(
  apiData: any,
  facilityName: string,
  serviceCategory?: string,
  currentSubTypeId?: string,
  currentSubTypeName?: string,
) {
  const trainers: any[] = Array.isArray(apiData?.trainers)
    ? apiData.trainers
    : [];
  const items: any[] = [];
  const defaultAvatar = 'https://staginghop.hkstest.uk/assets/Profile_icon.png';

  for (const tr of trainers) {
    const trainerId = String(tr?.trainerId ?? '');
    const trainerName = String(tr?.trainerName ?? '');
    const schedules = Array.isArray(tr?.schedule) ? tr.schedule : [];
    for (const sch of schedules) {
      const timeSlots = Array.isArray(sch?.timeSlots) ? sch.timeSlots : [];
      for (const slot of timeSlots) {
        if (!slot?.isAvailable) continue;
        items.push({
          trainer: trainerName,
          trainerId,
          image: defaultAvatar,
          facilityId: tr?.facilityId,
          location: facilityName || 'Facility',
          date: sch?.date ?? apiData?.startDate,
          from: slot?.from,
          to: slot?.to,
          status: 'available',
          classType: 'personalAppointment',
          subtypeName: currentSubTypeName || '',
          durationInMinutes: Number(slot?.durationInMinutes ?? ''),
          serviceName: '',
          payRateId: Array.isArray(slot?.payRateIds) ? slot.payRateIds[0] : undefined,
          serviceCategory,
          subTypeId: currentSubTypeId,
          Description: '',
        });
      }
    }
  }
  items.sort((a, b) => toMinutes(a.from) - toMinutes(b.from));
  return items;
}

// Rooms
function normalizeFromRoomsApi(apiData: any, facilityName: string, subtypeName?: string) {
  const rooms: any[] = Array.isArray(apiData?.rooms) ? apiData.rooms : [];
  const out: any[] = [];
  const defaultAvatar = 'https://staginghop.hkstest.uk/assets/Profile_icon.png';

  for (const r of rooms) {
    const roomId = String(r?.roomId ?? '');
    const roomName = String(r?.roomName ?? 'Room');
    const capacity = Number(r?.capacity ?? 1);
    const schedule = Array.isArray(r?.schedule) ? r.schedule : [];

    for (const s of schedule) {
      const dur =
        toMinutes(s?.to) - toMinutes(s?.from) > 0
          ? toMinutes(s.to) - toMinutes(s.from)
          : apiData?.durationInMinutes ?? '';

      out.push({
        trainer: roomName,
        trainerId: roomId,
        image: defaultAvatar,
        facilityId: apiData?.facilityId,
        location: facilityName || 'Facility',
        date: apiData?.date,
        from: s?.from,
        to: s?.to,
        status: 'available',
        classType: 'bookings',
        subtypeName: subtypeName || '',
        durationInMinutes: dur,
        serviceName: '',
        payRateId: undefined,
        serviceCategory: apiData?.serviceCategoryId,
        subTypeId: apiData?.subTypeId,
        Description: `Capacity: ${capacity}`,
      });
    }
  }
  out.sort((a, b) => toMinutes(a.from) - toMinutes(b.from));
  return out;
}

/* =========================== Unified component =========================== */
export default function ScheduleTab({
  branding,
  widgetId,
  classType, // 'personalAppointment' | 'bookings'
  subId, // optional preselected subtype id
  subTitle, // optional preselected subtype name
  onSelectSlot, // optional external handler
}: {
  branding: Branding;
  widgetId: string;
  classType: ClassType;
  subId?: string;
  subTitle?: string;
  onSelectSlot?: (slot: any) => void;
}) {
  const dispatch = useWidgetDispatch();
  const selectedService = useWidgetSelector(selectSelectedService);

  const today = useMemo(() => new Date(), []);
  const [currentWeekStart, setCurrentWeekStart] = useState(
    startOfWeek(today, { weekStartsOn: 1 }),
  );
  const [selectedDate, setSelectedDate] = useState(today);

  const [scheduleData, setScheduleData] = useState<any[]>([]);
  const [loader, startLoader, endLoader] = useLoader();

  // NEW: Track data status to control spinner vs. empty text
  const [dataStatus, setDataStatus] = useState<'loading' | 'success' | 'empty'>('loading');

  // Is this slot already in the past?
  const isBookingClosed = (slot: any) => {
    const now = new Date();

    // If selected day is before today → closed
    if (selectedDate < startOfDay(now)) return true;

    // If selected day is after today → open
    if (!isSameDay(selectedDate, now)) return false;

    // Same day: compare HH:mm
    const nowMinutes = now.getHours() * 60 + now.getMinutes();
    return nowMinutes >= toMinutes(slot.from);
  };

  // filters
  const [apiGroups, setApiGroups] = useState<SubtypeGroup[]>([]);
  const [subtypes, setSubtypes] = useState<SubtypeValue[]>([]);
  const [svcLoading, setSvcLoading] = useState<boolean>(false);

  const [facilityOptions, setFacilityOptions] = useState<FacilityOption[]>([]);
  const [selectedFacility, setSelectedFacility] = useState<FacilityOption | null>(null);
  const [facLoading, setFacLoading] = useState<boolean>(false);

  // local detail-view state
  const [view, setView] = useState<'list' | 'detail'>('list');
  const [detailParams, setDetailParams] = useState<SlotParams | null>(null);

  // week nav
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeekStart, i));
  const goToPreviousWeek = () => {
    const ns = subWeeks(currentWeekStart, 1);
    setCurrentWeekStart(ns);
    setSelectedDate(ns);
  };
  const goToNextWeek = () => {
    const ns = addWeeks(currentWeekStart, 1);
    setCurrentWeekStart(ns);
    setSelectedDate(ns);
  };

  /* ---- load services/subtypes ---- */
  useEffect(() => {
    if (!widgetId) return;
    let alive = true;
    (async () => {
      try {
        setSvcLoading(true);
        const raw = await dispatch(
          widgetServiceType({
            organizationId: widgetId,
            classType: [classType],
          }),
        ).unwrap();
        const key = classType === 'bookings' ? 'bookings' : 'personalAppointment';
        const data = (raw?.res?.data ?? raw) as Partial<Record<typeof key, AnyObj[]>>;
        const list = (data?.[key] ?? []).filter((m) => m?.isActive ?? true);

        const groups: SubtypeGroup[] = list.map((m) => {
          const svcId = pickServiceId(m);
          const subs = extractSubtypesFlexible(m);
          const opts: SubtypeValue[] = Array.isArray(subs)
            ? subs
                .map((s) => ({
                  label: pickSubtypeName(s),
                  value: pickSubtypeId(s),
                }))
                .filter((o) => o.value)
            : [];
          return {
            label: pickServiceName(m),
            options: opts,
            value: svcId as any,
          };
        });

        if (!alive) return;
        setApiGroups(groups);

        if (subId) {
          const all = groups.flatMap((g) => g.options);
          const match = all.find((o) => String(o.value) === String(subId));
          if (match) setSubtypes([match]);
        }
      } catch (err) {
        if (alive) setApiGroups([]);
        console.error('Failed to load services/subtypes', err);
      } finally {
        if (alive) setSvcLoading(false);
      }
    })();
    return () => {
      alive = false;
    };
  }, [widgetId, classType, subId, dispatch]);

  const subtypeGroups = useMemo(() => apiGroups, [apiGroups]);

  /* ---- load facilities ---- */
  useEffect(() => {
    if (!widgetId) return;
    let alive = true;
    (async () => {
      try {
        setFacLoading(true);
        const raw = await dispatch(widgetFacilityList(widgetId)).unwrap();
        const arr: AnyObj[] = (raw?.data?.data ?? raw ?? []) as AnyObj[];

        const opts: FacilityOption[] = Array.isArray(arr)
          ? arr
              .map((x) => ({
                label: pickFacilityName(x),
                value: pickFacilityId(x),
              }))
              .filter((o) => o.value || o.label)
          : [];

        if (!alive) return;
        setFacilityOptions(opts);
        if (opts.length) setSelectedFacility((prev) => prev ?? opts[0]);
      } catch (err) {
        if (!alive) return;
        console.error('Failed to load facility list', err);
        setFacilityOptions([]);
        setSelectedFacility(null);
      } finally {
        if (alive) setFacLoading(false);
      }
    })();
    return () => {
      alive = false;
    };
  }, [widgetId, dispatch]);

  /* ---- resolve serviceCategoryId from selected subtype ---- */
  const resolveServiceCategoryId = (): string | undefined => {
    const chosenSubId = subtypes[0]?.value ?? subId;
    if (chosenSubId) {
      const host = subtypeGroups.find((g) =>
        g.options?.some((o) => String(o.value) === String(chosenSubId)),
      );
      if (host?.value) return String(host.value);
    }
    return undefined;
  };

  /* ---- fetch availability ---- */
  useEffect(() => {
    let alive = true;

    (async () => {
      const needSelections = !selectedFacility?.value || (!subtypes[0]?.value && !subId);

      // If subtype/facility missing: show a blocking loader (no text)
      if (needSelections) {
        if (alive) {
          setScheduleData([]);
          setDataStatus('loading');
        }
        return;
      }

      // If we’re showing details, don’t refetch list behind the scenes unless date/facility/subtype changes
      if (view === 'detail') return;

      setDataStatus('loading');
      startLoader();
      try {
        const serviceCategoryId = resolveServiceCategoryId();
        const chosenSubId = (subtypes[0]?.value ?? subId) as string | undefined;
        const chosenSubName = (subtypes[0]?.label ?? subTitle) as string | undefined;

        if (!serviceCategoryId || !chosenSubId) {
          if (alive) {
            setScheduleData([]);
            setDataStatus('loading'); // keep loader if inputs are incomplete
          }
          return;
        }

        if (classType === 'personalAppointment') {
          const start = toIsoMidnightZ(selectedDate);
          const payload = {
            organizationId: widgetId,
            facilityIds: [String(selectedFacility.value)],
            startDate: start,
            endDate: start,
            classType: 'personalAppointment',
            serviceCategoryId,
            subTypeId: chosenSubId,
          };
          const res: any = await dispatch(getTrainerAvailability(payload)).unwrap();
          const body = res?.data ?? res;

          const flat = normalizeFromPAApi(
            body,
            selectedFacility?.label || '',
            serviceCategoryId,
            chosenSubId,
            chosenSubName,
          );
          if (alive) {
            setScheduleData(flat);
            setDataStatus(flat.length > 0 ? 'success' : 'empty');
          }
        } else {
          const payload = {
            organizationId: widgetId,
            facilityId: String(selectedFacility.value),
            serviceCategoryId,
            subTypeId: chosenSubId,
            date: format(selectedDate, 'yyyy-MM-dd'),
          };
          const res: any = await dispatch(getBookingRoomAvailability(payload)).unwrap();
          const body = res?.data ?? res;

          const flat = normalizeFromRoomsApi(body, selectedFacility?.label || '', chosenSubName);
          if (alive) {
            setScheduleData(flat);
            setDataStatus(flat.length > 0 ? 'success' : 'empty');
          }
        }
      } catch (err) {
        if (alive) {
          setScheduleData([]);
          setDataStatus('empty'); // after API error, show empty state
        }
        console.error('Error fetching availability:', err);
      } finally {
        if (alive) endLoader();
      }
    })();

    return () => {
      alive = false;
    };
  }, [
    classType,
    selectedDate,
    selectedFacility?.value,
    JSON.stringify(subtypes),
    subId,
    view, // re-run when switching back to list
    dispatch,
  ]);

  const clearAll = () => {
    setSubtypes([]);
    setSelectedFacility(facilityOptions[0] || null);
  };

  const visibleSchedule = scheduleData;

  // ====== handleBook -> render <SlotDetails> and pass props ======
  const handleBook = (item: any) => {
    if (onSelectSlot) {
      onSelectSlot(item);
      return;
    }

    const params: SlotParams = {
      classType,
      trainerId: item.trainerId,
      trainer: item.trainer,
      facilityId: String(item.facilityId ?? selectedFacility?.value ?? ''),
      facilityName: item.location || selectedFacility?.label || '',
      date: format(selectedDate, 'yyyy-MM-dd'),
      from: item.from,
      to: item.to,
      durationInMinutes: item.durationInMinutes ?? undefined,
      serviceCategoryId: (item.serviceCategory || resolveServiceCategoryId()) ?? undefined,
      subTypeId: item.subTypeId ?? (subtypes[0]?.value as string | undefined) ?? undefined,
      subtypeName: item.subtypeName ?? (subtypes[0]?.label as string | undefined) ?? undefined,
      image: item.image || undefined,
      payRateId: item.payRateId || undefined,
    };

    setDetailParams(params);
    setView('detail');
  };

  const handleBackFromDetails = () => {
    setView('list');
    setDetailParams(null);
  };

  // ====== RENDER ======
  if (view === 'detail' && detailParams) {
    return (
      <div style={{ fontFamily: branding.fontFamily }}>
        <SlotDetails
          branding={branding}
          organizationId={widgetId}
          initialParams={detailParams}
          onBack={handleBackFromDetails}
        />
      </div>
    );
  }

  const isPastCalendarDate = (d: Date, t = new Date()) =>
    startOfDay(d).getTime() < startOfDay(t).getTime();

  const isPrevWeekAllPast = (weekStart: Date) =>
    startOfDay(addDays(subWeeks(weekStart, 1), 6)) < startOfDay(new Date());

  const dayCellClasses = (selected: boolean, disabled: boolean) =>
    [
      'text-center select-none lg:min-w-16  lg:px-2 pt-1 pb-2',
      'border-b',
      selected ? 'border-b-2' : 'border-transparent',
      disabled ? 'opacity-45 cursor-default pointer-events-none' : 'cursor-pointer',
    ].join(' ');

  return (
    <div style={{ fontFamily: branding.fontFamily }}>
      {/* Filter bar */}
      <div style={{ marginBottom: 8 }}>
        <div className="flex items-center justify-center  lg:justify-between " style={{ marginBottom: 16 }}>
          <PAFilterBar
            subtypes={subtypeGroups}
            selectedSubtypes={subtypes}
            onSubtypesChange={setSubtypes}
            facilities={facilityOptions}
            selectedFacility={selectedFacility}
            onFacilityChange={setSelectedFacility}
            onClearAll={clearAll}
            loading={svcLoading}
            facilitiesLoading={facLoading}
          />
        </div>
      </div>

      {/* Week selector */}
      <div className="mb-4 flex items-center justify-start  lg:justify-center lg:gap-2 ">
        <ArrowLeftOutlined
          onClick={() => {
            if (!isPrevWeekAllPast(currentWeekStart)) goToPreviousWeek();
          }}
          className={[
            ' rounded-md text-[20px] leading-[20px] lg:p-1.5 ',
            isPrevWeekAllPast(currentWeekStart) ? 'cursor-default opacity-40' : 'cursor-pointer',
          ].join(' ')}
          aria-label="Previous week"
        />

        <div className="flex flex-1 justify-around ">
          {weekDays.map((date, idx) => {
            const selected = isSameDay(date, selectedDate);
            const disabled = isPastCalendarDate(date);

            const topText = disabled ? 'text-gray-400' : selected ? '' : 'text-gray-500';
            const numText = disabled ? 'text-gray-400' : selected ? '' : 'text-gray-900';
            const monthText = disabled ? 'text-gray-400' : selected ? '' : 'text-gray-500';

            return (
              <div
                key={idx}
                role="button"
                aria-disabled={disabled}
                onClick={() => {
                  if (!disabled) setSelectedDate(date);
                }}
                className={dayCellClasses(selected, disabled)}
                style={selected ? { borderBottomColor: branding.primaryColor } : undefined}
                title={format(date, 'EEEE, d MMM yyyy')}
              >
                <div
                  className={['text-[12px] font-bold uppercase', topText].join(' ')}
                  style={selected ? { color: branding.primaryColor } : undefined}
                >
                  {format(date, 'EEE')}
                </div>

                <div
                  className={['mt-[2px] text-[20px] font-bold leading-[22px]', numText].join(' ')}
                  style={selected ? { color: branding.primaryColor } : undefined}
                >
                  {format(date, 'd')}
                </div>

                <div
                  className={['mt-[2px] text-[11px] font-bold uppercase', monthText].join(' ')}
                  style={selected ? { color: branding.primaryColor } : undefined}
                >
                  {format(date, 'MMM')}
                </div>
              </div>
            );
          })}
        </div>

        <ArrowRightOutlined
          onClick={goToNextWeek}
          className=" cursor-pointer rounded-md text-[20px] leading-[20px] lg:p-1.5 "
          aria-label="Next week"
        />
      </div>

      {/* LIST view */}
      <Spin spinning={loader || dataStatus === 'loading'}>
        <div className="border-t sm:border-[#999] lg:border-[#eee]" style={{ minHeight: 220 }}>
          {dataStatus === 'success' &&
            visibleSchedule.map((item, index) => (
              <div
                key={index}
                className="flex flex-col gap-4 border-b border-[#ddd] px-0 py-6 sm:gap-6 sm:px-6 lg:flex-row lg:items-center lg:justify-between lg:px-4"
              >
                {/* Time + Avatar + Info */}
                <div className="flex w-full flex-row gap-4 sm:items-center sm:justify-between lg:w-[65%]">
                  <div className="w-[30%] lg:w-[20%]">
                    <div className="text-base font-semibold lg:text-lg">
                      {item.from} - {item.to}
                    </div>
                    {item.durationInMinutes !== '' && (
                      <div className="text-sm text-gray-500 lg:text-base">
                        {item.durationInMinutes}-Min
                      </div>
                    )}
                  </div>
                  <div className="order-1 flex w-[70%] flex-1 items-center gap-3 lg:w-[50%] lg:justify-around">
                    <img
                      src={
                        selectedService?.sub?.image ||
                        'https://staginghop.hkstest.uk/assets/Profile_icon.png'
                      }
                      alt="avatar"
                      className="h-10 w-10 rounded-full object-cover sm:h-16 sm:w-16"
                    />
                    <div className="pe-0 lg:pe-10">
                      <div className="text-sm font-semibold lg:text-base">
                        {item.subtypeName ||
                          (classType === 'bookings' ? 'Booking' : 'Personal Appointment')}
                      </div>
                      <div className="text-sm text-gray-500 lg:text-base">{item.trainer}</div>
                    </div>
                  </div>
                </div>

                {/* Location + Book Now */}
                <div className="flex w-full flex-row items-center justify-between gap-3 lg:w-[35%] ">
                  <div className="text-center text-base font-semibold text-gray-700 sm:text-left lg:text-lg">
                    {item.location}
                  </div>
                  {isBookingClosed(item) ? (
                    <span className="cursor-not-allowed select-none rounded-md border border-[#ccc] px-4 py-1 text-base text-[#999]">
                      Booking closed
                    </span>
                  ) : (
                    <button
                      type="button"
                      className="rounded-md border border-black px-4 py-1 text-base text-black"
                      onClick={() => handleBook(item)}
                    >
                      Book
                    </button>
                  )}
                </div>
              </div>
            ))}

          {dataStatus === 'empty' && (
            <div style={{ textAlign: 'center', color: '#999', padding: '24px 0' }}>
              No sessions for this day.
            </div>
          )}
          {/* No “Select a subtype…” message anymore */}
        </div>
      </Spin>
    </div>
  );
}
