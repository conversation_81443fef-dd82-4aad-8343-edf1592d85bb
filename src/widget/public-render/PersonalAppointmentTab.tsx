import React, { useEffect, useMemo, useState } from 'react';
import {
  startOfWeek,
  addDays,
  format,
  isSameDay,
  subWeeks,
  addWeeks,
} from 'date-fns';
import { <PERSON><PERSON>, Spin } from 'antd';
import { useAppDispatch } from '~/hooks/redux-hooks';
import {
  getTrainerAvailability,
  widgetFacilityList,
  widgetServiceType,
} from '~/redux/actions/widget/widget.action';
import { useLoader } from '~/hooks/useLoader';
import TrialBookingFlow from './TrialBookingFLow/TrialBookingFLow';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { useLocation } from 'wouter';
import PAFilterBar from './filters/PAFilterBar';
import type { SubtypeGroup, SubtypeValue } from './filters/SubTypeFilter';
import type { FacilityOption } from './filters/FacilityFilter';

type Branding = { fontFamily?: string; primaryColor?: string };

// ---- helpers ----
function parseDateSafe(isoLike?: string): Date | null {
  if (!isoLike) return null;
  const d = new Date(`${isoLike}T00:00:00`);
  return isNaN(d.getTime()) ? null : d;
}
function useHashQuery() {
  if (typeof window === 'undefined') return new URLSearchParams();
  const hash = window.location.hash || '';
  const i = hash.indexOf('?');
  return i >= 0 ? new URLSearchParams(hash.slice(i + 1)) : new URLSearchParams();
}
const toIsoMidnightZ = (d: Date) => `${format(d, 'yyyy-MM-dd')}T00:00:00Z`;

// Very tolerant pickers (services/subtypes)
type AnyObj = Record<string, any>;
type ApiMainType = AnyObj;
const pickServiceId = (x: AnyObj) =>
  String(x._id ?? x.id ?? x.slug ?? x.key ?? x.serviceId ?? x.value ?? '');
const pickServiceName = (x: AnyObj) =>
  String(x.name ?? x.title ?? x.displayName ?? x.serviceName ?? 'Unnamed');
const pickSubtypeId = (x: AnyObj) =>
  String(x._id ?? x.id ?? x.slug ?? x.key ?? x.appointmentTypeId ?? x.subtypeId ?? x.value ?? '');
const pickSubtypeName = (x: AnyObj) =>
  String(
    x.name ??
      x.title ??
      x.displayName ??
      x.appointmentTypeName ??
      x.subtypeName ??
      x.label ??
      'Untitled'
  );
function extractSubtypesFlexible(m: AnyObj): AnyObj[] {
  const candidates: any[] = [
    m.appointmentType,
    m.appointmentTypes,
    m.subTypes,
    m.subtypes,
    m.subtypeList,
    m.types,
    m.children,
  ];
  for (const c of candidates) if (Array.isArray(c) && c.length) return c;
  for (const v of Object.values(m)) {
    if (
      Array.isArray(v) &&
      v.length &&
      typeof v[0] === 'object' &&
      (('name' in v[0]) ||
        ('title' in v[0]) ||
        ('displayName' in v[0]) ||
        ('appointmentTypeName' in v[0]) ||
        ('subtypeName' in v[0]) ||
        ('label' in v[0]))
    )
      return v as AnyObj[];
  }
  return [];
}

// Facility pickers (tolerant)
const pickFacilityId = (x: AnyObj) =>
  String(x._id ?? x.id ?? x.facilityId ?? x.value ?? x.slug ?? x.key ?? x.code ?? x.uuid ?? '');
const pickFacilityName = (x: AnyObj) =>
  String(x.name ?? x.title ?? x.facilityName ?? x.displayName ?? x.label ?? 'Unnamed Facility');

// ---------- list normalizer for your current API (res.data) ----------
const toMinutes = (hhmm: string) => {
  const [h, m] = (hhmm || '').split(':').map(Number);
  if (isNaN(h) || isNaN(m)) return Number.MAX_SAFE_INTEGER;
  return h * 60 + m;
};

function normalizeFromTrainersApi(
  apiData: any,
  selectedFacilityName: string,
  currentServiceCategory?: string,
  currentSubTypeId?: string,
  currentSubTypeName?: string
) {
  const trainers: any[] = Array.isArray(apiData?.trainers) ? apiData.trainers : [];
  const items: any[] = [];

  for (const tr of trainers) {
    const trainerId = String(tr?.trainerId ?? '');
    const trainerName = String(tr?.trainerName ?? '');
    const facilityId = String(tr?.facilityId ?? '');

    const schedules = Array.isArray(tr?.schedule) ? tr.schedule : [];
    for (const sch of schedules) {
      const dateISO = String(sch?.date ?? apiData?.startDate ?? '');
      const timeSlots = Array.isArray(sch?.timeSlots) ? sch.timeSlots : [];
      for (const slot of timeSlots) {
        if (!slot?.isAvailable) continue;

        const payRateId =
          Array.isArray(slot?.payRateIds) && slot.payRateIds.length ? slot.payRateIds[0] : undefined;

        items.push({
          trainer: trainerName,
          trainerId,
          image: '',
          facilityId,
          location: selectedFacilityName || 'Facility',
          date: dateISO,
          from: slot?.from,
          to: slot?.to,
          status: 'available',
          privacy: undefined,
          classType: 'personalAppointment',
          subtypeName: currentSubTypeName || '',
          durationInMinutes: Number(slot?.durationInMinutes ?? ''),
          serviceName: '',
          payRateId,
          pricing: undefined,
          serviceCategory: currentServiceCategory,
          subTypeId: currentSubTypeId,
          Description: '',
        });
      }
    }
  }
  items.sort((a, b) => toMinutes(a.from) - toMinutes(b.from));
  return items;
}

// --- component ---
export default function PersonalAppointmentTab({
  branding,
  widgetId,
  subId,       // optional override (appointmentType)
  subTitle,    // optional override
}: {
  branding: Branding;
  widgetId: string;
  subId?: string;
  subTitle?: string;
}) {
  const dispatch = useAppDispatch();
  const [, setLocation] = useLocation();

  const qs = useHashQuery();
  const initialSvcId = qs.get('parentId') || '';   // service type id from service listing
  const qsSubId = qs.get('subId') || '';           // appointmentType id from service listing
  const initialSubId = subId ?? qsSubId;

  const today = new Date();
  const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(today, { weekStartsOn: 1 }));
  const [selectedDate, setSelectedDate] = useState(today);

  const [scheduleData, setScheduleData] = useState<any[]>([]);
  const [loader, startLoader, endLoader] = useLoader();
  const [selectedSlot, setSelectedSlot] = useState<any | null>(null);

  // Filters state
  const [apiGroups, setApiGroups] = useState<SubtypeGroup[]>([]);
  const [subtypes, setSubtypes] = useState<SubtypeValue[]>([]);
  const [svcLoading, setSvcLoading] = useState<boolean>(false);

  const [facilityOptions, setFacilityOptions] = useState<FacilityOption[]>([]);
  const [selectedFacility, setSelectedFacility] = useState<FacilityOption | null>(null);
  const [facLoading, setFacLoading] = useState<boolean>(false);

  // Broadcast date (optional)
  const containerEl = useMemo<HTMLElement | null>(() => {
    return (
      (document.getElementById('hop-widget-root') as HTMLElement | null) ||
      (document.querySelector('[data-hop-widget]') as HTMLElement | null)
    );
  }, []);
  const emitDate = () => {
    const filters: Record<string, any> = { date: format(selectedDate, 'yyyy-MM-dd') };
    containerEl?.dispatchEvent(new CustomEvent('hop-filter-change', { detail: { mode: 'live', filters } }));
  };

  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeekStart, i));
  const goToPreviousWeek = () => { const ns = subWeeks(currentWeekStart, 1); setCurrentWeekStart(ns); setSelectedDate(ns); };
  const goToNextWeek = () => { const ns = addWeeks(currentWeekStart, 1); setCurrentWeekStart(ns); setSelectedDate(ns); };

  useEffect(() => {
    const initial = (window as any)?.HopWidget?.config?.initialFilters || {};
    if (initial?.date) {
      const parsed = parseDateSafe(initial.date);
      if (parsed) { setSelectedDate(parsed); setCurrentWeekStart(startOfWeek(parsed, { weekStartsOn: 1 })); }
    }
  }, []);
  useEffect(() => { emitDate(); }, [selectedDate]); // keep external listeners updated

  // --- Services + Subtypes (for the filter UI) ---
  useEffect(() => {
    if (!widgetId) return;
    let alive = true;
    (async () => {
      try {
        setSvcLoading(true);
        const classType = ['personalAppointment'];
        const raw = await dispatch(widgetServiceType({ organizationId: widgetId, classType })).unwrap();
        const data = (raw?.res?.data ?? raw) as Partial<{ personalAppointment: ApiMainType[] }>;
        const nextPA = (data?.personalAppointment ?? []).filter((m) => (m?.isActive ?? true));

        const groups: SubtypeGroup[] = nextPA.map((m) => {
          const svcId = pickServiceId(m);
          const subs = extractSubtypesFlexible(m);
          const opts: SubtypeValue[] = Array.isArray(subs)
            ? subs.map((s) => ({ label: pickSubtypeName(s), value: pickSubtypeId(s) })).filter((o) => o.value)
            : [];
          return { label: pickServiceName(m), options: opts, value: svcId as any };
        });

        if (!alive) return;
        setApiGroups(groups);

        // Preselect subtype from ALL groups if subId provided
        if (initialSubId) {
          const allOpts = groups.flatMap((g) => g.options);
          const match = allOpts.find((o) => String(o.value) === String(initialSubId));
          if (match) setSubtypes([match]);
        }
      } catch (err) {
        if (alive) { console.error(err || 'Failed to load services/subtypes'); setApiGroups([]); }
      } finally {
        if (alive) setSvcLoading(false);
      }
    })();
    return () => { alive = false; };
  }, [widgetId, initialSubId, dispatch]);

  const subtypeGroups: SubtypeGroup[] = useMemo(() => apiGroups, [apiGroups]);

  // --- Facilities ---
  useEffect(() => {
    if (!widgetId) return;
    let alive = true;
    (async () => {
      try {
        setFacLoading(true);
        const raw = await dispatch(widgetFacilityList(widgetId)).unwrap();
        const arr: AnyObj[] = (raw?.data?.data ?? raw ?? []) as AnyObj[];

        const opts: FacilityOption[] = Array.isArray(arr)
          ? arr
            .map((x) => ({ label: pickFacilityName(x), value: pickFacilityId(x) }))
            .filter((o) => o.value || o.label)
          : [];

        if (!alive) return;
        setFacilityOptions(opts);
        if (opts.length) setSelectedFacility((prev) => prev ?? opts[0]);
      } catch (err) {
        if (!alive) return;
        console.error(err || 'Failed to load facility list');
        setFacilityOptions([]);
        setSelectedFacility(null);
      } finally {
        if (alive) setFacLoading(false);
      }
    })();
    return () => { alive = false; };
  }, [widgetId, dispatch]);

  // --- Resolve IDs for API payload from current selections ---
  const resolveServiceCategoryId = (): string | undefined => {
    // Find the group (service) that contains the selected subtype
    const chosenSubId = subtypes[0]?.value ?? initialSubId;
    if (chosenSubId) {
      const host = subtypeGroups.find((g) => g.options?.some((o) => String(o.value) === String(chosenSubId)));
      if (host?.value) return String(host.value);
    }
    return initialSvcId || undefined;
  };

  // --- Fetch availability (LIST view only) ---
  useEffect(() => {
    let alive = true;

    (async () => {
      // Guard early if no facility selected
      if (!selectedFacility?.value) {
        setScheduleData([]);
        return;
      }

      startLoader();
      try {
        const start = toIsoMidnightZ(selectedDate);
        const facilityIds = [String(selectedFacility.value)];
        const currentSubTypeId = (subtypes[0]?.value ?? initialSubId) || undefined;
        const currentSubTypeName = (subtypes[0]?.label ?? subTitle) || undefined;
        const serviceCategoryId = resolveServiceCategoryId();

        const payload = {
          organizationId: widgetId,
          facilityIds,
          startDate: start,
          endDate: start,
          classType: 'personalAppointment',
          serviceCategoryId,
          subTypeId: currentSubTypeId,
        };

        const res: any = await dispatch(getTrainerAvailability(payload)).unwrap();
        const body = res?.data ?? res; // your console showed useful payload at res.data

        const flat = normalizeFromTrainersApi(
          body,
          selectedFacility?.label || '',
          serviceCategoryId,
          currentSubTypeId,
          currentSubTypeName
        );

        if (!alive) return;
        setScheduleData(flat);
      } catch (err) {
        if (!alive) return;
        console.error('Error fetching availability:', err);
        setScheduleData([]);
      } finally {
        if (alive) endLoader();
      }
    })();

    return () => { alive = false; };
  }, [
    selectedDate,
    selectedFacility?.value,
    JSON.stringify(subtypes), // ensures re-fetch on subtype filter change
    initialSubId,
    initialSvcId,
    dispatch,
  ]);

  // If a slot is chosen, show booking flow
  if (selectedSlot) {
    return (
      <TrialBookingFlow
        slot={selectedSlot}
        widgetId={widgetId}
        onBack={() => setSelectedSlot(null)}
      />
    );
    // If your TrialBookingFlow doesn't have onBack, remove it and handle there.
  }

  const clearAll = () => {
    setSubtypes([]);
    setSelectedFacility(facilityOptions[0] || null); // reset to first facility
  };

  // Visible list (queried by selected facility & subtype)
  const visibleSchedule = scheduleData;

  return (
    <div style={{ fontFamily: branding.fontFamily, padding: '16px' }}>
      <div style={{ marginBottom: 8 }}>
        <Button type="link" onClick={() => setLocation('/services')}>
          ← Back to services
        </Button>

        {/* Filter bar: Subtypes + Facility (single select) */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 16,
          }}
        >
          <PAFilterBar
            subtypes={subtypeGroups}
            selectedSubtypes={subtypes}
            onSubtypesChange={setSubtypes}            // triggers re-fetch via effect dep
            facilities={facilityOptions}
            selectedFacility={selectedFacility}
            onFacilityChange={setSelectedFacility}    // triggers re-fetch via effect dep
            onClearAll={clearAll}
            loading={svcLoading}
            facilitiesLoading={facLoading}
          />
        </div>
      </div>

      {/* Week selector */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <ArrowLeftOutlined onClick={goToPreviousWeek} className="cursor-pointer sm:text-[2.5rem] lg:text-xl" />
        <div style={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
          {weekDays.map((date, idx) => {
            const selected = isSameDay(date, selectedDate);
            return (
              <div
                key={idx}
                onClick={() => setSelectedDate(date)}
                style={{
                  textAlign: 'center',
                  cursor: 'pointer',
                  borderBottom: selected ? `2px solid ${branding.primaryColor}` : '1px solid #eee',
                  paddingBottom: 4,
                  minWidth: 50,
                }}
              >
                <div className="text-[1rem] text-[#999] lg:text-[0.8rem] ">{format(date, 'EEE').toUpperCase()}</div>
                <div className="text-[0.9rem] lg:text-[1.2rem] ">{format(date, 'd MMM')}</div>
              </div>
            );
          })}
        </div>
        <ArrowRightOutlined onClick={goToNextWeek} className="cursor-pointer sm:text-[2.5rem] lg:text-xl" />
      </div>

      {/* LIST view */}
      <Spin spinning={loader}>
        <div className="border-t sm:border-[#999] lg:border-[#eee]">
          {visibleSchedule.length === 0 ? (
            <div style={{ textAlign: 'center', color: '#999', padding: '24px 0' }}>
              No sessions for this day.
            </div>
          ) : (
            visibleSchedule.map((item, index) => (
              <div key={index} className="flex flex-col gap-4 border-b border-[#ddd] px-0 py-6 sm:gap-6 sm:px-6 lg:flex-row lg:items-center lg:justify-between lg:px-4">
                {/* Time + Avatar + Info */}
                <div className="flex w-full flex-row gap-4 sm:items-center sm:justify-between lg:w-[65%]">
                  <div className="w-[30%] lg:w-[20%]">
                    <div className="text-base font-semibold lg:text-lg">{item.from} - {item.to}</div>
                    {item.durationInMinutes !== '' && (
                      <div className="text-sm text-gray-500 lg:text-base">{item.durationInMinutes}-Min</div>
                    )}
                  </div>
                  <div className="order-1 flex w-[70%] flex-1 items-center gap-3 lg:w-[50%] lg:justify-around">
                    <img
                      src={item.image || 'https://staginghop.hkstest.uk/assets/Profile_icon.png'}
                      alt="avatar"
                      className="h-10 w-10 rounded-full object-cover sm:h-16 sm:w-16"
                    />
                    <div className="pe-0 lg:pe-10">
                      <div className="text-sm font-semibold lg:text-base">
                        {item.subtypeName || 'Personal Appointment'}
                      </div>
                      <div className="text-sm text-gray-500 lg:text-base">{item.trainer}</div>
                    </div>
                  </div>
                </div>

                {/* Location + Book Now */}
                <div className="flex w-full flex-row items-center justify-between gap-3 lg:w-[35%] ">
                  <div className="text-center text-base font-semibold text-gray-700 sm:text-left lg:text-lg">
                    {item.location}
                  </div>
                  <button
                    type="button"
                    className="rounded-md border border-black px-4 py-1 text-base text-black"
                    onClick={() => setSelectedSlot(item)}
                  >
                    Book Now
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </Spin>
    </div>
  );
}
