import React, { useEffect, useMemo, useState } from 'react';
import {
  startOfWeek,
  addDays,
  format,
  isSameDay,
  subWeeks,
  addWeeks,
} from 'date-fns';
import { <PERSON><PERSON>, Spin } from 'antd';
import { useAppDispatch } from '~/hooks/redux-hooks';
import {
  widgetFacilityList,
  widgetServiceType,
  getBookingRoomAvailability, // <-- your new thunk
} from '~/redux/actions/widget/widget.action';
import { useLoader } from '~/hooks/useLoader';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import PAFilterBar from './filters/PAFilterBar';
import type { SubtypeGroup, SubtypeValue } from './filters/SubTypeFilter';
import type { FacilityOption } from './filters/FacilityFilter';
import { ClassType } from '~/types/enums';

type Branding = { fontFamily?: string; primaryColor?: string };

// ---- tolerant helpers (same spirit as PA tab) ----
type AnyObj = Record<string, any>;

const pickServiceId = (x: AnyObj) =>
  String(x._id ?? x.id ?? x.slug ?? x.key ?? x.serviceId ?? x.value ?? '');
const pickServiceName = (x: AnyObj) =>
  String(x.name ?? x.title ?? x.displayName ?? x.serviceName ?? 'Unnamed');
const pickSubtypeId = (x: AnyObj) =>
  String(x._id ?? x.id ?? x.slug ?? x.key ?? x.appointmentTypeId ?? x.subtypeId ?? x.value ?? '');
const pickSubtypeName = (x: AnyObj) =>
  String(
    x.name ??
      x.title ??
      x.displayName ??
      x.appointmentTypeName ??
      x.subtypeName ??
      x.label ??
      'Untitled'
  );
function extractSubtypesFlexible(m: AnyObj): AnyObj[] {
  const candidates: any[] = [
    m.appointmentType,
    m.appointmentTypes,
    m.subTypes,
    m.subtypes,
    m.subtypeList,
    m.types,
    m.children,
  ];
  for (const c of candidates) if (Array.isArray(c) && c.length) return c;
  for (const v of Object.values(m)) {
    if (
      Array.isArray(v) &&
      v.length &&
      typeof v[0] === 'object' &&
      (('name' in v[0]) ||
        ('title' in v[0]) ||
        ('displayName' in v[0]) ||
        ('appointmentTypeName' in v[0]) ||
        ('subtypeName' in v[0]) ||
        ('label' in v[0]))
    )
      return v as AnyObj[];
  }
  return [];
}

const pickFacilityId = (x: AnyObj) =>
  String(x._id ?? x.id ?? x.facilityId ?? x.value ?? x.slug ?? x.key ?? x.code ?? x.uuid ?? '');
const pickFacilityName = (x: AnyObj) =>
  String(x.name ?? x.title ?? x.facilityName ?? x.displayName ?? x.label ?? 'Unnamed Facility');

const toMinutes = (hhmm: string) => {
  const [h, m] = (hhmm || '').split(':').map(Number);
  if (isNaN(h) || isNaN(m)) return Number.MAX_SAFE_INTEGER;
  return h * 60 + m;
};

// Normalize rooms API -> PA-style flat items
function normalizeFromRoomsApi(
  apiData: any,
  facilityName: string,
  subtypeName?: string
) {
  const rooms: any[] = Array.isArray(apiData?.rooms) ? apiData.rooms : [];
  const out: any[] = [];
  const defaultAvatar = 'https://staginghop.hkstest.uk/assets/Profile_icon.png';

  for (const r of rooms) {
    const roomId = String(r?.roomId ?? '');
    const roomName = String(r?.roomName ?? 'Room');
    const capacity = Number(r?.capacity ?? 1);
    const schedule = Array.isArray(r?.schedule) ? r.schedule : [];

    for (const s of schedule) {
      const dur =
        toMinutes(s?.to) - toMinutes(s?.from) > 0
          ? toMinutes(s.to) - toMinutes(s.from)
          : (apiData?.durationInMinutes ?? '');

      out.push({
        // fields aligned to PA list renderer
        trainer: roomName,               // shown on secondary line (like trainer name)
        trainerId: roomId,
        image: defaultAvatar,            // reuse avatar slot
        facilityId: apiData?.facilityId,
        location: facilityName || 'Facility',
        date: apiData?.date,
        from: s?.from,
        to: s?.to,
        status: 'available',
        classType: 'bookings',
        subtypeName: subtypeName || '',
        durationInMinutes: dur,
        serviceName: '',
        payRateId: undefined,
        pricing: undefined,
        serviceCategory: apiData?.serviceCategoryId,
        subTypeId: apiData?.subTypeId,
        Description: '',
      });
    }
  }

  out.sort((a, b) => toMinutes(a.from) - toMinutes(b.from));
  return out;
}

// --- component ---
export default function BookingTab({
  branding,
  widgetId,
}: {
  branding: Branding;
  widgetId: string;
}) {
  const dispatch = useAppDispatch();

  const today = useMemo(() => new Date(), []);
  const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(today, { weekStartsOn: 1 }));
  const [selectedDate, setSelectedDate] = useState(today);

  const [scheduleData, setScheduleData] = useState<any[]>([]);
  const [loader, startLoader, endLoader] = useLoader();
  const [selectedSlot, setSelectedSlot] = useState<any | null>(null);

  // Filters state (same as PA tab)
  const [apiGroups, setApiGroups] = useState<SubtypeGroup[]>([]);
  const [subtypes, setSubtypes] = useState<SubtypeValue[]>([]);
  const [svcLoading, setSvcLoading] = useState<boolean>(false);

  const [facilityOptions, setFacilityOptions] = useState<FacilityOption[]>([]);
  const [selectedFacility, setSelectedFacility] = useState<FacilityOption | null>(null);
  const [facLoading, setFacLoading] = useState<boolean>(false);

  // Week Navigation (same as PA tab)
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeekStart, i));
  const goToPreviousWeek = () => { const ns = subWeeks(currentWeekStart, 1); setCurrentWeekStart(ns); setSelectedDate(ns); };
  const goToNextWeek = () => { const ns = addWeeks(currentWeekStart, 1); setCurrentWeekStart(ns); setSelectedDate(ns); };

  // --- Services + Subtypes (for Bookings) ---
  useEffect(() => {
    if (!widgetId) return;
    let alive = true;
    (async () => {
      try {
        setSvcLoading(true);
        const classType = [ClassType.BOOKING]; // "bookings"
        const raw = await dispatch(widgetServiceType({ organizationId: widgetId, classType })).unwrap();
        // expect { bookings: [...] }
        const data = (raw?.res?.data ?? raw) as Partial<{ bookings: AnyObj[] }>;
        const nextList = (data?.bookings ?? []).filter((m) => (m?.isActive ?? true));

        const groups: SubtypeGroup[] = nextList.map((m) => {
          const svcId = pickServiceId(m);
          const subs = extractSubtypesFlexible(m);
          const opts = Array.isArray(subs)
            ? subs.map((s) => ({ label: pickSubtypeName(s), value: pickSubtypeId(s) })).filter((o) => o.value)
            : [];
          return { label: pickServiceName(m), options: opts, value: svcId as any };
        });

        if (!alive) return;
        setApiGroups(groups);
        // no auto-select to force explicit choice like PA tab
      } catch (err) {
        if (alive) { console.error(err || 'Failed to load services/subtypes'); setApiGroups([]); }
      } finally {
        if (alive) setSvcLoading(false);
      }
    })();
    return () => { alive = false; };
  }, [widgetId, dispatch]);

  const subtypeGroups: SubtypeGroup[] = useMemo(() => apiGroups, [apiGroups]);

  // --- Facilities (same as PA tab) ---
  useEffect(() => {
    if (!widgetId) return;
    let alive = true;
    (async () => {
      try {
        setFacLoading(true);
        const raw = await dispatch(widgetFacilityList(widgetId)).unwrap();
        const arr: AnyObj[] = (raw?.data?.data ?? raw ?? []) as AnyObj[];

        const opts: FacilityOption[] = Array.isArray(arr)
          ? arr
            .map((x) => ({ label: pickFacilityName(x), value: pickFacilityId(x) }))
            .filter((o) => o.value || o.label)
          : [];

        if (!alive) return;
        setFacilityOptions(opts);
        if (opts.length) setSelectedFacility((prev) => prev ?? opts[0]);
      } catch (err) {
        if (!alive) return;
        console.error(err || 'Failed to load facility list');
        setFacilityOptions([]);
        setSelectedFacility(null);
      } finally {
        if (alive) setFacLoading(false);
      }
    })();
    return () => { alive = false; };
  }, [widgetId, dispatch]);

  // Resolve serviceCategoryId from selected subtype (same logic as PA tab)
  const resolveServiceCategoryId = (): string | undefined => {
    const chosenSubId = subtypes[0]?.value;
    if (chosenSubId) {
      const host = subtypeGroups.find((g) => g.options?.some((o) => String(o.value) === String(chosenSubId)));
      if (host?.value) return String(host.value);
    }
    return undefined;
  };

  // --- Fetch rooms availability for the selected day (match PA tab effect style) ---
  useEffect(() => {
    let alive = true;

    (async () => {
      if (!selectedFacility?.value) { setScheduleData([]); return; }
      if (!subtypes[0]?.value) { setScheduleData([]); return; }

      startLoader();
      try {
        const serviceCategoryId = resolveServiceCategoryId();
        if (!serviceCategoryId) { setScheduleData([]); return; }

        const payload = {
          organizationId: widgetId,
          facilityId: String(selectedFacility.value),
          serviceCategoryId,
          subTypeId: String(subtypes[0].value),
          date: format(selectedDate, 'yyyy-MM-dd'),
        };

        const res: any = await dispatch(getBookingRoomAvailability(payload)).unwrap();
        const body = res?.data ?? res;

        const flat = normalizeFromRoomsApi(
          body,
          selectedFacility?.label || '',
          subtypes[0]?.label
        );

        if (!alive) return;
        setScheduleData(flat);
      } catch (err) {
        if (!alive) return;
        console.error('Error fetching room availability:', err);
        setScheduleData([]);
      } finally {
        if (alive) endLoader();
      }
    })();

    return () => { alive = false; };
  }, [
    selectedDate,
    selectedFacility?.value,
    JSON.stringify(subtypes), // re-fetch when subtype changes
    dispatch,
  ]);

  const clearAll = () => {
    setSubtypes([]);
    setSelectedFacility(facilityOptions[0] || null);
  };

  // Visible list
  const visibleSchedule = scheduleData;

  return (
    <div style={{ fontFamily: branding.fontFamily, padding: '16px' }}>
      {/* Filter bar: SAME component & layout as PA tab */}
      <div style={{ marginBottom: 8 }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 16,
          }}
        >
          <PAFilterBar
            subtypes={subtypeGroups}
            selectedSubtypes={subtypes}
            onSubtypesChange={setSubtypes}
            facilities={facilityOptions}
            selectedFacility={selectedFacility}
            onFacilityChange={setSelectedFacility}
            onClearAll={clearAll}
            loading={svcLoading}
            facilitiesLoading={facLoading}
          />
        </div>
      </div>

      {/* Week selector: SAME markup/classes as PA tab */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <ArrowLeftOutlined onClick={goToPreviousWeek} className="cursor-pointer sm:text-[2.5rem] lg:text-xl" />
        <div style={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
          {weekDays.map((date, idx) => {
            const selected = isSameDay(date, selectedDate);
            return (
              <div
                key={idx}
                onClick={() => setSelectedDate(date)}
                style={{
                  textAlign: 'center',
                  cursor: 'pointer',
                  borderBottom: selected ? `2px solid ${branding.primaryColor}` : '1px solid #eee',
                  paddingBottom: 4,
                  minWidth: 50,
                }}
              >
                <div className="text-[1rem] text-[#999] lg:text-[0.8rem] ">{format(date, 'EEE').toUpperCase()}</div>
                <div className="text-[0.9rem] lg:text-[1.2rem] ">{format(date, 'd MMM')}</div>
              </div>
            );
          })}
        </div>
        <ArrowRightOutlined onClick={goToNextWeek} className="cursor-pointer sm:text-[2.5rem] lg:text-xl" />
      </div>

      {/* LIST view: SAME card layout as PA tab */}
      <Spin spinning={loader}>
        <div className="border-t sm:border-[#999] lg:border-[#eee]">
          {(!subtypes[0]?.value || !selectedFacility?.value) ? (
            <div style={{ textAlign: 'center', color: '#999', padding: '24px 0' }}>
              Select a subtype and facility to view availability.
            </div>
          ) : visibleSchedule.length === 0 ? (
            <div style={{ textAlign: 'center', color: '#999', padding: '24px 0' }}>
              No sessions for this day.
            </div>
          ) : (
            visibleSchedule.map((item, index) => (
              <div key={index} className="flex flex-col gap-4 border-b border-[#ddd] px-0 py-6 sm:gap-6 sm:px-6 lg:flex-row lg:items-center lg:justify-between lg:px-4">
                {/* Time + Avatar + Info */}
                <div className="flex w-full flex-row gap-4 sm:items-center sm:justify-between lg:w-[65%]">
                  <div className="w-[30%] lg:w-[20%]">
                    <div className="text-base font-semibold lg:text-lg">{item.from} - {item.to}</div>
                    {item.durationInMinutes !== '' && (
                      <div className="text-sm text-gray-500 lg:text-base">
                        {item.durationInMinutes}-Min
                      </div>
                    )}
                  </div>
                  <div className="order-1 flex w-[70%] flex-1 items-center gap-3 lg:w-[50%] lg:justify-around">
                    <img
                      src={item.image || 'https://staginghop.hkstest.uk/assets/Profile_icon.png'}
                      alt="avatar"
                      className="h-10 w-10 rounded-full object-cover sm:h-16 sm:w-16"
                    />
                    <div className="pe-0 lg:pe-10">
                      <div className="text-sm font-semibold lg:text-base">
                        {item.subtypeName || 'Booking'}
                      </div>
                      <div className="text-sm text-gray-500 lg:text-base">{item.trainer /* roomName */}</div>
                    </div>
                  </div>
                </div>

                {/* Location + Book Now */}
                <div className="flex w-full flex-row items-center justify-between gap-3 lg:w-[35%] ">
                  <div className="text-center text-base font-semibold text-gray-700 sm:text-left lg:text-lg">
                    {item.location}
                  </div>
                  <button
                    type="button"
                    className="rounded-md border border-black px-4 py-1 text-base text-black"
                    onClick={() => setSelectedSlot(item)}
                  >
                    Book Now
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </Spin>

      {selectedSlot && (
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <div style={{ marginBottom: 8 }}>
            Selected: <b>{selectedSlot.trainer}</b> — {selectedSlot.from} to {selectedSlot.to}
          </div>
          <Button onClick={() => setSelectedSlot(null)}>Close</Button>
        </div>
      )}
    </div>
  );
}
