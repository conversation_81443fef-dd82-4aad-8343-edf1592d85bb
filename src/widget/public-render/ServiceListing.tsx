import React, { useEffect, useState } from 'react';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { widgetServiceType } from '~/redux/actions/widget/widget.action';
import { Spin } from 'antd';
import { useLocation } from 'wouter';
import { ClassType } from '~/types/enums';
import { setSelectedService } from '~/redux/slices/widget/selectedService.slice';
function postFiltersToParent(
  filters: Record<string, string>,
  mode: 'live' | 'apply' = 'apply'
) {
  const root = document.getElementById('hop-widget-root');
  if (!root) return;
  root.dispatchEvent(new CustomEvent('hop-filter-change', { detail: { mode, filters } }));
}
type SubType = {
  _id: string;
  name: string;
  durationInMinutes: number;
  onlineBookingAllowed: boolean;
  isActive: boolean;
  isFeatured?: boolean;
  image?: string;
};
type MainType = {
  _id: string;
  name: string;
  description?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  image?: string;
  appointmentType: SubType[];
};
type Branding = { fontFamily?: string; primaryColor?: string };

type SubRowProps = { name: string; image?: string; isLast?: boolean; onClick?: () => void };

const initials = (name: string) =>
  name.trim().split(/\s+/).map((w) => w[0]).join('').slice(0, 2).toUpperCase();


function SubRow({ name, image, onClick }: SubRowProps) {
  const [error, setError] = useState(false);
  return (
    <div
      className={`flex items-center gap-3 px-3 py-2 border-b border-gray-200 bg-white ${
        onClick ? 'cursor-pointer hover:bg-gray-50 active:bg-gray-100' : ''
      }`}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : -1}
      onKeyDown={(e) => {
        if (!onClick) return;
        if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); onClick(); }
      }}
    >
      <div className="w-9 h-9 rounded-full border border-gray-200 bg-gray-100 overflow-hidden flex items-center justify-center font-semibold text-xs text-gray-600">
        {!!image && !error ? (
          <img src={image} className="w-full h-full object-cover" onError={() => setError(true)} />
        ) : (
          initials(name)
        )}
      </div>
      <div className="text-sm text-gray-900 truncate">{name}</div>
    </div>
  );
}


// ⬇️ Removed the box (no rounded/border/bg wrapper). Only lines between rows.
function MainBlock({
  main,
  classType,
  onSelectSubType,
}: {
  main: MainType;
  classType: 'personalAppointment' | 'bookings';
  onSelectSubType?: (sub: SubType, parent: MainType, classType: 'personalAppointment' | 'bookings') => void;
}) {
  const subs = (main.appointmentType || []).filter((s) => s.isActive);
  return (
    <div className="py-3"> {/* removed border-b here */}
      <p className="font-bold text-gray-900 mb-2">{main.name}</p>
      {subs.length ? (
        <div className="bg-transparent">
          {subs.map((s) => (
            <SubRow
              key={s._id}
              name={s.name}
              image={s.image}
              onClick={() => onSelectSubType?.(s, main, classType)}
            />
          ))}
        </div>
      ) : (
        <div className="text-sm text-gray-500">No appointment types</div>
      )}
    </div>
  );
}


export default function ServiceListing({
  organizationId,
  branding = {},
}: {
  organizationId: string;
  branding?: Branding;
}) {
  const dispatch = useAppDispatch();
  const [, setLocation] = useLocation();

  const [pA, setPA] = useState<MainType[]>([]);
  const [bookings, setBookings] = useState<MainType[]>([]);
  const [loading, setLoading] = useState(false);

 const handleSelect = (sub: SubType, parent: MainType, classType: string) => {
  dispatch(setSelectedService({ sub, parent }));

  const route =
    classType === ClassType.PERSONAL_APPOINTMENT ? 'schedule' :
    classType === ClassType.BOOKING ? 'booking' : 'schedule';

  const filters = {
    route,
    classType,               // keep if you use it elsewhere
    subId: sub._id,
    parentId: parent._id,
    title: sub.name,
  };

  // 1) Navigate inside the iframe (so your widget works on its own)
  setLocation(`/${route}?` + new URLSearchParams(filters).toString());

  // 2) Tell the parent to mirror these into the PARENT URL (pushState)
  postFiltersToParent(filters, 'apply');
};

  useEffect(() => {
    if (!organizationId) return;
    let alive = true;
    (async () => {
      setLoading(true);
      try {
        const classType = ['personalAppointment', 'bookings'];
        const raw = await dispatch(widgetServiceType({ organizationId, classType })).unwrap();
        const data = (raw?.res?.data ?? raw) as Partial<{
          personalAppointment: MainType[];
          bookings: MainType[];
        }>;
        const nextPA = (data.personalAppointment ?? []).filter((m) => m?.isActive ?? true);
        const nextBookings = (data.bookings ?? []).filter((m) => m?.isActive ?? true);
        if (alive) {
          setPA(nextPA);
          setBookings(nextBookings);
        }
      } catch (err) {
        if (alive) console.error(err || 'Failed to load services');
      } finally {
        if (alive) setLoading(false);
      }
    })();
    return () => {
      alive = false;
    };
  }, [organizationId, dispatch]);

  if (loading)
    return (
      <div className="flex justify-center items-center py-10">
        <Spin />
      </div>
    );

  return (
    <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-16 py-10">
      <section className="pr-0 md:pr-10">
        <h2 className="text-[22px] font-medium text-gray-900 mb-6">Personal Appointment</h2>
        <div>
          {pA.map((m) => (
            <MainBlock key={m._id} main={m} classType="personalAppointment" onSelectSubType={handleSelect} />
          ))}
          {!pA.length && <div className="text-gray-500">No personal appointments</div>}
        </div>
      </section>

      <section className="pl-0 md:pl-10 md:border-l border-gray-200">
        <h2 className="text-[22px] font-medium text-gray-900 mb-6">Bookings</h2>
        <div>
          {bookings.map((m) => (
            <MainBlock key={m._id} main={m} classType="bookings" onSelectSubType={handleSelect} />
          ))}
          {!bookings.length && <div className="text-gray-500">No bookings</div>}
        </div>
      </section>
    </div>
  );
}
