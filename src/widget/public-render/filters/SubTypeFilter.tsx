import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Input, Checkbox, Divider, Spin } from 'antd';
import { DownOutlined } from '@ant-design/icons';

export type SubtypeValue = { label: string; value: string };
export type SubtypeGroup = {
    label: string;
    options: SubtypeValue[];
    value?: string;
};

type Props = {
    subtypes: SubtypeGroup[];
    selected: SubtypeValue[];
    onChange: (next: SubtypeValue[]) => void;
    onClear?: () => void;
    buttonLabel?: string;
    loading?: boolean;
    disabled?: boolean;
};

const SubTypeFilter: React.FC<Props> = ({
    subtypes,
    selected,
    onChange,
    onClear,
    buttonLabel = 'Subtypes',
    loading,
    disabled,
}) => {
    const rootRef = useRef<HTMLDivElement>(null);
    const [open, setOpen] = useState(false);
    const [q, setQ] = useState('');

    // close on outside click
    useEffect(() => {
        const onDocDown = (e: MouseEvent) => {
            if (!rootRef.current) return;
            if (!rootRef.current.contains(e.target as Node)) setOpen(false);
        };
        document.addEventListener('mousedown', onDocDown, true);
        return () => document.removeEventListener('mousedown', onDocDown, true);
    }, []);

    const selectedMap = useMemo(() => {
        const m = new Map<string, SubtypeValue>();
        selected.forEach((s) => m.set(String(s.value), s));
        return m;
    }, [selected]);

    const filteredGroups = useMemo(() => {
        const query = q.trim().toLowerCase();
        if (!query) return subtypes;
        return subtypes
            .map((g) => ({
                ...g,
                options: g.options.filter((o) =>
                    o.label.toLowerCase().includes(query)
                ),
            }))
            .filter((g) => g.options.length > 0);
    }, [q, subtypes]);

    const toggleOption = (opt: SubtypeValue) => {
        const id = String(opt.value);
        const next = new Map(selectedMap);
        if (next.has(id)) next.delete(id);
        else next.set(id, opt);
        onChange(Array.from(next.values()));
    };

    return (
        <div
            ref={rootRef}
            style={{ position: 'relative', display: 'inline-block' }}
        >
            <button
                className="w-[65vw] lg:w-[13vw]"
                type="button"
                onClick={() => !disabled && setOpen((o) => !o)}
                style={{
                    border: '1px solid #ddd',
                    borderRadius: 8,
                    padding: '6px 12px',
                    background: '#fff',
                    cursor: disabled ? 'not-allowed' : 'pointer',
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: 6,
                }}
            >
                {buttonLabel} <DownOutlined style={{ fontSize: 12 }} />
            </button>

            {open && (
                <div
                    style={{
                        position: 'absolute',
                        top: 'calc(100% + 8px)',
                        left: 0,
                        width: 320,
                        maxHeight: 360,
                        overflow: 'auto',
                        background: '#fff',
                        border: '1px solid #eee',
                        borderRadius: 12,
                        boxShadow:
                            '0 10px 15px rgba(0,0,0,0.08), 0 3px 6px rgba(0,0,0,0.06)',
                        zIndex: 100000,
                        padding: 10,
                    }}
                    onMouseDown={(e) => e.stopPropagation()}
                    onClick={(e) => e.stopPropagation()}
                >
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8,
                        }}
                    >
                        <div style={{ fontWeight: 600, fontSize: 14, flex: 1 }}>
                            Subtypes
                        </div>
                        {onClear ? (
                            <a
                                onMouseDown={(e) => e.stopPropagation()}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onClear();
                                }}
                                style={{ fontSize: 12 }}
                            >
                                Clear All
                            </a>
                        ) : null}
                    </div>

                    <div style={{ marginTop: 8, marginBottom: 8 }}>
                        <Input
                            placeholder="Search subtypes"
                            value={q}
                            onChange={(e) => setQ(e.target.value)}
                            allowClear
                            disabled={loading}
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => e.stopPropagation()}
                        />
                    </div>

                    <Divider style={{ margin: '8px 0' }} />

                    {loading ? (
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                                padding: 12,
                            }}
                        >
                            <Spin />
                        </div>
                    ) : filteredGroups.length === 0 ? (
                        <div style={{ color: '#999', padding: '12px 0' }}>
                            No appointment types found.
                        </div>
                    ) : (
                        filteredGroups.map((group, gi) => (
                            <div key={gi} style={{ marginBottom: 10 }}>
                                <div
                                    style={{
                                        fontSize: 13,
                                        fontWeight: 600,
                                        marginBottom: 6,
                                    }}
                                >
                                    {group.label}
                                </div>
                                {group.options && group.options.length > 0 ? (
                                    <div
                                        style={{
                                            display: 'grid',
                                            gridTemplateColumns: '1fr',
                                            rowGap: 6,
                                        }}
                                    >
                                        {group.options.map((opt, oi) => {
                                            const checked = selectedMap.has(
                                                String(opt.value)
                                            );
                                            return (
                                                <Checkbox
                                                    key={oi}
                                                    checked={checked}
                                                    onMouseDown={(e) =>
                                                        e.stopPropagation()
                                                    }
                                                    onClick={(e) =>
                                                        e.stopPropagation()
                                                    }
                                                    onChange={() =>
                                                        toggleOption(opt)
                                                    }
                                                >
                                                    {opt.label}
                                                </Checkbox>
                                            );
                                        })}
                                    </div>
                                ) : (
                                    <div
                                        style={{
                                            color: '#aaa',
                                            fontSize: 12,
                                            paddingLeft: 2,
                                        }}
                                    >
                                        No appointment types
                                    </div>
                                )}
                            </div>
                        ))
                    )}
                </div>
            )}
        </div>
    );
};

export default SubTypeFilter;
