import React from 'react';
import SubTypeFilter, { SubtypeGroup, SubtypeValue } from './SubTypeFilter';
import FacilityFilter, { FacilityOption } from './FacilityFilter';

type Props = {
    // Subtypes (appointment types)
    subtypes: SubtypeGroup[];
    selectedSubtypes: SubtypeValue[];
    onSubtypesChange: (values: SubtypeValue[]) => void;

    // Facility single-select
    facilities: FacilityOption[];
    selectedFacility: FacilityOption | null;
    onFacilityChange: (value: FacilityOption | null) => void;

    onClearAll: () => void;
    loading?: boolean;
    facilitiesLoading?: boolean;
};

const PAFilterBar: React.FC<Props> = ({
    subtypes,
    selectedSubtypes,
    onSubtypesChange,
    facilities,
    selectedFacility,
    onFacilityChange,
    onClearAll,
    loading,
    facilitiesLoading,
}) => {
    return (
        <div className="flex flex-col items-center gap-3 lg:flex-row">
            <SubTypeFilter
                subtypes={subtypes}
                selected={selectedSubtypes}
                onChange={onSubtypesChange}
                onClear={onClearAll}
                loading={loading}
                buttonLabel="Subtypes"
            />

            <FacilityFilter
                facilities={facilities}
                selected={selectedFacility}
                onChange={onFacilityChange}
                loading={facilitiesLoading}
            />

            <a onClick={onClearAll} style={{ marginLeft: 8 }}>
                Clear All Filter
            </a>
        </div>
    );
};

export default PAFilterBar;
