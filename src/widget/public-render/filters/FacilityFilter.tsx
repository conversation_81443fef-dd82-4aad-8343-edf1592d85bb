import React from 'react';
import { Select } from 'antd';

export type FacilityOption = { label: string; value: string };

type Props = {
    facilities: FacilityOption[];
    selected: FacilityOption | null;
    onChange: (opt: FacilityOption | null) => void;
    loading?: boolean;
};

const FacilityFilter: React.FC<Props> = ({
    facilities,
    selected,
    onChange,
    loading,
}) => {
    return (
        <Select
            // style={{ minWidth: 220 }}
            className="w-[65vw] lg:w-[13vw]"
            placeholder="Facilities"
            options={facilities}
            value={selected?.value}
            onChange={(val) => {
                const next =
                    facilities.find((f) => String(f.value) === String(val)) ||
                    null;
                onChange(next);
            }}
            loading={loading}
            showSearch
            optionFilterProp="label"
            getPopupContainer={() =>
                document.getElementById('hop-widget-root') || document.body
            }
        />
    );
};

export default FacilityFilter;
